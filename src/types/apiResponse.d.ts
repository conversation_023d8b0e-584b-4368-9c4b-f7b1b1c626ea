import { TransactionDocument } from "../models/Transaction";
import { UserDocument } from "../models/User";
import { RewardDocument } from "../models/Reward";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { GiftDocument } from "../models/Gift";

export interface PaginatedTransactionsResponse<T extends TransactionDocument> {
  pagination: {
    page: number;
    pageSize: number;
    pages: number;
    total: number;
  };
  transactions: T[];
}

export interface PaginatedUsersResponse {
  pagination: {
    page: number;
    pageSize: number;
    pages: number;
    total: number;
  };
  users: UserDocument[];
}

export interface PaginatedRewardsResponse {
  pagination: {
    page: number;
    pageSize: number;
    pages: number;
    total: number;
  };
  rewards: RewardDocument[];
}

export interface PaginatedGiftsResponse {
  pagination: {
    page: number;
    pageSize: number;
    pages: number;
    total: number;
  };
  gifts: GiftDocument[];
}

export interface AnalyticsResponse {
  analytics: AnalyticsByDateType[];
}

export interface UnsubmittedOrderAnalyticsResponse {
  lines: {
    [etf in investmentUniverseConfig.AssetType]: {
      buyOrders: number;
      buyConsideration: number;
      sellOrders: number;
      sellConsideration: number;
    };
  };
}

export type AnalyticsByDateType = {
  date: string;
  lines: {
    [etf in investmentUniverseConfig.AssetType]: {
      buyOrders: number;
      buyConsideration: number;
      sellOrders: number;
      sellConsideration: number;
      allMatched: boolean;
      allRejected: boolean;
      allInTerminalState: boolean;
    };
  };
};

export interface PaginatedApiResponse<T> extends ApiResponse<T> {
  pagination: {
    page: number;
    pageSize: number;
    pages: number;
    total: number;
  };
}

export interface ApiResponse<T> {
  data: T[];
}
