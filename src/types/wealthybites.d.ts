import { investmentUniverseConfig, currenciesConfig, localeConfig } from "@wealthyhood/shared-configs";

export interface WealthybitesEmailBody {
  firstName?: string;
  userCurrency: currenciesConfig.MainCurrencyType;
  userLocale: localeConfig.LocaleType;
  userEmail: string;
  investmentSnapshot?: InvestmentSnapshotData;
  topPortfolioMovers?: {
    winners: TopPortfolioMoverItem[];
    losers: TopPortfolioMoverItem[];
  };
  marketRoundup: string;
  topMarketIndexes: MarketIndexItem[];
  topNews: TopNewsItem[];
  analystInsights: {
    insights: LearnInsight[];
    quickTakes: LearnInsight[];
  };
  learningGuide: LearningGuideData;
}

export interface InvestmentSnapshotData {
  investmentsValue: number;
  weeklyReturns: number; // Absolute value change, e.g., 25.05
  upByValue: number; // Percentage change, e.g., 2.05 (positive if up, negative if down)
  cashValue: number;
  savingsValue?: number;
  savingsDailyInterest?: number;
  totalValue: number;
}

export interface TopPortfolioMoverItem {
  assetId: investmentUniverseConfig.AssetType;
  weeklyReturns: number;
}

export interface MarketIndexItem {
  name: string;
  returns: number;
}

export interface TopNewsItem {
  imageUrl: string;
  companyName: string;
  title: string;
  content: string;
}

export interface LearnInsight {
  emoji: string;
  title: string;
  url: string;
}

export interface LearningGuideData {
  title: string;
  url: string;
  chapters: string[];
}
