import { Request, Response } from "express";
import { ParticipantRoleType } from "../models/Participant";
import ParticipantService from "../services/participantService";
import ParamsValidationUtil from "../utils/paramsValidationUtil";

export default class AdminParticipantController {
  public static async createParticipant(req: Request, res: Response): Promise<Response> {
    const email = req.body.email as string;
    const influencerId = req.body.influencerId as string;
    const referrerEmail = req.body.referrerEmail as string;
    const referrerWlthdId = req.body.referrerId as string;
    const participantRole = req.body.participantRole as ParticipantRoleType;

    ParamsValidationUtil.isEmailParamValid("email", email, { isRequired: false });
    ParamsValidationUtil.isEmailParamValid("referrerEmail", referrerEmail, { isRequired: false });

    const participantData = {
      email,
      influencerId,
      referrerEmail,
      referrerWlthdId,
      participantRole
    };
    const participant = await ParticipantService.createParticipant(participantData);
    return res.status(200).json(participant);
  }

  public static async getParticipants(req: Request, res: Response): Promise<Response> {
    // validate email
    const email = req.query.email as string;
    ParamsValidationUtil.isEmailParamValid("email", email, { isRequired: false });

    const participantRole = req.query.participantRole as ParticipantRoleType;
    const grsfId = req.query.grsf as string;

    const participants = await ParticipantService.getParticipants({ email, grsfId, participantRole });
    return res.status(200).json(participants);
  }
}
