import { CustomRequest } from "custom";
import { Response } from "express";
import AddressService from "../services/addressService";
import { BadRequestError } from "../models/ApiErrors";
import { AddressDTOInterface } from "../models/Address";
import { removeSpaces } from "../utils/stringUtil";

export default class AddressController {
  public static readonly createOrUpdateAddress = async (req: CustomRequest, res: Response): Promise<Response> => {
    const owner = req.user.id;

    ["line1", "city", "countryCode", "postalCode"].forEach((prop) => {
      if (!req.body[prop]) {
        throw new BadRequestError(`Missing field '${prop}'`, "Operation failed");
      }
    });

    const addressData: Partial<AddressDTOInterface> = {
      line1: req.body.line1,
      line2: req.body.line2,
      line3: req.body.line3,
      city: req.body.city,
      region: req.body.region,
      countryCode: req.body.countryCode,
      postalCode: removeSpaces(req.body.postalCode)
    };

    await AddressService.createOrUpdateAddress(owner, addressData);

    return res.sendStatus(204);
  };
}
