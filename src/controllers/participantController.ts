import { Request, Response } from "express";
import AppsflyerService from "../external-services/appsflyerService";
import { ParticipantDocument, ParticipantRoleType } from "../models/Participant";
import ParticipantService from "../services/participantService";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import UserService from "../services/userService";
import RewardInvitationService from "../services/rewardInvitationService";
import logger from "../external-services/loggerService";

export default class ParticipantController {
  public static async createParticipant(req: Request, res: Response): Promise<Response> {
    const email = req.body.email as string;

    ParamsValidationUtil.isEmailParamValid("email", email, { isRequired: false });

    // participant & referral data
    const { anonymousId, appsflyerId, gaClientId, pageUserLanded, grsf, wlthd, sid, submissionTechClickId } =
      AppsflyerService.parseAttributionParams(req.headers);
    const trackingSource = AppsflyerService.parseSource(req.headers);
    const googleAdsMetadata = AppsflyerService.parseGoogleAdMetadata(req.headers);

    const participantData: any = {
      appInstallInfo: {
        createdAt: new Date(),
        platform: req.headers.platform
      },
      email,
      anonymousId,
      appsflyerId,
      gaClientId,
      pageUserLanded,
      googleAdsMetadata,
      influencerId: sid,
      participantRole: "BASIC" as ParticipantRoleType,
      trackingSource,
      submissionTechClickId
    };

    const existingRewardInvitation = (
      await RewardInvitationService.getRewardInvitations({ targetUserEmail: email })
    )?.[0];

    if (existingRewardInvitation) {
      const referrerId = existingRewardInvitation.referrer.toString();

      const reffererParticipant: ParticipantDocument = (
        await UserService.getUser(referrerId, { participant: true })
      ).participant;

      if (reffererParticipant) {
        participantData.referrerWlthdId = reffererParticipant.wlthdId;
      } else {
        // Refferer has probably deleted their account or we didn't create a corresponding participant for some reason
        logger.error(
          `Attempted to set refferer for participant with email ${email}, but participant does not exist for refferer with id ${referrerId}`,
          {
            module: "participantController",
            method: "createParticipant",
            userEmail: email
          }
        );
      }
    } else if (wlthd) {
      participantData.referrerWlthdId = wlthd;
    } else if (grsf) {
      participantData.referrerGrsfId = grsf;
    }

    const participant = await ParticipantService.createParticipant(participantData, {
      emitEmailSubmittedEvent: true
    });
    return res.status(200).json(participant);
  }
}
