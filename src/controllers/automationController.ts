import { CustomRequest } from "custom";
import { Response } from "express";
import AutomationService from "../services/automationService";
import {
  AutomationCategoryArray,
  AutomationCategoryType,
  AutomationDocument,
  AutomationDTOInterface,
  SavingsTopUpAutomationDTOInterface,
  TopUpAutomationDTOInterface
} from "../models/Automation";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import PortfolioService, { PortfolioAllocationMethodEnum } from "../services/portfolioService";
import { UserDocument } from "../models/User";
import UserService from "../services/userService";
import { PortfolioDocument } from "../models/Portfolio";
import Decimal from "decimal.js";
import { BadRequestError } from "../models/ApiErrors";
import logger from "../external-services/loggerService";
import { investmentsConfig, savingsUniverseConfig } from "@wealthyhood/shared-configs";
import DateUtil from "../utils/dateUtil";
import { MandateDocument } from "../models/Mandate";
import MandateService from "../services/mandateService";
import CurrencyUtil from "../utils/currencyUtil";
import ConfigUtil from "../utils/configUtil";

const { MIN_ALLOWED_RECURRING_TOP_UP, MAX_ALLOWED_RECURRING_TOP_UP } = investmentsConfig;

export default class AutomationController {
  public static async getAutomations(req: CustomRequest, res: Response): Promise<Response> {
    const owner = req.user.id as string;
    const category = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "category",
      req.query.category,
      AutomationCategoryArray,
      { isRequired: false }
    ) as AutomationCategoryType;

    return res.status(200).json(
      await AutomationService.getAutomations({
        owner,
        categories: category ? [category] : null
      })
    );
  }

  public static async setupAutomation(req: CustomRequest, res: Response): Promise<Response> {
    const owner = req.user.id as string;
    const category = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "category",
      req.body.category as string,
      AutomationCategoryArray,
      {
        isRequired: true
      }
    ) as AutomationCategoryType;

    const user: UserDocument = await UserService.getUser(owner);
    const portfolio: PortfolioDocument = await PortfolioService.getGeneralInvestmentPortfolio(user);

    let automationData: AutomationDTOInterface;
    if (category === "TopUpAutomation") {
      const allocationMethod = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
        "allocationMethod",
        req.body.allocationMethod,
        PortfolioAllocationMethodEnum,
        {
          isRequired: false
        }
      ) as PortfolioAllocationMethodEnum;

      if (
        !portfolio.isTargetAllocationSetup &&
        allocationMethod === PortfolioAllocationMethodEnum.TARGET_ALLOCATION
      ) {
        throw new BadRequestError("Target allocation is not set up for this portfolio");
      }

      const dayOfMonth = AutomationController._validateDayOfMonth(req);
      const orderAmount = AutomationController._validateTopUpOrderAmount(req);
      const mandate = await AutomationController._validateTopUpMandate(req);

      automationData = {
        category,
        owner: user.id,
        portfolio: portfolio.id,
        mandate: mandate.id,
        frequency: "monthly",
        dayOfMonth,
        allocationMethod,
        consideration: {
          amount: Decimal.mul(orderAmount, 100).toNumber(),
          currency: user.currency
        }
      } as TopUpAutomationDTOInterface;

      const postponeActivation = ParamsValidationUtil.isBooleanParamValid(
        "postponeActivation",
        req.body.postponeActivation as string,
        {
          isRequired: false
        }
      );
      if (postponeActivation === true) {
        automationData.initialiseAt = DateUtil.getDateAfterNdays(new Date(), 15);
      }
    } else if (category === "SavingsTopUpAutomation") {
      const dayOfMonth = AutomationController._validateDayOfMonth(req);
      const orderAmount = AutomationController._validateTopUpOrderAmount(req);
      const mandate = await AutomationController._validateTopUpMandate(req);

      const savingsProduct = req.body.savingsProduct as savingsUniverseConfig.SavingsProductType;
      ParamsValidationUtil.isSavingProductValidValid("savingsProduct", savingsProduct, { isRequired: true });

      automationData = {
        category,
        owner: user.id,
        portfolio: portfolio.id,
        mandate: mandate.id,
        frequency: "monthly",
        savingsProduct: savingsProduct,
        dayOfMonth,
        consideration: {
          amount: Decimal.mul(orderAmount, 100).toNumber(),
          currency: user.currency
        }
      } as SavingsTopUpAutomationDTOInterface;
    } else if (category === "RebalanceAutomation") {
      if (!portfolio.isTargetAllocationSetup) {
        throw new BadRequestError("Target allocation is not set up for this portfolio");
      }

      automationData = {
        category,
        owner: user.id,
        portfolio: portfolio.id,
        frequency: "monthly"
      };
    }

    const automation = await AutomationService.createOrUpdateAutomation(automationData);

    return res.status(200).json(automation);
  }

  public static async cancelAutomation(req: CustomRequest, res: Response): Promise<Response> {
    const owner = req.user.id as string;
    const automationId = req.params.id;

    const automation: AutomationDocument = await AutomationService.getAutomation(automationId, {
      owner: false,
      mandate: false,
      portfolio: false
    });

    if (automation.owner.toString() !== owner) {
      throw new BadRequestError("User does not own this automation");
    }

    const cancelledAutomation = await AutomationService.cancelAutomation(automation);

    return res.status(200).json(cancelledAutomation);
  }

  private static _validateDayOfMonth(req: CustomRequest): number {
    const dayOfMonth = ParamsValidationUtil.isNumericParamValid("dayOfMonth", req.body.dayOfMonth, {
      isRequired: false
    });
    if (dayOfMonth > 28) {
      throw new BadRequestError(
        `Day of month should be between 1 and 28 or set to -1 but instead is ${dayOfMonth}`
      );
    }

    return dayOfMonth;
  }

  private static _validateTopUpOrderAmount(req: CustomRequest): number {
    const orderAmount = ParamsValidationUtil.isNumericParamValid("orderAmount", req.body.orderAmount);
    if (orderAmount < MIN_ALLOWED_RECURRING_TOP_UP) {
      logger.warn(
        `Attempted to set-up recurring top-up with less than ${CurrencyUtil.formatCurrency(MIN_ALLOWED_RECURRING_TOP_UP, req.user.currency, ConfigUtil.getDefaultUserLocale(req.user.residencyCountry))}`,
        {
          module: "AutomationController",
          method: "setupAutomation",
          userEmail: req.user.email
        }
      );
      throw new BadRequestError(
        `You need to invest at least ${CurrencyUtil.formatCurrency(MIN_ALLOWED_RECURRING_TOP_UP, req.user.currency, ConfigUtil.getDefaultUserLocale(req.user.residencyCountry))}.`
      );
    } else if (orderAmount > MAX_ALLOWED_RECURRING_TOP_UP) {
      logger.warn(
        `Attempted to set-up recurring top-up with more than ${CurrencyUtil.formatCurrency(MAX_ALLOWED_RECURRING_TOP_UP, req.user.currency, ConfigUtil.getDefaultUserLocale(req.user.residencyCountry))}`,
        {
          module: "AutomationController",
          method: "setupAutomation",
          userEmail: req.user.email
        }
      );
      throw new BadRequestError(
        `You need to invest at most ${CurrencyUtil.formatCurrency(MAX_ALLOWED_RECURRING_TOP_UP, req.user.currency, ConfigUtil.getDefaultUserLocale(req.user.residencyCountry))}.`
      );
    }

    return orderAmount;
  }

  private static async _validateTopUpMandate(req: CustomRequest): Promise<MandateDocument> {
    const mandateId = ParamsValidationUtil.isObjectIdParamValid("mandate", req.body.mandate as string, {
      isRequired: true
    });
    const mandate: MandateDocument = await MandateService.getMandate(mandateId);
    if (mandate.category !== "Top-Up") {
      logger.warn(`Attempted to set-up recurring top-up with mandate of type ${mandate.category}`, {
        module: "AutomationController",
        method: "setupAutomation",
        userEmail: req.user.email
      });
      throw new BadRequestError("You need to set-up recurring top-up with a valid top-up mandate");
    }

    return mandate;
  }
}
