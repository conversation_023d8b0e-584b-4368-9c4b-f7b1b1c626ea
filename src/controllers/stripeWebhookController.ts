import { CustomRequest } from "custom";
import { Response } from "express";
import logger from "../external-services/loggerService";
import SubscriptionService from "../services/subscriptionService";
import { ChargeTransactionDTOInterface } from "../models/Transaction";
import DateUtil from "../utils/dateUtil";
import { ProviderEnum } from "../configs/providersConfig";
import { TransactionService } from "../services/transactionService";
import UserService from "../services/userService";
import PaymentMethodService from "../services/paymentMethodService";
import { StripeService } from "../external-services/stripeService";
import { StripePricesEnum, StripeToWealthyhoodPrices } from "../configs/stripeConfig";

/**
 * This controller handles webhooks received from Stripe.
 *
 * We're listening for 4 webhooks related to subscriptions:
 * 1. invoice.finalized: This happens when a payment is initiated for a subscription. We will receive this both
 * when the user sets up their subscription but also in subsequent payments. When we receive this, we create the
 * appropriate charge transaction in our DB.
 * 2. invoice.paid: This happens when the above (invoice) is successful. In this case, we settle the charge
 * transaction, update the `nextChargeAt` field of the subscription (which is only used for UI purposes) and the
 * payment method used for the payment. We only add the payment method here as Stripe does not provide it when
 * the payment intent is finalized. In case of failure, we don't add a payment method.
 * 3. invoice.payment_failed: This happens when the above (invoice) fails. When we receive this, we don't
 * immediately reject the charge transaction, as Stripe will retry the payment up to 4 times (set from the Stripe
 * dashboard). The charge transaction will get rejected from our cron job that checks the date the charge transaction
 * was created was more than 8 days (as Stripe retries payments within one week).
 * 4. customer.subscription.deleted: This happens when a subscription ends. This can happen for two reasons:
 *   a) We cancelled the subscription through the API because the user requested a downgrade OR the user updated
 *   to a lifetime subscription.
 *   b) Subscription payments failed for a given billing period and the subscription is cancelled from Stripe.
 *
 * More information on webhooks we're listening to can be found here:
 * https://stripe.com/docs/billing/subscriptions/webhooks
 */
class StripeWebhookController {
  public static async processWebhook(req: CustomRequest, res: Response): Promise<Response> {
    const signature = req.headers["stripe-signature"] as string;
    const event = StripeService.Instance.constructEvent(req.body, signature, process.env.STRIPE_WEBHOOK_SECRET);

    logger.info(`Received event ${event.id} of type ${event.type}`, {
      module: "StripeWebhookController",
      method: "processWebhook",
      data: event
    });

    switch (event.type) {
      case "invoice.finalized": {
        const { payment_intent, customer, subscription } = event.data.object as {
          payment_intent: string;
          customer: string;
          subscription: string;
        };

        if (payment_intent === null) {
          logger.info(
            `Received an 'invoice.finalized' event with null payment intent for ${subscription} subscription...`,
            {
              module: "StripeWebhookController",
              method: "processWebhook"
            }
          );
          break;
        }

        logger.info("Create charge transaction due to invoice created!", {
          module: "StripeWebhookController",
          method: "processWebhook"
        });

        const charge = await TransactionService.getChargeByStripeId(payment_intent);
        if (charge) {
          logger.info(`Charge transaction already created for ${payment_intent}, skipping...`, {
            module: "StripeWebhookController",
            method: "processWebhook"
          });
          break;
        }

        const user = await UserService.getUserByStripeId(customer, {
          subscription: true,
          portfolios: true
        });
        if (!user) {
          throw new Error(
            `Could not create charge transaction for payment intent ${payment_intent} because we don't have a reference to the customer ${customer} in our database!`
          );
        }

        const { id, amount, status } = await StripeService.Instance.retrievePaymentIntent(payment_intent);

        const { items } = await StripeService.Instance.retrieveSubscription(subscription);
        const stripePrice = items.data[0].price.id;
        const price = StripeToWealthyhoodPrices[stripePrice as StripePricesEnum];
        if (!price) {
          throw new Error(
            `Could not create charge transaction for payment intent ${payment_intent} because we don't have a reference to the Stripe price ${stripePrice}!`
          );
        }

        const transactionData: Omit<ChargeTransactionDTOInterface, "createdAt" | "originalChargeAmount"> = {
          consideration: {
            currency: user.currency,
            amount
          },
          owner: user.id,
          portfolio: user.portfolios[0].id,
          chargeMethod: "card",
          subscription: user.subscription.id,
          chargeType: "subscription",
          price,
          chargeMonth: DateUtil.getYearAndMonth(new Date(Date.now())),
          activeProviders: [ProviderEnum.STRIPE], // As this webhook is Stripe-specific, we hardcode Stripe as the active provider
          providers: {
            stripe: {
              id: id,
              status: status
            }
          }
        };

        await TransactionService.createChargeTransaction(transactionData);

        break;
      }
      case "invoice.paid": {
        const { payment_intent, subscription } = event.data.object as {
          payment_intent: string;
          subscription: string;
        };

        const stripeSubscription = await StripeService.Instance.retrieveSubscription(subscription);
        const nextChargeAt = new Date(stripeSubscription.current_period_end * 1000);

        if (payment_intent === null) {
          logger.info(
            `Received an 'invoice.paid' event with null payment intent for ${subscription} subscription...`,
            {
              module: "StripeWebhookController",
              method: "processWebhook"
            }
          );
          const subscriptionDocument = await SubscriptionService.getSubscriptionByStripeID(subscription);
          if (!subscriptionDocument) {
            throw new Error(`Could not find subscription document for ${subscription} stripe subscription!`);
          }

          await SubscriptionService.updateNextChargeAt(subscriptionDocument.id, nextChargeAt);
          break;
        }

        // We have a payment intent, so we get full details from Stripe to retrieve the payment method used
        const { payment_method } = (await StripeService.Instance.retrievePaymentIntent(payment_intent)) as {
          payment_method: string;
        };
        const paymentMethod = await PaymentMethodService.getPaymentMethodByStripeId(payment_method);
        if (!paymentMethod) {
          throw new Error(
            `Could not create charge transaction for payment intent ${payment_intent} because we don't have a reference to the payment method ${payment_method} in our database!`
          );
        }

        logger.info("Settling charge transaction due to invoice paid!", {
          module: "StripeWebhookController",
          method: "processWebhook"
        });

        const charge = await TransactionService.getChargeByStripeId(payment_intent);
        if (!charge) {
          throw new Error(
            `Could not settle charge transaction for payment intent ${payment_intent} because we don't have a reference to it in our database!`
          );
        }

        if (charge.status === "Settled") {
          logger.info(`Not settling charge transaction ${charge.id} as it's already settled!`, {
            module: "StripeWebhookController",
            method: "processWebhook"
          });

          break;
        }

        await TransactionService.settleCardPaymentCharge(charge, paymentMethod, nextChargeAt);

        break;
      }
      case "invoice.payment_failed": {
        const { payment_intent, subscription } = event.data.object as {
          payment_intent: string;
          subscription: string;
        };

        if (payment_intent === null) {
          logger.warn(
            `Received an 'invoice.payment_failed' event with null payment intent for ${subscription} subscription...`,
            {
              module: "StripeWebhookController",
              method: "processWebhook"
            }
          );
          break;
        }

        logger.info("Updating charge transaction due to invoice payment failed!", {
          module: "StripeWebhookController",
          method: "processWebhook"
        });

        const charge = await TransactionService.getChargeByStripeId(payment_intent);
        if (!charge) {
          throw new Error(
            `Could not reject charge transaction for payment intent ${payment_intent} because we don't have a reference to it in our database!`
          );
        }

        const { status } = await StripeService.Instance.retrievePaymentIntent(payment_intent);

        await TransactionService.updateCardPaymentStatus(charge.id, status);

        break;
      }
      case "customer.subscription.deleted": {
        const stripeSubscription = event.data.object;

        const subscription = await SubscriptionService.getSubscriptionByStripeID(stripeSubscription.id);

        if (!subscription) {
          throw new Error(
            `Could not update subscription ${stripeSubscription.id} because we don't have a reference to it in our database`
          );
        }

        if (subscription.category !== "CardPaymentSubscription") {
          logger.info(
            `Received cancellation for ${subscription.id}, but it's already been moved to ${subscription.category}/${subscription.price}`,
            {
              module: "StripeWebhookController",
              method: "processWebhook"
            }
          );

          break;
        }

        logger.info(`Cancelling subscription ${subscription.id}`, {
          module: "StripeWebhookController",
          method: "processWebhook"
        });

        await SubscriptionService.downgradeSubscription(subscription, "free_monthly");

        break;
      }
      default: {
        logger.info(`Not processing events of type ${event.type}`, {
          module: "StripeWebhookController",
          method: "processWebhook"
        });
        break;
      }
    }

    return res.status(200).json({ received: true });
  }
}

export default StripeWebhookController;
