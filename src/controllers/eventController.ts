import { Request, Response } from "express";
import AppsflyerService from "../external-services/appsflyerService";
import eventEmitter from "../loaders/eventEmitter";
import events from "../event-handlers/events";
import logger from "../external-services/loggerService";
import ParticipantService from "../services/participantService";
import UserService from "../services/userService";
import { UserDocument } from "../models/User";
import { BadRequestError } from "../models/ApiErrors";

enum EventEnum {
  INSTALL = "install",
  APPSFLYER_CONVERSION_FAILURE = "appsflyerConversionFailure",
  APPSFLYER_CONVERSION_SUCCESS = "appsflyerConversionSuccess",
  APP_OPEN = "appOpened"
}

export default class EventController {
  public static async handleEvent(req: Request, res: Response): Promise<Response> {
    const eventId = req.body.eventId;

    if (eventId === EventEnum.INSTALL) {
      const { appsflyerId, grsf, wlthd, sid, pageUserLanded } = AppsflyerService.parseAttributionParams(
        req.headers
      );

      // emit event to notify other services
      eventEmitter.emit(events.general.install.eventId, {
        appsflyerId,
        wlthdId: wlthd,
        grsfId: grsf,
        sId: sid,
        pageUserLanded,
        platform: req.headers.platform
      });
    } else if (eventId === EventEnum.APPSFLYER_CONVERSION_SUCCESS) {
      logger.info(`Appsflyer conversion success for user ${req.headers.appsflyer_id}`, {
        module: "EventController",
        method: "handleEvent",
        data: {
          body: req.body,
          headers: req.headers
        }
      });

      const { appsflyerId } = AppsflyerService.parseAttributionParams(req.headers);

      const participant = await ParticipantService.getParticipantByAppsflyerId(appsflyerId, { referrer: true });
      if (!participant) {
        const trackingSource = AppsflyerService.parseSource(req.headers);
        const googleAdsMetadata = AppsflyerService.parseGoogleAdMetadata(req.headers);

        eventEmitter.emit(events.general.appsflyerConversionSuccess.eventId, {
          appsflyerId,
          trackingSource,
          googleAdsMetadata
        });
      }
    } else if (eventId === EventEnum.APPSFLYER_CONVERSION_FAILURE) {
      // In case of failure there is no reason to attempt to parse Appsflyer attribution
      // params. We'll only use the appsflyer id.
      const appsflyerId = req.headers.appsflyer_id as string;

      // The participant is created either during email submission or during sign up.
      // If the participant does not exist, that means that the endpoint is called
      // before the user submitting their email or using an auth provider (apple, google)
      const participant = await ParticipantService.getParticipantByAppsflyerId(appsflyerId, { referrer: true });
      if (participant) {
        await ParticipantService.updateParticipant(participant, {
          // TODO: add the error message sent by the client (for now adding a generic until
          // we have updated the mobile clients)
          attributionErrorMsg: "Appsflyer conversion data failed"
        });
      } else {
        const participantData: any = {
          appInstallInfo: {
            createdAt: new Date(),
            platform: req.headers.platform
          },
          appsflyerId,
          attributionErrorMsg: "Appsflyer conversion data failed",
          participantRole: "BASIC"
        };

        // if no participant exists => insert a participant document
        await ParticipantService.createParticipant(participantData);
      }
    } else if (eventId === EventEnum.APP_OPEN) {
      const platform = req.headers.platform as "ios" | "android" | "web";
      let user: UserDocument;

      if (platform === "android" || platform === "ios") {
        const { appsflyerId } = AppsflyerService.parseAttributionParams(req.headers);
        const participant = await ParticipantService.getParticipantByAppsflyerId(appsflyerId, { owner: true });
        user = participant?.owner;
      } else if (platform === "web") {
        const userId = req.headers["external-user-id"] as string;
        user = await UserService.getUser(userId);
      } else {
        throw new BadRequestError("Platform header must be ios, android or web.");
      }

      // emit event to notify other services
      if (user) eventEmitter.emit(events.general.appOpened.eventId, user, platform);
      else {
        logger.warn(`Did not found user from platform ${platform}, for 'appOpened' event`, {
          module: "EventController",
          method: "handleEvent"
        });
      }
    }

    return res.sendStatus(204);
  }
}
