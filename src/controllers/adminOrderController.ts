import { CustomRequest } from "custom";
import { Response } from "express";
import OrderService from "../services/orderService";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import { OrdersFilter } from "filters";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";

const { ASSET_CONFIG } = investmentUniverseConfig;

export default class AdminOrderController {
  public static async getOrders(req: CustomRequest, res: Response): Promise<Response> {
    const assetId = req.query.assetId as investmentUniverseConfig.AssetType;
    ParamsValidationUtil.isAssetValid("assetId", assetId);

    const isin = ASSET_CONFIG[assetId].isin;
    const submissionDay = ParamsValidationUtil.isDateParamValid(
      "submissionDay",
      req.query.submissionDay as string,
      {
        isRequired: true,
        isOnlyDate: true
      }
    );

    const sort = req.query.sort as string;

    const filter: OrdersFilter = { isin, submissionDay };

    return res.status(200).json(await OrderService.getOrders(filter, sort));
  }

  public static async getAnalytics(req: CustomRequest, res: Response): Promise<Response> {
    // Either a date or page parameter need to be passed.
    const date = req.query.date as string;
    const page = req.query.page
      ? ParamsValidationUtil.isNumericParamValid("page", req.query.page as string, { isRequired: false })
      : 0;

    return res.status(200).json(await OrderService.getAnalytics(page, date));
  }

  public static async getAnalyticsForUnsubmittedOrders(req: CustomRequest, res: Response): Promise<Response> {
    return res.status(200).json(await OrderService.getUnsubmittedOrderAnalytics());
  }

  public static async createMissingWealthkernelOrders(req: CustomRequest, res: Response): Promise<Response> {
    await OrderService.createMissingAggregateWealthkernelOrders();
    await OrderService.createFallbackRealtimeWealthkernelOrders();
    return res.sendStatus(204);
  }
}
