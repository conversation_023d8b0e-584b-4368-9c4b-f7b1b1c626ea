import { CustomRequest } from "custom";
import { Response } from "express";
import { BadRequestError } from "../models/ApiErrors";
import { SubscriptionDTOInterface } from "../models/Subscription";
import SubscriptionService from "../services/subscriptionService";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import { plansConfig } from "@wealthyhood/shared-configs";

const { PriceArrayConst } = plansConfig;

export default class SubscriptionController {
  public static readonly getSubscription = async (req: CustomRequest, res: Response): Promise<Response> => {
    const owner = req.user.id;

    const existingSubscription = await SubscriptionService.getSubscription(owner, { mandate: true });
    if (!existingSubscription) {
      return res.sendStatus(204);
    } else return res.status(200).json(existingSubscription);
  };

  public static readonly createSubscription = async (req: CustomRequest, res: Response): Promise<Response> => {
    const owner = req.user.id;
    const price = req.body["price"];

    if (!price) {
      throw new BadRequestError("Missing field 'price'", "Operation failed");
    }

    const existingSubscription = await SubscriptionService.getSubscription(owner);
    if (existingSubscription) {
      throw new BadRequestError("User already has a subscription", "Operation failed");
    }

    const category = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "category",
      req.body.category as string,
      plansConfig.SubscriptionCategoryArray
    ) as plansConfig.SubscriptionCategoryType;

    const subscriptionData: SubscriptionDTOInterface = {
      // Subscription is created as active by default unless the user has failed the verification process
      active: !req.user?.hasFailedKyc,
      owner,
      price,
      category
    };

    if (category === "DirectDebitSubscription") {
      throw new BadRequestError("Direct debit subscriptions are deprecated!");
    }

    const subscription = await SubscriptionService.createSubscription(subscriptionData);

    return res.status(200).json(subscription);
  };

  public static readonly updateSubscription = async (req: CustomRequest, res: Response): Promise<Response> => {
    const id = ParamsValidationUtil.isObjectIdParamValid("id", req.params.id as string);

    if (Object.keys(req.body).length === 0) {
      throw new BadRequestError("Request body cannot be empty");
    }

    const category = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "category",
      req.body.category as string,
      plansConfig.SubscriptionCategoryArray
    ) as plansConfig.SubscriptionCategoryType;

    const price = ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "price",
      req.body.price as string,
      PriceArrayConst,
      {
        isRequired: false
      }
    ) as plansConfig.PriceType;

    const subscription = await SubscriptionService.updateSubscription(id, { category, price });

    return res.status(200).json(subscription);
  };

  public static readonly renewSubscription = async (req: CustomRequest, res: Response): Promise<Response> => {
    const id = ParamsValidationUtil.isObjectIdParamValid("id", req.params.id as string);

    const subscription = await SubscriptionService.renewSubscription(id);

    return res.status(200).json(subscription);
  };

  public static readonly initiateStripe = async (req: CustomRequest, res: Response): Promise<Response> => {
    const { price, paymentMethod } = req.body;

    if (!price) {
      throw new BadRequestError("Price has not been passed!");
    } else if (!paymentMethod) {
      throw new BadRequestError("Payment method has not been passed!");
    }

    const initiateStripeData = await SubscriptionService.initiateStripe(
      req.user,
      paymentMethod,
      price as plansConfig.PriceType
    );

    return res.status(200).json(initiateStripeData);
  };

  public static readonly completeStripe = async (req: CustomRequest, res: Response): Promise<Response> => {
    const { paymentIntentId } = req.body;
    if (!paymentIntentId) {
      throw new BadRequestError("Payment intent ID has not been passed!");
    }

    const subscription = await SubscriptionService.completeStripe(req.user, paymentIntentId);

    return res.status(200).json(subscription);
  };

  public static readonly updatePaymentMethod = async (req: CustomRequest, res: Response): Promise<Response> => {
    const { paymentMethod } = req.body;
    if (!paymentMethod) {
      throw new BadRequestError("Payment method has not been passed!");
    }

    const subscription = await SubscriptionService.updatePaymentMethod(req.user, paymentMethod);

    return res.status(200).json(subscription);
  };
}
