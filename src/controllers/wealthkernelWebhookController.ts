import { addBreadcrumb } from "@sentry/node";
import { CustomRequest } from "custom";
import { Response } from "express";
import logger from "../external-services/loggerService";
import {
  WealthkernelAccountEventPayloadType,
  WealthkernelAccountRegionEnum,
  WealthkernelBankAccountEventPayloadType,
  WealthkernelBonusEventPayloadType,
  WealthkernelChargeEventPayloadType,
  WealthkernelDepositEventPayloadType,
  WealthkernelInternalTransferEventPayloadType,
  WealthkernelOrderEventPayloadType,
  WealthkernelService,
  WealthkernelWithdrawalEventPayloadType
} from "../external-services/wealthkernelService";
import { BadRequestError } from "../models/ApiErrors";
import AccountService from "../services/accountService";
import BankAccountService from "../services/bankAccountService";
import GiftService from "../services/giftService";
import OrderService from "../services/orderService";
import RewardService from "../services/rewardService";
import { TransactionService } from "../services/transactionService";
import { CreditTicketRepository } from "../repositories/creditTicketRepository";
import CreditTicketService from "../services/creditTicketService";

/**
 * This controller handles webhooks received from wealthkernel.
 * Check https://docs.wealthkernel.com/docs/guides/b98f2b80b7ec9-secrets
 *
 */
class WealthkernelWebhookController {
  public static async processBonusWebhook(req: CustomRequest, res: Response): Promise<Response> {
    WealthkernelWebhookController._validateSignature(req);

    const body = req.body as WealthkernelBonusEventPayloadType;
    const { eventType, payload } = body;

    if (!payload?.bonusId) {
      throw new BadRequestError("Received webhook without bonus id");
    }

    const newBonusStatus = WealthkernelService.getStatusFromBonusEvent(eventType.name);

    // A bonusId will only match with only one deposit, reward, WH dividend or cashback
    const [reward, gift, cashback, wealthyhoodDividend, deposit] = await Promise.all([
      RewardService.getRewardByDepositId(payload?.bonusId),
      GiftService.getGiftByDepositId(payload?.bonusId),
      TransactionService.getCashbackByDepositId(payload?.bonusId),
      TransactionService.getWealthyhoodDividendByDepositId(payload?.bonusId),
      TransactionService.getDepositByWealthkernelId(payload?.bonusId)
    ]);

    if (reward) {
      await RewardService.updateRewardDepositStatus(reward, newBonusStatus);
    } else if (gift) {
      await GiftService.updateGiftDepositStatus(gift, newBonusStatus);
    } else if (wealthyhoodDividend) {
      await TransactionService.updateWealthyhoodDividendDepositStatus(wealthyhoodDividend, newBonusStatus);
    } else if (cashback) {
      await TransactionService.updateCashbackDepositStatus(cashback, newBonusStatus);
    } else if (deposit) {
      logger.info(`Received webhook for admin-triggered deposit ${deposit.id}, skipping...`, {
        module: "WebhookController",
        method: "processBonusWebhook"
      });
    } else {
      throw new BadRequestError(
        `Bonus ID ${payload?.bonusId} does not match any reward, gift, cashback, WH dividend or deposit`
      );
    }

    logger.info(`Successfully handled webhook for event: ${eventType.name}`, {
      module: "WebhookController",
      method: "processBonusWebhook"
    });
    return res.sendStatus(200);
  }

  public static async processOrderWebhook(req: CustomRequest, res: Response): Promise<Response> {
    WealthkernelWebhookController._validateSignature(req);

    const body = req.body as WealthkernelOrderEventPayloadType;
    const { eventType, payload } = body;

    if (!payload?.orderId) {
      throw new BadRequestError("Received webhook without order id");
    }

    logger.info(`Received event for order ${payload.orderId} of type ${eventType.name}`, {
      module: "WealthkernelWebhookController",
      method: "processOrderWebhook",
      data: {
        payload,
        eventType
      }
    });

    const newOrderStatus = WealthkernelService.getStatusFromOrderEvent(eventType.name);

    // An order ID will only match with only one order or reward document
    const [order, reward] = await Promise.all([
      OrderService.getOrderByWealthkernelId(payload?.orderId),
      RewardService.getRewardByOrderId(payload?.orderId)
    ]);

    if (order) {
      await OrderService.syncOrderByWealthkernelId(order.id, newOrderStatus);
    } else if (reward) {
      await RewardService.updateRewardOrderStatus(reward, newOrderStatus);
    } else {
      throw new BadRequestError(
        `Received webhook with order ID ${payload?.orderId} but it does not match any order or reward`
      );
    }

    logger.info(`Successfully handled webhook for event: ${eventType.name}`, {
      module: "WebhookController",
      method: "processOrderWebhook"
    });

    return res.sendStatus(200);
  }

  public static async processWithdrawalWebhook(req: CustomRequest, res: Response): Promise<Response> {
    WealthkernelWebhookController._validateSignature(req);

    const body = req.body as WealthkernelWithdrawalEventPayloadType;
    const { eventType, payload } = body;

    if (!payload?.withdrawalId) {
      throw new BadRequestError("Invalid withdrawal id");
    }

    const newWithdrawalStatus = WealthkernelService.getStatusFromWithdrawalEvent(eventType.name);

    await TransactionService.syncWithdrawalStatusByWkWithdrawalId(payload.withdrawalId, newWithdrawalStatus);

    logger.info(`Successfully handled webhook for event: ${eventType.name}, withdrawal: ${payload.withdrawalId}`, {
      module: "WebhookController",
      method: "processWithdrawalWebhook"
    });
    return res.sendStatus(200);
  }

  public static async processAccountWebhook(req: CustomRequest, res: Response): Promise<Response> {
    WealthkernelWebhookController._validateSignature(req);

    const body = req.body as WealthkernelAccountEventPayloadType;
    const { eventType, payload } = body;

    if (!WealthkernelService.isAccountEvent(eventType.name)) {
      throw new BadRequestError(`Received unsupported event : ${eventType.name}`);
    }

    if (!payload?.accountId) {
      throw new BadRequestError("Received webhook without account id");
    }

    const newAccountStatus = WealthkernelService.getStatusFromAccountEvent(eventType.name);
    await AccountService.syncWkAccountStatusByWkAccountId(payload?.accountId, newAccountStatus);

    logger.info(`Successfully handled webhook for event: ${eventType.name}`, {
      module: "WebhookController",
      method: "processAccountWebhook"
    });
    return res.sendStatus(200);
  }

  public static async processDepositWebhook(req: CustomRequest, res: Response): Promise<Response> {
    WealthkernelWebhookController._validateSignature(req);

    const body = req.body as WealthkernelDepositEventPayloadType;
    const { eventType, payload } = body;

    if (!payload?.depositId) {
      throw new BadRequestError("Invalid deposit id");
    }

    const newDepositStatus = WealthkernelService.getStatusFromDepositEvent(eventType.name);

    await TransactionService.syncDepositStatusByWkDepositId(payload.depositId, newDepositStatus);

    logger.info(`Successfully handled webhook for event: ${eventType.name} and deposit ${payload.depositId}`, {
      module: "WebhookController",
      method: "processDepositWebhook"
    });

    return res.sendStatus(200);
  }

  public static async processInternalTransferWebhook(req: CustomRequest, res: Response): Promise<Response> {
    WealthkernelWebhookController._validateSignature(req);

    const body = req.body as WealthkernelInternalTransferEventPayloadType;
    const { eventType, payload } = body;

    if (!payload?.internalTransferId) {
      throw new BadRequestError("Invalid internal transfer id");
    }

    const newInternalTransferStatus = WealthkernelService.getStatusFromInternalTransferEvent(eventType.name);

    // An internal transfer ID will only match with only one credit ticket or charge transaction document.
    const [creditTicket, charge] = await Promise.all([
      CreditTicketRepository.getCreditTicketByDepositId(payload.internalTransferId),
      TransactionService.getChargeByWealthkernelId(payload.internalTransferId)
    ]);

    if (creditTicket) {
      await CreditTicketService.updateCreditTicketWealthkernelStatus(creditTicket, newInternalTransferStatus);
    } else if (charge) {
      await TransactionService.updateChargeStatusByWkChargeId(
        payload.internalTransferId,
        newInternalTransferStatus
      );
    } else {
      throw new BadRequestError(
        `Received webhook with internal transfer ID ${payload?.internalTransferId} but it does not match any credit ticket or charge transaction`
      );
    }

    logger.info(
      `Successfully handled webhook for event: ${eventType.name} and internal transfer ${payload.internalTransferId}`,
      {
        module: "WebhookController",
        method: "processInternalTransferWebhook"
      }
    );

    return res.sendStatus(200);
  }

  public static async processChargeWebhook(req: CustomRequest, res: Response): Promise<Response> {
    WealthkernelWebhookController._validateSignature(req);

    const body = req.body as WealthkernelChargeEventPayloadType;
    const { eventType, payload } = body;

    if (!payload?.chargeId) {
      throw new BadRequestError("Invalid charge id");
    }

    const newChargeStatus = WealthkernelService.getStatusFromChargeEvent(eventType.name);

    await TransactionService.updateChargeStatusByWkChargeId(payload.chargeId, newChargeStatus);

    logger.info(`Successfully handled webhook for event: ${eventType.name}`, {
      module: "WebhookController",
      method: "processChargeWebhook"
    });
    return res.sendStatus(200);
  }

  public static async processBankAccountWebhook(req: CustomRequest, res: Response): Promise<Response> {
    WealthkernelWebhookController._validateSignature(req);

    const body = req.body as WealthkernelBankAccountEventPayloadType;
    const { eventType, payload } = body;

    if (!WealthkernelService.isBankAccountEvent(eventType.name)) {
      addBreadcrumb({
        type: "default",
        category: "WealthkernelWebhookController.processBankAccountWebhook",
        level: "info",
        data: {
          body
        }
      });
      throw new BadRequestError(`Received unsupported event : ${eventType.name}`);
    }

    if (!payload?.bankAccountId) {
      addBreadcrumb({
        type: "default",
        category: "WealthkernelWebhookController.processBankAccountWebhook",
        level: "info",
        data: {
          body
        }
      });
      throw new BadRequestError("Invalid bank account id");
    }

    const newBankAccountStatus = WealthkernelService.getStatusFromBankAccountEvent(eventType.name);

    await BankAccountService.updateBankAccountStatusByWkBankAccountId(payload.bankAccountId, newBankAccountStatus);

    logger.info(`Successfully handled webhook for event: ${eventType.name}`, {
      module: "WebhookController",
      method: "processBankAccountWebhook"
    });
    return res.sendStatus(200);
  }

  private static _validateSignature(req: CustomRequest) {
    const signatureHeader = req.headers["webhook-signature"] as string;
    const region =
      (req.params.region?.toUpperCase() as WealthkernelAccountRegionEnum) ?? WealthkernelAccountRegionEnum.UK;

    WealthkernelService.validateWebhookSignature(signatureHeader, region, req.body);
  }
}

export default WealthkernelWebhookController;
