import { Response } from "express";
import DailyTickerService from "../services/dailyTickerService";
import { CustomRequest } from "custom";
import logger from "../external-services/loggerService";

export class DailyTickerController {
  public static readonly getPortfolioTickerPerformanceByTenor = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const portfolio = req.query.portfolio as string;

    const tickers = await DailyTickerService.getPortfolioTickerPerformanceByTenor(portfolio);
    if (!tickers) {
      logger.warn("Client requested tickers by tenor but there were no tickers for that portfolio!", {
        module: "DailyTickerController",
        method: "getPortfolioTickerPerformanceByTenor",
        data: {
          portfolio: portfolio
        }
      });

      return res.sendStatus(200);
    }

    return res.status(200).json(tickers);
  };
}
