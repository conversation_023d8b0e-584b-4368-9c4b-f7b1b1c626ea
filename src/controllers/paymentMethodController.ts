import { CustomRequest } from "custom";
import { Response } from "express";
import { BadRequestError } from "../models/ApiErrors";
import PaymentMethodService from "../services/paymentMethodService";

export default class PaymentMethodController {
  public static readonly getPaymentMethods = async (req: CustomRequest, res: Response): Promise<Response> => {
    const userId = req.user.id;

    const paymentMethods = await PaymentMethodService.getPaymentMethods({ owner: userId });

    return res.status(200).json({ data: paymentMethods });
  };

  public static readonly initiateStripe = async (req: CustomRequest, res: Response): Promise<Response> => {
    const { clientSecret, setupIntentId, ephemeralKey } = await PaymentMethodService.initiateStripe(req.user);

    return res.status(200).json({ clientSecret, setupIntentId, ephemeralKey });
  };

  public static readonly completeStripe = async (req: CustomRequest, res: Response): Promise<Response> => {
    const { setupIntentId } = req.body;

    if (!setupIntentId) {
      throw new BadRequestError("Setup intent ID has not been passed!");
    }

    const paymentMethod = await PaymentMethodService.completeStripe(req.user.id, setupIntentId);

    return res.status(200).json(paymentMethod);
  };
}
