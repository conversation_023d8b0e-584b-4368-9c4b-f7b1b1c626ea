import { CustomRequest } from "custom";
import { Response } from "express";
import UserBuilder, { UserStatusEnum } from "../tests/utils/userBuilder";

export default class TestController {
  public static async createUser(req: CustomRequest, res: Response): Promise<Response> {
    const { email, status, props } = req.body;

    const userBuilder = await new UserBuilder().build({ email, status, props }).finish();
    await userBuilder.user.populate("accounts addresses kycOperation portfolios");

    return res.status(200).json({ user: userBuilder.user });
  }

  public static async deleteUser(req: CustomRequest, res: Response): Promise<Response> {
    const { email } = req.body;

    await new UserBuilder().reset(email);

    return res.sendStatus(200);
  }

  public static async getAvailableStatuses(req: CustomRequest, res: Response): Promise<Response> {
    return res.status(200).json({ statuses: Object.values(UserStatusEnum) });
  }
}
