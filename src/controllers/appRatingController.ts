import { CustomRequest } from "custom";
import { Response } from "express";
import { BadRequestError } from "../models/ApiErrors";
import AppRatingService from "../services/appRatingService";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import { StarRatingAllowedValues } from "../models/AppRating";

export default class AppRatingController {
  public static readonly submitAppRating = async (req: CustomRequest, res: Response): Promise<Response> => {
    const appRatingId = ParamsValidationUtil.isObjectIdParamValid("appRatingId", req.params.id as string);

    const appRatingSubmissionData = AppRatingController._validateAppRatingSubmission(req);

    await AppRatingService.submitAppRating(appRatingId, appRatingSubmissionData);

    return res.sendStatus(204);
  };

  private static _validateAppRatingSubmission = (
    req: CustomRequest
  ): { feedback?: string; starRating: number } => {
    if (!StarRatingAllowedValues.includes(req.body.starRating)) {
      throw new BadRequestError("Invalid star rating");
    }

    return {
      feedback: req.body.feedback,
      starRating: req.body.starRating
    };
  };
}
