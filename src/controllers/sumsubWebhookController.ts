import { CustomRequest } from "custom";
import { Response } from "express";
import logger from "../external-services/loggerService";
import { EventType, SumsubService } from "../external-services/sumsubService";
import UserService from "../services/userService";
import KycOperationService from "../services/kycOperationService";

/**
 * This controller handles webhooks received from Sumsub
 *
 * Currently listening to the following events:
 * a) Applicant created: when this is received, it means a user has just started a KYC flow with Sumsub. The
 * applicant has been created in Sumsub, but it does not have any details yet.
 * b) Applicant personal info changed: when this is received, it means that Sumsub has just extracted details
 * from the user's documents.
 * b) Applicant reviewed: when this is received, it means that Sumsub has a final decision (GREEN/RED) on the user.
 */
export default class SumsubWebhookController {
  public static async processWebhook(req: CustomRequest, res: Response): Promise<Response> {
    const rawBody = req.body as string;
    const signature = req.headers["x-payload-digest"] as string;
    const event = JSON.parse(rawBody) as EventType;

    SumsubService.validateWebhookSignature(signature, rawBody);

    logger.info(`Received event for applicant ${event.applicantId} of type ${event.type}`, {
      module: "SumsubWebhookController",
      method: "_processEvent",
      data: event
    });

    switch (event.type) {
      case "applicantCreated":
        await SumsubWebhookController._handleApplicantCreated(event);
        break;
      case "applicantPersonalInfoChanged":
        await SumsubWebhookController._handleApplicantPersonalInfoChanged(event);
        break;
      case "applicantReviewed":
        await SumsubWebhookController._handleApplicantReviewed(event);
        break;
      default:
        logger.info(`Not processing events of type ${event.type}`, {
          module: "SumsubWebhookController",
          method: "_processEvent"
        });
        break;
    }

    return res.sendStatus(204);
  }

  private static async _handleApplicantCreated(event: EventType): Promise<void> {
    await UserService.addSumsubIdToUser(event.externalUserId, event.applicantId);
  }

  private static async _handleApplicantPersonalInfoChanged(event: EventType): Promise<void> {
    const user = await UserService.getUser(event.externalUserId, { kycOperation: true });

    await KycOperationService.syncSumsubKycOperation(user);
  }

  private static async _handleApplicantReviewed(event: EventType): Promise<void> {
    const user = await UserService.getUser(event.externalUserId, { kycOperation: true });

    await KycOperationService.syncSumsubKycOperation(user);
  }
}
