import { CustomRequest } from "custom";
import { Response } from "express";
import { Readable } from "stream";
import CloudflareService, { BucketsEnum, ContentTypeEnum } from "../external-services/cloudflareService";
import { AddressDocument } from "../models/Address";
import { BadRequestError } from "../models/ApiErrors";
import { UserPopulationFieldsEnum } from "../models/User";
import InvestmentProductService from "../services/investmentProductService";
import OrderService from "../services/orderService";
import DbUtil from "../utils/dbUtil";
import RestUtil from "../utils/restUtil";
import StatementUtil, { TradeTypeEnum } from "../utils/statementUtil";

export default class OrderController {
  /**
   * Returns a list of pending orders that are polled by the clients to refresh transactions/portfolio.
   */
  /* This endpoint should be deleted after the /latest/matched endpoint has been implemented in the mobile apps */
  public static async getPendingOrders(req: CustomRequest, res: Response): Promise<Response> {
    const owner = req.user.id as string;

    return res.status(200).json({ data: await OrderService.getPendingOrders(owner) });
  }

  public static async getLatestMatchedOrderId(req: CustomRequest, res: Response): Promise<Response> {
    const owner = req.user.id as string;

    const orderId = await OrderService.getLatestMatchedOrderId(owner);

    if (orderId) {
      return res.status(200).json({ id: orderId });
    }

    return res.sendStatus(204);
  }

  public static async cancelOrder(req: CustomRequest, res: Response): Promise<Response> {
    const order = await RestUtil.getOrderFromResponse(req, res, { transaction: true });

    return res.status(200).json(await OrderService.cancelOrder(order));
  }

  public static readonly generateTradeConfirmation = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const user = req.user;

    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.ADDRESSES);
    const address = user.addresses[0] as AddressDocument;

    const order = await RestUtil.getOrderFromResponse(req, res, { transaction: true });
    const investmentProduct = await InvestmentProductService.getInvestmentProductByIsin(order.isin, false);

    if (!order.isMatched) {
      throw new BadRequestError("Trade confirmation can only be generated for matched orders!");
    }

    const data = StatementUtil.generateTradeConfirmationPDF(
      user,
      address,
      order,
      investmentProduct,
      TradeTypeEnum.ORDER
    );

    const { fileUri } = await CloudflareService.Instance.uploadObject(
      BucketsEnum.TRADE_CONFIRMATIONS,
      StatementUtil.generateTradeConfirmationFilePath(order.id),
      Readable.from(data),
      {
        contentType: ContentTypeEnum.APPLICATION_PDF
      }
    );

    return res.status(200).json({ fileUri });
  };
}
