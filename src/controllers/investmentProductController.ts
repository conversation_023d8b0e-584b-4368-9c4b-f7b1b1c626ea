import { OrderUtil } from "./../utils/orderUtil";
import { Response } from "express";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import InvestmentProductService, { AssetPricesWithReturnsType } from "../services/investmentProductService";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import { CustomRequest } from "custom";
import { AssetType } from "@wealthyhood/shared-configs/dist/investmentUniverse";
import PortfolioService from "../services/portfolioService";
import { BadRequestError } from "../models/ApiErrors";
import { UserDocument } from "../models/User";
import { InvestmentProductDocument } from "../models/InvestmentProduct";
import ConfigUtil from "../utils/configUtil";
import { TenorEnum } from "../configs/durationConfig";
import OrderService, { OrderActivityItemType } from "../services/orderService";
import { AggregatedAssetPriceDataPointType } from "tickers";
import { PartialRecord } from "utils";
import logger from "../external-services/loggerService";

type FillClientDisplayFields = {
  price: boolean;
};

type AggregatedAssetPricesWithReturnsType = {
  data: AggregatedAssetPriceDataPointType[];
  returns: number;
  displayIntraday: boolean;
};

export default class InvestmentProductController {
  public static async getInvestmentProducts(req: CustomRequest, res: Response): Promise<Response> {
    const { populateTicker } = req.query;

    const investmentProducts = await InvestmentProductService.getInvestmentProducts({
      populateTicker: populateTicker === "true",
      useCache: true,
      listedOnly: true
    });

    return res
      .status(200)
      .json(
        investmentProducts.map((investmentProduct) =>
          populateTicker === "true"
            ? InvestmentProductController._fillClientDisplayFields(req.user, investmentProduct, { price: true })
            : investmentProduct
        )
      );
  }

  public static async getInvestmentProductFundamentalsData(req: CustomRequest, res: Response): Promise<Response> {
    const assetId = req.query.assetId as investmentUniverseConfig.AssetType;
    ParamsValidationUtil.isAssetValid("asset", assetId, { isRequired: true });

    const { fundamentals, currentPrice, tradedCurrency, tags, marketInfo, kid } =
      await InvestmentProductService.getAssetData(
        assetId,
        ConfigUtil.getDefaultUserLocale(req.user.residencyCountry),
        req.user.companyEntity,
        { isRealtimeETFExecutionEnabled: req.user.isRealtimeETFExecutionEnabled }
      );

    return res.status(200).json({ currentPrice, tradedCurrency, tags, marketInfo, kid, ...fundamentals });
  }

  public static async getInvestmentProductPricesByTenor(req: CustomRequest, res: Response): Promise<Response> {
    const userId = req.user.id;
    const assetId = req.query.assetId as investmentUniverseConfig.AssetType;
    ParamsValidationUtil.isAssetValid("asset", assetId, { isRequired: true });

    const [assetPricesByTenor, orderActivityByTenor] = await Promise.all([
      InvestmentProductService.getPricesAndReturnsByTenor(assetId),
      OrderService.getAssetOrderActivityByTenor(userId, assetId)
    ]);

    const aggregatedAssetPricesByTenor = InvestmentProductController._addOrderActivityToPriceDataPoints(
      assetPricesByTenor,
      orderActivityByTenor
    );

    return res.status(200).json(aggregatedAssetPricesByTenor);
  }

  public static readonly getAssetRecentActivity = async (req: CustomRequest, res: Response): Promise<Response> => {
    const assetId = req.query.assetId as AssetType;
    ParamsValidationUtil.isAssetValid("assetId", assetId, { isRequired: true });

    let limit;
    if (req.query.limit) {
      limit = ParamsValidationUtil.isNumericParamValid("limit", req.query.limit, {
        isRequired: false
      });
    }

    return res
      .status(200)
      .json(await InvestmentProductService.getAssetRecentActivity(req.user.id, assetId, limit));
  };

  public static readonly getUserInvestmentDetails = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const assetId = req.query.assetId as AssetType;
    ParamsValidationUtil.isAssetValid("assetId", assetId, { isRequired: true });

    const portfolio = await PortfolioService.getGeneralInvestmentPortfolio(req.user, true);
    if (!portfolio.holdings.some((holding) => holding.assetCommonId === assetId)) {
      throw new BadRequestError("User does not hold that asset");
    }

    return res.status(200).json(await InvestmentProductService.getUserInvestmentDetails(portfolio, assetId));
  };

  /**
   * Enhances an investment product object with properties useful for the clients.
   * @param user
   * @param investmentProduct The investment product to fill.
   * @param fields
   * @returns An enhanced investment product with additional display properties
   * @public
   */
  private static _fillClientDisplayFields(
    user: UserDocument,
    investmentProduct: InvestmentProductDocument,
    fields: FillClientDisplayFields = {
      price: true
    }
  ): InvestmentProductDocument {
    if (fields.price && investmentProduct.currentTicker) {
      const sanitizedInvestmentProduct = JSON.parse(JSON.stringify(investmentProduct));

      return {
        ...sanitizedInvestmentProduct,
        currentTicker: {
          ...sanitizedInvestmentProduct.currentTicker,
          price: investmentProduct.currentTicker.getPrice(user.currency)
        }
      };
    }

    return investmentProduct;
  }

  private static _addOrderActivityToPriceDataPoints(
    pricesByTenor: PartialRecord<TenorEnum, AssetPricesWithReturnsType>,
    orderActivityByTenor: Record<TenorEnum, OrderActivityItemType[]>
  ): PartialRecord<TenorEnum, AggregatedAssetPricesWithReturnsType> {
    return Object.keys(pricesByTenor).reduce(
      (dict, tenorKey) => {
        const tenor = tenorKey as TenorEnum;

        dict[tenor as TenorEnum] = {
          data: OrderUtil.addOrderActivityToPriceDataPoints(
            pricesByTenor[tenor].data,
            orderActivityByTenor[tenor]
          ),
          returns: pricesByTenor[tenor].returns,
          displayIntraday: pricesByTenor[tenor].displayIntraday
        };

        return dict;
      },
      {} as PartialRecord<TenorEnum, AggregatedAssetPricesWithReturnsType>
    );
  }
}
