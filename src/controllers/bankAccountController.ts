import { CustomRequest } from "custom";
import { Response } from "express";
import { BankOriginType } from "requestBody";
import { BadRequestError } from "../models/ApiErrors";
import BankAccountService from "../services/bankAccountService";
import BankAccountValidationUtil from "../utils/bankAccountValidationUtil";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import { banksConfig, entitiesConfig } from "@wealthyhood/shared-configs";
import ProviderService from "../services/providerService";
import { RequisitionDTOInterface } from "../models/Requisition";
import RequisitionService from "../services/requisitionService";
import { PlatformEnum, PlatformTypeToPlatformEnumMapping } from "../configs/platformConfig";

export default class BankAccountController {
  /**
   * Returns a list of available providers based on their residency country, company entity and requested scope
   * (e.g. pay or data). Apart from a list of IDs, the response includes additional information for each provider,
   * such as the display name & logo.
   * @param req
   * @param res
   */
  public static async getAvailableBanks(req: CustomRequest, res: Response): Promise<Response> {
    const scope = req.query.scope ?? banksConfig.BankProviderScopeEnum.DATA;

    const providers = BankAccountService.getAvailableBanks(
      req.user.companyEntity,
      req.user.residencyCountry,
      scope as banksConfig.BankProviderScopeEnum
    );

    return res.status(200).json(providers);
  }

  /**
   * For the GoCardless bank account linking flow, this endpoint is called to create a requisition and return
   * a redirection URL to the client. Following that URL takes the user through their selected bank authorisation
   * flow.
   * @param req
   * @param res
   */
  public static async initiateBankLinking(req: CustomRequest, res: Response): Promise<Response> {
    const bankId = req.body.bankId as banksConfig.BankType;

    if (!bankId) {
      throw new BadRequestError("Client has not passed bankId");
    }

    const { redirectUri, reference, id } = await ProviderService.getOpenBankingDataService(
      req.user.companyEntity
    ).initiateBankLinking(
      req.user.id,
      bankId,
      PlatformTypeToPlatformEnumMapping[req.platform] ?? PlatformEnum.WEB,
      req.body?.redirectUriState
    );

    const requisitionData: RequisitionDTOInterface = {
      owner: req.user.id,
      reference,
      providers: {
        gocardlessData: {
          id
        }
      }
    };

    // We create a requisition to keep track of the reference/requisition ID mapping.
    await RequisitionService.createRequisition(requisitionData);

    return res.status(200).json({ redirectUri });
  }

  public static async createBankAccount(req: CustomRequest, res: Response): Promise<Response> {
    BankAccountValidationUtil.validateBankAccountData(req.body.bankAccount);
    const bankLinkedFrom = req.body.bankLinkedFrom;
    ParamsValidationUtil.isStringParamFromAllowedValuesValid(
      "bankLinkedFrom",
      bankLinkedFrom,
      ["modal", "my_account"] as BankOriginType[],
      {
        isRequired: false
      }
    );

    if (req.user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE) {
      throw new BadRequestError("Bank account linking not allowed for European entity!");
    }

    const bankAccount = await BankAccountService.createOrUpdateBankAccount(
      req.user.id,
      req.body.bankAccount,
      bankLinkedFrom
    );
    return res.status(200).json({ data: [bankAccount] });
  }

  public static async deactivateBankAccount(req: CustomRequest, res: Response) {
    const bankAccountID = ParamsValidationUtil.isObjectIdParamValid("id", req.params.id as string);

    if (Object.keys(req.body).length != 0) {
      throw new BadRequestError("Wrong request body");
    }

    const existingBankAccounts = await BankAccountService.getBankAccounts({ owner: req.user.id });
    const bankAccountBelongsToUser = existingBankAccounts.every(
      (bankAccount) => bankAccount.owner.toString() === req.user.id
    );

    if (!bankAccountBelongsToUser) {
      throw new BadRequestError("Current bank account does not belong to user");
    }

    await BankAccountService.deactivateBankAccount(existingBankAccounts, bankAccountID);
    return res.sendStatus(200);
  }
}
