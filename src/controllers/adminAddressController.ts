import { CustomRequest } from "custom";
import { Response } from "express";
import AddressService from "../services/addressService";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import { BadRequestError } from "../models/ApiErrors";

export default class AdminAddressController {
  public static readonly createOrUpdateAddress = async (req: CustomRequest, res: Response): Promise<Response> => {
    const { owner, addressData } = AdminAddressController._validateInput(req);

    await AddressService.createOrUpdateAddress(owner, addressData);

    return res.sendStatus(204);
  };

  public static createAllWkAddresses = async (req: CustomRequest, res: Response): Promise<Response> => {
    await AddressService.createAllWkAddresses();
    return res.sendStatus(204);
  };

  private static _validateInput = (req: CustomRequest) => {
    const owner = req.query.owner as string;
    const addressData = req.body;

    ["owner", "line1", "city", "countryCode", "postalCode"].forEach((prop) => {
      if (!addressData[prop]) {
        throw new BadRequestError(`Missing field '${prop}'`, "Operation failed");
      }
    });

    if (owner != addressData.owner) {
      throw new BadRequestError("Owner in query param should match owner in body");
    }
    ParamsValidationUtil.isObjectIdParamValid("owner", owner);

    return { owner, addressData };
  };
}
