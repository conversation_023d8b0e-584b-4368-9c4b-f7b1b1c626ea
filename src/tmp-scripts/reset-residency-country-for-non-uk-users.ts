import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User } from "../models/User";

class ResetResidencyCountryRunner extends ScriptRunner {
  scriptName = "reset-residency-country-for-non-uk-users";

  async processFn(): Promise<void> {
    logger.info("Migrating users that have non-UK residency country...", {
      module: `script:${this.scriptName}`
    });

    await User.updateMany(
      {
        $and: [{ residencyCountry: { $exists: true } }, { residencyCountry: { $ne: "GB" } }]
      },
      {
        $unset: { residencyCountry: true }
      }
    );

    logger.info("Finished migrating users that have non-UK residency country!", {
      module: `script:${this.scriptName}`
    });
  }
}

new ResetResidencyCountryRunner().run();
