import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import InvestmentProductService from "../services/investmentProductService";
import { faker } from "@faker-js/faker";
import { IntraDayAssetTicker } from "../models/IntraDayTicker";
import { envIsProd } from "../utils/environmentUtil";

class AddMockDailyAssetTickerScriptRunner extends ScriptRunner {
  scriptName = "daily-asset-ticker-migration";

  async processFn(): Promise<void> {
    logger.info("Adding mock daily asset tickers...", {
      module: `script:${this.scriptName}`
    });

    if (envIsProd()) {
      return;
    }

    const investmentProducts = await InvestmentProductService.getInvestmentProducts({
      populateTicker: false,
      useCache: false,
      listedOnly: true
    });

    for (let i = 0; i < investmentProducts.length; i++) {
      const investmentProduct = investmentProducts[i];

      const assetValuationPerCurrency = {
        GBP: faker.number.int({ min: 1, max: 1000 }),
        USD: faker.number.int({ min: 1, max: 1000 }),
        EUR: faker.number.int({ min: 1, max: 1000 })
      };
      const timestamp = new Date();

      await new IntraDayAssetTicker({
        currency: investmentProduct.tradedCurrency,
        timestamp,
        pricePerCurrency: assetValuationPerCurrency,
        investmentProduct: investmentProduct.id
      }).save();
    }

    logger.info("Finished migrating daily asset tickers!", {
      module: `script:${this.scriptName}`
    });
  }
}

new AddMockDailyAssetTickerScriptRunner().run();
