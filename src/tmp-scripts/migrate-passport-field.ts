import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User } from "../models/User";

class MigratePassportFieldRunner extends ScriptRunner {
  scriptName = "migrate-passport-field";

  async processFn(): Promise<void> {
    logger.info("Migrating passport field...", {
      module: `script:${this.scriptName}`
    });

    await User.updateMany({ isPassportMatchingKycProvider: { $exists: true } }, [
      { $set: { isPassportVerified: "$isPassportMatchingKycProvider" } }
    ]);

    logger.info("✅ Finished migrating passport field!", {
      module: `script:${this.scriptName}`
    });
  }
}

new MigratePassportFieldRunner().run();
