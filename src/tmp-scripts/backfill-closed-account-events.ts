import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { Account, AccountDocument } from "../models/Account";
import { UserDocument } from "../models/User";
import analytics from "../external-services/segmentAnalyticsService";
import events from "../event-handlers/events";

const delay = (time: number) => new Promise((resolve, reject) => setTimeout(resolve, time));

class BackfillClosedAccountEventsRunner extends ScriptRunner {
  scriptName = "backfill-closed-accounts-events";

  async processFn(): Promise<void> {
    logger.info("Backfilling events for closed accounts...", {
      module: `script:${this.scriptName}`
    });

    const numberOfClosedAccounts = await Account.countDocuments({
      "providers.wealthkernel.status": "Closed"
    });

    logger.info(`Total number of closed accounts: ${numberOfClosedAccounts}`, {
      module: `script:${this.scriptName}`
    });

    await Account.find({ "providers.wealthkernel.status": "Closed" })
      .populate("owner")
      .cursor()
      .addCursorFlag("noCursorTimeout", true)
      .eachAsync(async (account: AccountDocument, i: number) => {
        logger.info(`Going through user ${i + 1}/${numberOfClosedAccounts}`, {
          module: `script:${this.scriptName}`
        });

        const user = account.owner as UserDocument;

        analytics.track(user, events.user.wkAccountClosed.name, { All: false, Mixpanel: true });

        if (i % 20 === 0) {
          await delay(5000);
        }
      });

    await delay(5000);

    logger.info("✅ Finished backfilling events for closed accounts!", {
      module: `script:${this.scriptName}`
    });
  }
}

new BackfillClosedAccountEventsRunner().run();
