import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { InvestmentProduct } from "../models/InvestmentProduct";
import InvestmentProductService from "../services/investmentProductService";

const { ASSET_CONFIG } = investmentUniverseConfig;

class AddInvestmentProductsForAssetsRunner extends ScriptRunner {
  scriptName = "add-investment-products-for-assets";

  async processFn(): Promise<void> {
    logger.info("Adding investment products for stocks & etfs...", {
      module: `script:${this.scriptName}`
    });

    // Get list of asset IDs for stocks & etfs
    const assets = Object.entries(ASSET_CONFIG).map(
      ([assetId]) => assetId
    ) as investmentUniverseConfig.AssetType[];

    for (let i = 0; i < assets.length; i++) {
      const assetCommonId = assets[i];

      const existingInvestmentProduct = await InvestmentProductService.getInvestmentProduct(assetCommonId, false);
      if (existingInvestmentProduct) {
        logger.info(`There is already an investment product document for ${assetCommonId}, skipping...`, {
          module: `script:${this.scriptName}`
        });
        continue;
      }

      await new InvestmentProduct({
        commonId: assetCommonId,
        listed: true,
        buyLine: { active: true },
        sellLine: { active: true }
      }).save();

      logger.info(`Created investment product for ${assetCommonId}!`, {
        module: `script:${this.scriptName}`
      });
    }

    logger.info("✅ Added investment products for stocks!", {
      module: `script:${this.scriptName}`
    });
  }
}

new AddInvestmentProductsForAssetsRunner().run();
