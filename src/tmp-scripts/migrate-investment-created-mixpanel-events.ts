import <PERSON>riptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import axios from "axios";
import Decimal from "decimal.js";

const OLD_EVENTS = [
  {
    event: "Investment Created",
    properties: {
      time: 1715008405,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "d033753e-2fc6-5cc4-9669-aef0ec32abee",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1715008408348,
      amount: 100000,
      assetName: "mmf_dist_gbp",
      cashbackAmount: 0,
      category: "savings",
      commissionFees: 0,
      email: "<EMAIL>",
      executionSpreadFees: 0,
      frequency: "one-off",
      fxFees: 0,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1715008408384,
      side: "buy"
    }
  },
  {
    event: "Investment Created",
    properties: {
      time: 1714734913,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "d87f7d88-ed32-51ff-952e-2f8828bafc30",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714734916180,
      amount: 500000,
      assetName: "mmf_dist_gbp",
      cashbackAmount: 0,
      category: "savings",
      commissionFees: 0,
      email: "<EMAIL>",
      executionSpreadFees: 0,
      frequency: "one-off",
      fxFees: 0,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714734916206,
      side: "buy"
    }
  },
  {
    event: "Investment Created",
    properties: {
      time: 1714598708,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "dc090572-36e7-584e-8116-6984704e7584",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714598711769,
      amount: 100000,
      assetName: "mmf_dist_gbp",
      cashbackAmount: 0,
      category: "savings",
      commissionFees: 0,
      email: "<EMAIL>",
      executionSpreadFees: 0,
      frequency: "one-off",
      fxFees: 0,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714598711808,
      side: "buy"
    }
  },
  {
    event: "Investment Created",
    properties: {
      time: 1714598708,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "6a275b7f-7a82-5356-ad94-1305347b40ec",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714598711895,
      amount: 200000,
      assetName: "mmf_dist_gbp",
      cashbackAmount: 0,
      category: "savings",
      commissionFees: 0,
      email: "<EMAIL>",
      executionSpreadFees: 0,
      frequency: "one-off",
      fxFees: 0,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714598711947,
      side: "buy"
    }
  },
  {
    event: "Investment Created",
    properties: {
      time: 1714598407,
      distinct_id: "60ab78969a6b39003e0cfb10",
      $insert_id: "8e28a5ea-16e0-5c67-97bf-95670fc139a1",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714598408653,
      amount: 200000,
      assetName: "mmf_dist_gbp",
      cashbackAmount: 0,
      category: "savings",
      commissionFees: 0,
      email: "<EMAIL>",
      executionSpreadFees: 0,
      frequency: "one-off",
      fxFees: 0,
      id: "60ab78969a6b39003e0cfb10",
      lastLoginPlatform: "ios",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714598408692,
      side: "buy"
    }
  },
  {
    event: "Investment Created",
    properties: {
      time: 1714999807,
      distinct_id: "1714996287884-3538384728684165113",
      $insert_id: "01fe0df1-2e28-5c7b-86d1-b41600f608a3",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714999809611,
      amount: 10000,
      assetName: "mmf_dist_gbp",
      cashbackAmount: 0,
      category: "savings",
      commissionFees: 0,
      email: "<EMAIL>",
      executionSpreadFees: 0,
      frequency: "one-off",
      fxFees: 0,
      id: "6638c47d7cb5942c774627f8",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714999809633,
      side: "buy"
    }
  },
  {
    event: "Investment Created",
    properties: {
      time: 1714739107,
      distinct_id: "63518e19a30a0f004d4cc202",
      $insert_id: "b58dcefb-06d0-547c-be16-041193b47a7a",
      $mp_api_endpoint: "api.mixpanel.com",
      $mp_api_timestamp_ms: 1714739110002,
      amount: 5000,
      assetName: "mmf_dist_gbp",
      cashbackAmount: 0,
      category: "savings",
      commissionFees: 0,
      email: "<EMAIL>",
      executionSpreadFees: 0,
      frequency: "one-off",
      fxFees: 0,
      id: "63518e19a30a0f004d4cc202",
      lastLoginPlatform: "android",
      mp_lib: "Segment: @segment/analytics-node",
      mp_processing_time_ms: 1714739112085,
      side: "buy"
    }
  }
];

const NEW_UUIDS = [
  "c51a2ef9-b6b3-4814-994c-d00dc28ccb0b",
  "c927c647-be9d-48d1-ad6b-59ce1ad2a544",
  "d79c8208-949d-404b-8fde-10fdfe75e960",
  "35419faf-5a69-4e3c-be33-d1387b5aa0a1",
  "67955ed2-8543-426b-a6a9-585f6fe2033e",
  "0ebb0abb-f982-41e4-9beb-b699d1d4e083",
  "1e12a0e1-e684-41b3-9e20-f07319d0c4d2"
];

class MigrateInvestmentCreatedMixpanelEventsScriptRunner extends ScriptRunner {
  scriptName = "migrate-investment-created-mixpanel-events";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Migrating investment created mixpanel events with wrong savings...", {
      module: `script:${this.scriptName}`
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };

    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will apply!", {
        module: `script:${this.scriptName}`
      });
    }

    logger.info(`About to migrate ${OLD_EVENTS.length} events...`, {
      module: `script:${this.scriptName}`
    });
    for (let i = 0; i < OLD_EVENTS.length; i++) {
      await this.migrateEvent(OLD_EVENTS[i], NEW_UUIDS[i]);
    }

    logger.info("Finished migratinginvestment created mixpanel events with inflated savings...", {
      module: `script:${this.scriptName}`
    });
  }

  private async migrateEvent(event: any, newUUID: string) {
    const oldUUID = event.properties.$insert_id;

    const newEvent = {
      event: event.event,
      properties: {
        ...event.properties,
        $insert_id: newUUID,
        amount: Decimal.div(event.properties.amount, 100).toNumber()
      }
    };
    logger.info(`Migrating event with old UUID: ${oldUUID} to new UUID: ${newUUID}`, {
      module: `script:${this.scriptName}`,
      data: {
        old: event,
        new: newEvent
      }
    });

    if (this.options.dryRun) {
      return;
    }

    await axios.post("https://api.mixpanel.com/import?strict=1&project_id=2537575", [newEvent], {
      auth: {
        username: process.env.MIXPANEL_PROJECT_TOKEN,
        password: ""
      }
    });
  }
}

new MigrateInvestmentCreatedMixpanelEventsScriptRunner().run();
