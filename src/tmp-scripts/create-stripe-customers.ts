import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User } from "../models/User";
import DateUtil from "../utils/dateUtil";
import { ProviderEnum } from "../configs/providersConfig";
import { StripeService } from "../external-services/stripeService";

class AddStripeCustomersScriptRunner extends ScriptRunner {
  scriptName = "add-stripe-customers";

  async processFn(): Promise<void> {
    logger.info("Creating Stripe customers...", {
      module: `script:${this.scriptName}`
    });

    const users = await User.find({
      residencyCountry: "GB",
      activeProviders: { $ne: "stripe" },
      $or: [{ "providers.stripe.id": { $exists: false } }, { "providers.stripe.id": { $eq: undefined } }],
      submittedRequiredInfoAt: { $lt: DateUtil.getDateOfMinutesAgo(10) }
    });

    for (let i = 0; i < users.length; i++) {
      const user = users[i];

      logger.info(`Creating Stripe customer for ${user.id}...`, {
        module: `script:${this.scriptName}`
      });

      try {
        if (user.activeProviders.includes(ProviderEnum.STRIPE)) {
          throw new Error(`User ${user.id} includes Stripe active provider!`);
        } else if (user.providers?.stripe?.id) {
          throw new Error(`User ${user.id} already has Stripe provider ID!`);
        }

        const customerData = {
          email: user.email,
          name: `${user.firstName} ${user.lastName}`,
          metadata: {
            wealthyhoodId: user.id
          }
        };

        const { id } = await StripeService.Instance.createCustomer(customerData);

        await User.findByIdAndUpdate(user.id, {
          activeProviders: user.activeProviders.concat(ProviderEnum.STRIPE),
          "providers.stripe": { id }
        });
      } catch (err) {
        logger.error(`Failed to create customer for ${user.id}...`, {
          module: `script:${this.scriptName}`,
          data: { error: err }
        });
      }
    }

    logger.info("Finished creating Stripe customers!", {
      module: `script:${this.scriptName}`
    });
  }
}

new AddStripeCustomersScriptRunner().run();
