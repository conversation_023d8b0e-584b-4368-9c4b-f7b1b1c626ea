import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import {
  SavingsTopupTransaction,
  SavingsWithdrawalTransaction,
  Transaction,
  TransactionDocument
} from "../models/Transaction";
import mongoose from "mongoose";
import { savingsUniverseConfig } from "@wealthyhood/shared-configs";

class MigrationAddSavingsProductToTransactionsScriptRunner extends ScriptRunner {
  scriptName = "add-savingsProduct-field-to-transactions";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Adding savingsProduct field to transactions...", {
      module: `script:${this.scriptName}`
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };

    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will appy!", {
        module: `script:${this.scriptName}`
      });
    }

    const query: mongoose.FilterQuery<TransactionDocument> = {
      category: { $in: ["SavingsTopupTransaction", "SavingsWithdrawalTransaction"] },
      savingsProduct: { $exists: false }
    };

    const transactionsCount = await Transaction.countDocuments(query);

    logger.info(`Going to update savingsProduct field for ${transactionsCount} transactions...`, {
      module: `script:${this.scriptName}`
    });

    if (!this.options.dryRun) {
      await SavingsTopupTransaction.updateMany(
        { savingsProduct: { $exists: false } },
        {
          savingsProduct: "mmf_dist_gbp" as savingsUniverseConfig.SavingsProductType
        }
      );
      await SavingsWithdrawalTransaction.updateMany(
        { savingsProduct: { $exists: false } },
        {
          savingsProduct: "mmf_dist_gbp" as savingsUniverseConfig.SavingsProductType
        }
      );
    }

    logger.info("Finished adding savingsProduct field to transactions!", {
      module: `script:${this.scriptName}`
    });
  }
}

new MigrationAddSavingsProductToTransactionsScriptRunner().run();
