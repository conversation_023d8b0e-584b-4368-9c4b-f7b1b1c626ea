import <PERSON><PERSON>tRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User } from "../models/User";
import { ComplyAdvantageService } from "../external-services/complyAdvantageService";

class UpdateComplyAdvantageRunner extends ScriptRunner {
  scriptName = "update-comply-advantage-id";

  async processFn(): Promise<void> {
    logger.info("Adding aml checks to users...", {
      module: `script:${this.scriptName}`
    });

    const users = await User.find({ "providers.complyAdvantage.id": { $exists: true } });

    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      logger.info(`Processing user ${user.id}...`, {
        module: `script:${this.scriptName}`
      });

      const searches = await ComplyAdvantageService.retrieveSearches(user.id);
      const complyAdvantageSearchId = searches?.[0]?.ref;
      if (!complyAdvantageSearchId) {
        logger.info(`Removed comply advantage id for user ${user.id}...`, {
          module: `script:${this.scriptName}`,
          data: {
            complyAdvantageSearchIdRemoved: user.providers.complyAdvantage.id
          }
        });
        await user.updateOne({
          $unset: { "providers.complyAdvantage": true }
        });
        continue;
      }

      await user.updateOne({
        "providers.complyAdvantage.id": complyAdvantageSearchId
      });

      logger.info(`Replaced comply advantage to user ${user.id}...`, {
        module: `script:${this.scriptName}`,
        data: {
          complyAdvantageSearchIdOld: user.providers.complyAdvantage.id,
          complyAdvantageSearchIdNew: complyAdvantageSearchId
        }
      });
    }

    logger.info("✅ Added aml checks to users!", {
      module: `script:${this.scriptName}`
    });
  }
}

new UpdateComplyAdvantageRunner().run();
