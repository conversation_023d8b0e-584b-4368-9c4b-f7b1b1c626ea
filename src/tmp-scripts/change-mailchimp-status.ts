import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import MailchimpService, { AudienceIdEnum } from "../external-services/mailchimpService";

const userEmails = [
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>"
];

class ChangeMailchimpStatusScriptRunner extends ScriptRunner {
  scriptName = "change-mailchimp-status";
  private updatedCount = 0;

  async processFn(): Promise<void> {
    logger.info("Starting to change Mailchimp status to 'Signed Up'...", {
      module: `script:${this.scriptName}`
    });

    for (const email of userEmails) {
      try {
        await MailchimpService.updateMember(
          email,
          {
            merge_fields: { STATUS: "Signed Up" }
          },
          AudienceIdEnum.WEALTHYHOOD,
          { silent: true }
        );

        this.updatedCount++;
      } catch (error) {
        logger.error(`Failed to update status for user ${email}`, {
          module: `script:${this.scriptName}`,
          data: {
            email: email,
            error: error.message
          }
        });
      }
    }

    logger.info(`✅ Finished updating ${this.updatedCount} users!`, {
      module: `script:${this.scriptName}`
    });
  }
}

new ChangeMailchimpStatusScriptRunner().run();
