import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User } from "../models/User";
import { envIsProd } from "../utils/environmentUtil";
import UserBuilder from "../tests/utils/userBuilder";
import { Transaction } from "../models/Transaction";
import OrderService from "../services/orderService";
import mongoose from "mongoose";

class CleanStagingEnvironmentScriptRunner extends ScriptRunner {
  scriptName = "clean-staging-environment";

  async processFn(): Promise<void> {
    if (envIsProd()) {
      logger.warn("Trying to run clean up in prod! 😱...", {
        module: `script:${this.scriptName}`
      });
      return;
    }

    // Delete unused accounts (204 users)
    const usersToDelete = await User.find({
      $or: [
        { email: { $regex: "eleni" } },
        { email: { $regex: "elina" } },
        { email: { $regex: "nick" } },
        { email: { $regex: "-savvas" } },
        { email: { $regex: "-anastasioscho" } },
        { email: { $regex: "pavlos.kosmetatos+" } }
      ]
    });

    const userBuilder = new UserBuilder();

    await Promise.all(
      usersToDelete.map((user) => {
        try {
          return userBuilder.reset(user.email);
        } catch (err) {
          logger.warn(`Could not clean up user ${user.email}! 😱...`, {
            module: `script:${this.scriptName}`
          });
          return;
        }
      })
    );

    // 764 transactions
    await Transaction.deleteMany({
      owner: {
        $in: [
          new mongoose.Types.ObjectId("657b20667719be4478e76d43"),
          new mongoose.Types.ObjectId("657b31b8dce2a38c7c80d806")
        ]
      },
      status: { $in: ["PendingDeposit", "PendingGift", "Pending"] }
    });

    await OrderService.deleteOrphanedOrders();
  }
}

new CleanStagingEnvironmentScriptRunner().run();
