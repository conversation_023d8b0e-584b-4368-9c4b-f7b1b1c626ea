import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User } from "../models/User";
import UserDataRequestService from "../services/userDataRequestService";
import { UserDataRequestReasonEnum } from "../models/UserDataRequest";

const USER_WK_PARTY_IDS = [
  "pty-36wnwfiwd244eo",
  "pty-36riap5wi243sy",
  "pty-364zzy26h243hm",
  "pty-36og33hmx243ho",
  "pty-36ibx4bl5242l2",
  "pty-36g66n7s4242l2",
  "pty-36crzy6gb243ce",
  "pty-35yayedhu2425s",
  "pty-35regszxc244hw",
  "pty-356qbidpk242cq",
  "pty-355dblbax243ec"
];

const delay = (time: number) => new Promise((resolve, reject) => setTimeout(resolve, time));

class CloseBelarusAndRussianUsersScriptRunner extends ScriptRunner {
  scriptName = "close-russian-and-belarus-users";

  async processFn(): Promise<void> {
    logger.info("Closing russian and belarus users  flagged by Wealthkernel...", {
      module: `script:${this.scriptName}`
    });

    for (let i = 0; i < USER_WK_PARTY_IDS.length; i++) {
      const wkPartyId = USER_WK_PARTY_IDS[i];

      try {
        const user = await User.findOne({ "providers.wealthkernel.id": wkPartyId });

        if (!user) {
          throw new Error(`Could not find user with wk partyId ${wkPartyId}`);
        }

        await UserDataRequestService.createUserDataRequest(
          user,
          "disassociation",
          UserDataRequestReasonEnum.INACTIVE_USER
        );
        if (i % 20 === 0) {
          await delay(5000);
        }
      } catch (err) {
        logger.error(`Could not create user data request for user with wk partyId ${wkPartyId}`, {
          module: `script:${this.scriptName}`,
          method: "processFn"
        });
      }
    }

    await delay(5000);

    logger.info("Closed inactive  russian and belarus users flagged by Wealthkernel!", {
      module: `script:${this.scriptName}`
    });
  }
}

new CloseBelarusAndRussianUsersScriptRunner().run();
