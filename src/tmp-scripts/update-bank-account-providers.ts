import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { BankAccount } from "../models/BankAccount";
import { ProviderEnum } from "../configs/providersConfig";

class UpdateBankAccountProviders extends ScriptRunner {
  scriptName = "update-bank-account-providers";

  async processFn(): Promise<void> {
    logger.info("Updating bank account providers...", {
      module: `script:${this.scriptName}`
    });

    // First update activeProviders
    await BankAccount.updateMany(
      {},
      {
        activeProviders: [ProviderEnum.WEALTHKERNEL, ProviderEnum.TRUELAYER]
      }
    );

    // Then unset providers.gocardless
    await BankAccount.updateMany(
      {},
      {
        $unset: {
          "providers.gocardless": true
        }
      }
    );

    logger.info("✅ Finished updating bank account providers!", {
      module: `script:${this.scriptName}`
    });
  }
}

new UpdateBankAccountProviders().run();
