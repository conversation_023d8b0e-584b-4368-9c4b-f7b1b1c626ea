import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User } from "../models/User";

class ResetAcceptedTermsScriptRunner extends ScriptRunner {
  scriptName = "reset-accepted-terms-field";

  async processFn(): Promise<void> {
    logger.info("Resetting 'hasAcceptedTerms' field to true for all verified & KYC Failed users...", {
      module: `script:${this.scriptName}`
    });

    await User.updateMany({ kycStatus: { $in: ["passed", "failed"] } }, { hasAcceptedTerms: true });

    logger.info("Finished resetting 'hasAcceptedTerms' field to true for all verified & KYC Failed users!", {
      module: `script:${this.scriptName}`
    });
  }
}

new ResetAcceptedTermsScriptRunner().run();
