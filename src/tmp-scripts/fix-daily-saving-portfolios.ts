import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import {
  DailyPortfolioSavingsTicker,
  DailySavingsProductTicker,
  DailySavingsProductTickerDocument
} from "../models/DailyTicker";
import Decimal from "decimal.js";
import PortfolioService from "../services/portfolioService";
import { savingsUniverseConfig, fees } from "@wealthyhood/shared-configs";
import { PortfolioDocument } from "../models/Portfolio";
import SavingsProductService from "../services/savingsProductService";

const startDate = new Date("2025-05-21T00:00:00.000Z");
const endDate = new Date("2025-05-21T23:59:59.999Z");

const { SavingsProductArray } = savingsUniverseConfig;

class FixDailySavingsPortfolios extends ScriptRunner {
  scriptName = "fix-daily-saving-portfolios";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Fix daily savings portfolios for 2025-04-09", {
      module: `script:${this.scriptName}`
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };

    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will appy!", {
        module: `script:${this.scriptName}`,
        method: "processFn"
      });
    }

    const commonIdToSavingsProductDict = await SavingsProductService.getSavingsProductsDict("commonId", false);

    // Update savings product tickers with the correct distribution values
    const savingsProductTickersTickerGBP = await DailySavingsProductTicker.findOneAndUpdate(
      {
        savingsProduct: commonIdToSavingsProductDict["mmf_dist_gbp"].id,
        date: {
          $gte: startDate,
          $lte: endDate
        }
      },
      {
        dailyDistributionFactor: 0.000118301,
        oneDayYield: 4.32,
        fixingDate: startDate
      }
    );

    const savingsProductTickersTickerEUR = await DailySavingsProductTicker.findOneAndUpdate(
      {
        savingsProduct: commonIdToSavingsProductDict["mmf_dist_eur"].id,
        date: {
          $gte: startDate,
          $lte: endDate
        }
      },
      {
        dailyDistributionFactor: 0.000057932,
        oneDayYield: 2.11,
        fixingDate: startDate
      }
    );

    // We want to query all the portfolios where the savings has any of the MMFs in the universe with
    // amount larger than 0.
    const orOperatorQuery = SavingsProductArray.map((savingsProductId) => ({
      [`savings.${savingsProductId}.amount`]: { $gt: 0 }
    }));

    await PortfolioService.getPortfoliosStreamed(
      {
        $or: orOperatorQuery
      },
      { owner: true, currentTicker: false }
    ).eachAsync(
      async (portfolios: PortfolioDocument[]) => {
        const promises = portfolios.map(async (portfolio) => {
          try {
            for (const savings of portfolio.savings) {
              const [savingsProductId, portfolioSavings] = savings;
              if (new Decimal(portfolioSavings.amount).gt(0)) {
                const dailySavingsProductTicker =
                  savingsProductId === "mmf_dist_gbp"
                    ? savingsProductTickersTickerGBP
                    : savingsProductTickersTickerEUR;
                await this._updateDailyPortfolioSavingsTicker(portfolio, dailySavingsProductTicker);
              }
            }
          } catch (err) {
            logger.error(`Could not update user savings product ticker for ${portfolio.id} portfolio`, {
              module: "DailyTickerCronService",
              method: "createDailyPortfolioSavingsTicker"
            });
          }
        });
        await Promise.all(promises);
      },
      { batchSize: 20 }
    );

    logger.info("✅ Finished fixing daily savings portfolios for 2025-04-09", {
      module: `script:${this.scriptName}`
    });
  }

  private async _updateDailyPortfolioSavingsTicker(
    portfolio: PortfolioDocument,
    dailySavingsProductTicker: DailySavingsProductTickerDocument
  ): Promise<void> {
    const ticker = await DailyPortfolioSavingsTicker.findOne({
      portfolio: portfolio.id,
      savingsProduct: dailySavingsProductTicker.savingsProduct,
      date: { $gte: startDate, $lte: endDate }
    });
    if (!ticker) {
      logger.info(`Portfolio Savings Ticker doesnt exist for ${portfolio.id} portfolio`);
      return;
    }

    const annualFee = ticker.planFee;
    const oneDayFee = Decimal.div(annualFee, 365);
    const { dailyDistributionFactor } = dailySavingsProductTicker;
    const dailyDistributionFactorAfterWealthyhoodFee = Decimal.sub(dailyDistributionFactor, oneDayFee);

    const currentHeldAmount = ticker.holdingAmount;
    const dailyAccrual = Decimal.mul(currentHeldAmount, dailyDistributionFactorAfterWealthyhoodFee).toNumber();

    await DailyPortfolioSavingsTicker.updateOne(
      { _id: ticker._id },
      {
        dailyAccrual
      }
    );
  }
}

new FixDailySavingsPortfolios().run();
