import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User, UserDocument } from "../models/User";
import analytics from "../external-services/segmentAnalyticsService";
import { Participant } from "../models/Participant";

const delay = (time: number) => new Promise((resolve, reject) => setTimeout(resolve, time));

class BackfillResidencyFieldsRunner extends ScriptRunner {
  scriptName = "backfill-residency-fields";

  async processFn(): Promise<void> {
    logger.info("Starting backfill for extra fields...", {
      module: `script:${this.scriptName}`
    });

    // We are keeping count so that we know when to wait 5 seconds (every 20 identify calls)
    let userCount = 0;
    await User.find({ residencyCountry: { $exists: true } })
      .cursor()
      .addCursorFlag("noCursorTimeout", true)
      .eachAsync(async (user) => {
        const traits = {
          residencyC: user.residencyCountry,
          currency: user.currency
        } as any;

        if (user.nationalities?.length > 0) {
          traits.nation = user.nationalities[0];
        }

        try {
          analytics.identify(user, traits, { All: false, MailChimp: true });

          userCount++;

          if (userCount % 20 === 0) {
            logger.info(`We have migrated ${userCount} users!`, {
              module: `script:${this.scriptName}`
            });

            await delay(5000);
          }
        } catch (err) {
          logger.error(`Could not identify fields for user ${user.id} 😵‍💫`, {
            module: `script:${this.scriptName}`
          });
        }
      });

    // We are keeping count so that we know when to wait 5 seconds (every 20 identify calls)
    let participantCount = 0;
    await Participant.find({ pageUserLanded: { $exists: true } })
      .populate("owner")
      .cursor()
      .addCursorFlag("noCursorTimeout", true)
      .eachAsync(async (participant) => {
        const user = participant.owner as UserDocument;

        try {
          analytics.identify(user, { pageLanded: participant.pageUserLanded }, { All: false, MailChimp: true });

          participantCount++;

          if (participantCount % 20 === 0) {
            logger.info(`We have migrated ${participantCount} users!`, {
              module: `script:${this.scriptName}`
            });

            await delay(5000);
          }
        } catch (err) {
          logger.error(`Could not identify fields for user ${user.id} 😵‍💫`, {
            module: `script:${this.scriptName}`
          });
        }
      });

    logger.info("✅ Finished backfill for extra fields!", {
      module: `script:${this.scriptName}`
    });
  }
}

new BackfillResidencyFieldsRunner().run();
