import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User } from "../models/User";
import { entitiesConfig } from "@wealthyhood/shared-configs";

class MigrationCurrencyAndCompanyEntityScriptRunner extends ScriptRunner {
  scriptName = "migrate-currency-and-company-entity";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Migrating users to have company entity & currency...", {
      module: `script:${this.scriptName}`
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };

    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will appy!", {
        module: `script:${this.scriptName}`,
        method: "processFn"
      });
    }

    // For all users who have **set** residency country to GB, we want to set their currency & company entity.
    await User.updateMany(
      { residencyCountry: "GB" },
      { currency: "GBP", companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK }
    );

    // For all users who have **set** residency country to any other country than GB, we want to set their currency & company entity.
    await User.updateMany(
      {
        $and: [{ residencyCountry: { $ne: "GB" } }, { residencyCountry: { $exists: true } }]
      },
      { currency: "EUR", companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE }
    );

    // For all users who have **NOT set** residency country, we unset their currency.
    await User.updateMany(
      {
        residencyCountry: { $exists: false }
      },
      { $unset: { currency: true } }
    );
  }
}

new MigrationCurrencyAndCompanyEntityScriptRunner().run();
