import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { Order } from "../models/Order";

class AddUnitPriceToOldOrdersScriptRunner extends ScriptRunner {
  scriptName = "add-unit-price-to-old-orders";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Migrating orders to have unit price...", {
      module: `script:${this.scriptName}`
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };

    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will apply!", {
        module: `script:${this.scriptName}`,
        method: "processFn"
      });
    }

    const numberOfDocs = await Order.countDocuments({
      $and: [
        { "consideration.amount": { $exists: true } },
        { "consideration.amount": { $gt: 0 } },
        { quantity: { $exists: true } },
        { quantity: { $gt: 0 } }
      ],
      "providers.wealthkernel.status": "Matched",
      unitPrice: { $exists: false }
    });

    logger.info(`Found ${numberOfDocs} orders to update...`, {
      module: `script:${this.scriptName}`
    });

    if (!this.options.dryRun) {
      logger.info(`Going to add unit price field for ${numberOfDocs} orders...`, {
        module: `script:${this.scriptName}`
      });

      // To store unit price, we divide consideration.amount / quantity, then divide by 100 (to convert to whole currency),
      // then round to 2 decimal places
      await Order.updateMany(
        {
          $and: [
            { "consideration.amount": { $exists: true } },
            { "consideration.amount": { $gt: 0 } },
            { quantity: { $exists: true } },
            { quantity: { $gt: 0 } }
          ],
          "providers.wealthkernel.status": "Matched",
          unitPrice: { $exists: false }
        },
        [
          {
            $set: {
              unitPrice: {
                amount: { $round: [{ $divide: [{ $divide: ["$consideration.amount", "$quantity"] }, 100] }, 2] },
                currency: "GBP"
              }
            }
          }
        ]
      );
    } else {
      logger.info(`Not adding unit price field for ${numberOfDocs} orders since we're in dry run...`, {
        module: `script:${this.scriptName}`
      });
    }

    logger.info("Finished migrating orders!", {
      module: `script:${this.scriptName}`
    });
  }
}

new AddUnitPriceToOldOrdersScriptRunner().run();
