import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { Order } from "../models/Order";

class SettleOrdersScriptRunner extends ScriptRunner {
  scriptName = "settle-orders";

  async processFn(): Promise<void> {
    logger.info("Finding matched orders ready to be settled...", {
      module: `script:${this.scriptName}`
    });

    const updateResult = await Order.updateMany(
      {
        status: "Matched",
        marketSettledAt: { $lt: new Date() }
      },
      {
        status: "Settled"
      }
    );

    logger.info(`Updated ${updateResult.modifiedCount} orders to Settled`, {
      module: `script:${this.scriptName}`,
      data: {
        modifiedCount: updateResult.modifiedCount,
        matchedCount: updateResult.matchedCount
      }
    });
  }
}

new SettleOrdersScriptRunner().run();
