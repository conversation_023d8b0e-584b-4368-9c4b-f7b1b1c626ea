import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { Account, AccountDocument } from "../models/Account";
import { UserDocument } from "../models/User";
import analytics from "../external-services/segmentAnalyticsService";
import events from "../event-handlers/events";

const delay = (time: number) => new Promise((resolve, reject) => setTimeout(resolve, time));

class BackfillSuspendedAccountEventsRunner extends ScriptRunner {
  scriptName = "backfill-suspended-accounts";

  async processFn(): Promise<void> {
    logger.info("Backfilling events for suspended accounts...", {
      module: `script:${this.scriptName}`
    });

    const numberOfSuspendedAccounts = await Account.countDocuments({
      "providers.wealthkernel.status": "Suspended"
    });

    logger.info(`Total number of suspended accounts: ${numberOfSuspendedAccounts}`, {
      module: `script:${this.scriptName}`
    });

    await Account.find({ "providers.wealthkernel.status": "Suspended" })
      .populate("owner")
      .cursor()
      .addCursorFlag("noCursorTimeout", true)
      .eachAsync(async (account: AccountDocument, i: number) => {
        logger.info(`Going through user ${i + 1}/${numberOfSuspendedAccounts}`, {
          module: `script:${this.scriptName}`
        });

        const user = account.owner as UserDocument;

        analytics.track(user, events.user.accountSuspended.name, { All: false, Mixpanel: true });

        if (i % 20 === 0) {
          await delay(5000);
        }
      });

    await delay(5000);

    logger.info("✅ Finished backfilling events for suspended accounts!", {
      module: `script:${this.scriptName}`
    });
  }
}

new BackfillSuspendedAccountEventsRunner().run();
