import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { ContentIngestionCronService } from "../jobs/services/contentIngestionCronService";
import { envIsProd } from "../utils/environmentUtil";

// Backfill 9 months of data in prod, 21 days in sandbox/dev
const DAYS_TO_BACKFILL = envIsProd() ? 9 * 31 : 21;

class BackfillAnalystInsightsRunner extends ScriptRunner {
  scriptName = "backfill-analyst-insights";

  async processFn(): Promise<void> {
    logger.info("Backfilling analyst insights...", {
      module: `script:${this.scriptName}`
    });

    await ContentIngestionCronService.createAnalystInsightContentEntries(DAYS_TO_BACKFILL);

    logger.info("✅ Backfilled analyst insights!", {
      module: `script:${this.scriptName}`
    });
  }
}

new BackfillAnalystInsightsRunner().run();
