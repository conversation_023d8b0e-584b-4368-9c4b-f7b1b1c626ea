import {
  DailyPortfolioSavingsTicker,
  DailyPortfolioSavingsTickerDocument,
  DailySavingsProductTickerDocument
} from "./../models/DailyTicker";
import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import mongoose from "mongoose";
import DbUtil from "../utils/dbUtil";
import { savingsUniverseConfig } from "@wealthyhood/shared-configs";
import { DailySavingsProductTicker } from "../models/DailyTicker";
import DateUtil from "../utils/dateUtil";
import Decimal from "decimal.js";

const TICKER_SPLIT: Record<string, Date[]> = {
  "2024-05-03": [
    new Date("2024-05-03T12:00:00.000+00:00"),
    new Date("2024-05-04T12:00:00.000+00:00"),
    new Date("2024-05-05T12:00:00.000+00:00"),
    new Date("2024-05-06T12:00:00.000+00:00") // Monday - UK bank holiday
  ],
  "2024-05-10": [
    new Date("2024-05-10T12:00:00.000+00:00"),
    new Date("2024-05-11T12:00:00.000+00:00"),
    new Date("2024-05-12T12:00:00.000+00:00")
  ],
  "2024-05-17": [
    new Date("2024-05-17T12:00:00.000+00:00"),
    new Date("2024-05-18T12:00:00.000+00:00"),
    new Date("2024-05-19T12:00:00.000+00:00")
  ],
  "2024-05-24": [
    new Date("2024-05-24T12:00:00.000+00:00"),
    new Date("2024-05-25T12:00:00.000+00:00"),
    new Date("2024-05-26T12:00:00.000+00:00"),
    new Date("2024-05-27T12:00:00.000+00:00") // Monday - UK bank holiday
  ],
  "2024-05-31": [
    new Date("2024-05-31T12:00:00.000+00:00"),
    new Date("2024-06-01T12:00:00.000+00:00"),
    new Date("2024-06-02T12:00:00.000+00:00")
  ],
  "2024-06-07": [
    new Date("2024-06-07T12:00:00.000+00:00"),
    new Date("2024-06-08T12:00:00.000+00:00"),
    new Date("2024-06-09T12:00:00.000+00:00")
  ],
  "2024-06-14": [
    new Date("2024-06-14T12:00:00.000+00:00"),
    new Date("2024-06-15T12:00:00.000+00:00"),
    new Date("2024-06-16T12:00:00.000+00:00")
  ]
};

const MMF_PRODUCT_ID: savingsUniverseConfig.SavingsProductType = "mmf_dist_gbp";
const DECIMAL_PLACES = 9;

class MigrateFridaySavingsTickersScriptRunner extends ScriptRunner {
  scriptName = "migrate-friday-savings-tickers";
  private options: { dryRun: boolean };
  async processFn(): Promise<void> {
    logger.info("Migrating savings product & portfolio tickers created on Fridays...", {
      module: `script:${this.scriptName}`,
      method: "processFn"
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };

    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will appy!", {
        module: `script:${this.scriptName}`,
        method: "processFn"
      });
    }

    for (const [date, tickers] of Object.entries(TICKER_SPLIT)) {
      logger.info(`About to split tickers for date ${date}`, {
        module: `script:${this.scriptName}`,
        method: "processFn"
      });

      await DbUtil.runInSession(async (session: mongoose.ClientSession) => {
        const dateRangeStart = DateUtil.getStartAndEndOfDay(tickers[0]).start;
        const dateRangeEnd = DateUtil.getStartAndEndOfDay(tickers[tickers.length - 1]).end;

        const savingsProductTickers = await DailySavingsProductTicker.find({
          date: {
            $gte: dateRangeStart,
            $lte: dateRangeEnd
          }
        });
        if (savingsProductTickers.length > 1) {
          logger.warn(`Found more than one ticker for date ${date} - ${MMF_PRODUCT_ID}, aborting...`, {
            module: `script:${this.scriptName}`,
            method: "processFn",
            data: {
              tickerCount: savingsProductTickers.length,
              savingsProductId: MMF_PRODUCT_ID
            }
          });
        } else {
          await this._splitSavingsProductTicker(savingsProductTickers[0], tickers, session);
        }

        const portfolioSavingsTickers = await DailyPortfolioSavingsTicker.find({
          date: {
            $gte: dateRangeStart,
            $lte: dateRangeEnd
          }
        });

        const portfolioSavingsTickersGroupedByPortfolio = portfolioSavingsTickers.reduce(
          (acc, ticker) => {
            if (!acc[ticker.portfolio.toString()]) {
              acc[ticker.portfolio.toString()] = [];
            }
            acc[ticker.portfolio.toString()].push(ticker);
            return acc;
          },
          {} as Record<string, DailyPortfolioSavingsTickerDocument[]>
        );

        for (const [portfolioId, portfolioTickers] of Object.entries(portfolioSavingsTickersGroupedByPortfolio)) {
          if (portfolioTickers.length > 1) {
            logger.warn(
              `Found more than one portfolio savings ticker for date ${date} - ${portfolioId}, aborting...`,
              {
                module: `script:${this.scriptName}`,
                method: "processFn",
                data: {
                  portfolioId,
                  tickerCount: portfolioTickers.length
                }
              }
            );
            continue;
          }
          await this._splitPortfolioSavingsTicker(portfolioTickers[0], tickers, session);
        }
      });

      logger.info("✅ Finished migrating savings product & portfolio tickers created on Fridays...", {
        module: `script:${this.scriptName}`,
        method: "processFn"
      });
    }
  }

  private async _splitSavingsProductTicker(
    ticker: DailySavingsProductTickerDocument,
    tickerDates: Date[],
    session: mongoose.ClientSession
  ): Promise<void> {
    const splitCount = tickerDates.length;
    /**
     * Divide daily distribution factor by split count
     * Example:
     * - Daily disribution factor 0.3
     * - Split count 3
     * - New Daily distribution factor 0.1
     */
    const dailyDistributionFactorNew = Decimal.div(ticker.dailyDistributionFactor, splitCount)
      .toDecimalPlaces(DECIMAL_PLACES)
      .toNumber();

    for (let i = 0; i < tickerDates.length; i++) {
      const tickerDate = tickerDates[i];
      if (DateUtil.datesAreEqual(tickerDate, ticker.date)) {
        logger.info(
          `Updating ticker ${ticker.id} for ${DateUtil.formatDateToDDMONYYYY(tickerDate)} with new values`,
          {
            module: `script:${this.scriptName}`,
            method: "_splitSavingsProductTicker"
          }
        );

        if (!this.options.dryRun) {
          await DailySavingsProductTicker.findByIdAndUpdate(
            ticker.id,
            {
              dailyDistributionFactor: dailyDistributionFactorNew
            },
            {
              session
            }
          );
        }
      } else {
        const newTicker = new DailySavingsProductTicker({
          savingsProduct: ticker.savingsProduct,
          dailyDistributionFactor: dailyDistributionFactorNew,
          // One Day yield remains the same
          oneDayYield: ticker.oneDayYield,
          /**
           * TBD: new fixing date
           */
          fixingDate: ticker.fixingDate,
          date: tickerDate
        });

        logger.info(`Creating new ticker ${newTicker.id} for date ${DateUtil.formatDateToDDMONYYYY(tickerDate)}`, {
          module: `script:${this.scriptName}`,
          method: "_splitSavingsProductTicker"
        });

        if (!this.options.dryRun) {
          await newTicker.save({ session });
        }
      }
    }
  }

  private async _splitPortfolioSavingsTicker(
    ticker: DailyPortfolioSavingsTickerDocument,
    tickerDates: Date[],
    session: mongoose.ClientSession
  ): Promise<void> {
    const splitCount = tickerDates.length;
    /**
     * Divide Daily Accrual by split count
     * Example:
     * - Daily Accrual 0.3
     * - Split count 3
     * - New Daily Accrual 0.1
     */
    const dailyAccrualNew = Decimal.div(ticker.dailyAccrual, splitCount)
      .toDecimalPlaces(DECIMAL_PLACES)
      .toNumber();

    for (let i = 0; i < tickerDates.length; i++) {
      const tickerDate = tickerDates[i];
      if (DateUtil.datesAreEqual(tickerDate, ticker.date)) {
        logger.info(
          `Updating portfolio ticker ${ticker.id} for date ${DateUtil.formatDateToDDMONYYYY(tickerDate)} with new values`,
          {
            module: `script:${this.scriptName}`,
            method: "_splitPortfolioSavingsTicker",
            data: {
              portfolioId: ticker.portfolio.toString()
            }
          }
        );

        if (!this.options.dryRun) {
          await DailyPortfolioSavingsTicker.findByIdAndUpdate(
            ticker.id,
            {
              dailyAccrual: dailyAccrualNew
            },
            {
              session
            }
          );
        }
      } else {
        const newTicker = new DailyPortfolioSavingsTicker({
          portfolio: ticker.portfolio,
          planPrice: ticker.planPrice,
          planFee: ticker.planFee,
          holdingAmount: ticker.holdingAmount,
          savingsProduct: ticker.savingsProduct,
          date: tickerDate,
          dailyAccrual: dailyAccrualNew
        });

        logger.info(
          `Creating portfolio new ticker ${newTicker.id} for date ${DateUtil.formatDateToDDMONYYYY(tickerDate)}`,
          {
            module: `script:${this.scriptName}`,
            method: "_splitPortfolioSavingsTicker",
            data: {
              portfolioId: ticker.portfolio.toString()
            }
          }
        );

        if (!this.options.dryRun) {
          await newTicker.save({ session });
        }
      }
    }
  }
}

new MigrateFridaySavingsTickersScriptRunner().run();
