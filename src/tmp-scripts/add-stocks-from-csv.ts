import * as fs from "fs";
import { writeFile } from "fs/promises";
import axios from "axios";
import { investmentUniverseConfig, currenciesConfig } from "@wealthyhood/shared-configs";

const { CURRENCY_SYMBOLS } = currenciesConfig;

// eslint-disable-next-line @typescript-eslint/no-var-requires
require("dotenv").config({ path: "dev.env" });

type EodSector =
  | "Technology"
  | "Healthcare"
  | "Consumer Defensive"
  | "Consumer Cyclical"
  | "Energy"
  | "Communication Services"
  | "Financial Services"
  | "Industrials"
  | "Utilities"
  | "Basic Materials";

const EOD_TO_WEALTHYHOOD_SECTOR_MAPPINGS: Record<EodSector, investmentUniverseConfig.InvestmentSectorType> = {
  Technology: "technology",
  Healthcare: "healthcare",
  "Consumer Defensive": "consumer",
  "Consumer Cyclical": "consumer",
  Energy: "energy",
  "Communication Services": "communication",
  "Financial Services": "financials",
  Industrials: "industrials",
  Utilities: "utilities",
  "Basic Materials": "materials"
};

type StockCSVHeaderType =
  | "Name"
  | "Short description"
  | "Ticker"
  | "ISIN"
  | "Icon name"
  | "Similar companies"
  | "Search terms"
  | "Typos";

const ARRAY_FIELDS: StockCSVHeaderType[] = ["Similar companies", "Typos", "Search terms"];

const CSV_HEADER_TO_KEY_MAPPINGS: Record<StockCSVHeaderType, string> = {
  Name: "simpleName",
  "Short description": "shortDescription",
  Ticker: "formalTicker",
  ISIN: "isin",
  "Icon name": "keyName",
  "Similar companies": "similarCompanies",
  Typos: "typos",
  "Search terms": "searchTerms"
};

/**
 * When we add new stocks, we can run this script to retrieve EOD sectors and translate them to our sectors.
 */
async function printStocksFromCSV(): Promise<void> {
  const FILE_NAME = "stocks.csv";

  const csv = fs.readFileSync(`./src/tmp-scripts/${FILE_NAME}`, { encoding: "utf8" });
  const lines = csv.split("\n");
  const headers = lines[0].split(",");

  if (headers.some((header) => header === "Priority")) {
    throw new Error("Remove the priority column first!");
  }

  const result = [];
  for (let i = 1; i < lines.length; i++) {
    const config: any = {};
    const currentLine = lines[i].split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);

    console.log(`Processing line ${currentLine}`);

    for (let j = 0; j < headers.length; j++) {
      if (ARRAY_FIELDS.includes(headers[j].trim() as StockCSVHeaderType)) {
        config[CSV_HEADER_TO_KEY_MAPPINGS[headers[j].trim() as StockCSVHeaderType]] = currentLine[j]
          .trim()
          .replace(/"+/g, "")
          .split(",")
          .map((element) => element.trim());
      } else {
        config[CSV_HEADER_TO_KEY_MAPPINGS[headers[j].trim() as StockCSVHeaderType]] = currentLine[j]
          .trim()
          .replace(/"+/g, "");
      }
    }

    // Apart from what's in the CSV, we also add tradedCurrency, formalExchange (always USD and US respectively), category, assetClass and sector.
    config.tradedCurrency = "USD";
    config.tickerWithCurrency = `${CURRENCY_SYMBOLS["USD"]}${config.formalTicker}`;
    config.formalExchange = "US";
    config.category = "stock";
    config.assetClass = "equities";
    config.sector = "USD";

    try {
      const response = await axios.get(
        `https://eodhistoricaldata.com/api/fundamentals/${config.formalTicker}.${config.formalExchange}?api_token=${process.env.EOD_DATA_TOKEN}&fmt=json`
      );

      config.sector = EOD_TO_WEALTHYHOOD_SECTOR_MAPPINGS[response.data.General.Sector as EodSector];
    } catch (err) {
      console.log(`Could not add sector for ${config.keyName}`);
    }

    result.push(config);
  }

  const items = JSON.stringify(
    Object.fromEntries(
      result.map((config) => {
        const { keyName, ...configWithoutKeyname } = config;
        return [keyName, configWithoutKeyname];
      })
    )
  );

  // The content of these two files go in the shared-configs project.
  await writeFile("assetConfig.json", items, "utf8");
  await writeFile("assets.json", JSON.stringify(result.map((config) => config.keyName)), "utf8");

  // The content of these two files go in the statistics-api project.
  // ASSETS_SERVICES_MAPPING
  await writeFile(
    "assetCommonIdToTicker.json",
    JSON.stringify(
      Object.fromEntries(
        result.map((config) => {
          return [config.keyName, config.formalTicker];
        })
      )
    ),
    "utf8"
  );

  // ASSETS_CONFIG
  await writeFile(
    "assetStatisticsUniverse.json",
    JSON.stringify(
      Object.fromEntries(
        result.map((config) => {
          return [
            [config.formalTicker],
            {
              ticker: config.formalTicker,
              exchange: config.formalExchange,
              asset_class: config.assetClass
            }
          ];
        })
      )
    ),
    "utf8"
  );
}

(async () => {
  try {
    await printStocksFromCSV();
    console.log("success");
    process.exit(1);
  } catch (err) {
    console.log("error");
    console.log(err);
  }
})();
