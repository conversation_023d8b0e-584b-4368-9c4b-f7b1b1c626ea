import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import mongoose from "mongoose";
import { IntraDayPortfolioTicker, IntraDayPortfolioTickerDocument } from "../models/IntraDayTicker";

const DELETION_START_DATE = new Date("2024-10-01T00:00:00.000Z");
const DELETION_END_DATE = new Date("2024-10-01T22:00:00.000Z");

const PORTFOLIOS = [
  "60901e1d68d3be003ecacd68",
  "60abe58934d2dd003e14b1a5",
  "60ab7a599a6b39003e0cfb16",
  "64dc0ac90f18b9fc06df7001",
  "651d80b341b2bfde2599da54",
  "661532e0df0a1faa3bacadad",
  "66902137a8e456c2e7168b80"
];

class DeleteIntraDayPortfolioTickersScriptRunner extends ScriptRunner {
  scriptName = "delete-aml-checks";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Deleting IntraDay Portfolio Tickers...", {
      module: `script:${this.scriptName}`
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };

    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will apply!", {
        module: `script:${this.scriptName}`
      });
    }

    const query: mongoose.FilterQuery<IntraDayPortfolioTickerDocument> = {
      createdAt: { $gte: DELETION_START_DATE, $lte: DELETION_END_DATE },
      portfolio: { $in: PORTFOLIOS }
    };

    const intraDayPortfolioTickers = await IntraDayPortfolioTicker.countDocuments(query);
    logger.info(`Deleting ${intraDayPortfolioTickers} aml checks`, {
      module: `script:${this.scriptName}`
    });

    if (!this.options.dryRun) {
      await IntraDayPortfolioTicker.deleteMany(query);
    }

    logger.info("Finished deleting IntraDay Portfolio Tickers...", {
      module: `script:${this.scriptName}`
    });
  }
}

new DeleteIntraDayPortfolioTickersScriptRunner().run();
