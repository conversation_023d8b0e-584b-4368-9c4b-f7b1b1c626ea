import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { ChargeTransaction } from "../models/Transaction";
import DateUtil from "../utils/dateUtil";

class RetryChargesNovScriptRunner extends ScriptRunner {
  scriptName = "retry-charges-nov";

  async processFn(): Promise<void> {
    logger.info("Finding rejected charges from November...", {
      module: `script:${this.scriptName}`
    });

    const rejectedCharges = await ChargeTransaction.find({
      "providers.wealthkernel.status": "Rejected",
      createdAt: {
        $gte: DateUtil.getFirstDayOfLastMonth(),
        $lte: DateUtil.getFirstDayOfThisMonth()
      }
    });

    logger.info(`Found ${rejectedCharges.length} rejected charges to retry`, {
      module: `script:${this.scriptName}`,
      data: {
        chargeCount: rejectedCharges.length
      }
    });

    await ChargeTransaction.updateMany(
      {
        "providers.wealthkernel.status": "Rejected",
        createdAt: {
          $gte: DateUtil.getFirstDayOfLastMonth(),
          $lte: DateUtil.getFirstDayOfThisMonth()
        }
      },
      {
        $unset: { providers: 1 }
      }
    );

    logger.info("Finished retrying rejected charges!", {
      module: `script:${this.scriptName}`
    });
  }
}

new RetryChargesNovScriptRunner().run();
