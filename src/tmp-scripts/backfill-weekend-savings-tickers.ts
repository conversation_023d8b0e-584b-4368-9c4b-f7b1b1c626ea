import { DailyPortfolioSavingsTicker, DailyPortfolioSavingsTickerDocument } from "../models/DailyTicker";
import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import DateUtil from "../utils/dateUtil";

const TICKER_DATES: Record<string, Date[]> = {
  // Ticker on sunday that we want to replicate
  "2024-06-23": [
    new Date("2024-06-21T12:00:00.000+00:00"), // missing ticker on Friday
    new Date("2024-06-22T12:00:00.000+00:00") // missing ticker on Saturday
  ],

  // Ticker on sunday that we want to replicate
  "2024-06-30": [
    new Date("2024-06-28T12:00:00.000+00:00"), // missing ticker on Friday
    new Date("2024-06-29T12:00:00.000+00:00") // missing ticker on Saturday
  ]
};

class BackfillWeekendSavingsTickerScriptRunner extends ScriptRunner {
  scriptName = "backfill-weekend-savings-tickers";
  private options: { dryRun: boolean };
  async processFn(): Promise<void> {
    logger.info("Backfilling missing portfolio savings tickers...", {
      module: `script:${this.scriptName}`,
      method: "processFn"
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };

    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will appy!", {
        module: `script:${this.scriptName}`,
        method: "processFn"
      });
    }

    for (const [tickerDateToReplicate, tickerDatesToBackfill] of Object.entries(TICKER_DATES)) {
      logger.info(`About to backfill missing tickers for date ${tickerDateToReplicate}`, {
        module: `script:${this.scriptName}`,
        method: "processFn"
      });

      const { start, end } = DateUtil.getStartAndEndOfDay(new Date(tickerDateToReplicate));
      const portfolioTickersToReplicate = await DailyPortfolioSavingsTicker.find({
        date: {
          $gte: start,
          $lte: end
        }
      });

      for (const portfolioTickerToReplicate of portfolioTickersToReplicate) {
        await this._replicatePortfolioSavingsTickers(portfolioTickerToReplicate, tickerDatesToBackfill);
      }
    }

    logger.info("✅ Finished backfilling missing portfolio savings tickers...", {
      module: `script:${this.scriptName}`,
      method: "processFn"
    });
  }

  private async _replicatePortfolioSavingsTickers(
    ticker: DailyPortfolioSavingsTickerDocument,
    tickerDates: Date[]
  ): Promise<void> {
    for (let i = 0; i < tickerDates.length; i++) {
      const tickerDate = tickerDates[i];

      const tickerExists = await DailyPortfolioSavingsTicker.exists({
        portfolio: ticker.portfolio,
        date: tickerDate
      });
      if (tickerExists) {
        continue;
      }

      const newTicker = new DailyPortfolioSavingsTicker({
        portfolio: ticker.portfolio,
        planPrice: ticker.planPrice,
        planFee: ticker.planFee,
        holdingAmount: ticker.holdingAmount,
        savingsProduct: ticker.savingsProduct,
        date: tickerDate,
        dailyAccrual: ticker.dailyAccrual
      });

      logger.info(
        `Creating portfolio new ticker ${newTicker.id} for date ${DateUtil.formatDateToDDMONYYYY(tickerDate)}`,
        {
          module: `script:${this.scriptName}`,
          method: "_replicatePortfolioSavingsTickers",
          data: {
            portfolioId: ticker.portfolio.toString()
          }
        }
      );

      if (!this.options.dryRun) {
        await newTicker.save();
      }
    }
  }
}

new BackfillWeekendSavingsTickerScriptRunner().run();
