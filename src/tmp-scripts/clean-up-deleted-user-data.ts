import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";

import { Automation } from "../models/Automation";
import { Account } from "../models/Account";
import { Address } from "../models/Address";
import { BankAccount } from "../models/BankAccount";
import { DailyPortfolioTicker } from "../models/DailyTicker";
import { KycOperation } from "../models/KycOperation";
import { Mandate } from "../models/Mandate";
import { Order } from "../models/Order";
import { PaymentMethod } from "../models/PaymentMethod";
import { Portfolio } from "../models/Portfolio";
import { ReferralCode } from "../models/ReferralCode";
import { RewardInvitation } from "../models/RewardInvitation";
import { Reward } from "../models/Reward";
import { RiskAssessment } from "../models/RiskAssessment";
import { Subscription } from "../models/Subscription";
import { Transaction } from "../models/Transaction";
import { User } from "../models/User";
import { UserDataRequest } from "../models/UserDataRequest";

const DELETED_USER_IDS = [
  "62f1657fd46581004bfff6a6",
  "62f7f7f49ef8ee004b3609a5",
  "632cbd834fa535004b1ce497",
  "634bedfed8ea42004b2c1c2f",
  "63bd4a6e563fc4caa939ad12",
  "6418624c2c68e51c656862f2"
];

class CleanUpDeletedUserDataScriptRunner extends ScriptRunner {
  scriptName = "clean-up-deleted-user-data";

  async processFn(): Promise<void> {
    logger.info("Removing data of deleted users...", {
      module: `script:${this.scriptName}`
    });

    for (let i = 0; i < DELETED_USER_IDS.length; i++) {
      const userId = DELETED_USER_IDS[i];

      const user = await User.findOne({ _id: userId });
      if (user) {
        logger.error(`User documer with id ${userId} exists`, {
          module: `script:${this.scriptName}`,
          method: "processFn"
        });
        continue;
      }

      try {
        await _removeData(userId);
      } catch (err) {
        logger.error(`Could not remove data for user ${userId}`, {
          module: `script:${this.scriptName}`,
          method: "processFn"
        });
      }
    }

    logger.info("Removed data of inactive users!", {
      module: `script:${this.scriptName}`
    });
  }
}

async function _removeData(userId: string): Promise<void> {
  // delete all documents with owner the given user
  await Promise.all([
    Account.deleteMany({ owner: userId }),
    Address.deleteMany({ owner: userId }),
    Automation.deleteMany({ owner: userId }),
    BankAccount.deleteMany({ owner: userId }),
    KycOperation.deleteMany({ owner: userId }),
    Mandate.deleteMany({ owner: userId }),
    PaymentMethod.deleteMany({ owner: userId }),
    Portfolio.deleteMany({ owner: userId }),
    ReferralCode.deleteMany({ owner: userId }),
    RewardInvitation.deleteMany({ referrer: userId }),
    Reward.deleteMany({ targetUser: userId }),
    RiskAssessment.deleteMany({ owner: userId }),
    Subscription.deleteMany({ owner: userId }),
    UserDataRequest.deleteMany({ owner: userId })
  ]);

  const transactions = await Transaction.find({ owner: userId });
  await Order.deleteMany({ transaction: { $in: transactions.map((transaction) => transaction.id) } });

  const transactionWithPortfolio = transactions.find((transaction) => Boolean(transaction.portfolio));
  const portfolioId = transactionWithPortfolio.portfolio;
  if (portfolioId) {
    await DailyPortfolioTicker.deleteMany({ portfolio: portfolioId });
  }

  await Transaction.deleteMany({ owner: userId });
}

new CleanUpDeletedUserDataScriptRunner().run();
