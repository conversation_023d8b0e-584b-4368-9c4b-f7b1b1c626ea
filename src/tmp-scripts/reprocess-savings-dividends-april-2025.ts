import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import {
  SavingsDividendTransaction,
  SavingsDividendTransactionDocument,
  SavingsDividendTransactionDTOInterface
} from "../models/Transaction";
import { PortfolioDocument } from "../models/Portfolio";
import { SavingsProductDocument } from "../models/SavingsProduct";
import { captureException } from "@sentry/node";
import Decimal from "decimal.js/decimal";
import { DailyPortfolioSavingsTicker, DailySavingsProductTicker } from "../models/DailyTicker";
import DateUtil from "../utils/dateUtil";
import SavingsProductService from "../services/savingsProductService";
import UserService from "../services/userService";
import PortfolioService from "../services/portfolioService";
import ProviderService from "../services/providerService";

// April 2025 dividend month
const DIVIDEND_MONTH = "2025-04";

class ReprocessSavingsDividendsApril2025ScriptRunner extends ScriptRunner {
  scriptName = "reprocess-savings-dividends-april-2025";
  private options: { dryRun: boolean; bonus: boolean; topup: boolean };

  async processFn(): Promise<void> {
    logger.info("Reprocessing savings dividend transactions for April 2025...", {
      module: `script:${this.scriptName}`
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run"),
      bonus: process.argv.includes("--bonus"),
      topup: process.argv.includes("--topup")
    };

    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will apply!", {
        module: `script:${this.scriptName}`
      });
    }

    // Find all savings dividend transactions for April 2025
    const dividends = await SavingsDividendTransaction.find({
      dividendMonth: DIVIDEND_MONTH
    }).populate("portfolio");

    logger.info(`Found ${dividends.length} savings dividend transactions for April 2025`, {
      module: `script:${this.scriptName}`
    });

    const savingProducts = await SavingsProductService.getSavingsProductsDict("commonId", false);

    // Process each dividend transaction sequeantially to avoid rate-limiting
    for (const dividend of dividends) {
      await this._reprocessDividendTransaction(dividend, savingProducts[dividend.savingsProduct]);
    }

    logger.info("✅ Finished reprocessing savings dividend transactions for April 2025!", {
      module: `script:${this.scriptName}`
    });
  }

  private async _reprocessDividendTransaction(
    existingDividend: SavingsDividendTransactionDocument,
    savingsProduct: SavingsProductDocument
  ): Promise<void> {
    try {
      // Get the portfolio and savings product
      const portfolio = existingDividend.portfolio as PortfolioDocument;
      if (!portfolio) {
        logger.error(`Portfolio not found for dividend ${existingDividend._id}`, {
          module: `script:${this.scriptName}`,
          method: "_reprocessDividendTransaction"
        });
        return;
      }

      const { dividendAmountPostFeesInCents: newDividendAmountPostFeesInCents } =
        await this._applyFeesToSavingsDividend(portfolio, existingDividend.originalDividendAmount, savingsProduct);

      const missingDividendAmount = Decimal.sub(
        newDividendAmountPostFeesInCents,
        existingDividend.consideration.amount
      ).toNumber();

      // If there is no difference between the new dividend amount minus the existing one, skip
      if (missingDividendAmount <= 0) {
        logger.info(`Dividend ${existingDividend._id} is already correct, skipping...`, {
          module: `script:${this.scriptName}`,
          method: "_reprocessDividendTransaction"
        });
        return;
      }

      //if it's a dry run, log what the result would be and return
      if (this.options.dryRun) {
        logger.info("DrynRun: Create new dividend transaction with the missing dividend amount", {
          module: `script:${this.scriptName}`,
          method: "_reprocessDividendTransaction",
          data: {
            dividendId: existingDividend._id,
            missingDividendAmount
          }
        });

        return;
      }

      //Check if we have already created a fixing transaction for this dividend
      const fixingDividendTransaction = await SavingsDividendTransaction.findOne({
        owner: existingDividend.owner,
        createdAt: { $gte: new Date("2025-05-19") }
      });
      if (fixingDividendTransaction) {
        logger.info(`Fixing transaction already exists for dividend ${existingDividend._id}, skipping...`, {
          module: `script:${this.scriptName}`,
          method: "_reprocessDividendTransaction"
        });
        return;
      }

      const user = await UserService.getUser(portfolio.owner.toString(), {
        addresses: false,
        portfolios: false,
        subscription: false
      });

      if (this.options.bonus) {
        // Create a cash bonus with the missing dividend amount for the user
        const bonusPayment = await ProviderService.getBrokerageService(user.companyEntity).createBonus({
          destinationPortfolio: portfolio.providers?.wealthkernel?.id,
          consideration: {
            currency: user.currency,
            amount: Decimal.div(missingDividendAmount, 100).toNumber()
          }
        });

        logger.info("Create new bonus payment with the missing dividend amount", {
          module: `script:${this.scriptName}`,
          method: "_reprocessDividendTransaction",
          data: {
            dividendId: existingDividend._id,
            bonusPayment
          }
        });
      }

      if (this.options.topup) {
        // Create a new dividend transaction with the missing dividend amount
        const newDividendData: SavingsDividendTransactionDTOInterface = {
          fees: {
            fx: {
              amount: 0,
              currency: user.currency
            },
            executionSpread: {
              amount: 0,
              currency: user.currency
            },
            commission: {
              amount: 0,
              currency: user.currency
            }
          },
          consideration: {
            currency: existingDividend.consideration.currency,
            amount: missingDividendAmount
          },
          originalDividendAmount: missingDividendAmount,
          owner: user.id,
          portfolio: portfolio.id,
          savingsProduct: savingsProduct.commonId,
          activeProviders: existingDividend.activeProviders,
          providers: existingDividend.providers,
          status: "Pending",
          createdAt: new Date(Date.now()),
          dividendMonth: DateUtil.getYearAndMonth(DateUtil.getFirstDayOfLastMonth())
        };
        const newDividendTransaction = await new SavingsDividendTransaction(newDividendData).save();

        // Create new topup
        const savingsTopup = await PortfolioService.topupSavings(
          portfolio,
          newDividendTransaction.savingsProduct,
          Decimal.div(newDividendTransaction.consideration.amount, 100).toNumber(),
          { dividend: newDividendTransaction }
        );

        await newDividendTransaction.updateOne({
          status: "PendingReinvestment",
          linkedSavingsTopup: savingsTopup.id
        });

        logger.info("Create new dividend transaction with the missing dividend amount", {
          module: `script:${this.scriptName}`,
          method: "_reprocessDividendTransaction",
          data: {
            dividendId: existingDividend._id,
            missingDividendAmount
          }
        });
      }
    } catch (error) {
      captureException(error);
      logger.error(`Error reprocessing dividend ${existingDividend._id}`, {
        module: `script:${this.scriptName}`,
        method: "_reprocessDividendTransaction",
        data: {
          error,
          dividendId: existingDividend._id
        }
      });
    }
  }

  private async _applyFeesToSavingsDividend(
    portfolio: PortfolioDocument,
    originalDividendAmount: number,
    savingsProduct: SavingsProductDocument
  ): Promise<{
    dividendPayoutInCents: number;
    dividendAmountPostFeesInCents: number;
    dividendFeeInCents: number;
  }> {
    const originaDividendPayoutInCents = Decimal(originalDividendAmount);

    const { start, end } = DateUtil.getStartAndEndOfLastMonth();
    const [portfolioSavingsTickerOfLastMonth, savingsProductTickerOfLastMonth] = await Promise.all([
      DailyPortfolioSavingsTicker.find({
        date: { $gte: start, $lte: end },
        portfolio: portfolio.id
      }),
      DailySavingsProductTicker.find({
        date: { $gte: start, $lte: end },
        savingsProduct: savingsProduct.id
      })
    ]);

    const savingsTickers = portfolioSavingsTickerOfLastMonth.filter((ticker) => ticker && ticker.dailyAccrual > 0);
    // Remove any decimals places, as we are working with cents
    const dailyAccrualsSumInCents = savingsTickers
      .reduce((sum, saving) => sum.plus(saving.dailyAccrual), new Decimal(0))
      .toDecimalPlaces(0);

    const smallestWealthyhoodFeeRate = Decimal.min(
      ...savingsTickers.map(({ planFee }) => new Decimal(planFee))
    ).mul(100);

    const highestOneDayYieldRate = Decimal.max(
      ...savingsProductTickerOfLastMonth.map(({ oneDayYield }) => new Decimal(oneDayYield))
    );

    // Remove any decimals places, as we are working with cents
    // We use floor because this is just the minimum estimate
    const estimatedMinimumWealthyhoodFee = new Decimal(originaDividendPayoutInCents)
      .mul(smallestWealthyhoodFeeRate)
      .div(highestOneDayYieldRate)
      .floor();

    let wealthyhoodFee = new Decimal(originaDividendPayoutInCents).minus(dailyAccrualsSumInCents);
    let dividendAmountPostFeesInCents = dailyAccrualsSumInCents;

    // Edge case: If the fee is equal to the original dividend (should only happen for 1 cent original
    // dividend and 1 cent fee), set the fee to 0 and pay out the original dividend to the user
    if (wealthyhoodFee.equals(originaDividendPayoutInCents)) {
      wealthyhoodFee = Decimal(0);
      dividendAmountPostFeesInCents = originaDividendPayoutInCents;
    }

    logger.info(
      `Completed fee calculations for ${savingsProduct.commonId} savings product of ${portfolio.id} portfolio`,
      {
        module: "TransactionService",
        method: "_calculateSavingsDividendFee",
        data: {
          savingsProductId: savingsProduct.commonId,
          portfolio: portfolio.id,
          dateRange: { start, end },
          originalDividendPayoutInCents: originaDividendPayoutInCents.toNumber(),
          dailyAccrualsSum: dailyAccrualsSumInCents.toNumber(),
          smallestWealthyhoodFeeRate: smallestWealthyhoodFeeRate.toNumber(),
          highestOneDayYieldRate: highestOneDayYieldRate.toNumber(),
          estimatedMinimumWealthyhoodFee: estimatedMinimumWealthyhoodFee.toNumber(),
          wealthyhoodFee: wealthyhoodFee.toNumber(),
          dividendAmountPostFeesInCents: dividendAmountPostFeesInCents.toNumber()
        }
      }
    );

    if (wealthyhoodFee.lt(estimatedMinimumWealthyhoodFee)) {
      logger.warn(
        `Wealthyhood fee is less than the estimated minimum fee for ${savingsProduct.commonId} savings product of ${portfolio.id} portfolio`,
        {
          module: "TransactionService",
          method: "_calculateSavingsDividendFee",
          data: {
            savingsProductId: savingsProduct.commonId,
            portfolio: portfolio.id,
            estimatedMinimumWealthyhoodFee: estimatedMinimumWealthyhoodFee.toNumber(),
            wealthyhoodFee: wealthyhoodFee.toNumber()
          }
        }
      );
      // throw new Error(
      //   `Wealthyood fee is less than the estimated minimum fee for ${savingsProduct.commonId} savings product of ${portfolio.id} portfolio`
      // );
    }

    if (dividendAmountPostFeesInCents.gt(originaDividendPayoutInCents)) {
      logger.error(
        `Savings accruals are greater than original payout for ${savingsProduct.commonId} savings product of ${portfolio.id} portfolio`,
        {
          module: "TransactionService",
          method: "_calculateSavingsDividendFee",
          data: {
            savingsProductId: savingsProduct.commonId,
            portfolio: portfolio.id,
            dailyAccrualsSum: dailyAccrualsSumInCents.toNumber(),
            originalDividendPayoutInCents: originaDividendPayoutInCents.toNumber(),
            dividendAmountPostFeesInCents: dividendAmountPostFeesInCents.toNumber()
          }
        }
      );
      throw new Error(
        `Savings accruals are greater than actual payout for ${savingsProduct.commonId} savings product of ${portfolio.id} portfolio`
      );
    }

    return {
      dividendPayoutInCents: originaDividendPayoutInCents.toNumber(),
      dividendAmountPostFeesInCents: dividendAmountPostFeesInCents.toNumber(),
      dividendFeeInCents: wealthyhoodFee.toNumber()
    };
  }
}

new ReprocessSavingsDividendsApril2025ScriptRunner().run();
