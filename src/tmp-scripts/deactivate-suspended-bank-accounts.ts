import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { BankAccount } from "../models/BankAccount";

class DeactivateSuspendedBankAccountsScriptRunner extends ScriptRunner {
  scriptName = "deactivate-suspended-bank-accounts";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Deactivating wealthkernel suspended bank accounts...", {
      module: `script:${this.scriptName}`
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };

    const documentCount = await BankAccount.find({
      "providers.wealthkernel.status": "Suspended",
      active: true
    });

    logger.info(`Found ${documentCount.length} bank accounts with status "Suspended"`, {
      module: `script:${this.scriptName}`
    });

    if (!this.options.dryRun) {
      await BankAccount.updateMany(
        {
          "providers.wealthkernel.status": "Suspended",
          active: true
        },
        {
          active: false,
          deactivationReason: "Manual deactivation due to Wealthkernel suspension - 2024-10-23"
        }
      );
    }

    logger.info("✅ Finished deactivating wealthkernel suspended bank accounts...", {
      module: `script:${this.scriptName}`
    });
  }
}

new DeactivateSuspendedBankAccountsScriptRunner().run();
