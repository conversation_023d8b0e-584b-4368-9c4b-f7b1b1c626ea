import { DailyPortfolioSavingsTicker, DailyPortfolioSavingsTickerDocument } from "../models/DailyTicker";
import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import DateUtil from "../utils/dateUtil";

// Start from April 4th, 2025 (first Saturday after April 4th)
const START_DATE = new Date("2025-04-04T00:00:00.000Z");
// End date can be adjusted as needed
const END_DATE = new Date("2025-05-20T23:59:59.999Z");

class BackfillWeekendSavingsTickersAfterApril2025ScriptRunner extends ScriptRunner {
  scriptName = "backfill-weekend-savings-tickers-after-april-2025";
  private options: { dryRun: boolean };

  async processFn(): Promise<void> {
    logger.info("Backfilling weekend portfolio savings tickers after April 4th, 2025...", {
      module: `script:${this.scriptName}`,
      method: "processFn"
    });

    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };

    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will apply!", {
        module: `script:${this.scriptName}`,
        method: "processFn"
      });
    }

    // Get all weekends between start and end date
    const weekendDates = this._getWeekendDates(START_DATE, END_DATE);

    for (const weekendDate of weekendDates) {
      logger.info(`Processing weekend date: ${DateUtil.formatDateToDDMONYYYY(weekendDate)}`, {
        module: `script:${this.scriptName}`,
        method: "processFn"
      });

      // Find the previous Friday's tickers to replicate
      const fridayDate = new Date(weekendDate);
      fridayDate.setDate(weekendDate.getDate() - (weekendDate.getDay() === 0 ? 2 : 1)); // Friday before the weekend

      const { start: fridayStart, end: fridayEnd } = DateUtil.getStartAndEndOfDay(fridayDate);

      const fridayTickers = await DailyPortfolioSavingsTicker.find({
        date: {
          $gte: fridayStart,
          $lte: fridayEnd
        }
      });

      logger.info(
        `Found ${fridayTickers.length} Friday tickers to replicate for ${DateUtil.formatDateToDDMONYYYY(weekendDate)}`,
        {
          module: `script:${this.scriptName}`,
          method: "processFn"
        }
      );

      await Promise.all(
        fridayTickers.map((fridayTicker) => {
          return this._replicatePortfolioSavingsTicker(fridayTicker, weekendDate);
        })
      );
    }

    logger.info("✅ Finished backfilling weekend portfolio savings tickers after April 4th, 2025", {
      module: `script:${this.scriptName}`,
      method: "processFn"
    });
  }

  private _getWeekendDates(startDate: Date, endDate: Date): Date[] {
    const weekendDates: Date[] = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      // Check if it's a weekend (0 = Sunday, 6 = Saturday)
      if (currentDate.getDay() === 0 || currentDate.getDay() === 6) {
        weekendDates.push(new Date(currentDate));
      }

      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return weekendDates;
  }

  private async _replicatePortfolioSavingsTicker(
    sourceTicker: DailyPortfolioSavingsTickerDocument,
    targetDate: Date
  ): Promise<void> {
    // Check if ticker already exists for this portfolio and date
    const tickerExists = await DailyPortfolioSavingsTicker.exists({
      portfolio: sourceTicker.portfolio,
      date: targetDate
    });

    if (tickerExists) {
      logger.info(
        `Ticker already exists for portfolio ${sourceTicker.portfolio} on ${DateUtil.formatDateToDDMONYYYY(targetDate)}`,
        {
          module: `script:${this.scriptName}`,
          method: "_replicatePortfolioSavingsTicker"
        }
      );
      return;
    }

    // Create new ticker based on the Friday ticker
    const newTicker = new DailyPortfolioSavingsTicker({
      portfolio: sourceTicker.portfolio,
      planPrice: sourceTicker.planPrice,
      planFee: sourceTicker.planFee,
      holdingAmount: sourceTicker.holdingAmount,
      savingsProduct: sourceTicker.savingsProduct,
      date: DateUtil.getDateAfterNHours(targetDate, 12), // Set to noon of the target date
      dailyAccrual: sourceTicker.dailyAccrual,
      createdAt: DateUtil.getDateAfterNHours(targetDate, 22),
      updatedAt: DateUtil.getDateAfterNHours(targetDate, 22)
    });

    logger.info(
      `Creating new weekend ticker for portfolio ${sourceTicker.portfolio} on ${DateUtil.formatDateToDDMONYYYY(targetDate)}`,
      {
        module: `script:${this.scriptName}`,
        method: "_replicatePortfolioSavingsTicker"
      }
    );

    if (!this.options.dryRun) {
      await newTicker.save();
    }
  }
}

new BackfillWeekendSavingsTickersAfterApril2025ScriptRunner().run();
