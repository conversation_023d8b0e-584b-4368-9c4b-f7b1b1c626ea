import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User } from "../models/User";
import { DateTime } from "luxon";

class MigrateUnder18UsersWithSubmittedInfoScriptRunner extends ScriptRunner {
  scriptName = "migrate-under-18-users-with-submitted-info";

  async processFn(): Promise<void> {
    logger.info("Migrating under 18 users with submitted info...", {
      module: `script:${this.scriptName}`
    });

    // Calculate the cutoff date for being under 18 years old
    const cutoffDate = DateTime.now().minus({ years: 18 }).toJSDate();

    const numberOfDocs = await User.countDocuments({
      dateOfBirth: { $gte: cutoffDate },
      submittedRequiredInfoAt: { $exists: true }
    });

    logger.info(`Going to migrate ${numberOfDocs}...`, {
      module: `script:${this.scriptName}`
    });

    await User.updateMany(
      {
        dateOfBirth: { $gte: cutoffDate },
        submittedRequiredInfoAt: { $exists: true }
      },
      { $unset: { submittedRequiredInfoAt: "" } }
    );

    logger.info("Migrating under 18 users with submitted info!", {
      module: `script:${this.scriptName}`
    });
  }
}

new MigrateUnder18UsersWithSubmittedInfoScriptRunner().run();
