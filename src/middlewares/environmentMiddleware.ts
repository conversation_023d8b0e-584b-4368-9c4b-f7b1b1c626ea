import { NextFunction, Response } from "express";
import { CustomRequest } from "custom";
import { BadRequestError } from "../models/ApiErrors";
import { envIsDev, envIsDemo } from "../utils/environmentUtil";

export default class EnvironmentMiddleware {
  public static envIsDevOrStaging = (req: CustomRequest, res: Response, next: NextFunction): void => {
    if (envIsDev() || envIsDemo()) {
      next();
      return;
    } else {
      throw new BadRequestError("This route exists in DEV mode only.");
    }
  };
}
