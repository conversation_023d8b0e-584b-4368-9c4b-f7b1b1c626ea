import { NextFunction } from "express";
import { CustomRequest, CustomResponse } from "custom";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import RestUtil from "../utils/restUtil";

export default class RewardMiddleware {
  public static loadReward = (): ((
    req: CustomRequest,
    res: CustomResponse,
    next: NextFunction
  ) => Promise<void>) => {
    return async (req: CustomRequest, res: CustomResponse, next: NextFunction) => {
      const rewardId = req.params.id;

      ParamsValidationUtil.isObjectIdParamValid("id", rewardId, {
        responseMessage: "Reward ID is invalid"
      });

      res.locals.reward = await RestUtil.getRewardFromResponse(req, res);

      next();
    };
  };
}
