import { CustomRequest } from "custom";
import { NextFunction, Request, Response } from "express";
import { captureException } from "@sentry/node";
import { BaseError, NotFoundError } from "../models/ApiErrors";
import { ApiErrorResponse } from "../models/ApiErrorResponse";
import logger from "../external-services/loggerService";
import { envIsDev } from "../utils/environmentUtil";

/**
 *
 * This class represents a middleware to be used for global error handling.
 *
 */
export default class ErrorMiddleware {
  /**
   * @description Middleware for global error handling in order to work it must be
   * used by app as last route. It handles errors that are instances of BaseError,
   * setting http status code and response message accordingly to error class.
   * Otherwise it returns https status code 500 with a generic message.
   * @note Do not forget to use 'catchErrors()' wrapper to also delegate async errors
   * to handler.
   *
   */
  public static readonly handleGlobalErrors = (
    err: any,
    req: CustomRequest,
    res: Response,
    next: NextFunction
  ) => {
    logger.error(err?.message || "Error with no message in ErrorMiddleware", {
      module: "ErrorMiddleware",
      method: "handleGlobalErrors",
      userEmail: req?.user?.email,
      data: {
        headers: req?.headers,
        method: req?.method,
        url: req?.url,
        user: req?.user?.id,
        error: err
      }
    });
    captureException(err);
    if (err instanceof BaseError) {
      const { status, message, description } = err;
      return res.status(err.status).json(new ApiErrorResponse({ status, message, description }));
    } else if (err instanceof URIError) {
      return res
        .status(400)
        .json(new ApiErrorResponse({ status: 400, message: "Invalid url", description: "Invalid url" }));
    } else {
      return res.status(500).json(new ApiErrorResponse({ message: envIsDev() ? err : "An error occured" }));
    }
  };

  /**
   *
   * @description Middleware for handling not found urls.
   */
  public static readonly handleNotFound = (req: CustomRequest, res: Response, next: NextFunction) => {
    throw new NotFoundError("Not found");
  };

  /**
   * @description With async/await, you need some way to catch errors
   * Instead of using try{} catch(e) {} in each controller, we wrap the function in catchErrors(), catch and errors
   * they throw, and pass it along to our express middleware with next()
   */
  public static readonly catchAsyncErrors = (
    fn: (req: CustomRequest | Request, res: Response, next: NextFunction) => any
  ) => {
    return (req: CustomRequest, res: Response, next: NextFunction): any => {
      return fn(req, res, next).catch(next);
    };
  };
}
