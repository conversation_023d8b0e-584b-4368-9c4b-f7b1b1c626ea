import { NextFunction } from "express";
import { CustomRequest, CustomResponse } from "custom";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import { ForbiddenError, NotFoundError } from "../models/ApiErrors";
import { TransactionWithOrders } from "../services/transactionService";
import RestUtil from "../utils/restUtil";

export default class OrderMiddleware {
  public static loadOrder = (populate: {
    transaction: boolean;
  }): ((req: CustomRequest, res: CustomResponse, next: NextFunction) => Promise<void>) => {
    return async (req: CustomRequest, res: CustomResponse, next: NextFunction) => {
      const orderId = req.params.id;

      ParamsValidationUtil.isObjectIdParamValid("id", orderId, {
        responseMessage: "Order ID is invalid"
      });

      res.locals.order = await RestUtil.getOrderFromResponse(req, res, populate);

      next();
    };
  };

  public static orderBelongsToUser = async (
    req: CustomRequest,
    res: CustomResponse,
    next: NextFunction
  ): Promise<void> => {
    const user = req.user;
    const orderId = req.params.id;

    ParamsValidationUtil.isObjectIdParamValid("id", orderId, {
      responseMessage: "Order ID is invalid"
    });

    const order = await RestUtil.getOrderFromResponse(req, res, { transaction: true });
    if (!order) throw new NotFoundError("Order does not exist");
    else if ((order.transaction as TransactionWithOrders).owner.toString() !== user.id)
      throw new ForbiddenError("Order does not belong to user");

    next();
  };
}
