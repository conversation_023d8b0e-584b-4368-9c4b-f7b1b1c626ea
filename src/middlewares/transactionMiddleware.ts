import { NextFunction, Response } from "express";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import { BadRequestError, ForbiddenError } from "../models/ApiErrors";
import { Transaction, TransactionDocument } from "../models/Transaction";
import { CustomRequest } from "custom";

export default class TransactionMiddleware {
  public static transactionBelongsToUser = async (
    req: CustomRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const user = req.user;
    const transactionId = req.params.id;

    ParamsValidationUtil.isObjectIdParamValid("id", transactionId, {
      responseMessage: "Transaction does not exist"
    });

    const transaction = await Transaction.findOne({ _id: transactionId, owner: user.id });
    if (!transaction) throw new ForbiddenError("Transaction does not belong to user");

    next();
  };

  public static transactionIsWealthyhoodDividend = async (
    req: CustomRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const transactionId = req.params.id;

    ParamsValidationUtil.isObjectIdParamValid("id", transactionId, {
      responseMessage: "Transaction does not exist"
    });

    const transaction: TransactionDocument = await Transaction.findOne({ _id: transactionId });
    if (transaction.category !== "WealthyhoodDividendTransaction")
      throw new BadRequestError("Transaction is not of type Wealthyhood dividend");

    next();
  };

  public static paymentBelongsToUser = async (
    req: CustomRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const user = req.user;

    const truelayerId = req.query.truelayerId as string;
    if (!truelayerId) {
      throw new BadRequestError("Param 'truelayerId' is required", "Invalid parameter");
    }
    const transaction = await Transaction.findOne({
      "providers.truelayer.id": truelayerId,
      owner: user.id
    });
    if (!transaction) throw new ForbiddenError("Transaction does not belong to user");

    next();
  };
}
