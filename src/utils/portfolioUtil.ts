import { currenciesConfig, investmentsConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { InvestmentProductDocument } from "../models/InvestmentProduct";
import { HoldingsType, InitialHoldingsAllocationType, PortfolioDocument } from "../models/Portfolio";
import Decimal from "decimal.js";
import { PendingOrderType } from "../services/portfolioService";
import { AssetType } from "@wealthyhood/shared-configs/dist/investmentUniverse";
import InvestmentProductService from "../services/investmentProductService";
import { InvestmentProductsDictType } from "investmentProducts";
import { UserDocument } from "../models/User";
import { HoldingsDictType } from "holdings";

const {
  MIN_ALLOWED_ASSET_INVESTMENT,
  MIN_ALLOWED_ASSET_QUANTITY,
  MIN_ALLOWED_SELL_ORDER_AMOUNT,
  MIN_ALLOWED_PORTFOLIO_ORDER_AMOUNT
} = investmentsConfig;
const { ASSET_CONFIG } = investmentUniverseConfig;

type AssetPercentageType = {
  holdingKey: investmentUniverseConfig.AssetType;
  percentage: number;
};

export default class PortfolioUtil {
  /**
   * ===============
   * PUBLIC METHODS
   * ===============
   */
  public static estimateHoldingAmount(
    quantity: number,
    options: {
      userCurrency: currenciesConfig.MainCurrencyType;
      investmentProduct: InvestmentProductDocument;
    }
  ): number {
    const { userCurrency, investmentProduct } = options;

    return Decimal.mul(quantity, investmentProduct.currentTicker.getPrice(userCurrency)).toNumber();
  }

  /**
   * @description Takes a definition of holdings array with quantities and the desirable amount and filters it
   * to allow only holdings with investment amount larger than MIN_ALLOWED_PORTFOLIO_ORDER_AMOUNT and quantity over MIN_ALLOWED_ASSET_QUANTITY units.
   *
   * @param userCurrency
   * @param holdingsArray
   * @param orderAmount
   */
  public static filterSmallQuantityAndAmountHoldings(
    userCurrency: currenciesConfig.MainCurrencyType,
    holdingsArray: HoldingsType[],
    orderAmount: number
  ): HoldingsType[] {
    const holdingsValue = holdingsArray
      .reduce((sum, { quantity, asset }) => {
        return Decimal.mul(quantity, asset.currentTicker.getPrice(userCurrency)).add(sum);
      }, new Decimal(0))
      .toNumber();

    return holdingsArray.filter(({ asset, quantity }) => {
      const productPrice = asset.currentTicker.getPrice(userCurrency);
      const percentage = Decimal.min(1, Decimal.div(orderAmount, holdingsValue));
      const soldQuantity = Decimal.mul(quantity, percentage);
      const investmentPerAsset = Decimal.mul(soldQuantity, productPrice);

      return (
        investmentPerAsset.greaterThanOrEqualTo(MIN_ALLOWED_PORTFOLIO_ORDER_AMOUNT) &&
        soldQuantity.greaterThanOrEqualTo(MIN_ALLOWED_ASSET_QUANTITY)
      );
    });
  }

  /**
   * @description Similar to filterSmallPercentageHoldings, but only filters based on quantity.
   *
   * @param userCurrency
   * @param holdingsArray
   * @param orderAmount
   */
  public static filterSmallQuantityHoldings(
    userCurrency: currenciesConfig.MainCurrencyType,
    holdingsArray: HoldingsType[],
    orderAmount: number
  ): HoldingsType[] {
    const holdingsValue = holdingsArray.reduce(
      (sum, { quantity, asset }) => sum + quantity * asset.currentTicker.getPrice(userCurrency),
      0
    );

    return holdingsArray.filter(({ quantity }) => {
      const percentage = Decimal.min(1, Decimal.div(orderAmount, holdingsValue));
      const soldQuantity = Decimal.mul(quantity, percentage);

      return soldQuantity.greaterThanOrEqualTo(MIN_ALLOWED_ASSET_QUANTITY);
    });
  }

  /**
   * @description Takes holdings and the desirable amount and returns a list of holdings and the respective order
   * amount for them.
   *
   * Filtering works as follows:
   * - We partition the holding percentages into two arrays, those passing criteria and those that don't.
   * - We get the total amount that is not passing the criteria.
   * - To assign that amount to the holdings passing criteria, we increment each holding in the list passing criteria
   * by MIN_ALLOWED_ASSET_QUANTITY units, until the unassigned amount is less than the minimum order amount.
   *
   * @param userCurrency
   * @param holdingsArray
   * @param orderAmount The amount that will be allocated in pounds.
   * @param options
   * @returns a list of holdings and the *quantity* to be sold
   */
  public static filterSmallPercentageHoldingsDistributingAllocationForSelling(
    userCurrency: currenciesConfig.MainCurrencyType,
    holdingsArray: HoldingsType[],
    orderAmount: number,
    options: { minimumOrderAmount: number } = { minimumOrderAmount: MIN_ALLOWED_SELL_ORDER_AMOUNT }
  ): { [key in investmentUniverseConfig.AssetType]?: number } {
    const [ordersMeetingCriteria, ordersNotMeetingCriteria] =
      PortfolioUtil._partitionHoldingsIntoOrdersBasedOnInvestmentCriteria(
        userCurrency,
        holdingsArray,
        orderAmount,
        options
      );

    const holdingsDict = Object.fromEntries(holdingsArray.map((holding) => [holding.assetCommonId, holding]));

    if (ordersMeetingCriteria.length === 0) {
      // If there are no orders meeting criteria (which could happen in very low order amounts), we just charge the
      // whole amount to the user's biggest holding
      const portfolioBiggestHolding = holdingsDict[ordersNotMeetingCriteria[0].assetCommonId];
      const quantityToSell = Decimal.div(
        orderAmount,
        portfolioBiggestHolding.asset.currentTicker.getPrice(userCurrency)
      )
        .toDecimalPlaces(4)
        .toNumber();
      const finalQuantity = Decimal.min(
        portfolioBiggestHolding.quantity,
        Decimal.max(quantityToSell, MIN_ALLOWED_ASSET_QUANTITY)
      ).toNumber();

      return {
        [ordersNotMeetingCriteria[0].assetCommonId]: finalQuantity
      };
    }

    let unassignedAmount = ordersNotMeetingCriteria
      .map(({ estimatedAmount }) => estimatedAmount)
      .reduce((sum, estimatedAmount) => Decimal.add(sum, estimatedAmount), new Decimal(0));
    let largestUnassignedHoldingIndex = 0;

    while (
      unassignedAmount.greaterThanOrEqualTo(options.minimumOrderAmount) &&
      !PortfolioUtil._allQuantitiesAreBeingSold(holdingsDict, ordersMeetingCriteria)
    ) {
      const currentAssetCommonId = ordersMeetingCriteria[largestUnassignedHoldingIndex].assetCommonId;
      const productPrice = holdingsDict[currentAssetCommonId].asset.currentTicker.getPrice(userCurrency);
      const updatedQuantity =
        ordersMeetingCriteria[largestUnassignedHoldingIndex].quantityToSell.add(MIN_ALLOWED_ASSET_QUANTITY);
      const oldEstimatedAmount = ordersMeetingCriteria[largestUnassignedHoldingIndex].estimatedAmount;
      const updatedEstimatedAmount = Decimal.mul(updatedQuantity, productPrice);

      ordersMeetingCriteria[largestUnassignedHoldingIndex] = {
        assetCommonId: currentAssetCommonId,
        quantityToSell: Decimal.min(updatedQuantity, holdingsDict[currentAssetCommonId].quantity),
        estimatedAmount: updatedEstimatedAmount
      };

      unassignedAmount = unassignedAmount.sub(updatedEstimatedAmount.sub(oldEstimatedAmount));

      if (largestUnassignedHoldingIndex === ordersMeetingCriteria.length - 1) {
        largestUnassignedHoldingIndex = 0;
      } else largestUnassignedHoldingIndex++;
    }

    return Object.fromEntries(
      ordersMeetingCriteria.map(({ assetCommonId, quantityToSell }) => {
        return [assetCommonId as investmentUniverseConfig.AssetType, quantityToSell.toDecimalPlaces(4).toNumber()];
      })
    );
  }

  /**
   * @description Takes holdings and the desirable amount and filters it to allow only holdings with investment amount
   * larger than the minimum order amount and quantity over MIN_ALLOWED_ASSET_QUANTITY units.
   *
   * Filtering works as follows:
   * - We partition the holding percentages into two arrays, those passing criteria and those that don't. Those arrays
   * are sorted in descending order.
   * - We call the sum of percentages of the first array the **assigned** percentage and that of the second array
   * the **unassigned** percentage. For example at this point we could have 88% assigned and 12% unassigned.
   * - While the unassigned percentage (that sum of the percentages in the filtered array) >= minimum allowed percentage
   * (the equivalent of minimum order amount represented by a percentage), we allocate that minimum allowed percentage to
   * the largest unassigned holding.
   * - If at the end of this process we end up with an unassigned percentage (e.g 0.4% still unassigned), we distribute
   * it to the holdings that were in the successful criteria partition in the first step.
   *
   * @param userCurrency
   * @param holdingsPercentage
   * @param investmentAmount The amount that will be allocated in pounds.
   * @param options
   */
  public static async filterSmallPercentageHoldingsDistributingAllocationForBuying(
    userCurrency: currenciesConfig.MainCurrencyType,
    holdingsPercentage: { [key in investmentUniverseConfig.AssetType]?: number },
    investmentAmount: number,
    options: { minimumOrderAmount: number } = { minimumOrderAmount: MIN_ALLOWED_PORTFOLIO_ORDER_AMOUNT }
  ): Promise<{ [key in investmentUniverseConfig.AssetType]?: number }> {
    const [holdingsInitiallyMeetingCriteria, holdingsInitiallyNotMeetingCriteria] =
      await PortfolioUtil._partitionHoldingsPercentagesBasedOnInvestmentCriteria(
        userCurrency,
        holdingsPercentage,
        investmentAmount,
        options
      );

    // We initialize the array of holdings that will be returned to the user of this method. This array initially
    // has a value same as the holdings initially meeting criteria, but will be updated during this method.
    let finalHoldingsMeetingCriteria: AssetPercentageType[] = holdingsInitiallyMeetingCriteria;

    // We find the allocation that represents MIN_ALLOWED_PORTFOLIO_ORDER_AMOUNT in %.
    const minimumAllowedPercentage = Decimal.div(options.minimumOrderAmount, investmentAmount)
      .mul(100)
      .toDecimalPlaces(1, Decimal.ROUND_UP);

    let unassignedInvestmentPercentage = holdingsInitiallyNotMeetingCriteria
      .map(({ percentage }) => percentage)
      .reduce((sum, percentage) => Decimal.add(sum, percentage), new Decimal(0));
    let largestUnassignedHoldingIndex = 0;

    while (unassignedInvestmentPercentage.greaterThanOrEqualTo(minimumAllowedPercentage)) {
      const largestUnassignedHolding = holdingsInitiallyNotMeetingCriteria[largestUnassignedHoldingIndex]; // As the array is descending

      // The element is pushed at the end of the array, which means the array is still sorted in descending order
      finalHoldingsMeetingCriteria.push({
        holdingKey: largestUnassignedHolding.holdingKey as investmentUniverseConfig.AssetType,
        percentage: minimumAllowedPercentage.toNumber()
      });

      largestUnassignedHoldingIndex++;
      unassignedInvestmentPercentage = unassignedInvestmentPercentage.sub(minimumAllowedPercentage);
    }

    if (unassignedInvestmentPercentage.lessThan(minimumAllowedPercentage)) {
      let assignedInvestmentPercentage = Decimal.sub(100, unassignedInvestmentPercentage);

      // We reverse the array which until this point was in descending order. This means that now, the elements that
      // were added to the array because we increased their allocation are in the beginning of the list.
      finalHoldingsMeetingCriteria.reverse();

      // We still have an unassigned investment percentage that is less than the minimum. We want to distribute that
      // to the assets currently passing criteria.
      finalHoldingsMeetingCriteria = finalHoldingsMeetingCriteria.map(({ holdingKey, percentage }) => {
        if (
          holdingsInitiallyNotMeetingCriteria.find(
            (holdingMeetingCriteria) => holdingMeetingCriteria.holdingKey === holdingKey
          )
        ) {
          assignedInvestmentPercentage = assignedInvestmentPercentage.sub(percentage);
          return { holdingKey, percentage };
        }

        const percentageDiff = Decimal.mul(
          unassignedInvestmentPercentage,
          Decimal.div(percentage, assignedInvestmentPercentage)
        );

        const updatedPercentage = Decimal.add(percentage, percentageDiff).toDecimalPlaces(2).toNumber();

        assignedInvestmentPercentage = assignedInvestmentPercentage.sub(percentage);
        unassignedInvestmentPercentage = Decimal.sub(unassignedInvestmentPercentage, percentageDiff);

        return { holdingKey, percentage: updatedPercentage };
      });
    }

    return Object.fromEntries(
      finalHoldingsMeetingCriteria.map(({ holdingKey, percentage }) => {
        return [holdingKey as investmentUniverseConfig.AssetType, percentage];
      })
    );
  }

  /**
   * Verifies submitted order passes our criteria:
   * 1. Quantity > MIN_ALLOWED_ASSET_QUANTITY
   * 2. Amount > MIN_ALLOWED_ASSET_INVESTMENT
   * 3. If we detect this is a 'sell all' operation, we allow it and skip (1) and (2) UNLESS it's an ETF and we
   * have enabled realtime ETF execution for the user.
   *
   * If the order does not pass our criteria, then null is returned
   */
  public static checkSubmittedOrder(
    user: UserDocument,
    commonId: AssetType,
    submittedOrder: PendingOrderType,
    holdingsDict: HoldingsDictType,
    investmentProduct: InvestmentProductDocument
  ): PendingOrderType {
    if (
      (!user.isRealtimeETFExecutionEnabled || ASSET_CONFIG[commonId].category === "stock") &&
      submittedOrder.side === "sell" &&
      Decimal.mul(submittedOrder.quantity, investmentProduct.currentTicker.getPrice(user.currency)).lessThan(
        MIN_ALLOWED_ASSET_INVESTMENT
      ) &&
      Decimal.sub(submittedOrder.quantity, holdingsDict[commonId]?.quantity ?? 0)
        .abs()
        .lessThan(MIN_ALLOWED_ASSET_QUANTITY)
    ) {
      // User has attempted to sell all of their asset which is less than MIN_ALLOWED_ASSET_INVESTMENT
      // We want to allow that.
      return {
        side: submittedOrder.side,
        quantity: holdingsDict[commonId].quantity
      };
    }

    let money: number;
    let quantity: number;
    if (submittedOrder.side === "buy") {
      money = submittedOrder.money;
      quantity = Decimal.div(money, investmentProduct.currentTicker.getPrice(user.currency)).toNumber();
    } else if (submittedOrder.side === "sell") {
      quantity = submittedOrder.quantity;
      money = Decimal.mul(quantity, investmentProduct.currentTicker.getPrice(user.currency)).toNumber();
    }

    if (money >= MIN_ALLOWED_ASSET_INVESTMENT && quantity >= MIN_ALLOWED_ASSET_QUANTITY) {
      return submittedOrder;
    } else return null;
  }

  /**
   * @param userCurrency
   * @param holdings
   */
  public static mapHoldingsToAllocationFormat(
    userCurrency: currenciesConfig.MainCurrencyType,
    holdings: HoldingsType[]
  ): investmentUniverseConfig.AllocationType {
    const assetAllocation: { [key in investmentUniverseConfig.AssetType]?: number } = {};
    const investedAmount = holdings
      .reduce(
        (sum, { quantity, asset }) => Decimal.mul(quantity, asset.currentTicker.getPrice(userCurrency)).add(sum),
        new Decimal(0)
      )
      .toNumber();

    // 1. Calculate asset allocation
    holdings.forEach(({ quantity, asset, assetCommonId }) => {
      const assetValue = Decimal.mul(quantity, asset.currentTicker.getPrice(userCurrency));
      assetAllocation[assetCommonId] = Decimal.mul(100, assetValue).div(investedAmount).toNumber();
    });

    // 2. Use asset allocation to calculate asset class allocation
    const assetClassAllocation: { [key in investmentUniverseConfig.AssetClassType]?: number } = {};
    Object.keys(assetAllocation).forEach((assetKey: investmentUniverseConfig.AssetType) => {
      const assetClassKey = ASSET_CONFIG[assetKey].assetClass;
      if (assetClassAllocation[assetClassKey] > 0) {
        assetClassAllocation[assetClassKey] += assetAllocation[assetKey];
      } else {
        assetClassAllocation[assetClassKey] = assetAllocation[assetKey];
      }
    });

    return { assetClasses: assetClassAllocation, assets: assetAllocation };
  }

  public static allocationToHoldingsPercentArray(allocation: {
    [key in investmentUniverseConfig.AssetType]?: number;
  }): InitialHoldingsAllocationType[] {
    return Object.entries(allocation).map(
      ([assetCommonId, percentage]: [investmentUniverseConfig.AssetType, number]) => {
        return { assetCommonId, percentage };
      }
    );
  }

  /**
   * Returns current holdings with a total amount in user currency for each asset held (based on the latest ticker). For example:
   * [
   *   {
   *     assetCommonId: 'equities_china', amount: 40, asset: {...}
   *   },
   *   {
   *     assetCommonId: 'equities_eu', amount: 60, asset: {...}
   *   }
   * ]
   */
  public static getTotalAmountPerHolding(
    userCurrency: currenciesConfig.MainCurrencyType,
    portfolio: PortfolioDocument
  ): { amount: number; assetCommonId: investmentUniverseConfig.AssetType; asset: InvestmentProductDocument }[] {
    return portfolio.holdings.map((holding) => {
      return {
        assetCommonId: holding.assetCommonId,
        amount: Decimal.mul(holding.asset.currentTicker.getPrice(userCurrency), holding.quantity).toNumber(),
        asset: holding.asset
      };
    });
  }

  /**
   * Returns target holdings with a total target amount in currency for each asset. For example:
   * { equities_china: 50, equities_eu: 50 }
   */
  public static getTargetAllocationInCurrency(
    userCurrency: currenciesConfig.MainCurrencyType,
    portfolio: PortfolioDocument,
    targetAllocation: InitialHoldingsAllocationType[]
  ): { [key: string]: number } {
    return Object.fromEntries(
      targetAllocation.map((targetHolding) => {
        return [
          targetHolding.assetCommonId,
          Decimal.mul(portfolio.currentTicker.getPrice(userCurrency), targetHolding.percentage).div(100).toNumber()
        ];
      })
    );
  }

  public static getReturns({ startValue, endValue }: { startValue: Decimal; endValue: Decimal }): Decimal {
    return startValue.gt(0) ? Decimal.sub(endValue, startValue).div(startValue) : new Decimal(0);
  }

  /**
   * @description Given an array of values calculates the returns when comparing the values of the first
   * and last element of the array. The result is x100.
   *
   * Example
   * input: [10, 15]
   * output: 50.00
   *
   * @param data
   */
  public static getReturnsOnArray(data: number[]): number {
    return PortfolioUtil.getReturns({
      startValue: new Decimal(data[0]),
      endValue: new Decimal(data[data.length - 1])
    })
      .mul(100)
      .toDecimalPlaces(2)
      .toNumber();
  }

  /**
   * @description Returns true if portfolio has cash in any currency.
   *
   * @param portfolio
   */
  public static hasCash(portfolio: PortfolioDocument): boolean {
    return (
      portfolio?.cash?.GBP?.available > 0 ||
      portfolio?.cash?.EUR?.available > 0 ||
      portfolio?.cash?.USD?.available > 0
    );
  }

  /**
   * Method to manually populate the portfolio with pre-fetched investment products.
   * @param portfolio
   * @param investmentProductsDict
   */
  public static populatePortfolioWithInvestmentProducts(
    portfolio: PortfolioDocument,
    investmentProductsDict: InvestmentProductsDictType
  ): PortfolioDocument {
    portfolio.holdings = portfolio.holdings.map((holding) => ({
      ...holding,
      asset: investmentProductsDict[holding.assetCommonId]
    }));

    return portfolio;
  }

  public static async populatePortfolioAssetsIfNotAlreadyPopulated(portfolio: PortfolioDocument): Promise<void> {
    if (portfolio?.holdings.length > 0 && !portfolio?.holdings?.[0]?.asset?.currentTicker?.pricePerCurrency) {
      await portfolio.populate({
        path: "holdings.asset",
        populate: {
          path: "currentTicker"
        }
      });
    }
  }

  /**
   * ===============
   * PRIVATE METHODS
   * ===============
   */

  /**
   * Partitions the holding percentages passed into two **sorted** arrays, one for holdings that pass our investment
   * criteria and one for holdings that do not.
   * @param userCurrency
   * @param holdingsPercentage
   * @param investmentAmount
   * @param options
   * @private
   */
  private static async _partitionHoldingsPercentagesBasedOnInvestmentCriteria(
    userCurrency: currenciesConfig.MainCurrencyType,
    holdingsPercentage: {
      [key in investmentUniverseConfig.AssetType]?: number;
    },
    investmentAmount: number,
    options: { minimumOrderAmount: number } = { minimumOrderAmount: MIN_ALLOWED_PORTFOLIO_ORDER_AMOUNT }
  ) {
    const investmentProductsDict = await InvestmentProductService.getInvestmentProductsDict("commonId", true);
    const holdingsInitiallyMeetingCriteria: AssetPercentageType[] = [];
    const holdingsInitiallyNotMeetingCriteria: AssetPercentageType[] = [];

    Object.entries(holdingsPercentage).forEach(([holdingKey, percentage]) => {
      const investmentForAsset = Decimal.mul(percentage, investmentAmount).div(100);
      const currentTicker = investmentProductsDict[holdingKey].currentTicker.getPrice(userCurrency);
      const quantity = investmentForAsset.div(currentTicker);

      if (
        investmentForAsset.lessThan(options.minimumOrderAmount) ||
        quantity.lessThan(MIN_ALLOWED_ASSET_QUANTITY)
      ) {
        holdingsInitiallyNotMeetingCriteria.push({
          holdingKey: holdingKey as investmentUniverseConfig.AssetType,
          percentage
        });
      } else
        holdingsInitiallyMeetingCriteria.push({
          holdingKey: holdingKey as investmentUniverseConfig.AssetType,
          percentage
        });
    });

    // We sort both arrays in descending order
    holdingsInitiallyNotMeetingCriteria.sort((a, b) => b.percentage - a.percentage);
    holdingsInitiallyMeetingCriteria.sort((a, b) => b.percentage - a.percentage);

    return [holdingsInitiallyMeetingCriteria, holdingsInitiallyNotMeetingCriteria];
  }

  /**
   * Partitions the holding passed into two **sorted** arrays, one for holdings that pass our investment
   * criteria and one for holdings that do not.
   * @param userCurrency
   * @param holdingsArray
   * @param orderAmount
   * @param options
   * @private
   */
  private static _partitionHoldingsIntoOrdersBasedOnInvestmentCriteria(
    userCurrency: currenciesConfig.MainCurrencyType,
    holdingsArray: HoldingsType[],
    orderAmount: number,
    options: { minimumOrderAmount: number } = { minimumOrderAmount: MIN_ALLOWED_SELL_ORDER_AMOUNT }
  ): [
    {
      assetCommonId: AssetType;
      quantityToSell: Decimal;
      estimatedAmount: Decimal;
    }[],
    {
      assetCommonId: AssetType;
      quantityToSell: Decimal;
      estimatedAmount: Decimal;
    }[]
  ] {
    const holdingsValue = holdingsArray.reduce(
      (sum, { quantity, asset }) => Decimal.mul(quantity, asset.currentTicker.getPrice(userCurrency)).add(sum),
      new Decimal(0)
    );

    const ordersMeetingCriteria: {
      assetCommonId: AssetType;
      quantityToSell: Decimal;
      estimatedAmount: Decimal;
    }[] = [];
    const ordersNotMeetingCriteria: {
      assetCommonId: AssetType;
      quantityToSell: Decimal;
      estimatedAmount: Decimal;
    }[] = [];

    holdingsArray
      .map(({ asset, assetCommonId, quantity }) => {
        const productPrice = asset.currentTicker.getPrice(userCurrency);
        const percentage = Decimal.min(1, Decimal.div(orderAmount, holdingsValue));
        const quantityToSell = Decimal.mul(quantity, percentage);
        const estimatedAmount = Decimal.mul(quantityToSell, productPrice);

        return { assetCommonId, quantityToSell, estimatedAmount };
      })
      .forEach((holding) => {
        if (
          holding.estimatedAmount.greaterThanOrEqualTo(options.minimumOrderAmount) &&
          holding.quantityToSell.greaterThanOrEqualTo(MIN_ALLOWED_ASSET_QUANTITY)
        ) {
          ordersMeetingCriteria.push(holding);
        } else {
          ordersNotMeetingCriteria.push(holding);
        }
      });

    ordersMeetingCriteria.sort((a, b) => b.estimatedAmount.sub(a.estimatedAmount).toNumber());
    ordersNotMeetingCriteria.sort((a, b) => b.estimatedAmount.sub(a.estimatedAmount).toNumber());

    return [ordersMeetingCriteria, ordersNotMeetingCriteria];
  }

  /**
   * Returns true if passed orders have quantities equal to the quantities held be the user.
   * @param holdingsDict
   * @param orders
   * @private
   */
  private static _allQuantitiesAreBeingSold(
    holdingsDict: HoldingsDictType,
    orders: {
      assetCommonId: AssetType;
      quantityToSell: Decimal;
      estimatedAmount: Decimal;
    }[]
  ): boolean {
    orders.forEach((order) => {
      if (order.quantityToSell.lessThan(holdingsDict[order.assetCommonId].quantity)) {
        return false;
      }
    });

    return true;
  }
}
