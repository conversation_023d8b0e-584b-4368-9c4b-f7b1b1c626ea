import Decimal from "decimal.js";

export default class ObjectUtil {
  public static calculateObjectSize(data: any): number {
    // Calculate the size of the JSON string in bytes
    const byteSize = Buffer.byteLength(typeof data === "string" ? data : JSON.stringify(data), "utf8");

    // Convert bytes to megabytes
    const mbSize = new Decimal(byteSize).div(1024).div(1024).toNumber();

    return mbSize;
  }

  /**
   * Converts a Map to a plain object.
   * @param map Map to convert
   * @returns Plain object with the same key-value pairs as the map, or undefined if map is undefined/null
   */
  public static mapToObject<K extends string | number | symbol, V>(
    map: Map<K, V> | undefined | null
  ): Record<K, V> | undefined {
    if (!map) return undefined;
    return Object.fromEntries(map.entries()) as Record<K, V>;
  }

  /**
   * @description Compares two objects for equality. Objects must have the same properties with matching values.
   * If either object is undefined/null, they are considered equal only if both are undefined/null.
   *
   * Note: does not check for nested objects or arrays.
   *
   * @param obj1 First object to compare
   * @param obj2 Second object to compare
   * @returns boolean indicating if the objects are equal
   */
  public static areObjectsEqual<T extends Record<string, any>>(
    obj1: T | undefined | null,
    obj2: T | undefined | null
  ): boolean {
    if (obj1 && !obj2) return false;
    if (!obj1 && obj2) return false;
    if (!obj1 && !obj2) return true;

    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    if (keys1.length !== keys2.length) return false;

    return keys1.every((key) => obj1[key] === obj2[key]);
  }
}
