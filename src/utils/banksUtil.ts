import { banksConfig, countriesConfig } from "@wealthyhood/shared-configs";
import ConfigUtil from "./configUtil";
import { envIsDemo } from "./environmentUtil";
import { ThirdPartyType } from "../external-services/devengoService";

export type BankProvidersType = {
  id: string;
  name: string;
  logo: string;
};

export const UNKNOWN_LOGO_URL = "https://img.wealthyhood.dev/bank-icons/Unknown.png";

export default class BanksUtil {
  public static getEuropeanBankFullAddress(): string {
    return `${banksConfig.EU_BANK_DETAILS.bank.address.number} ${banksConfig.EU_BANK_DETAILS.bank.address.street}, ${banksConfig.EU_BANK_DETAILS.bank.address.city}, ${banksConfig.EU_BANK_DETAILS.bank.address.postCode}, ${banksConfig.EU_BANK_DETAILS.bank.address.country}`;
  }

  public static getBankFromGoCardlessInstitutionId(institutionId: string): banksConfig.BankType {
    return Object.entries(banksConfig.BANKS_CONFIG).find(
      ([, config]) => config.goCardlessDataInstitutionId === institutionId
    )?.[0] as banksConfig.BankType;
  }

  public static getBankFromSaltedgeInstitutionId(institutionId: string): banksConfig.BankType {
    return Object.entries(banksConfig.BANKS_CONFIG).find(
      ([, config]) => config.saltedgeInstitutionId === institutionId
    )?.[0] as banksConfig.BankType;
  }

  public static getBankFromBIC(bic: string): banksConfig.BankType {
    return Object.entries(banksConfig.BANKS_CONFIG).find(([, { bicPrefixes }]) =>
      bicPrefixes?.some((bicPrefix) => bic?.startsWith(bicPrefix))
    )?.[0] as banksConfig.BankType;
  }

  public static getBankFromTruelayerProviderId(institutionId: string): banksConfig.BankType {
    return Object.entries(banksConfig.BANKS_CONFIG).find(
      ([, config]) => config.truelayerInstitutionId === institutionId
    )?.[0] as banksConfig.BankType;
  }

  public static getTruelayerAvailableProviders(
    residencyCountry: countriesConfig.CountryCodesType
  ): banksConfig.BankType[] {
    if (envIsDemo()) {
      // on bank account linking, Truelayer will give provider id "mock"
      return ["mock-payments-gb-redirect", "mock"];
    }

    return banksConfig.AVAILABLE_PROVIDERS[ConfigUtil.getRegion(residencyCountry)][
      banksConfig.BankProviderScopeEnum.DATA
    ];
  }

  public static getLogoURLFromBIC(bic: string): string {
    return Object.entries(banksConfig.BANKS_CONFIG).find(([, { bicPrefixes }]) =>
      bicPrefixes?.some((bicPrefix) => bic?.startsWith(bicPrefix))
    )?.[1].logo;
  }

  /**
   * A very naive validation function to check a string is an IBAN. Checks that:
   * 1) It conforms to IBAN lengths as defined by ISO 13616-1:2007.
   * 2) It only contains letters and digits.
   * 3) It starts with a two-letter code.
   *
   * @param ibanCandidate
   */
  public static isIBAN(ibanCandidate: string) {
    // Remove spaces and convert to uppercase
    const trimmedIBAN = ibanCandidate.replace(/ /g, "").toUpperCase();

    // Check if IBAN contains only letters and numbers
    const ibanRegex = /^[A-Z0-9]+$/;
    if (!ibanRegex.test(trimmedIBAN)) {
      return false;
    }

    // According to the international standard ISO 13616-1:2007, IBANs have between 15 and 34 characters.
    if (trimmedIBAN.length > 34 || trimmedIBAN.length < 15) {
      return false;
    }

    // Check that the first two characters are letters
    const countryCode = trimmedIBAN.slice(0, 2);
    const lettersRegex = /^[A-Z]{2}$/;

    return lettersRegex.test(countryCode);
  }

  public static getIBANFromDevengoThirdParty(thirdParty: ThirdPartyType): string {
    return thirdParty.account.identifiers.find((identifier) => identifier.type === "iban").iban;
  }
}
