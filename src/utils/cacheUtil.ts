import { RedisClientService } from "../loaders/redis";

const CACHE_REQUEST_BATCH_SIZE = 4;

export const fetchAndCacheDataMulti = async <E>(
  elements: E[],
  fetchFn: (element: E) => Promise<Record<string, any>>,
  onError: (err: any) => void
): Promise<void> => {
  for (let i = 0; i < elements.length; i += CACHE_REQUEST_BATCH_SIZE) {
    const batch = elements.slice(i, i + CACHE_REQUEST_BATCH_SIZE);

    try {
      // 1. Fetch data
      const dataArray = await Promise.all(batch.map(fetchFn));

      // 2. Pipeline caching
      const p = RedisClientService.Instance.pipeline();
      dataArray.forEach((data) => {
        if (data) {
          p.mset(data);
        }
      });
      if (p.length()) {
        // a pipeline can be empty if all data to be cached is over the allowed request size
        await p.exec();
      }
    } catch (err) {
      onError(err);
    }
  }
};

export const getCachedDataWithFallback = async <T>(
  cacheKey: string,
  fallbackFn: () => Promise<T>,
  expirationFn: (response: T) => number = () => undefined
): Promise<T> => {
  const cachedData = await RedisClientService.Instance.get<T>(cacheKey);
  if (!cachedData) {
    const fetchedData = await fallbackFn();
    if (fetchedData) {
      await RedisClientService.Instance.set(cacheKey, fetchedData, {
        ex: expirationFn(fetchedData)
      });
    }

    return fetchedData;
  } else {
    return cachedData;
  }
};
