import { CreateUserData, UserData } from "requestBody";
import { EmploymentInfoWithIncomeRangeIdType, UserDocument, UserTypeEnum } from "../models/User";
import ParamsValidationUtil from "./paramsValidationUtil";
import { countriesConfig, taxResidencyConfig } from "@wealthyhood/shared-configs";
import { BadRequestError } from "../models/ApiErrors";
import {
  EMPLOYMENT_STATUSES_REQUIRE_INDUSTRY,
  EmploymentStatusArray,
  IndustryArray,
  SourceOfWealthArray
} from "../external-services/wealthkernelService";
import { AmlScreeningResultEnum } from "../configs/riskAssessmentConfig";

export default class UserValidationUtil {
  public static validateUserData(body: UserData | CreateUserData): Partial<UserDocument> {
    const {
      // UserData
      firstName,
      lastName,
      dateOfBirth,
      nationalities,
      taxResidency,
      isUKTaxResident,
      viewedWelcomePage,
      viewedKYCSuccessPage,
      hasAcceptedTerms,
      hasSeenBilling,
      referredByEmail,
      // CreateUserData
      email,
      emailVerified,
      auth0,
      role,
      lastLogin,
      amlScreening,
      skippedPortfolioCreation
    } = body as UserData & CreateUserData;

    if (
      ParamsValidationUtil.isObjectParamValid("taxResidency", taxResidency, { isRequired: false }) &&
      !(taxResidency?.countryCode && taxResidency?.proofType && taxResidency?.value)
    ) {
      throw new BadRequestError(
        "Invalid parameter 'taxResidency' properties 'countryCode', 'proofType', 'value' must coexist",
        "Invalid parameter"
      );
    }

    const userData: Partial<UserDocument> = {
      firstName,
      lastName,
      dateOfBirth: ParamsValidationUtil.isDateParamValid("dateOfBirth", dateOfBirth, {
        isRequired: false
      }),
      isUKTaxResident: ParamsValidationUtil.isBooleanParamValid("isUKTaxResident", isUKTaxResident, {
        isRequired: false
      }),
      nationalities: (
        ParamsValidationUtil.isArrayParamValid("nationalities", nationalities, {
          isRequired: false
        }) as string[]
      )?.map(
        (nationality) =>
          ParamsValidationUtil.isStringParamFromAllowedValuesValid(
            "nationalities",
            nationality,
            countriesConfig.countryCodesArray,
            {
              isRequired: false
            }
          ) as countriesConfig.CountryCodesType
      ),
      taxResidency: taxResidency
        ? {
            countryCode: ParamsValidationUtil.isStringParamFromAllowedValuesValid(
              "taxResidencyCountryCode",
              taxResidency.countryCode,
              countriesConfig.countryCodesArray,
              {
                isRequired: false
              }
            ) as countriesConfig.CountryCodesType,
            proofType: ParamsValidationUtil.isStringParamFromAllowedValuesValid(
              "taxResidencyProofType",
              taxResidency.proofType,
              taxResidencyConfig.IdentifierArray,
              {
                isRequired: false
              }
            ) as taxResidencyConfig.IdentifierType,
            value: taxResidency.value
          }
        : null,
      viewedWelcomePage: ParamsValidationUtil.isBooleanParamValid("viewedWelcomePage", viewedWelcomePage, {
        isRequired: false
      }),
      viewedKYCSuccessPage: ParamsValidationUtil.isBooleanParamValid(
        "viewedKYCSuccessPage",
        viewedKYCSuccessPage,
        {
          isRequired: false
        }
      ),
      hasAcceptedTerms: ParamsValidationUtil.isBooleanParamValid("hasAcceptedTerms", hasAcceptedTerms, {
        isRequired: false
      }),
      hasSeenBilling: ParamsValidationUtil.isBooleanParamValid("hasSeenBilling", hasSeenBilling, {
        isRequired: false
      }),
      referredByEmail: referredByEmail !== undefined ? referredByEmail : null,
      // new
      email: ParamsValidationUtil.isEmailParamValid("email", email, { isRequired: false }),
      emailVerified: ParamsValidationUtil.isBooleanParamValid("emailVerified", emailVerified, {
        isRequired: false
      }),
      auth0: ParamsValidationUtil.isObjectParamValid("auth0", auth0, { isRequired: false }),
      lastLogin: ParamsValidationUtil.isDateParamValid("lastLogin", lastLogin, { isRequired: false }),
      role: (
        ParamsValidationUtil.isArrayParamValid("role", role, {
          isRequired: false
        }) as string[]
      )?.map(
        (r) => ParamsValidationUtil.isStringParamFromAllowedValuesValid("role", r, UserTypeEnum) as UserTypeEnum
      ),
      amlScreening: ParamsValidationUtil.isStringParamFromAllowedValuesValid(
        "amlScreening",
        amlScreening,
        AmlScreeningResultEnum,
        { isRequired: false }
      ) as AmlScreeningResultEnum,
      skippedPortfolioCreation: ParamsValidationUtil.isBooleanParamValid(
        "skippedPortfolioCreation",
        skippedPortfolioCreation,
        {
          isRequired: false
        }
      )
    };

    // remove null properties
    return Object.fromEntries(Object.entries(userData).filter(([, value]) => value != null));
  }

  /**
   * @description
   * Validates that employment info object meets all requirements from Wealthkernel.
   * It does **not** throw an error because this is used from a User virtual as well.
   * @returns true/false
   */
  public static validateEmploymentInfo(employmentInfo: EmploymentInfoWithIncomeRangeIdType): boolean {
    const isAnnualIncomeValid = Boolean(
      employmentInfo.annualIncome?.amount && employmentInfo.annualIncome?.currency
    );
    const isSourcesOfWealthValid =
      Array.isArray(employmentInfo.sourcesOfWealth) &&
      employmentInfo.sourcesOfWealth?.length > 0 &&
      employmentInfo.sourcesOfWealth.every((source) => SourceOfWealthArray.includes(source));
    const isEmploymentStatusValid = EmploymentStatusArray.includes(employmentInfo.employmentStatus);
    /**
     * Industry is required for specific employment statuses (eg. "FullTime")
     */
    const isIndustryValid = EMPLOYMENT_STATUSES_REQUIRE_INDUSTRY.includes(employmentInfo.employmentStatus)
      ? IndustryArray.includes(employmentInfo.industry)
      : true;

    return Boolean(isAnnualIncomeValid && isSourcesOfWealthValid && isEmploymentStatusValid && isIndustryValid);
  }
}
