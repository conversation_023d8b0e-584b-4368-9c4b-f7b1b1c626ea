import { BadRequestError } from "../models/ApiErrors";
import { BankAccount, BankAccountDTOInterface } from "../models/BankAccount";
import mongoose from "mongoose";

const ACCOUNT_NUMBER_LENGTH = 8;

export default class BankAccountValidationUtil {
  public static validateBankAccountData(bankAccountData: BankAccountDTOInterface): void {
    if (!bankAccountData) {
      throw new BadRequestError("Param 'bankAccount' is required");
    }
    ["name", "number", "sortCode", "truelayerProviderId"].forEach((prop) => {
      if (!(bankAccountData as any)[prop]) {
        throw new BadRequestError(`Missing field '${prop}'`, "Operation failed");
      }
    });

    BankAccountValidationUtil.isAccountNumberValid(bankAccountData.number);
    BankAccountValidationUtil.isSortCodeValid(bankAccountData.sortCode);
  }

  public static async validateBankAccountIsActive(bankAccountId: string): Promise<void> {
    const bankAccountIsActive = await BankAccount.exists({
      _id: new mongoose.Types.ObjectId(bankAccountId),
      active: { $in: [true, null] } // null value is also included for backwards compatibility with older accounts with no 'active' prop
    });
    if (!bankAccountIsActive) throw new BadRequestError("Bank account is not active");
  }

  private static isAccountNumberValid(number: string): string {
    if (BankAccountValidationUtil.isPositiveInteger(number) && number.length === ACCOUNT_NUMBER_LENGTH) {
      return number;
    } else throw new BadRequestError("Invalid value for 'number'", "Invalid parameter");
  }

  private static isSortCodeValid(sortCode: string) {
    if (/^\d{2}-\d{2}-\d{2}$/.test(sortCode)) {
      return sortCode;
    } else throw new BadRequestError("Invalid value for 'sortCode'", "Invalid parameter");
  }

  private static isPositiveInteger = (num: string): boolean => /^[0-9]+$/.test(num);
}
