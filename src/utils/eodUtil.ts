import Decimal from "decimal.js";
import {
  EodAssetAnalystRatingsFundamentalsType,
  EodETFFundamentalsResponseType,
  EodStockFundamentalsResponseType
} from "../external-services/eodService";
import { RedisClientService } from "../loaders/redis";
import { BUCKET_URL_CONFIG, BucketsEnum } from "../external-services/cloudflareService";
import { removeSpaces } from "./stringUtil";
import { EODHoldingType } from "../services/investmentProductService";
import { etfHoldings, localeConfig, publicInvestmentUniverseConfig } from "@wealthyhood/shared-configs";

const { HOLDINGS_NAME_MAPPING } = etfHoldings;

export default class EodUtil {
  public static getTotalAnalysts(analystRatings: EodAssetAnalystRatingsFundamentalsType): number {
    if (!analystRatings) {
      return 0;
    }

    return new Decimal(analystRatings.StrongSell ?? 0)
      .add(analystRatings.Sell ?? 0)
      .add(analystRatings.Hold ?? 0)
      .add(analystRatings.Buy ?? 0)
      .add(analystRatings.StrongBuy ?? 0)
      .toNumber();
  }

  public static getCEO(fundamentals: EodStockFundamentalsResponseType): string {
    return (
      (
        [fundamentals.General.Officers?.[0], fundamentals.General.Officers?.[1]].find((officer) =>
          (officer?.Title as string)?.toLowerCase()?.includes("ceo")
        )?.Name as string
      )
        ?.replace("  ", " ")
        ?.replace("Ms.", "")
        ?.replace("Mr.", "")
        ?.replace("Mrs.", "")
        ?.trim() ?? "-"
    );
  }

  public static async getTopHoldings(
    response: EodETFFundamentalsResponseType,
    assetCommonId: publicInvestmentUniverseConfig.PublicAssetType,
    userLocale: localeConfig.LocaleType
  ): Promise<EODHoldingType[]> {
    const etfHoldingCodesToCustomLogosResponse = await RedisClientService.Instance.get<Record<string, string>>(
      `eod:customLogos:${assetCommonId}`
    );

    const etfHoldingCodesToCustomLogos: Record<string, string> = etfHoldingCodesToCustomLogosResponse || {};

    const topHoldings = response.ETF_Data.Top_10_Holdings;

    return Object.values(topHoldings)
      .filter((item: { Name: string; "Assets_%": number }) => item.Name)
      .map((holdingInfo: { Name: string; "Assets_%": number; Code: string; Exchange: string }) => {
        let name = holdingInfo["Name"];
        if (holdingInfo["Code"] && HOLDINGS_NAME_MAPPING[holdingInfo["Code"]]) {
          name = HOLDINGS_NAME_MAPPING[holdingInfo["Code"]];
        }

        let logoUrl = "";
        const customLogo = etfHoldingCodesToCustomLogos[holdingInfo["Code"]];
        if (customLogo) {
          logoUrl = customLogo;
        } else if (holdingInfo["Code"]) {
          logoUrl = `${BUCKET_URL_CONFIG[BucketsEnum.ETF_HOLDING_LOGOS]}/eod/${removeSpaces(holdingInfo["Code"])}.png`;
        }

        return {
          name,
          weight: (holdingInfo["Assets_%"] / 100).toLocaleString(userLocale, {
            style: "percent",
            maximumFractionDigits: 2
          }),
          logoUrl
        };
      });
  }
}
