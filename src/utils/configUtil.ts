import {
  countriesConfig,
  currenciesConfig,
  entitiesConfig,
  plansConfig,
  savingsUniverseConfig,
  localeConfig,
  taxResidencyConfig,
  investmentUniverseConfig
} from "@wealthyhood/shared-configs";
import { ASSET_CLASS_CONFIG } from "@wealthyhood/shared-configs/dist/investmentUniverse";
import { PartialRecord } from "utils";

const { MAIN_CURRENCY_CONFIG } = currenciesConfig;
const { COMPANY_ENTITY_CONFIG } = entitiesConfig;
const { COUNTRIES_REGIONS_MAPPING } = countriesConfig;
const { SAVINGS_UNIVERSE_CONFIG } = savingsUniverseConfig;
const { INVESTMENT_UNIVERSE_CONFIG } = investmentUniverseConfig;
const { getPriceConfig, getPlanConfig } = plansConfig;
const { REGIONS_TAX_RESIDENCY_MAPPING } = taxResidencyConfig;

export default class ConfigUtil {
  /**
   * PUBLIC METHODS
   */
  public static getPricing(
    companyEntity = entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
  ): Record<plansConfig.PriceType, plansConfig.PriceConfigType> {
    return getPriceConfig(companyEntity);
  }

  public static getPlans(
    companyEntity = entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
  ): Record<plansConfig.PlanType, plansConfig.PlanConfigType> {
    return getPlanConfig(companyEntity);
  }

  public static getSavingsUniverse(
    companyEntity = entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
  ): PartialRecord<savingsUniverseConfig.SavingsProductType, savingsUniverseConfig.SavingsProductConfigType> {
    return SAVINGS_UNIVERSE_CONFIG[companyEntity].universe;
  }

  public static getInvestmentUniverse(
    companyEntity = entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
  ): Record<investmentUniverseConfig.AssetType, investmentUniverseConfig.AssetConfigType> {
    return INVESTMENT_UNIVERSE_CONFIG[companyEntity];
  }

  public static getAssetClasses(
    companyEntity = entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
  ): PartialRecord<investmentUniverseConfig.AssetClassType, investmentUniverseConfig.AssetClassConfigType> {
    return ASSET_CLASS_CONFIG[companyEntity];
  }

  public static getPromotionalSavingsProduct(
    companyEntity = entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
  ): savingsUniverseConfig.SavingsProductType {
    return SAVINGS_UNIVERSE_CONFIG[companyEntity].promotionalSavingsProduct;
  }

  public static getDefaultUserCurrency(
    country: countriesConfig.CountryCodesType = "GB"
  ): currenciesConfig.MainCurrencyType {
    const region = ConfigUtil.getRegion(country);
    return MAIN_CURRENCY_CONFIG[region];
  }

  public static getDefaultUserLocale(country: countriesConfig.CountryCodesType = "GB"): localeConfig.LocaleType {
    return localeConfig.COUNTRY_LOCALE_MAPPINGS[country];
  }

  public static getDefaultCompanyEntity(
    country: countriesConfig.CountryCodesType = "GB"
  ): entitiesConfig.CompanyEntityEnum {
    const region = ConfigUtil.getRegion(country);
    return COMPANY_ENTITY_CONFIG[region];
  }

  public static getTaxResidencyConfig(
    country: countriesConfig.CountryCodesType = "GB"
  ): taxResidencyConfig.TaxResidencyConfigType {
    const region = ConfigUtil.getRegion(country);

    return REGIONS_TAX_RESIDENCY_MAPPING[region];
  }

  public static getRegion(country: countriesConfig.CountryCodesType): countriesConfig.RegionsType {
    return COUNTRIES_REGIONS_MAPPING[country];
  }
}
