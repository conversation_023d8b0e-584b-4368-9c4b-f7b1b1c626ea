export const replaceLineBreaksWithSpaces = (str: string): string => {
  return str.replace(/(\r\n|\n|\r)/gm, " ");
};

export const removeSpaces = (str: string): string => {
  return str.replace(/\s+/g, "");
};

export const slugify = (str: string): string =>
  str
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, "")
    .replace(/[\s_-]+/g, "-")
    .replace(/^-+|-+$/g, "");

export const capitalizeFirstLetter = (str: string): string => {
  if (!str || str.length === 0) {
    return str;
  }

  return str.charAt(0).toUpperCase() + str.slice(1);
};

export const spaceOnCapitalLetter = (str: string) => {
  return str.split(/(?=[A-Z])/).join(" ");
};

export const capitalizeWord = (str: string): string => {
  if (!str || str.length === 0) {
    return str;
  }

  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

/**
 * @description  Capitalize the first letter of each word in the string and convert the rest of the characters to lowercase.
 * This transformation is known as "Title Case".
 */
export const titleCaseString = (str: string): string => {
  if (!str || str.length === 0) {
    return str;
  }

  return str
    .split(" ")
    .map((part) => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())
    .join(" ");
};

export const removeMarkdownLinksFromString = (inputString: string): string => {
  // Regex to match markdown links of the format [text](url)
  const markdownLinkRegex = /\[([^\]]+)\]\([^)]+\)/g;

  // Replace the matched markdown links with just the text (group 1 of the regex)
  return inputString.replace(markdownLinkRegex, "$1");
};
