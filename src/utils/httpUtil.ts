import axios, { AxiosRequestConfig } from "axios";
import { addBreadcrumb, captureException } from "@sentry/node";
import logger from "../external-services/loggerService";

type ErrorHandlingSettingsType = {
  throwError: boolean;
  addSentryBreadcrumb: boolean;
  captureSentryException: boolean;
  logOptions: {
    active: true;
    module: string;
    method: string;
  };
};

export default class HttpUtil {
  /**
   * @description
   * Wrapper of axios, with added error handling, mean't to be used by external-services
   * @returns request body
   */
  public static async fetch(config: AxiosRequestConfig, errorHandlingSettings: ErrorHandlingSettingsType) {
    try {
      const response = await axios(config);
      return response.data;
    } catch (err) {
      const { addSentryBreadcrumb, captureSentryException, logOptions, throwError } = errorHandlingSettings;
      if (logOptions.active) {
        logger.error("http request failed", {
          module: logOptions.module,
          method: logOptions.method,
          data: {
            url: config.url,
            method: config.method?.toUpperCase(),
            // eslint-disable-next-line camelcase
            status: err.response && err.response.status,
            reason: err.response ? JSON.stringify(err.response.data, null, 4) : "",
            requestData: {
              data: JSON.stringify(config.data, null, 4)
            },
            error: err
          }
        });
      }

      if (addSentryBreadcrumb) {
        addBreadcrumb({
          type: "http",
          category: "http",
          level: "error",
          data: {
            url: config.url,
            method: config.method?.toUpperCase(),
            // eslint-disable-next-line camelcase
            status_code: err.response && err.response.status,
            reason: err.response && JSON.stringify(err.response.data, null, 4),
            requestData: {
              data: JSON.stringify(config.data, null, 4)
            }
          }
        });
      }

      if (captureSentryException) {
        captureException(err);
      }

      if (throwError) {
        throw err;
      }
    }
  }

  /**
   * Encodes URI components including characters ( and ).
   * @param str
   * @returns
   */
  public static extendedEncodeURIComponent(str: string) {
    return encodeURIComponent(str).replace(/\(/g, "%28").replace(/\)/g, "%29");
  }
}
