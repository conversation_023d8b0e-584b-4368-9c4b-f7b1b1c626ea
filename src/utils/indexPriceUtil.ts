import { indexesConfig } from "@wealthyhood/shared-configs";
import DateUtil from "./dateUtil";
import { RedisClientService } from "../loaders/redis";
import type { PartialRecord } from "types/utils";

export default class IndexPriceUtil {
  public static async getCachedCurrentIndexPrices(): Promise<
    PartialRecord<indexesConfig.IndexType, { close: number; timestamp: number; dailyReturnPercentage: number }>
  > {
    const data = await RedisClientService.Instance.get<
      PartialRecord<indexesConfig.IndexType, { close: number; timestamp: number; dailyReturnPercentage: number }>
    >("fmp:today:latest_index_prices");
    const latestTickers = data ?? {};

    // filter out any empty prices & prices that are not today's
    const { start } = DateUtil.getStartAndEndOfToday();
    const filteredIndexPrices = Object.fromEntries(
      Object.entries(latestTickers).filter(
        ([, entry]) => entry && entry.close > 0 && entry.timestamp > new Date(start).getTime()
      )
    );

    return filteredIndexPrices;
  }
}
