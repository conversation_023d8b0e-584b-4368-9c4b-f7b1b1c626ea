import Decimal from "decimal.js";
import { currenciesConfig, localeConfig } from "@wealthyhood/shared-configs";
import { ExchangeRates } from "../external-services/eodService";
import { UserDocument } from "../models/User";
import { MultiCurrencyPriceType } from "currencies";

const { MainCurrencies, SubUnitCurrencies, CURRENCY_SUB_UNIT_CONFIG } = currenciesConfig;

const MAXIMUM_CURRENCY_DIGITS = 2;
const MINIMUM_CURRENCY_DIGITS = 2;

export default class CurrencyUtil {
  public static isForeignCurrency(
    userCurrency: currenciesConfig.MainCurrencyType,
    requestedCurrency: currenciesConfig.MainCurrencyType
  ): boolean {
    return userCurrency !== requestedCurrency;
  }

  public static isSubUnitCurrency(currency: currenciesConfig.CurrencyType): boolean {
    return SubUnitCurrencies.includes(currency as currenciesConfig.SubUnitCurrencyType);
  }

  public static getMainCurrency(currency: currenciesConfig.CurrencyType): currenciesConfig.MainCurrencyType {
    if (CurrencyUtil.isSubUnitCurrency(currency)) {
      return CURRENCY_SUB_UNIT_CONFIG[currency as currenciesConfig.SubUnitCurrencyType].mainCurrency;
    } else return currency as currenciesConfig.MainCurrencyType;
  }

  public static convertAmountToMainCurrency(currency: currenciesConfig.CurrencyType, amount: number): number {
    if (!this.isSubUnitCurrency(currency)) {
      return new Decimal(amount).toDecimalPlaces(2).toNumber();
    }

    return Decimal.div(
      amount,
      CURRENCY_SUB_UNIT_CONFIG[currency as currenciesConfig.SubUnitCurrencyType].multiplier
    )
      .toDecimalPlaces(2)
      .toNumber();
  }

  public static getForeignCurrencies(user: UserDocument): currenciesConfig.MainCurrencyType[] {
    return MainCurrencies.filter((currency: currenciesConfig.MainCurrencyType) =>
      CurrencyUtil.isForeignCurrency(user.currency, currency)
    );
  }

  /**
   * Formats currency using the given currency & locale.
   *
   * @param money
   * @param currency
   * @param locale
   * @param includeFractionDigits
   * @param includeSign
   * @param minimumFractionDigits
   * @returns
   */
  public static formatCurrency(
    money: number,
    currency: currenciesConfig.MainCurrencyType,
    locale: localeConfig.LocaleType,
    includeFractionDigits = true,
    includeSign: "auto" | "always" | "never" = "auto",
    minimumFractionDigits = MINIMUM_CURRENCY_DIGITS
  ): string {
    return new Intl.NumberFormat(locale, {
      style: "currency",
      currency: currency,
      minimumFractionDigits: includeFractionDigits ? minimumFractionDigits : 0,
      maximumFractionDigits: includeFractionDigits
        ? MAXIMUM_CURRENCY_DIGITS > minimumFractionDigits
          ? MAXIMUM_CURRENCY_DIGITS
          : minimumFractionDigits
        : 0,
      signDisplay: includeSign
    }).format(money);
  }

  /**
   * @description Converts the amount given in base currency to an object that has
   * the value of that amount in each of the main currencies. The amount is expected to not be in
   * cents (we convert to 2 decimals).
   *
   * Example, for £20 valuation ("GBP", 20), returns a map:
   * {
   *   "USD" -> 23
   *   "EUR" -> 21
   *   "GBP" -> 20
   * }
   *
   * @param baseCurrency
   * @param amountInBaseCurrency
   * @param fxRates are in the format:
   *
   * {
   *   USD: { GBP: 0.78, EUR: 0.92, USD: 1 },
   *   EUR: { GBP: 0.85, USD: 1.09, EUR: 1 },
   *   GBP: { EUR: 1.18, USD: 1.28, GBP: 1 }
   * }
   *
   * @returns
   */
  public static mapFxDataToAmountPerCurrency(
    baseCurrency: currenciesConfig.MainCurrencyType,
    amountInBaseCurrency: number,
    fxRates: ExchangeRates
  ): MultiCurrencyPriceType {
    return Object.fromEntries(
      MainCurrencies.map((currency: currenciesConfig.MainCurrencyType) => [
        currency,
        Decimal.mul(fxRates[baseCurrency][currency], amountInBaseCurrency).toDecimalPlaces(2).toNumber()
      ])
    ) as MultiCurrencyPriceType;
  }
}
