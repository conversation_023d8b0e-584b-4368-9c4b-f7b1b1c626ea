import Decimal from "decimal.js";
import { currenciesConfig, fees, plansConfig } from "@wealthyhood/shared-configs";
import { FX_TARGET_SPREADS } from "../configs/fxSpreadsConfig";
import { FXSpreadSide } from "../types/fees";
import { FeesType } from "../models/Transaction";
import { ExchangeRates } from "../external-services/eodService";
import * as CacheUtil from "./cacheUtil";
import eodService from "../external-services/eodService";

const { FX_RATES } = fees;

/**
 * Returns the broker FX spread percentage based on user's subscription plan
 * gold => 25 bps
 * plus => 30 bps
 * free => 40 bps
 *
 * NOTE: numbers tbd offline
 *
 * @param plan User's subscription plan
 * @returns Spread percentage as a decimal (e.g., 0.0025 for 0.25%)
 */
const _getBrokerFxSpreadByPlan = (plan: plansConfig.PlanType): number => {
  switch (plan) {
    case "paid_mid":
      return 0.0025;
    case "paid_low":
      return 0.003;
    case "free":
      return 0.004;
    default:
      return 0.004;
  }
};

export const getZeroFees = (currency: currenciesConfig.MainCurrencyType): FeesType => {
  return {
    fx: {
      currency,
      amount: 0
    },
    commission: {
      currency,
      amount: 0
    },
    executionSpread: {
      currency,
      amount: 0
    },
    realtimeExecution: {
      currency,
      amount: 0
    }
  };
};

export const getTotalFeeAmount = (fees: FeesType): number => {
  if (!fees) return 0;

  return Object.entries(fees)
    .map(([, fee]) => fee.amount)
    .filter((amount) => amount > 0)
    .reduce((sum, fee) => Decimal.add(sum, fee), new Decimal(0))
    .toNumber();
};

/**
 * Calculates the broker FX fee amount based on consideration amount, plan, and broker FX rate
 * @param params Object containing considerationAmount, plan, and brokerFxRate
 * @param params.considerationAmount Amount in EUR/GBP/USD
 * @param params.plan User's subscription plan
 * @param params.brokerFxRate Broker FX rate (if 1, no FX fee is applied)
 * @returns FX fee amount in EUR/GBP/USD
 */
export const calculateBrokerFxFee = ({
  considerationAmount,
  plan,
  brokerFxRate
}: {
  considerationAmount: number;
  plan: plansConfig.PlanType;
  brokerFxRate: number;
}): number => {
  // If broker FX rate is 1, no FX fee is applied
  if (brokerFxRate === 1) {
    return 0;
  }

  const spread = _getBrokerFxSpreadByPlan(plan);
  return Decimal.mul(considerationAmount, spread).toDecimalPlaces(2).toNumber();
};

/**
 * Calculates the the display FX fee amount based on consideration amount, plan, and display FX rate
 * @param params Object containing considerationAmount, plan
 * @param params.considerationAmount Amount in cents
 * @param params.plan User's subscription plan
 * @returns FX fee amount in whole currency
 */
export const calculateDisplayFxFee = ({
  considerationAmount,
  plan
}: {
  considerationAmount: number;
  plan: plansConfig.PlanType;
}): number => {
  const spread = FX_RATES[plan];
  return Decimal.mul(considerationAmount, spread).toNumber();
};

export const aggregateFees = (fees: FeesType[], currency: currenciesConfig.MainCurrencyType): FeesType => {
  return fees.reduce((totalFees, currentFees) => {
    return {
      executionSpread: {
        amount: Decimal.add(
          totalFees.executionSpread.amount,
          currentFees?.executionSpread?.amount ?? 0
        ).toNumber(),
        currency: totalFees.executionSpread.currency
      },
      fx: {
        amount: Decimal.add(totalFees.fx.amount, currentFees?.fx?.amount ?? 0).toNumber(),
        currency: totalFees.fx.currency
      },
      commission: {
        amount: Decimal.add(totalFees.commission.amount, currentFees?.commission?.amount ?? 0).toNumber(),
        currency: totalFees.commission.currency
      },
      realtimeExecution: {
        amount: Decimal.add(
          totalFees.realtimeExecution.amount,
          currentFees?.realtimeExecution?.amount ?? 0
        ).toNumber(),
        currency: totalFees.realtimeExecution.currency
      }
    } as FeesType;
  }, getZeroFees(currency));
};

/**
 * Returns the FX rate for the given currency with spread.
 *
 * Examples:
 * If £1 -> $2, and our FX fee rate is 1%, then our FX rate with spread for buys is £1 -> $1.98 ($2 - 1% of $2)
 *
 * In the same scenario for a sell, our FX rate with spread would be £1 -> $2.02
 *
 * @param spreadSide
 * @param plan
 * @param options
 */
export const calculateFXRateWithSpread = async (
  spreadSide: FXSpreadSide,
  plan: plansConfig.PlanType,
  options: {
    userCurrency?: currenciesConfig.MainCurrencyType; // If FX rate is not passed
    targetCurrency?: currenciesConfig.MainCurrencyType; // If FX rate is not passed
    exchangeRate?: number;
    rounding?: boolean;
  }
): Promise<number> => {
  let fxRateToUse = options?.exchangeRate;
  if (!fxRateToUse) {
    const fxRates = await CacheUtil.getCachedDataWithFallback<ExchangeRates>(
      "fxRates",
      async (): Promise<ExchangeRates> => eodService.getLatestFXRates()
    );
    // if options.exchangeRate is empty userCurrency & targetUserCurrency should be defined
    fxRateToUse = fxRates[options.userCurrency][options.targetCurrency];
  }

  let fxRateWithSpread = new Decimal(fxRateToUse);
  if (spreadSide === FXSpreadSide.BUY) {
    fxRateWithSpread = new Decimal(fxRateToUse).mul(
      Decimal.sub(1, (FX_TARGET_SPREADS as Record<string, number>)[plan])
    );
  } else if (spreadSide === FXSpreadSide.SELL) {
    fxRateWithSpread = new Decimal(fxRateToUse).mul(
      Decimal.add(1, (FX_TARGET_SPREADS as Record<string, number>)[plan])
    );
  }

  if (options?.rounding) {
    return fxRateWithSpread.toDecimalPlaces(3).toNumber();
  } else return fxRateWithSpread.toNumber();
};
