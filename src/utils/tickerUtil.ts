import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import DateUtil from "./dateUtil";
import { RedisClientService } from "../loaders/redis";
import { PartialRecord } from "types/utils";
import { AssetPriceDataPointType, PortfolioPriceDataPointType } from "tickers";
import { StockSplitCorporateEventDocument } from "../models/CorporateEvent";
import Decimal from "decimal.js";

export const getCachedTodaysTickers = async (): Promise<
  PartialRecord<investmentUniverseConfig.AssetType, { close: number; timestamp: number }>
> => {
  // fetch cached data
  const data =
    await RedisClientService.Instance.get<
      PartialRecord<investmentUniverseConfig.AssetType, { close: number; timestamp: number }>
    >("fmp:today:latest_tickers");
  const latestTickers = data ?? {};

  // filter out any empty prices & prices that are not today's
  const { start } = DateUtil.getStartAndEndOfToday();
  const filteredLatestTickers = Object.fromEntries(
    Object.entries(latestTickers).filter(
      ([, entry]) => entry && entry.close > 0 && entry.timestamp > new Date(start).getTime()
    )
  );

  return filteredLatestTickers;
};

/**
 * @description Given a ticker data point & an interval in minutes (for example 10, for 10 minutes),
 * it maps the input ticker to one with timestamp the closest date as specified by the interval.
 *
 * For example:
 * { close: 10, timestamp: new Date("2024-09-01T03:57:00" }
 *
 * returns
 * { close: 10, timestamp: new Date("2024-09-01T04:00:00" }
 *
 */
export const mapTickerToClosestTimeMark = <T extends AssetPriceDataPointType | PortfolioPriceDataPointType>(
  ticker: T,
  interval: number,
  unit: "minutes" | "weeks" = "minutes"
): T => {
  const updatedTicker = { ...ticker };
  updatedTicker.timestamp = DateUtil.mapDateToClosestTimeMark(
    new Date(ticker.timestamp),
    interval,
    unit
  ).getTime();
  return updatedTicker;
};

/**
 * @description Takes an array of tickers (timestamp & closing price) and an interval and
 * returns an array of tickers sampled by the interval defined and mapped to the closest timemark as
 * specified by the interval.
 *
 * Steps:
 * 1. For each ticker create a key value mapping where key is the timestamp of the closest time mark
 * 2. Iterate on all tickers and assign them to the closest time mark key if the entry is empty or
 *    if the diff is smaller
 * 3. Convert the dict to an array of tickers sorted by timestamp
 *
 * @param tickers Array of ticker data points
 * @param interval the sampling frequency in minutes or weeks (for example 10 for 10 minute frequency or 1 for 1 week frequency)
 * @param unit The unit of the interval ('minutes' | 'weeks')
 * @returns an array of tickers sampled by the interval defined and mapped to the closest timemark as specified by the interval.
 *
 * For example, for 10 minute interval and input
 * [
 *   { timestamp: new Date("2024-08-01T04:02:00").getTime(), close: 9 },
 *   { timestamp: new Date("2024-09-01T03:57:00").getTime(), close: 8 },
 *   { timestamp: new Date("2024-09-01T04:02:00").getTime(), close: 10 },
 *   { timestamp: new Date("2024-09-01T04:08:00").getTime(), close: 20 },
 *   { timestamp: new Date("2024-09-01T04:09:00").getTime(), close: 30 },
 *   { timestamp: new Date("2024-09-01T05:18:00").getTime(), close: 40 },
 *   { timestamp: new Date("2024-09-01T05:35:00").getTime(), close: 5 },
 *   { timestamp: new Date("2024-09-01T05:47:00").getTime(), close: 1 }
 * ]
 *
 * the output is
 * [
 *   { timestamp: new Date("2024-08-01T04:00:00").getTime(), close: 9 }, // 04:02 => 04:00
 *   { timestamp: new Date("2024-09-01T04:00:00").getTime(), close: 10 }, // 04:02 => 04:00
 *   { timestamp: new Date("2024-09-01T04:10:00").getTime(), close: 30 }, // 04:09 => 04:10
 *   { timestamp: new Date("2024-09-01T05:20:00").getTime(), close: 40 }, // 05:18 => 05:20
 *   { timestamp: new Date("2024-09-01T05:40:00").getTime(), close: 5 }, // 05:35 => 05:40
 *   { timestamp: new Date("2024-09-01T05:50:00").getTime(), close: 1 } // 05:47 => 05:50
 * ]
 *
 */
export const sampleTickers = <T extends AssetPriceDataPointType | PortfolioPriceDataPointType>(
  tickers: T[],
  interval: number,
  unit: "minutes" | "weeks" = "minutes"
): T[] => {
  const tickersClosestTimeMarkMapping: Map<number, T> = new Map();

  tickers.forEach((ticker) => {
    const tickerTimeMark = DateUtil.mapDateToClosestTimeMark(new Date(ticker.timestamp), interval, unit).getTime();

    if (!tickersClosestTimeMarkMapping.has(tickerTimeMark)) {
      tickersClosestTimeMarkMapping.set(tickerTimeMark, { ...ticker });
    } else if (
      Math.abs(tickerTimeMark - ticker.timestamp) <
      Math.abs(tickerTimeMark - tickersClosestTimeMarkMapping.get(tickerTimeMark).timestamp)
    ) {
      tickersClosestTimeMarkMapping.set(tickerTimeMark, { ...ticker });
    }
  });

  return Array.from(tickersClosestTimeMarkMapping.entries())
    .map(([timeMark, ticker]) => {
      return {
        ...ticker,
        timestamp: timeMark
      };
    })
    .sort((tickerA, tickerB) => tickerA.timestamp - tickerB.timestamp) as T[];
};

export const adjustHoldingForSplit = (quantity: number, stockSplit: StockSplitCorporateEventDocument): number => {
  if (!stockSplit) {
    return quantity;
  }

  return Decimal.div(quantity, stockSplit.multiplier)
    .mul(stockSplit.divider)
    .toDecimalPlaces(4, Decimal.ROUND_DOWN)
    .toNumber();
};

export const adjustPriceForSplit = (close: number, stockSplit: StockSplitCorporateEventDocument): number => {
  if (!stockSplit) {
    return close;
  }

  return Decimal.mul(close, stockSplit.multiplier).div(stockSplit.divider).toNumber();
};

/**
 * Adjusts the price within the array of data points based on the given stock split.
 *
 * @param data
 * @param stockSplit
 */
export const adjustPricesForSplit = (
  data: AssetPriceDataPointType[],
  stockSplit: StockSplitCorporateEventDocument
): AssetPriceDataPointType[] => {
  return data.map((point) => {
    if (DateUtil.isSameOrFutureDate(new Date(point.timestamp), stockSplit.date)) {
      return point;
    }

    return {
      ...point,
      close: adjustPriceForSplit(point.close, stockSplit)
    };
  });
};

export const filterOlderThanNDaysOfPriceData = (
  data: AssetPriceDataPointType[],
  days: number
): AssetPriceDataPointType[] => {
  if (!data?.length) {
    return data;
  }

  const startOfToday = DateUtil.getStartOfDay(new Date(Date.now()));
  const hasDataFromToday = data.at(-1).timestamp >= startOfToday.getTime();

  // If we have data from today, request 1 less day
  const daysToFilter = hasDataFromToday ? days - 1 : days;
  const cutoffDate = DateUtil.getStartOfDay(DateUtil.getDateOfDaysAgo(new Date(Date.now()), daysToFilter));

  return data.filter((point) => point.timestamp >= cutoffDate.getTime());
};
