import { ASSET_CONFIG } from "@wealthyhood/shared-configs/dist/investmentUniverse";
import * as InvestmentUniverseUtil from "../investmentUniverseUtil";

describe("InvestmentUniverseUtil", () => {
  describe("getAssetIdFromIsin", () => {
    it("should return the active assetId given an active ISIN", () => {
      // equities_supermicro
      const assetId = InvestmentUniverseUtil.getAssetIdFromIsin("US86800U3023");
      expect(assetId).toBe("equities_supermicro");
      expect(ASSET_CONFIG[assetId].deprecated).toBeUndefined();
    });

    it("should return the active assetId given a deprecated ISIN", () => {
      // equities_supermicro_deprecated_1
      const assetId = InvestmentUniverseUtil.getAssetIdFromIsin("US86800U1043");
      expect(assetId).toBe("equities_supermicro");
      expect(ASSET_CONFIG[assetId].deprecated).toBeUndefined();
    });
  });

  describe("getIsinActiveAndDeprecated", () => {
    it("should return an array with 1 element for an asset with active ISIN", () => {
      const isins = InvestmentUniverseUtil.getIsinActiveAndDeprecated("equities_apple");
      expect(isins).toEqual([ASSET_CONFIG["equities_apple"].isin]);
    });

    it("should return an array with 2 element for an asset with active & deprecated ISIN", () => {
      const isins = InvestmentUniverseUtil.getIsinActiveAndDeprecated("equities_supermicro");
      expect(isins).toEqual([
        ASSET_CONFIG["equities_supermicro"].isin,
        ASSET_CONFIG["equities_supermicro_deprecated_1"].isin
      ]);
    });
  });

  describe("isAssetActive", () => {
    it("should be true for an assetId with active isin", () => {
      expect(InvestmentUniverseUtil.isAssetActive("equities_supermicro")).toBe(true);
    });

    it("should be false for an assetId with deprecated isin", () => {
      expect(InvestmentUniverseUtil.isAssetActive("equities_supermicro_deprecated_1")).toBe(false);
    });
  });
});
