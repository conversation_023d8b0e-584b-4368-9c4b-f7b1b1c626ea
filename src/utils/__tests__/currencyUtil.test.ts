import { currenciesConfig } from "@wealthyhood/shared-configs";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import CurrencyUtil from "../currencyUtil";

describe("CurrencyUtil", () => {
  beforeEach(() => jest.clearAllMocks());
  beforeAll(async () => await connectDb("CurrencyUtil"));
  afterEach(async () => await clearDb());
  afterAll(async () => await closeDb());

  describe("formatCurrency", () => {
    describe("when a GBP amount has been passed with 'en' locale", () => {
      it("should return the amount in the format £X,XXX.YY", async () => {
        const result = CurrencyUtil.formatCurrency(1234.56, "GBP", "en", true);
        expect(result).toBe("£1,234.56");
      });
    });

    describe("when a EUR amount has been passed with 'en' locale", () => {
      it("should return the amount in the format €X,XXX.YY", async () => {
        const result = CurrencyUtil.formatCurrency(1234.56, "EUR", "en", true);
        expect(result).toBe("€1,234.56");
      });
    });

    describe("when a USD amount has been passed with 'en' locale", () => {
      it("should return the amount in the format $X,XXX.YY", async () => {
        const result = CurrencyUtil.formatCurrency(1234.56, "USD", "en", true);
        expect(result).toBe("$1,234.56");
      });
    });

    describe("when a GBP amount has been passed with 'el' locale", () => {
      it("should return the amount in the format X.XXX,YY £", async () => {
        const result = CurrencyUtil.formatCurrency(1234.56, "GBP", "el", true);
        expect(result).toMatch("1.234,56\u00A0£");
      });
    });

    describe("when a EUR amount has been passed with 'el' locale", () => {
      it("should return the amount in the format X.XXX,YY €", async () => {
        const result = CurrencyUtil.formatCurrency(1234.56, "EUR", "el", true);
        expect(result).toMatch("1.234,56\u00A0€");
      });
    });

    describe("when a USD amount has been passed with 'el' locale", () => {
      it("should return the amount in the format X.XXX,YY $", async () => {
        const result = CurrencyUtil.formatCurrency(1234.56, "USD", "el", true);
        expect(result).toEqual("1.234,56\u00A0$");
      });
    });
  });

  describe("convertAmountToMainCurrency", () => {
    describe("when a main currency is passed", () => {
      it("should return the same amount", async () => {
        const result = CurrencyUtil.convertAmountToMainCurrency("GBP", 100);
        expect(result).toBe(100);
      });
    });

    describe("when a subunit currency is passed", () => {
      it("should return that amount converted to the main currency", async () => {
        const result = CurrencyUtil.convertAmountToMainCurrency("GBX", 100);
        expect(result).toBe(1);
      });
    });
  });

  describe("mapFxDataToAmountPerCurrency", () => {
    it("should return the price per currency for a given base currency & fx rates object", () => {
      const FX_RATES = {
        USD: { GBP: 0.78, EUR: 0.92, USD: 1 },
        EUR: { GBP: 0.85, USD: 1.09, EUR: 1 },
        GBP: { EUR: 1.18, USD: 1.28, GBP: 1 }
      };
      const BASE_CURRENCY = "USD" as currenciesConfig.MainCurrencyType;
      const AMOUNT = 100;

      const pricePerCurrency = CurrencyUtil.mapFxDataToAmountPerCurrency(BASE_CURRENCY, AMOUNT, FX_RATES);
      expect(pricePerCurrency).toMatchObject({
        USD: 100,
        EUR: 92,
        GBP: 78
      });
    });
  });
});
