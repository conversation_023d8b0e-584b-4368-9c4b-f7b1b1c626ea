import { countriesConfig, entitiesConfig } from "@wealthyhood/shared-configs";
import * as fs from "fs";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { customAlphabet } from "nanoid";
import path from "path";
import { InvestmentProductDocument } from "../models/InvestmentProduct";
import { TransactionDocument } from "../models/Transaction";
import { AddressDocument } from "../models/Address";
import { OrderDocument } from "../models/Order";
import { RewardDocument } from "../models/Reward";
import { UserDocument } from "../models/User";
import { AccountStatementActivity } from "../services/userService";
import ConfigUtil from "./configUtil";
import CurrencyUtil from "./currencyUtil";
import DateUtil from "./dateUtil";
import { getAssetConfigFromIsin } from "./investmentUniverseUtil";
import { capitalizeFirstLetter } from "./stringUtil";
import Decimal from "decimal.js/decimal";

const alphabet = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
export const nanoid = customAlphabet(alphabet, 6);

export enum TradeTypeEnum {
  ORDER = "ORDER",
  REWARD = "REWARD"
}

export default class StatementUtil {
  /**
   * Generates a PDF account statement in the format of an array buffer.
   * @param user
   * @param address
   * @param data
   * @param options
   * @private
   */
  public static generateAccountStatementPDF(
    user: UserDocument,
    address: AddressDocument,
    data: AccountStatementActivity,
    options?: {
      start?: Date;
      end?: Date;
    }
  ): Buffer {
    // Retrieve static file paths needed for PDF (logo & fonts).
    const logoPath = path.join(__dirname + "/../../static/whlogobanner.png");
    const lightFontPath = path.join(__dirname + "/../../static/poppins-light.txt");
    const regularFontPath = path.join(__dirname + "/../../static/poppins-regular.txt");
    const semiBoldFontPath = path.join(__dirname + "/../../static/poppins-semibold.txt");
    const boldFontPath = path.join(__dirname + "/../../static/poppins-bold.txt");

    const document = new jsPDF();

    // Add files for fonts
    document.addFileToVFS("Poppins-Light.ttf", fs.readFileSync(lightFontPath, "utf-8"));
    document.addFileToVFS("Poppins-Regular.ttf", fs.readFileSync(regularFontPath, "utf-8"));
    document.addFileToVFS("Poppins-SemiBold.ttf", fs.readFileSync(semiBoldFontPath, "utf-8"));
    document.addFileToVFS("Poppins-Bold.ttf", fs.readFileSync(boldFontPath, "utf-8"));
    document.addFont("Poppins-Light.ttf", "Poppins Light", "normal");
    document.addFont("Poppins-Regular.ttf", "Poppins", "normal");
    document.addFont("Poppins-SemiBold.ttf", "Poppins", "semibold");
    document.addFont("Poppins-Bold.ttf", "Poppins", "bold");

    // Add Wealthyhood logo (top-left)
    document.addImage(fs.readFileSync(logoPath, "base64"), "PNG", 15, 24, 61, 10);

    // Add Account statement title and subtitle (top-right)
    document.setFont("Poppins", "semibold");
    document.setFontSize(20);
    document.text("Account Statement", 190, 30, { align: "right" });

    document.setFont("Poppins Light", "normal");
    document.setFontSize(9);
    document.setTextColor(128, 128, 128);
    document.text(`Generated on ${DateUtil.formatDateToDDMONTHYYYY(new Date(Date.now()))}`, 190, 37, {
      align: "right"
    });

    // Reset text styling
    document.setFont("Poppins", "normal");
    document.setTextColor("black");

    // Add user details
    const endingHeight = this._addUserDetailsSection(document, user, address, {
      startingHeight: 55
    });

    // Add table title
    document.setFontSize(12);
    document.setFont("Poppins", "bold");

    const formattedData = data.map((row) => {
      if (row.type === "order") {
        return [
          DateUtil.formatDateToDDMONYYYY(row.date),
          capitalizeFirstLetter(row.type),
          row.side,
          row.asset,
          row.isin,
          row.currency,
          row.amount.toFixed(2)
        ];
      } else if (row.type === "dividend") {
        return [
          DateUtil.formatDateToDDMONYYYY(row.date),
          capitalizeFirstLetter(row.type),
          "-",
          row.asset,
          row.isin,
          row.currency,
          row.amount.toFixed(2)
        ];
      } else if (row.type === "reward") {
        return [
          DateUtil.formatDateToDDMONYYYY(row.date),
          "Bonus",
          "-",
          row.asset,
          row.isin,
          row.currency,
          row.amount.toFixed(2)
        ];
      } else {
        return [
          DateUtil.formatDateToDDMONYYYY(row.date),
          capitalizeFirstLetter(row.type),
          "-",
          "-",
          "-",
          row.currency,
          row.amount.toFixed(2)
        ];
      }
    });

    const startDate = DateUtil.formatDateToDDMONYYYY(options?.start ?? data?.at(-1)?.date);
    const endDate = DateUtil.formatDateToDDMONYYYY(options?.end ?? new Date(Date.now()));

    document.text(`Transactions from ${startDate} to ${endDate}`, 15, endingHeight + 25);

    // Add table
    this._addAccountStatementTransactionsSection(document, formattedData, endingHeight);

    // Add footer
    this._addFooterSection(document, user);

    return Buffer.from(document.output("arraybuffer"));
  }

  /**
   * Generates a PDF trade confirmation in the format of an array buffer.
   * @param user
   * @param address
   * @param data
   * @param investmentProduct
   * @param tradeType
   * @private
   */
  public static generateTradeConfirmationPDF(
    user: UserDocument,
    address: AddressDocument,
    data: OrderDocument | RewardDocument,
    investmentProduct: InvestmentProductDocument,
    tradeType: TradeTypeEnum
  ): Buffer {
    // Retrieve static file paths needed for PDF (logo & fonts).
    const logoPath = path.join(__dirname + "/../../static/whlogobanner.png");
    const lightFontPath = path.join(__dirname + "/../../static/poppins-light.txt");
    const regularFontPath = path.join(__dirname + "/../../static/poppins-regular.txt");
    const semiBoldFontPath = path.join(__dirname + "/../../static/poppins-semibold.txt");
    const boldFontPath = path.join(__dirname + "/../../static/poppins-bold.txt");

    const document = new jsPDF();

    // Add files for fonts
    document.addFileToVFS("Poppins-Light.ttf", fs.readFileSync(lightFontPath, "utf-8"));
    document.addFileToVFS("Poppins-Regular.ttf", fs.readFileSync(regularFontPath, "utf-8"));
    document.addFileToVFS("Poppins-SemiBold.ttf", fs.readFileSync(semiBoldFontPath, "utf-8"));
    document.addFileToVFS("Poppins-Bold.ttf", fs.readFileSync(boldFontPath, "utf-8"));
    document.addFont("Poppins-Light.ttf", "Poppins Light", "normal");
    document.addFont("Poppins-Regular.ttf", "Poppins", "normal");
    document.addFont("Poppins-SemiBold.ttf", "Poppins", "semibold");
    document.addFont("Poppins-Bold.ttf", "Poppins", "bold");

    // Add Wealthyhood logo (top-left)
    document.addImage(fs.readFileSync(logoPath, "base64"), "PNG", 15, 24, 61, 10);

    // Add Trade confirmation title and subtitle (top-right)
    document.setFont("Poppins", "semibold");
    document.setFontSize(16);
    document.text("Trade Confirmation", 190, 30, { align: "right" });

    document.setFont("Poppins Light", "normal");
    document.setFontSize(8);
    document.setTextColor(128, 128, 128);
    document.text(`Generated on ${DateUtil.formatDateToDDMONTHYYYY(new Date(Date.now()))}`, 190, 37, {
      align: "right"
    });
    document.text(`For order ${data.displayUserFriendlyId}`, 190, 42, { align: "right" });

    // Reset text styling
    document.setFont("Poppins", "normal");
    document.setTextColor("black");

    // Add user details
    const endingHeight = this._addUserDetailsSection(document, user, address, {
      startingHeight: 55
    });

    // Add table title and subtitle
    document.setFontSize(11);
    document.setFont("Poppins", "bold");
    document.text("Executed trade", 15, endingHeight + 20);

    document.setFont("Poppins Light", "normal");
    document.setFontSize(8);
    document.setTextColor(128, 128, 128);
    document.text("Reporting firm: Wealthyhood Europe Investment Services SA", 15, endingHeight + 26);
    document.text(
      `Executed on: ${DateUtil.formatDateToDDMONYYYYHHMMSSUTC(data.updatedAt)}`,
      15,
      endingHeight + 32
    );

    // Prepare table data
    const assetConfig = getAssetConfigFromIsin(data.isin);

    // Get order/reward amount
    let amount;
    if (tradeType === TradeTypeEnum.ORDER) {
      amount = CurrencyUtil.formatCurrency(
        Decimal.div(
          (data as OrderDocument).getDisplayAmount(
            user.currency,
            investmentProduct,
            (data as OrderDocument).transaction as TransactionDocument
          ),
          100
        ).toNumber(),
        data.consideration?.currency,
        ConfigUtil.getDefaultUserLocale(user.residencyCountry)
      );
    } else {
      amount = CurrencyUtil.formatCurrency(
        Decimal.div((data as RewardDocument).displayAmount, 100).toNumber(),
        data.consideration?.currency,
        ConfigUtil.getDefaultUserLocale(user.residencyCountry)
      );
    }

    // Get FX rate in the form of €1 = $1.23
    const fxRate =
      user.currency === data.unitPrice?.currency
        ? "-"
        : `${CurrencyUtil.formatCurrency(1, user.currency, ConfigUtil.getDefaultUserLocale(user.residencyCountry), false)} = ${CurrencyUtil.formatCurrency(data.exchangeRate, data.unitPrice.currency, ConfigUtil.getDefaultUserLocale(user.residencyCountry), true, "never", 3)}`;

    autoTable(document, {
      theme: "plain",
      startY: endingHeight + 40,
      margin: { bottom: 40 },
      rowPageBreak: "avoid",
      styles: {
        font: "Poppins Light",
        fontSize: 8,
        minCellHeight: 10,
        valign: "middle",
        halign: "center",
        minCellWidth: 12
      },
      headStyles: {
        font: "Poppins",
        fontSize: 8,
        lineWidth: { bottom: 0.5 },
        lineColor: "black",
        valign: "middle",
        halign: "center"
      },
      head: [["Order ID", "ISIN", "Symbol", "Exchange", "Type", "Side", "Quantity", "Amount", "Price", "FX rate"]],
      body: [
        [
          data.displayUserFriendlyId,
          data.isin,
          assetConfig?.formalTicker,
          assetConfig?.displayExchange,
          "Market",
          (data as any).side ? (data as any).side : "Buy",
          data.quantity,
          amount,
          CurrencyUtil.formatCurrency(
            data.displayUnitPrice.amount,
            data.displayUnitPrice.currency,
            ConfigUtil.getDefaultUserLocale(user.residencyCountry)
          ),
          fxRate
        ]
      ]
    });

    // Fees section
    document.setFontSize(11);
    document.setFont("Poppins", "bold");
    document.setTextColor("black");

    const lastY = (document as any).lastAutoTable?.finalY;
    document.text("Fees", 15, lastY + 10);
    const feesTableHead = [["Order ID", "Symbol", "Fee type", "Fee amount"]];
    const feesTableBody: string[][] = [];

    feesTableBody.push([
      data.displayUserFriendlyId,
      assetConfig?.formalTicker,
      "FX fee",
      CurrencyUtil.formatCurrency(
        data.displayFxFee,
        data.consideration.currency,
        ConfigUtil.getDefaultUserLocale(user.residencyCountry)
      )
    ]);

    feesTableBody.push([
      data.displayUserFriendlyId,
      assetConfig?.formalTicker,
      "Commission",
      CurrencyUtil.formatCurrency(
        data.fees?.realtimeExecution?.amount ?? 0,
        data.consideration.currency,
        ConfigUtil.getDefaultUserLocale(user.residencyCountry)
      )
    ]);

    autoTable(document, {
      theme: "plain",
      startY: lastY + 15,
      margin: { bottom: 40 },
      rowPageBreak: "avoid",
      styles: {
        font: "Poppins Light",
        fontSize: 8,
        lineWidth: { bottom: 0.5 },
        minCellHeight: 10,
        lineColor: [228, 228, 228],
        valign: "middle",
        halign: "center",
        minCellWidth: 12
      },
      headStyles: {
        font: "Poppins",
        fontSize: 8,
        lineWidth: { bottom: 0.5 },
        lineColor: "black",
        valign: "middle",
        halign: "center"
      },
      head: feesTableHead,
      body: feesTableBody
    });

    // Note above footer
    document.setFontSize(7);
    document.setFont("Poppins Light", "normal");
    document.setTextColor(128, 128, 128);
    document.text(
      "Note: Your order may have been executed in multiple fills at various prices and venues. Nominal charges are rounded to the nearest cent.",
      15,
      250
    );

    // Add footer
    this._addFooterSection(document, user);

    return Buffer.from(document.output("arraybuffer"));
  }

  public static generateAccountStatementFilePath(userId: string): string {
    return `${userId}/${nanoid()}/wealthyhood-account-statement_${DateUtil.formatDateToDDMONYYYY(new Date(), { separatorCharacter: "-" })}.pdf`.toLowerCase();
  }

  public static generateTradeConfirmationFilePath(documentId: string): string {
    return `${documentId}/${nanoid()}/wealthyhood-trade-confirmation_${DateUtil.formatDateToDDMONYYYY(new Date(), { separatorCharacter: "-" })}.pdf`.toLowerCase();
  }

  private static _addUserDetailsSection(
    document: jsPDF,
    user: UserDocument,
    address: AddressDocument,
    options: { startingHeight: number }
  ): number {
    // Default height padding between every user detail row
    const padding = 7;

    // Extra padding under the user full name
    const extraNamePadding = 3;

    document.setFontSize(16);
    document.setFont("Poppins", "bold");
    document.text(user.fullName, 15, options?.startingHeight);

    document.setFontSize(10);
    document.setFont("Poppins", "semibold");
    document.text("Account reference:", 15, options?.startingHeight + extraNamePadding + padding);
    document.text("Email:", 15, options?.startingHeight + extraNamePadding + padding * 2);
    document.text("Address:", 15, options?.startingHeight + extraNamePadding + padding * 3);

    document.setFont("Poppins Light", "normal");
    document.text(user.id, 50, options?.startingHeight + extraNamePadding + padding);
    document.text(user.email, 27, options?.startingHeight + extraNamePadding + padding * 2);
    document.text(
      `${address.line1}, ${address.city}, ${address.postalCode}, ${countriesConfig.countries.find(({ code }) => code === address.countryCode).name}`,
      32,
      options?.startingHeight + extraNamePadding + padding * 3
    );

    return options?.startingHeight + extraNamePadding + padding * 3;
  }

  /**
   * Adds a footer (specific to the user's company entity) in **every** page of the PDF statement.
   * @param document
   * @param user
   * @private
   */
  private static _addFooterSection(document: jsPDF, user: UserDocument) {
    const pageCount = document.getNumberOfPages();

    document.setFontSize(6);
    document.setFont("Poppins Light", "normal");
    for (let i = 1; i <= pageCount; i++) {
      document.setPage(i);
      document.setTextColor(164, 164, 164);

      const currentYear = DateUtil.getCurrentYear();
      const companyName =
        user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
          ? `${currentYear} Wealthyhood Europe AEPEY`
          : `${currentYear} Wealthyhood Ltd`;
      const disclaimer =
        user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
          ? "Wealthyhood Europe AEPEY is authorised and regulated by the Hellenic Capital Markets Commission (3/1014). Wealthyhood Europe AEPEY is registered in Greece\n(178577960000) at Solonos 60, Athens, 10672, Greece."
          : "Wealthyhood Ltd (FCA Register: 933675) is an appointed representative of RiskSave Technologies Ltd, which is authorised and regulated by the Financial Conduct Authority\n(FRN 775330). Wealthyhood Ltd is registered in England and Wales (********) at 9 Kingsland Road, London, E2 8DD, United Kingdom.";

      document.text(disclaimer, 15, 270);
      document.text(companyName, 15, 280);
      document.text(`Page ${i} of ${pageCount}`, 190, 280, { align: "right" });
    }
  }

  private static _addAccountStatementTransactionsSection(
    document: jsPDF,
    data: string[][],
    previousElementEndingHeight: number
  ) {
    autoTable(document, {
      theme: "plain",
      startY: previousElementEndingHeight + 33, // Previous height
      margin: { bottom: 40 },
      rowPageBreak: "avoid",
      styles: {
        font: "Poppins Light",
        fontSize: 7,
        lineWidth: {
          bottom: 0.5
        },
        minCellHeight: 14,
        lineColor: [228, 228, 228],
        valign: "middle",
        minCellWidth: 20
      },
      headStyles: {
        font: "Poppins",
        fontSize: 9,
        lineWidth: {
          bottom: 0.5
        },
        lineColor: "black"
      },
      head: [["Date", "Category", "Side", "Description", "ISIN", "Currency", "Amount"]],
      body: data
    });
  }
}
