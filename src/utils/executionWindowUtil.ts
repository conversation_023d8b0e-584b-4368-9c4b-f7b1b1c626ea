import { OrderInterface, OrderSubmissionIntentEnum } from "../models/Order";
import { currenciesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import {
  AggregateExecutionWindowType,
  EXECUTION_WINDOW_HOURS_CONFIG,
  ExecutionTypeEnum,
  ExecutionWindowsType,
  ExecutionWindowType
} from "../configs/executionWindowConfig";
import { DateTime } from "luxon";
import DateUtil from "./dateUtil";
import { InvestmentProductDocument } from "../models/InvestmentProduct";
import type { InvestmentProductsDictType } from "types/investmentProducts";
import SubmissionWindowUtil from "./submissionWindowUtil";
import { getAssetConfigFromIsin } from "./investmentUniverseUtil";

/**
 * Utility class to determine the execution window for an order or a transaction.
 *
 * #### Note:
 * Execution window is meant to be used for display purposes only.
 */
export default class ExecutionWindowUtil {
  public static getOrderExecutionWindow(
    order: Partial<OrderInterface>,
    userCurrency: currenciesConfig.MainCurrencyType,
    investmentProduct: InvestmentProductDocument
  ): ExecutionWindowType {
    const assetCategory = getAssetConfigFromIsin(order.isin as string).category;
    if (
      SubmissionWindowUtil.isOrderEligibleForRealtimeSubmissionNow(order, {
        userCurrency,
        investmentProduct,
        assetCategory
      })
    ) {
      return { executionType: ExecutionTypeEnum.REALTIME };
    }

    return ExecutionWindowUtil._getMarketHoursExecutionWindow({
      requiredWorkDays: 1,
      assetCategory,
      submissionIntent: order.submissionIntent
    });
  }

  public static getRebalanceExecutionWindow(): {
    buyExecutionWindow: AggregateExecutionWindowType;
    sellExecutionWindow: AggregateExecutionWindowType;
  } {
    const sellExecutionWindow = ExecutionWindowUtil._getMarketHoursExecutionWindow();
    const buyExecutionWindow = ExecutionWindowUtil._getMarketHoursExecutionWindow({
      requiredWorkDays: 2,
      assetCategory: "etf"
    });

    return { buyExecutionWindow, sellExecutionWindow };
  }

  public static getAssetTransactionExecutionWindow(
    orders: Partial<OrderInterface>[],
    userCurrency: currenciesConfig.MainCurrencyType,
    options: { investmentProducts: InvestmentProductsDictType }
  ): ExecutionWindowsType {
    // There is unlikely event that we are trying to get the execution window for a transaction that has no
    // orders, e.g. all orders are skipped because of low investment amount.
    if (!orders || orders?.length === 0) {
      return {
        etfs: ExecutionWindowUtil._getMarketHoursExecutionWindow(),
        stocks: ExecutionWindowUtil._getMarketHoursExecutionWindow({
          assetCategory: "stock"
        })
      };
    }

    const stockOrders = orders.filter((order) => getAssetConfigFromIsin(order.isin).category === "stock");
    const etfOrders = orders.filter((order) => getAssetConfigFromIsin(order.isin).category === "etf");

    const executionWindow: ExecutionWindowsType = {};
    if (stockOrders.length > 0) {
      const someOrdersEligibleForRealtimeExecutionNow = stockOrders.some((order) => {
        const investmentProduct = options.investmentProducts[order.isin];
        const assetCategory = getAssetConfigFromIsin(order.isin as string).category;

        return SubmissionWindowUtil.isOrderEligibleForRealtimeSubmissionNow(order, {
          assetCategory,
          userCurrency,
          investmentProduct
        });
      });

      if (someOrdersEligibleForRealtimeExecutionNow) {
        executionWindow.stocks = {
          executionType: ExecutionTypeEnum.REALTIME
        };
      } else {
        executionWindow.stocks = ExecutionWindowUtil._getMarketHoursExecutionWindow({
          assetCategory: "stock",
          submissionIntent: stockOrders.some(
            (order) => order.submissionIntent === OrderSubmissionIntentEnum.REAL_TIME
          )
            ? OrderSubmissionIntentEnum.REAL_TIME
            : OrderSubmissionIntentEnum.AGGREGATE
        });
      }
    }

    if (etfOrders.length > 0) {
      const someOrdersEligibleForRealtimeExecutionNow = etfOrders.some((order) => {
        const investmentProduct = options.investmentProducts[order.isin];
        const assetCategory = getAssetConfigFromIsin(order.isin as string).category;

        return SubmissionWindowUtil.isOrderEligibleForRealtimeSubmissionNow(order, {
          assetCategory,
          userCurrency,
          investmentProduct
        });
      });

      if (someOrdersEligibleForRealtimeExecutionNow) {
        executionWindow.etfs = {
          executionType: ExecutionTypeEnum.REALTIME
        };
      } else {
        executionWindow.etfs = ExecutionWindowUtil._getMarketHoursExecutionWindow({
          assetCategory: "etf",
          submissionIntent: etfOrders.some(
            (order) => order.submissionIntent === OrderSubmissionIntentEnum.REAL_TIME
          )
            ? OrderSubmissionIntentEnum.REAL_TIME
            : OrderSubmissionIntentEnum.AGGREGATE
        });
      }
    }

    return executionWindow;
  }

  private static _getMarketHoursExecutionWindow(
    options: {
      requiredWorkDays?: number;
      assetCategory?: investmentUniverseConfig.AssetCategoryType;
      submissionIntent?: OrderSubmissionIntentEnum;
    } = {}
  ): AggregateExecutionWindowType {
    const { requiredWorkDays = 1, assetCategory = "etf" } = options;
    const executionDay: Date = ExecutionWindowUtil._calculateAggregateExecutionDay({
      requiredWorkDays,
      assetCategory
    });
    const submissionIntent =
      options?.submissionIntent === OrderSubmissionIntentEnum.REAL_TIME ? "realtime" : "aggregate";

    const start = DateTime.fromJSDate(executionDay)
      .setZone(EXECUTION_WINDOW_HOURS_CONFIG[assetCategory][submissionIntent].timeZone)
      .set({
        hour: EXECUTION_WINDOW_HOURS_CONFIG[assetCategory][submissionIntent].start.HOUR,
        minute: EXECUTION_WINDOW_HOURS_CONFIG[assetCategory][submissionIntent].start.MINUTE,
        second: 0,
        millisecond: 0
      });
    const end = DateTime.fromJSDate(executionDay)
      .setZone(EXECUTION_WINDOW_HOURS_CONFIG[assetCategory][submissionIntent].timeZone)
      .set({
        hour: EXECUTION_WINDOW_HOURS_CONFIG[assetCategory][submissionIntent].end.HOUR,
        minute: EXECUTION_WINDOW_HOURS_CONFIG[assetCategory][submissionIntent].end.MINUTE,
        second: 0,
        millisecond: 0
      });

    return {
      executionType: ExecutionTypeEnum.MARKET_HOURS,
      start: DateUtil.convertToTimeZone(start, "GMT"),
      end: DateUtil.convertToTimeZone(end, "GMT")
    };
  }

  private static _calculateAggregateExecutionDay(options: {
    requiredWorkDays: number;
    assetCategory: investmentUniverseConfig.AssetCategoryType;
  }): Date {
    const { requiredWorkDays, assetCategory } = options;
    const now = new Date(Date.now());

    const today = DateTime.fromJSDate(now).setZone("Europe/London").toJSDate();
    const isBeforeCutoffTime = SubmissionWindowUtil.isDateBeforeAggregateSubmissionCutoffTime(
      today,
      assetCategory
    );

    const isWorkday = assetCategory === "stock" ? DateUtil.isUSWorkDay(today) : DateUtil.isUKWorkDay(today);
    const getNextDayAfterNWorkDaysMethod =
      assetCategory === "stock" ? DateUtil.getDateAfterNthUSWorkDays : DateUtil.getDateAfterNthUKWorkDays;

    const executionDay =
      isWorkday && isBeforeCutoffTime
        ? getNextDayAfterNWorkDaysMethod(today, requiredWorkDays - 1)
        : getNextDayAfterNWorkDaysMethod(today, requiredWorkDays);

    // We set execution to the middle of the day to avoid date switches if this date is later converted to another timezone.
    executionDay.setHours(12, 0, 0, 0);

    return executionDay;
  }
}
