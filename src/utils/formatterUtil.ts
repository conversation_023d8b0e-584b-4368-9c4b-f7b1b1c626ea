import { localeConfig } from "@wealthyhood/shared-configs";
import Decimal from "decimal.js";

export function formatPercentage(
  num: number,
  locale: localeConfig.LocaleType,
  minimumFractionDigits = 2,
  maximumFractionDigits = 2,
  includeSign: "auto" | "always" | "never" = "auto"
): string {
  return (num ?? 0).toLocaleString(locale, {
    style: "percent",
    minimumFractionDigits,
    maximumFractionDigits,
    signDisplay: includeSign
  });
}

export function numberToTwoDecimalsStr(number: number, locale: localeConfig.LocaleType): string {
  if (number === null || number === undefined) return null;
  return new Decimal(number).toNumber().toLocaleString(locale, { maximumFractionDigits: 2 });
}
