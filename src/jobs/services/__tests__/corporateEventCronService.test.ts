import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import eodService, {
  EodStockFundamentalsResponseType,
  StockSplitType
} from "../../../external-services/eodService";
import CorporateEventCronService from "../corporateEventCronService";
import {
  AssetIsinChangeCorporateEvent,
  AssetIsinChangeCorporateEventDTOInterface,
  StockSplitCorporateEvent
} from "../../../models/CorporateEvent";
import {
  buildInvestmentProduct,
  buildAssetIsinChangeCorporateEvent,
  buildStockSplitCorporateEvent
} from "../../../tests/utils/generateModels";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import DateUtil from "../../../utils/dateUtil";
import eventEmitter from "../../../loaders/eventEmitter";
import events from "../../../event-handlers/events";
import { RedisClientService } from "../../../loaders/redis";
import { buildStockFundamentalsResponse } from "../../../tests/utils/generateEod";

describe("CorporateEventCronService", () => {
  beforeAll(async () => await connectDb("CorporateEventCronService"));
  afterAll(async () => await closeDb());

  describe("createStockSplits", () => {
    describe("when there are no splits for the next 7 days", () => {
      const TODAY = new Date("2024-01-10");

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");
        Date.now = jest.fn(() => +TODAY);
        jest.spyOn(eodService, "getStockSplits").mockResolvedValue([]);

        await CorporateEventCronService.createAllStockSplits();
      });
      afterAll(async () => await clearDb());

      it("should NOT add a corporate event in our database", async () => {
        const split = await StockSplitCorporateEvent.findOne();
        expect(split).toBeNull();
      });

      it("should NOT emit an event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.corporateEvents.stockSplitCreation.eventId,
          expect.anything()
        );
      });
    });

    describe("when there is a split for tomorrow but we don't have that asset in our universe", () => {
      const TODAY = new Date("2024-01-10");
      const SPLIT_RATIO = "3.000000/1.000000";

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");
        Date.now = jest.fn(() => +TODAY);
        jest
          .spyOn(eodService, "getStockSplits")
          .mockResolvedValue([{ code: "WLTHD", exchange: "US", date: "2024-01-10", split: SPLIT_RATIO }]);

        await CorporateEventCronService.createAllStockSplits();
      });
      afterAll(async () => await clearDb());

      it("should NOT add a corporate event in our database", async () => {
        const split = await StockSplitCorporateEvent.findOne();
        expect(split).toBeNull();
      });

      it("should NOT emit an event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.corporateEvents.stockSplitCreation.eventId,
          expect.anything()
        );
      });
    });

    describe("when there is a split for tomorrow but it's already in our database", () => {
      const TODAY = new Date("2024-01-10");
      const SPLIT_RATIO = "3.000000/1.000000";

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");
        Date.now = jest.fn(() => +TODAY);
        jest
          .spyOn(eodService, "getStockSplits")
          .mockImplementation(
            (exchange: investmentUniverseConfig.ExchangeType, date: Date): Promise<StockSplitType[]> => {
              if (exchange === "US" && DateUtil.datesAreEqual(date, TODAY)) {
                return Promise.resolve([{ code: "SONY", exchange: "US", date: "2024-01-10", split: SPLIT_RATIO }]);
              } else return Promise.resolve([]);
            }
          );

        // This corporate event already exists in our DB.
        await buildStockSplitCorporateEvent({ asset: "equities_sony", date: TODAY });

        await CorporateEventCronService.createAllStockSplits();
      });
      afterAll(async () => await clearDb());

      it("should NOT add a corporate event in our database", async () => {
        const splits = await StockSplitCorporateEvent.find();
        expect(splits.length).toBe(1);
      });

      it("should NOT emit an event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.corporateEvents.stockSplitCreation.eventId,
          expect.anything()
        );
      });
    });

    describe("when there is a split for tomorrow and it is not in our database", () => {
      const TODAY = new Date("2024-01-10");
      const SPLIT_RATIO = "3.000000/1.000000";

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");
        Date.now = jest.fn(() => +TODAY);
        jest
          .spyOn(eodService, "getStockSplits")
          .mockImplementation(
            (exchange: investmentUniverseConfig.ExchangeType, date: Date): Promise<StockSplitType[]> => {
              if (exchange === "US" && DateUtil.datesAreEqual(date, TODAY)) {
                return Promise.resolve([{ code: "SONY", exchange: "US", date: "2024-01-10", split: SPLIT_RATIO }]);
              } else return Promise.resolve([]);
            }
          );

        await CorporateEventCronService.createAllStockSplits();
      });
      afterAll(async () => await clearDb());

      it("should add a corporate event in our database", async () => {
        const split = await StockSplitCorporateEvent.findOne();
        expect(split?.toObject()).toEqual(
          expect.objectContaining({
            category: "StockSplitCorporateEvent",
            date: TODAY,
            asset: "equities_sony",
            splitRatio: SPLIT_RATIO
          })
        );
      });

      it("should emit a stock split creation event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(events.corporateEvents.stockSplitCreation.eventId, {
          assetName: "SONY",
          date: TODAY
        });
      });
    });

    describe("when the method is called twice in a row", () => {
      const TODAY = new Date("2024-01-10");
      const SPLIT_RATIO = "3.000000/1.000000";

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");
        Date.now = jest.fn(() => +TODAY);
        jest
          .spyOn(eodService, "getStockSplits")
          .mockImplementation(
            (exchange: investmentUniverseConfig.ExchangeType, date: Date): Promise<StockSplitType[]> => {
              if (exchange === "US" && DateUtil.datesAreEqual(date, TODAY)) {
                return Promise.resolve([{ code: "SONY", exchange: "US", date: "2024-01-10", split: SPLIT_RATIO }]);
              } else return Promise.resolve([]);
            }
          );

        await CorporateEventCronService.createAllStockSplits();
        await CorporateEventCronService.createAllStockSplits();
      });
      afterAll(async () => await clearDb());

      it("should add only ONE corporate event in our database", async () => {
        const splits = await StockSplitCorporateEvent.find();
        expect(splits.length).toBe(1);
      });

      it("should only emit an event once", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe("createAllAssetIsinChanges", () => {
    const _overrideBuildStockFundamentalsResponse = (
      overrides: Partial<EodStockFundamentalsResponseType["General"]> = {}
    ): EodStockFundamentalsResponseType => {
      const defaultValues: EodStockFundamentalsResponseType["General"] = {
        Exchange: "NASDAQ",
        GicIndustry: "Software",
        WebURL: "https://www.microsoft.com",
        FullTimeEmployees: 22000,
        ISIN: "US5949181046", //original: US5949181045
        AddressData: {
          City: "Redmond",
          State: "WA",
          Country: "United States"
        },
        Description:
          "Microsoft Corporation develops and supports software, services, devices and solutions worldwide.",
        Officers: {
          0: {
            Name: "Mr. Satya  Nadella",
            Title: "Chairman & CEO"
          }
        },
        LogoURL: "/img/logos/US/MSFT.png"

        // Add other default fields as needed
      };

      return buildStockFundamentalsResponse({
        General: { ...defaultValues, ...overrides }
      });
    };

    describe("when there are no assets with changed ISIN", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");

        await buildInvestmentProduct(false, { assetId: "equities_microsoft", listed: true });

        const microsoftFundamentalData = buildStockFundamentalsResponse();
        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", microsoftFundamentalData);

        await CorporateEventCronService.createAllAssetIsinChanges();
      });
      afterAll(async () => {
        await clearDb();
        await RedisClientService.Instance.del("eod:fundamentals:equities_microsoft");
      });

      it("should NOT add a corporate event in our database", async () => {
        const split = await AssetIsinChangeCorporateEvent.findOne();
        expect(split).toBeNull();
      });

      it("should NOT emit an event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.corporateEvents.assetIsinChange.eventId,
          expect.anything()
        );
      });
    });

    describe("when there is an asset with changed ISIN", () => {
      const TODAY = new Date("2024-01-10");

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");
        Date.now = jest.fn(() => +TODAY);

        await buildInvestmentProduct(false, { assetId: "equities_microsoft", listed: true });

        const microsoftFundamentalData = _overrideBuildStockFundamentalsResponse();
        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", microsoftFundamentalData);

        await CorporateEventCronService.createAllAssetIsinChanges();
      });
      afterAll(async () => {
        await clearDb();
        await RedisClientService.Instance.del("eod:fundamentals:equities_microsoft");
      });
      it("should add a corporate event in our database", async () => {
        const IsinChangeEvent = await AssetIsinChangeCorporateEvent.findOne();
        expect(IsinChangeEvent?.toObject()).toEqual(
          expect.objectContaining({
            asset: "equities_microsoft"
          })
        );
      });

      it("should emit an event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(events.corporateEvents.assetIsinChange.eventId, {
          assetName: "Microsoft",
          date: TODAY,
          oldISIN: "US5949181045",
          newISIN: "US5949181046"
        });
      });
    });

    describe("when there is an asset with changed ISIN on eod", () => {
      const TODAY = new Date("2024-01-10");

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");
        Date.now = jest.fn(() => +TODAY);

        await buildInvestmentProduct(false, { assetId: "equities_microsoft", listed: true });

        const microsoftFundamentalData = _overrideBuildStockFundamentalsResponse();
        jest.spyOn(eodService, "getAssetFundamentalsData").mockResolvedValue(microsoftFundamentalData);

        await CorporateEventCronService.createAllAssetIsinChanges();
      });
      afterAll(async () => await clearDb());

      it("should add a corporate event in our database", async () => {
        const IsinChangeEvent = await AssetIsinChangeCorporateEvent.findOne();
        expect(IsinChangeEvent?.toObject()).toEqual(
          expect.objectContaining({
            asset: "equities_microsoft"
          })
        );
      });

      it("should emit an event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(events.corporateEvents.assetIsinChange.eventId, {
          assetName: "Microsoft",
          date: TODAY,
          oldISIN: "US5949181045",
          newISIN: "US5949181046"
        });
      });
    });

    describe("when there is an asset with changed ISIN, but there already exists a db entry for it with the SAME oldISIN & newISIN", () => {
      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");

        const assetIsinChangeData: AssetIsinChangeCorporateEventDTOInterface = {
          asset: "equities_microsoft",
          oldISIN: "US5949181045",
          newISIN: "US5949181046",
          date: new Date()
        };

        await buildAssetIsinChangeCorporateEvent(assetIsinChangeData);
        await buildInvestmentProduct(false, { assetId: "equities_microsoft", listed: true });

        const microsoftFundamentalData = _overrideBuildStockFundamentalsResponse();
        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", microsoftFundamentalData);

        await CorporateEventCronService.createAllAssetIsinChanges();
      });
      afterAll(async () => {
        await clearDb();
        await RedisClientService.Instance.del("eod:fundamentals:equities_microsoft");
      });

      it("should NOT add another corporate event in our database", async () => {
        const IsinChangeEvents = await AssetIsinChangeCorporateEvent.find();
        expect(IsinChangeEvents?.length).toBe(1);
      });

      it("should NOT emit an event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.corporateEvents.assetIsinChange.eventId,
          expect.anything()
        );
      });
    });

    describe("when there is an asset with changed ISIN, but there already exists a db entry for it with DIFFERENT oldISIN & newISIN ", () => {
      const TODAY = new Date("2024-01-10");

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");
        Date.now = jest.fn(() => +TODAY);

        const assetIsinChangeData: AssetIsinChangeCorporateEventDTOInterface = {
          asset: "equities_microsoft",
          oldISIN: "US5949181045",
          newISIN: "US5949181046",
          date: new Date()
        };

        await buildAssetIsinChangeCorporateEvent(assetIsinChangeData);
        await buildInvestmentProduct(false, { assetId: "equities_microsoft", listed: true });

        const microsoftFundamentalData = _overrideBuildStockFundamentalsResponse({ ISIN: "US5949181047" });
        await RedisClientService.Instance.set("eod:fundamentals:equities_microsoft", microsoftFundamentalData);

        await CorporateEventCronService.createAllAssetIsinChanges();
      });
      afterAll(async () => {
        await clearDb();
        await RedisClientService.Instance.del("eod:fundamentals:equities_microsoft");
      });

      it("should add a SECOND corporate event in our database", async () => {
        const IsinChangeEvents = await AssetIsinChangeCorporateEvent.find();
        expect(IsinChangeEvents.length).toBe(2);
        expect(IsinChangeEvents).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              asset: "equities_microsoft",
              oldISIN: "US5949181045",
              newISIN: "US5949181046"
            }),
            expect.objectContaining({
              asset: "equities_microsoft",
              oldISIN: "US5949181045",
              newISIN: "US5949181047"
            })
          ])
        );
      });

      it("should emit an event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(events.corporateEvents.assetIsinChange.eventId, {
          assetName: "Microsoft",
          date: TODAY,
          oldISIN: "US5949181045",
          newISIN: "US5949181047"
        });
      });
    });

    describe("when the method is called twice in a row", () => {
      const TODAY = new Date("2024-01-10");

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");
        Date.now = jest.fn(() => +TODAY);

        await buildInvestmentProduct(false, { assetId: "equities_microsoft", listed: true });

        const microsoftFundamentalData = _overrideBuildStockFundamentalsResponse();

        jest.spyOn(eodService, "getAssetFundamentalsData").mockResolvedValue(microsoftFundamentalData);

        await CorporateEventCronService.createAllAssetIsinChanges();
        await CorporateEventCronService.createAllAssetIsinChanges();
      });
      afterAll(async () => await clearDb());

      it("should add only ONE corporate event in our database", async () => {
        const splits = await AssetIsinChangeCorporateEvent.find();
        expect(splits.length).toBe(1);
      });

      it("should only emit an event once", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledTimes(1);
      });
    });
  });
});
