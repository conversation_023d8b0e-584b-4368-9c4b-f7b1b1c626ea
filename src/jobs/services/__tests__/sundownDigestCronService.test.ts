import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import SundownDigestCronService from "../sundownDigestCronService";
import StockNewsService from "../../../external-services/stockNewsService";
import eodService from "../../../external-services/eodService";
import { faker } from "@faker-js/faker";
import { SundownDigest } from "../../../models/SundownDigest";
import { buildSundownDigest } from "../../../tests/utils/generateModels";
import { publicInvestmentUniverseConfig } from "@wealthyhood/shared-configs";

describe("SundownDigestCronService", () => {
  beforeAll(async () => await connectDb("SundownDigestCronService"));
  afterAll(async () => await closeDb());

  describe("createAllSundownDigests", () => {
    describe("when stock news has a sundown digest we have already ingested", () => {
      const STOCK_NEWS_TEXT =
        "As the sun sets on another bustling day in the world of finance, let\u2019s unpack the notable moves that shaped the markets today. In a surprising turn of events, Nordstrom (JWN) exceeded Wall Street\u2019s expectations.";
      const STOCK_NEWS_ID = faker.number.int();

      beforeAll(async () => {
        jest.resetAllMocks();

        await buildSundownDigest({
          content: STOCK_NEWS_TEXT,
          providers: {
            stockNews: {
              id: STOCK_NEWS_ID.toString()
            }
          }
        });

        const sundownDigests = await SundownDigest.find();
        expect(sundownDigests.length).toBe(1);

        jest.spyOn(StockNewsService, "getSundownDigests").mockResolvedValue([
          {
            id: STOCK_NEWS_ID,
            text: STOCK_NEWS_TEXT,
            date: "2024-11-27T23:46:16.000000Z"
          }
        ]);

        await SundownDigestCronService.createAllSundownDigests();
      });
      afterAll(async () => await clearDb());

      it("should NOT create a new sundown digest document", async () => {
        const sundownDigests = await SundownDigest.find();
        expect(sundownDigests.length).toBe(1);
      });
    });

    describe("when stock news has a sundown digest we have not ingested", () => {
      const STOCK_NEWS_TEXT =
        "As the sun sets on another bustling day in the world of finance, let's unpack the notable moves that shaped the markets today. In a surprising turn of events, Nordstrom (JWN) exceeded Wall Street's expectations.";
      const STOCK_NEWS_ID = faker.number.int();
      const STOCK_NEWS_DATE = "2024-11-27T23:46:16.000Z";

      const expectedFormattedContent = {
        overview: "GM sales up, Intel new CEO, Palantir faces pressure.",
        sections: [
          {
            companyName: "General Motors",
            companyTicker: "GM",
            assetId: "equities_general_motors",
            assetReturnPercentage: -0.0439,
            title: "Auto Industry Q1 Performance",
            content:
              "GM leads auto sector with 17% increase in U.S. Q1 sales, driven by strong pickup and SUV demand."
          },
          {
            companyName: "Intel Corporation",
            companyTicker: "INTC",
            assetId: "equities_intel",
            assetReturnPercentage: -0.0766,
            title: "Intel Welcomes New CEO",
            content: "Intel appoints Lip-Bu Tan as new CEO following Pat Gelsinger's departure."
          },
          {
            companyName: "Palantir Technologies",
            companyTicker: "PLTR",
            assetId: "equities_palantir_technologies",
            assetReturnPercentage: -0.0372,
            title: "Palantir Stock Faces Pressure",
            content: "Palantir experiences share price decline."
          }
        ]
      };

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(StockNewsService, "getSundownDigests").mockResolvedValue([
          {
            id: STOCK_NEWS_ID,
            text: STOCK_NEWS_TEXT,
            date: STOCK_NEWS_DATE
          }
        ]);

        jest.spyOn(eodService, "getHistoricalPrices").mockImplementation(async (assetId) => {
          if (assetId === ("equities_general_motors" as publicInvestmentUniverseConfig.PublicAssetType)) {
            // Return percentage should be EXACTLY -0.0439
            return [
              { close: 100, date: "2024-11-26" },
              { close: 95.61, date: "2024-11-27" } // Exactly 4.39% decrease
            ];
          }
          if (assetId === ("equities_intel" as publicInvestmentUniverseConfig.PublicAssetType)) {
            // Return percentage = -0.0766 = (27.702 - 30.0) / 30.0
            return [
              { close: 30.0, date: "2024-11-26" },
              { close: 27.702, date: "2024-11-27" } // 7.66% decrease
            ];
          }
          if (assetId === ("equities_palantir_technologies" as publicInvestmentUniverseConfig.PublicAssetType)) {
            // Return percentage = -0.0372 = (24.07 - 25.0) / 25.0
            return [
              { close: 25.0, date: "2024-11-26" },
              { close: 24.07, date: "2024-11-27" } // 3.72% decrease
            ];
          }

          // Default fallback for any other assets
          return [
            { close: 100, date: "2024-11-26" },
            { close: 100, date: "2024-11-27" } // 0% change
          ];
        });

        await SundownDigestCronService.createAllSundownDigests();
      });
      afterAll(async () => await clearDb());

      it("should create a new sundown digest document with correct formattedContent", async () => {
        const sundownDigests = await SundownDigest.find();
        expect(sundownDigests.length).toBe(1);
        expect(sundownDigests[0].toObject()).toMatchObject(
          expect.objectContaining({
            content:
              "As the sun sets on another bustling day in the world of finance, let's unpack the notable moves that shaped the markets today. In a surprising turn of events, Nordstrom **(JWN)** exceeded Wall Street's expectations.",
            date: new Date(STOCK_NEWS_DATE),
            providers: {
              stockNews: {
                id: STOCK_NEWS_ID.toString()
              }
            },
            formattedContent: expect.objectContaining(expectedFormattedContent)
          })
        );
      });
    });

    describe("when stock news has a sundown digest we have not ingested and the method is called twice", () => {
      const STOCK_NEWS_TEXT =
        "As the sun sets on another bustling day in the world of finance, let\u2019s unpack the notable moves that shaped the markets today. In a surprising turn of events, Nordstrom (JWN) exceeded Wall Street\u2019s expectations.";
      const STOCK_NEWS_ID = faker.number.int();
      const STOCK_NEWS_DATE = "2024-11-27T23:46:16.000000Z";

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(StockNewsService, "getSundownDigests").mockResolvedValue([
          {
            id: STOCK_NEWS_ID,
            text: STOCK_NEWS_TEXT,
            date: STOCK_NEWS_DATE
          }
        ]);

        await SundownDigestCronService.createAllSundownDigests();
        await SundownDigestCronService.createAllSundownDigests();
      });
      afterAll(async () => await clearDb());

      it("should create only ONE sundown digest document", async () => {
        const sundownDigests = await SundownDigest.find();
        expect(sundownDigests.length).toBe(1);
      });
    });
  });
});
