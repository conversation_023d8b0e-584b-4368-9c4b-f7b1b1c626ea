import "jest";
import { connectDb, clearDb, closeDb, createSqliteDb, clearSqliteDb } from "../../../../tests/utils/db";
import AccountingCronService from "../../accountingCronService";
import AccountingLedgerStorageService from "../../../../external-services/accountingLedgerStorageService";
import ProviderService from "../../../../services/providerService";
import { CASH_ACCOUNTS_WK_MAPPING } from "../../../../configs/accountingConfig";
import { LedgerAccounts } from "../../../../types/accounting";
import DateUtil from "../../../../utils/dateUtil";

// Mock external services that make HTTP calls
jest.mock("../../../../services/providerService");
jest.mock("../../../../external-services/loggerService");

const mockProviderService = ProviderService as jest.Mocked<typeof ProviderService>;

describe("AccountingCronService.captureWealthyhoodAccountsCashBalanceSnapshots", () => {
  const TODAY = "2025-01-15";

  beforeAll(async () => {
    Date.now = jest.fn(() => +new Date(TODAY));
    await connectDb("captureWealthyhoodAccountsCashBalanceSnapshots");
    await createSqliteDb();
  });

  afterAll(async () => await closeDb());

  beforeEach(async () => {
    await clearDb();
    await clearSqliteDb();
    jest.clearAllMocks();
  });

  it("should successfully capture and store cash balance snapshots for all configured accounts", async () => {
    // Mock the brokerage service to return cash balances
    const mockBrokerageService = {
      listCashBalances: jest.fn()
    };

    mockProviderService.getBrokerageService.mockReturnValue(mockBrokerageService as any);

    // Mock cash balances for different accounts
    const mockBalances = [
      { value: { currency: "EUR", amount: 1250.75 } },
      { value: { currency: "USD", amount: 500.25 } }
    ];

    mockBrokerageService.listCashBalances.mockResolvedValue(mockBalances);

    // Execute the method
    await AccountingCronService.captureWealthyhoodAccountsCashBalanceSnapshots();

    // Verify that cash balance snapshots were stored
    const storedSnapshots = await AccountingLedgerStorageService.queryCashBalances({
      toDate: DateUtil.getYearAndMonthAndDay(new Date(Date.now()))
    });

    expect(storedSnapshots).toBeDefined();

    // Check that we captured snapshots for all configured accounts
    const configuredAccountsCount = Object.values(CASH_ACCOUNTS_WK_MAPPING).filter(
      (portfolioId) => portfolioId !== undefined
    ).length;

    expect(storedSnapshots.length).toBe(configuredAccountsCount);

    // Verify that each snapshot has the correct structure
    storedSnapshots.forEach((snapshot) => {
      expect(snapshot.accountCode).toBeDefined();
      expect(typeof snapshot.balance).toBe("number");
      expect(snapshot.asOfDate).toBe(DateUtil.getYearAndMonthAndDay(new Date(Date.now())));
    });

    // Verify that getBrokerageService was called for each configured account
    expect(mockProviderService.getBrokerageService).toHaveBeenCalledTimes(configuredAccountsCount);
  });

  it("should handle portfolios with no EUR balance gracefully", async () => {
    const mockBrokerageService = {
      listCashBalances: jest.fn()
    };

    mockProviderService.getBrokerageService.mockReturnValue(mockBrokerageService as any);

    // Mock balances with no EUR currency
    const mockBalancesNoEUR = [
      { value: { currency: "USD", amount: 500.25 } },
      { value: { currency: "GBP", amount: 200.5 } }
    ];

    mockBrokerageService.listCashBalances.mockResolvedValue(mockBalancesNoEUR);

    // Execute the method
    await AccountingCronService.captureWealthyhoodAccountsCashBalanceSnapshots();

    // Should still store snapshots with 0 balance for accounts without EUR
    const storedSnapshots = await AccountingLedgerStorageService.queryCashBalances({
      toDate: DateUtil.getYearAndMonthAndDay(new Date(Date.now()))
    });

    expect(storedSnapshots).toBeDefined();

    // All snapshots should have 0 balance since no EUR was found
    storedSnapshots.forEach((snapshot) => {
      expect(snapshot.balance).toBe(0);
    });
  });

  it("should handle provider service failures gracefully", async () => {
    const mockBrokerageService = {
      listCashBalances: jest.fn()
    };

    mockProviderService.getBrokerageService.mockReturnValue(mockBrokerageService as any);

    // Mock provider service to throw an error
    mockBrokerageService.listCashBalances.mockRejectedValue(new Error("Provider service unavailable"));

    // Execute the method - should not throw but handle error gracefully
    await expect(AccountingCronService.captureWealthyhoodAccountsCashBalanceSnapshots()).resolves.not.toThrow();

    // Verify that no snapshots were stored due to the error
    const storedSnapshots = await AccountingLedgerStorageService.queryCashBalances({
      toDate: DateUtil.getYearAndMonthAndDay(new Date(Date.now()))
    });

    expect(storedSnapshots).toEqual([]);
  });

  it("should store snapshots with correct account codes from configuration", async () => {
    const mockBrokerageService = {
      listCashBalances: jest.fn()
    };

    mockProviderService.getBrokerageService.mockReturnValue(mockBrokerageService as any);

    const mockBalances = [{ value: { currency: "EUR", amount: 750.25 } }];

    mockBrokerageService.listCashBalances.mockResolvedValue(mockBalances);

    // Execute the method
    await AccountingCronService.captureWealthyhoodAccountsCashBalanceSnapshots();

    // Verify stored snapshots have correct account codes
    const storedSnapshots = await AccountingLedgerStorageService.queryCashBalances({
      toDate: DateUtil.getYearAndMonthAndDay(new Date(Date.now()))
    });

    expect(storedSnapshots).toBeDefined();

    // Check that all stored account codes exist in the configuration
    const validAccountCodes = Object.keys(CASH_ACCOUNTS_WK_MAPPING) as LedgerAccounts[];

    storedSnapshots.forEach((snapshot) => {
      expect(validAccountCodes).toContain(snapshot.accountCode);
    });
  });

  it("should handle empty configuration gracefully", async () => {
    // Temporarily override the configuration to be empty
    const originalConfig = { ...CASH_ACCOUNTS_WK_MAPPING };
    Object.keys(CASH_ACCOUNTS_WK_MAPPING).forEach((key) => {
      (CASH_ACCOUNTS_WK_MAPPING as any)[key] = undefined;
    });

    try {
      // Execute the method
      await AccountingCronService.captureWealthyhoodAccountsCashBalanceSnapshots();

      // Should complete without errors even with empty configuration
      const storedSnapshots = await AccountingLedgerStorageService.queryCashBalances({
        toDate: DateUtil.getYearAndMonthAndDay(new Date(Date.now()))
      });

      expect(storedSnapshots).toEqual([]);
    } finally {
      // Restore original configuration
      Object.assign(CASH_ACCOUNTS_WK_MAPPING, originalConfig);
    }
  });
});
