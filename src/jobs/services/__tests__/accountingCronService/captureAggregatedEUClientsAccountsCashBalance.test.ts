import "jest";
import { connectDb, clearDb, closeDb, createSqliteDb, clearSqliteDb } from "../../../../tests/utils/db";
import { buildUser, buildPortfolio } from "../../../../tests/utils/generateModels";
import AccountingCronService from "../../accountingCronService";
import AccountingLedgerStorageService from "../../../../external-services/accountingLedgerStorageService";
import ProviderService from "../../../../services/providerService";

import { LedgerAccounts } from "../../../../types/accounting";
import DateUtil from "../../../../utils/dateUtil";
import { UserDocument } from "../../../../models/User";
import { entitiesConfig } from "@wealthyhood/shared-configs";

// Mock external services that make HTTP calls
jest.mock("../../../../services/providerService");
jest.mock("../../../../external-services/loggerService");

const mockProviderService = ProviderService as jest.Mocked<typeof ProviderService>;

describe("AccountingCronService.captureAggregatedEUClientsAccountsCashBalance", () => {
  const TODAY = "2025-01-15";

  beforeAll(async () => {
    Date.now = jest.fn(() => +new Date(TODAY));
    await connectDb("captureAggregatedEUClientsAccountsCashBalance");
    await createSqliteDb();
  });

  afterAll(async () => await closeDb());

  beforeEach(async () => {
    await clearDb();
    await clearSqliteDb();
    jest.clearAllMocks();
  });

  it("should successfully aggregate cash balances from multiple client portfolios and store snapshot", async () => {
    // Create test users and portfolios
    const user1: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const user2: UserDocument = await buildUser({
      residencyCountry: "DE",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });

    // Create portfolios with WealthKernel integration
    await buildPortfolio({
      owner: user1.id,
      currency: "EUR",
      providers: {
        wealthkernel: {
          id: "wk-portfolio-1",
          status: "Active"
        }
      }
    });

    await buildPortfolio({
      owner: user2.id,
      currency: "EUR",
      providers: {
        wealthkernel: {
          id: "wk-portfolio-2",
          status: "Active"
        }
      }
    });

    // Mock the brokerage service to return different balances for each portfolio
    const mockBrokerageService = {
      listCashBalances: jest.fn()
    };

    mockProviderService.getBrokerageService.mockReturnValue(mockBrokerageService as any);

    // Mock different cash balances for each portfolio
    mockBrokerageService.listCashBalances
      .mockResolvedValueOnce([{ value: { currency: "EUR", amount: 1250.75 } }]) // Portfolio 1
      .mockResolvedValueOnce([{ value: { currency: "EUR", amount: 2550.5 } }]); // Portfolio 2

    // Execute the method
    const totalBalance = await AccountingCronService.captureAggregatedEUClientsAccountsCashBalance();

    // Verify the returned total balance
    const expectedTotal = 1250.75 + 2550.5;
    expect(totalBalance).toBe(expectedTotal);

    // Verify that aggregated balance snapshot was stored in the ledger
    const storedSnapshots = await AccountingLedgerStorageService.queryCashBalances({
      accountCodes: [LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS],
      toDate: DateUtil.getYearAndMonthAndDay(new Date(Date.now()))
    });

    expect(storedSnapshots).toHaveLength(1);
    expect(storedSnapshots[0].accountCode).toBe(LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS);
    expect(storedSnapshots[0].balance).toBe(expectedTotal);
    expect(storedSnapshots[0].asOfDate).toBe(DateUtil.getYearAndMonthAndDay(new Date(Date.now())));

    // Verify that brokerage service was called for each portfolio
    expect(mockBrokerageService.listCashBalances).toHaveBeenCalledTimes(2);
    expect(mockBrokerageService.listCashBalances).toHaveBeenCalledWith("wk-portfolio-1");
    expect(mockBrokerageService.listCashBalances).toHaveBeenCalledWith("wk-portfolio-2");
  });

  it("should handle portfolios with no EUR balance by treating them as zero", async () => {
    // Create test user and portfolio
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });

    await buildPortfolio({
      owner: user.id,
      currency: "EUR",
      providers: {
        wealthkernel: {
          id: "wk-portfolio-no-eur",
          status: "Active"
        }
      }
    });

    // Mock brokerage service with no EUR balance
    const mockBrokerageService = {
      listCashBalances: jest.fn()
    };

    mockProviderService.getBrokerageService.mockReturnValue(mockBrokerageService as any);

    mockBrokerageService.listCashBalances.mockResolvedValue([
      { value: { currency: "USD", amount: 500.25 } },
      { value: { currency: "GBP", amount: 200.5 } }
    ]);

    // Execute the method
    const totalBalance = await AccountingCronService.captureAggregatedEUClientsAccountsCashBalance();

    // Should return 0 since no EUR balances were found
    expect(totalBalance).toBe(0);

    // Verify snapshot was still stored with 0 balance
    const storedSnapshots = await AccountingLedgerStorageService.queryCashBalances({
      accountCodes: [LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS],
      toDate: DateUtil.getYearAndMonthAndDay(new Date(Date.now()))
    });

    expect(storedSnapshots).toHaveLength(1);
    expect(storedSnapshots[0].balance).toBe(0);
  });

  it("should only include active EUR portfolios with WealthKernel integration", async () => {
    // Create various portfolio configurations
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });

    // Active EUR portfolio with WealthKernel - should be included
    await buildPortfolio({
      owner: user.id,
      currency: "EUR",
      providers: {
        wealthkernel: {
          id: "wk-active-eur",
          status: "Active"
        }
      }
    });

    // Inactive EUR portfolio - should be excluded
    await buildPortfolio({
      owner: user.id,
      currency: "EUR",
      providers: {
        wealthkernel: {
          id: "wk-inactive-eur",
          status: "Closed"
        }
      }
    });

    // USD portfolio (active) - should be excluded
    await buildPortfolio({
      owner: user.id,
      currency: "USD",
      providers: {
        wealthkernel: {
          id: "wk-active-usd",
          status: "Active"
        }
      }
    });

    // EUR portfolio without WealthKernel - should be excluded
    await buildPortfolio({
      owner: user.id,
      currency: "EUR"
    });

    // Mock brokerage service
    const mockBrokerageService = {
      listCashBalances: jest.fn()
    };

    mockProviderService.getBrokerageService.mockReturnValue(mockBrokerageService as any);
    mockBrokerageService.listCashBalances.mockResolvedValue([{ value: { currency: "EUR", amount: 750.25 } }]);

    // Execute the method
    const totalBalance = await AccountingCronService.captureAggregatedEUClientsAccountsCashBalance();

    // Should only process the one active EUR portfolio with WealthKernel
    expect(totalBalance).toBe(750.25);
    expect(mockBrokerageService.listCashBalances).toHaveBeenCalledTimes(1);
    expect(mockBrokerageService.listCashBalances).toHaveBeenCalledWith("wk-active-eur");
  });

  it("should handle provider service failures gracefully", async () => {
    // Create test portfolio
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });

    await buildPortfolio({
      owner: user.id,
      currency: "EUR",
      providers: {
        wealthkernel: {
          id: "wk-portfolio-error",
          status: "Active"
        }
      }
    });

    // Mock brokerage service to throw error
    const mockBrokerageService = {
      listCashBalances: jest.fn()
    };

    mockProviderService.getBrokerageService.mockReturnValue(mockBrokerageService as any);
    mockBrokerageService.listCashBalances.mockRejectedValue(new Error("WealthKernel API unavailable"));

    // Execute the method - should handle error gracefully and return undefined
    const totalBalance = await AccountingCronService.captureAggregatedEUClientsAccountsCashBalance();

    expect(totalBalance).toBeUndefined();

    // Should not store any snapshot due to error
    const storedSnapshots = await AccountingLedgerStorageService.queryCashBalances({
      accountCodes: [LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS],
      toDate: DateUtil.getYearAndMonthAndDay(new Date(Date.now()))
    });

    expect(storedSnapshots).toHaveLength(0);
  });

  it("should handle scenario with no eligible portfolios", async () => {
    // Don't create any portfolios - empty database

    // Mock brokerage service (shouldn't be called)
    const mockBrokerageService = {
      listCashBalances: jest.fn()
    };

    mockProviderService.getBrokerageService.mockReturnValue(mockBrokerageService as any);

    // Execute the method
    const totalBalance = await AccountingCronService.captureAggregatedEUClientsAccountsCashBalance();

    // Should return 0 for no portfolios
    expect(totalBalance).toBe(0);

    // Should store snapshot with 0 balance
    const storedSnapshots = await AccountingLedgerStorageService.queryCashBalances({
      accountCodes: [LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS],
      toDate: DateUtil.getYearAndMonthAndDay(new Date(Date.now()))
    });

    expect(storedSnapshots).toHaveLength(1);
    expect(storedSnapshots[0].balance).toBe(0);

    // Brokerage service should not be called
    expect(mockBrokerageService.listCashBalances).not.toHaveBeenCalled();
  });

  it("should aggregate multiple portfolios with mixed positive and zero balances", async () => {
    // Create multiple portfolios with different balance scenarios
    const user1: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const user2: UserDocument = await buildUser({
      residencyCountry: "DE",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const user3: UserDocument = await buildUser({
      residencyCountry: "FR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });

    await buildPortfolio({
      owner: user1.id,
      currency: "EUR",
      providers: { wealthkernel: { id: "wk-portfolio-1", status: "Active" } }
    });

    await buildPortfolio({
      owner: user2.id,
      currency: "EUR",
      providers: { wealthkernel: { id: "wk-portfolio-2", status: "Active" } }
    });

    await buildPortfolio({
      owner: user3.id,
      currency: "EUR",
      providers: { wealthkernel: { id: "wk-portfolio-3", status: "Active" } }
    });

    // Mock brokerage service with mixed results
    const mockBrokerageService = {
      listCashBalances: jest.fn()
    };

    mockProviderService.getBrokerageService.mockReturnValue(mockBrokerageService as any);

    mockBrokerageService.listCashBalances
      .mockResolvedValueOnce([{ value: { currency: "EUR", amount: 500.0 } }]) // Portfolio 1: positive
      .mockResolvedValueOnce([{ value: { currency: "USD", amount: 100.0 } }]) // Portfolio 2: no EUR (0)
      .mockResolvedValueOnce([{ value: { currency: "EUR", amount: 1250.75 } }]); // Portfolio 3: positive

    // Execute the method
    const totalBalance = await AccountingCronService.captureAggregatedEUClientsAccountsCashBalance();

    // Should aggregate only the EUR balances: 500.00 + 0 + 1250.75 = 1750.75
    expect(totalBalance).toBe(1750.75);

    // Verify snapshot was stored with correct total
    const storedSnapshots = await AccountingLedgerStorageService.queryCashBalances({
      accountCodes: [LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS],
      toDate: DateUtil.getYearAndMonthAndDay(new Date(Date.now()))
    });

    expect(storedSnapshots).toHaveLength(1);
    expect(storedSnapshots[0].balance).toBe(1750.75);
  });
});
