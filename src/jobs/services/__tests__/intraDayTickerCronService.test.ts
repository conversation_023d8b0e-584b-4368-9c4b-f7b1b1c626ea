import { faker } from "@faker-js/faker";
import { investmentUniverseConfig, marketHoursConfig } from "@wealthyhood/shared-configs";
import Decimal from "decimal.js";
import { DateTime } from "luxon";
import events from "../../../event-handlers/events";
import { FMPService, QuoteType } from "../../../external-services/fmpService";
import eventEmitter from "../../../loaders/eventEmitter";
import { RedisClientService } from "../../../loaders/redis";
import {
  IntraDayAssetTicker,
  IntraDayPortfolioTicker,
  IntraDayPortfolioTickerDocument
} from "../../../models/IntraDayTicker";
import { HoldingsType, InitialHoldingsAllocationType, PortfolioDocument } from "../../../models/Portfolio";
import { UserDocument } from "../../../models/User";
import { RoomActionEnum, RoomEnum } from "../../../socket/rooms";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import {
  buildIntraDayAssetTicker,
  buildIntraDayPortfolioTicker,
  buildInvestmentProduct,
  buildPortfolio,
  buildUser
} from "../../../tests/utils/generateModels";
import { PartialRecord } from "../../../types/utils";
import DateUtil from "../../../utils/dateUtil";
import IntraDayTickerCronService from "../intraDayTickerCronService";
import SocketEmitter from "../../../socket/emitter";
import eodService from "../../../external-services/eodService";

const { ASSET_CONFIG } = investmentUniverseConfig;
const { MARKET_TRADING_HOURS } = marketHoursConfig;

const realDateNow = Date.now;

describe("IntraDayTickerCronService", () => {
  beforeAll(async () => await connectDb("IntraDayTickerCronService"));
  afterEach(async () => await clearDb());
  afterAll(async () => await closeDb());

  describe("cacheTodaysAssetPrices", () => {
    afterEach(() => {
      jest.resetAllMocks();
      Date.now = realDateNow;
    });

    it("should cache EOD only today's tickers in open ETF market hours even if EOD returns less assets than requested", async () => {
      const PRICE_EU_INCOME = faker.number.int({ min: 1, max: 1000 });
      const PRICE = faker.number.int({ min: 1, max: 1000 });

      // 12:30 London time
      const londonMarketTime = DateTime.utc(2024, 6, 26)
        .setZone(MARKET_TRADING_HOURS.LSE.timeZone)
        .set({
          hour: 12,
          minute: 30
        })
        .toJSDate()
        .valueOf();
      Date.now = jest.fn(() => londonMarketTime);

      // set conditions
      jest.spyOn(eodService, "getRealTimeAssetsData").mockImplementation(
        async (
          assetCommonIds: string[]
        ): Promise<
          {
            assetCommonId: investmentUniverseConfig.AssetType;
            close: number;
            timestamp: number;
          }[]
        > => {
          return assetCommonIds
            .map((assetCommonId) => {
              if (assetCommonId === "equities_france") {
                return undefined;
              }

              return {
                assetCommonId: assetCommonId as investmentUniverseConfig.AssetType,
                close: ["equities_eu_income"].includes(assetCommonId) ? PRICE_EU_INCOME : PRICE,
                timestamp: new Decimal(new Date(Date.now()).getTime()).floor().toNumber()
              };
            })
            .filter((asset) => asset !== undefined);
        }
      );

      // run method
      await IntraDayTickerCronService.cacheTodaysAssetPrices();

      // assertions
      const latestTickers =
        await RedisClientService.Instance.get<
          PartialRecord<investmentUniverseConfig.AssetType, { close: number; timestamp: number }>
        >("fmp:today:latest_tickers");
      expect(Object.entries(latestTickers)).toEqual(
        expect.arrayContaining([
          [
            "equities_eu_income",
            {
              close: PRICE_EU_INCOME,
              timestamp: Decimal.div(new Date(Date.now()).getTime(), 1000).floor().mul(1000).toNumber()
            }
          ]
        ])
      );

      expect(Object.keys(latestTickers)).not.toContain("equities_france");
    });

    it("should cache EOD only today's tickers in open ETF market hours", async () => {
      const PRICE = faker.number.int({ min: 1, max: 1000 });

      // 12:30 London time
      const londonMarketTime = DateTime.utc(2024, 6, 26)
        .setZone(MARKET_TRADING_HOURS.LSE.timeZone)
        .set({
          hour: 12,
          minute: 30
        })
        .toJSDate()
        .valueOf();
      Date.now = jest.fn(() => londonMarketTime);

      // set conditions
      jest.spyOn(eodService, "getRealTimeAssetsData").mockImplementation(
        async (
          assetCommonIds: string[]
        ): Promise<
          {
            assetCommonId: investmentUniverseConfig.AssetType;
            close: number;
            timestamp: number;
          }[]
        > => {
          return assetCommonIds.map((assetCommonId) => {
            return {
              assetCommonId: assetCommonId as investmentUniverseConfig.AssetType,
              close: PRICE,
              timestamp: ["equities_eu_income"].includes(assetCommonId)
                ? new Decimal(new Date(Date.now()).getTime()).floor().toNumber()
                : new Decimal(DateUtil.getDateOfDaysAgo(new Date(Date.now()), 3).getTime()).floor().toNumber()
            };
          });
        }
      );

      // run method
      await IntraDayTickerCronService.cacheTodaysAssetPrices();

      // assertions
      const latestTickers =
        await RedisClientService.Instance.get<
          PartialRecord<investmentUniverseConfig.AssetType, { close: number; timestamp: number }>
        >("fmp:today:latest_tickers");
      expect(Object.entries(latestTickers)).toEqual(
        expect.arrayContaining([
          [
            "equities_eu_income",
            {
              close: PRICE,
              timestamp: Decimal.div(new Date(Date.now()).getTime(), 1000).floor().mul(1000).toNumber()
            }
          ]
        ])
      );

      // stocks will not be cached due to being in closed markets.
      expect(Object.keys(latestTickers)).not.toContain("equities_tesla");
      // skip deprecated assets
      expect(Object.keys(latestTickers)).not.toContain("equities_blackrock_deprecated_1");
    });

    it("should cache today's tickers in open stock market hours and only for non-deprecated assets", async () => {
      const PRICE = faker.number.int({ min: 1, max: 1000 });

      // 15:30 New York time - 20:30 London time
      const newYorkMarketTime = DateTime.utc(2024, 6, 26)
        .setZone(MARKET_TRADING_HOURS.US.timeZone)
        .set({
          hour: 15,
          minute: 30
        })
        .toJSDate()
        .valueOf();
      Date.now = jest.fn(() => newYorkMarketTime);

      // set conditions
      jest
        .spyOn(FMPService.Instance, "getBatchQuote")
        .mockImplementation(async (symbols: string[]): Promise<QuoteType[]> => {
          return symbols.map((symbol) => {
            const assetCommonId = Object.keys(ASSET_CONFIG).find(
              (assetId) => ASSET_CONFIG[assetId as investmentUniverseConfig.AssetType].formalTicker === symbol
            );

            return {
              symbol,
              price: PRICE,
              changePercentage: 0,
              timestamp: ["equities_apple", "equities_microsoft"].includes(assetCommonId)
                ? Decimal.div(new Date(Date.now()).getTime(), 1000).floor().toNumber()
                : Decimal.div(DateUtil.getDateOfDaysAgo(new Date(Date.now()), 3).getTime(), 1000)
                    .floor()
                    .toNumber()
            };
          });
        });

      // run method
      await IntraDayTickerCronService.cacheTodaysAssetPrices();

      // assertions
      const latestTickers =
        await RedisClientService.Instance.get<
          PartialRecord<investmentUniverseConfig.AssetType, { close: number; timestamp: number }>
        >("fmp:today:latest_tickers");
      expect(Object.entries(latestTickers)).toEqual(
        expect.arrayContaining([
          [
            "equities_apple",
            {
              close: PRICE,
              timestamp: Decimal.div(new Date(Date.now()).getTime(), 1000).floor().mul(1000).toNumber()
            }
          ],
          [
            "equities_microsoft",
            {
              close: PRICE,
              timestamp: Decimal.div(new Date(Date.now()).getTime(), 1000).floor().mul(1000).toNumber()
            }
          ]
        ])
      );

      // 15:30 New York time is 20:30 London time so Gold will be skipped from caching
      // due to being in closed markets.
      expect(Object.keys(latestTickers)).not.toContain("commodities_gold");
      // skip deprecated assets
      expect(Object.keys(latestTickers)).not.toContain("equities_blackrock_deprecated_1");
    });
  });

  describe("storeAssetPrices", () => {
    it("should NOT create any ticker documents if the cache is empty", async () => {
      // set conditions
      await RedisClientService.Instance.del("fmp:today:latest_tickers");

      // run method
      await IntraDayTickerCronService.storeAssetPrices();

      // assertions
      const intraDayAssetTickers = await IntraDayAssetTicker.find({});
      expect(intraDayAssetTickers.length).toBe(0);
    });

    it("should NOT create any ticker documents if there are no cached tickers for today", async () => {
      // set conditions
      const ASSET_ID: investmentUniverseConfig.AssetType = "equities_apple";
      const latestTickers: PartialRecord<
        investmentUniverseConfig.AssetType,
        { close: number; timestamp: number }
      > = {
        [ASSET_ID]: {
          close: 1000,
          timestamp: DateUtil.getDateOfDaysAgo(new Date(), 3).getTime()
        }
      };
      await RedisClientService.Instance.set("fmp:today:latest_tickers", latestTickers);
      await buildInvestmentProduct(false, { assetId: ASSET_ID });

      // run method
      await IntraDayTickerCronService.storeAssetPrices();

      // assertions
      const intraDayAssetTickers = await IntraDayAssetTicker.find({});
      expect(intraDayAssetTickers.length).toBe(0);
    });

    it("should NOT create asset ticker documents for today's tickers if asset is not currently traded (and we're past the 50 minute window allowance)", async () => {
      // set conditions
      // 16:55 New York time - 21:55 London time
      const newYorkMarketTime = DateTime.utc(2024, 6, 26)
        .setZone(MARKET_TRADING_HOURS.US.timeZone)
        .set({
          hour: 16,
          minute: 55
        })
        .toJSDate()
        .valueOf();
      Date.now = jest.fn(() => newYorkMarketTime);

      const CONFIG: { assetId: investmentUniverseConfig.AssetType; close: number; timestamp: number }[] = [
        {
          assetId: "equities_apple",
          close: 2000,
          timestamp: new Date(newYorkMarketTime).getTime()
        },
        {
          assetId: "equities_microsoft",
          close: 1000,
          timestamp: new Date(newYorkMarketTime).getTime() - 1
        },
        {
          assetId: "equities_coinbase",
          close: 50,
          timestamp: DateUtil.getDateOfDaysAgo(new Date(newYorkMarketTime), 3).getTime()
        }
      ];
      const latestTickers: PartialRecord<
        investmentUniverseConfig.AssetType,
        { close: number; timestamp: number }
      > = Object.fromEntries(CONFIG.map(({ assetId, close, timestamp }) => [assetId, { close, timestamp }]));
      const FX_RATES = {
        USD: {
          USD: 1,
          EUR: 0.5,
          GBP: 0.5
        },
        EUR: {
          USD: 2,
          EUR: 1,
          GBP: 1
        },
        GBP: {
          USD: 2,
          GBP: 1,
          EUR: 1
        }
      };
      await RedisClientService.Instance.set("fxRates", FX_RATES);
      await RedisClientService.Instance.set("fmp:today:latest_tickers", latestTickers);
      await Promise.all(CONFIG.map(({ assetId }) => buildInvestmentProduct(false, { assetId })));

      // run method
      await IntraDayTickerCronService.storeAssetPrices();

      // assertions
      const intraDayAssetTickers = await IntraDayAssetTicker.find({});
      expect(intraDayAssetTickers.length).toBe(0);
    });

    it("should create asset ticker documents for today's tickers if asset is not currently traded but we are within 50 minutes of window close", async () => {
      // set conditions
      // 16:15 New York time - 21:15 London time
      const newYorkMarketTime = DateTime.utc(2024, 6, 26)
        .setZone(MARKET_TRADING_HOURS.US.timeZone)
        .set({
          hour: 16,
          minute: 15
        })
        .toJSDate()
        .valueOf();
      Date.now = jest.fn(() => newYorkMarketTime);

      const CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        close: number;
        close30DaysAgo: number;
        close1DayAgo: number;
        timestamp: number;
      }[] = [
        {
          assetId: "equities_apple",
          close: 2100,
          close30DaysAgo: 2000, // A 5% increase.
          close1DayAgo: 2000, // A 5% increase.
          timestamp: new Date(newYorkMarketTime).getTime()
        },
        {
          assetId: "equities_microsoft",
          close: 1000,
          close30DaysAgo: 1000, // No change (0%)
          close1DayAgo: 1000, // No change (0%)
          timestamp: new Date(newYorkMarketTime).getTime() - 1
        },
        {
          assetId: "equities_coinbase",
          close: 50,
          close30DaysAgo: 100,
          close1DayAgo: 100,
          timestamp: DateUtil.getDateOfDaysAgo(new Date(newYorkMarketTime), 3).getTime()
        }
      ];
      const latestTickers: PartialRecord<
        investmentUniverseConfig.AssetType,
        { close: number; timestamp: number }
      > = Object.fromEntries(CONFIG.map(({ assetId, close, timestamp }) => [assetId, { close, timestamp }]));
      const FX_RATES = {
        USD: {
          USD: 1,
          EUR: 0.5,
          GBP: 0.5
        },
        EUR: {
          USD: 2,
          EUR: 1,
          GBP: 1
        },
        GBP: {
          USD: 2,
          GBP: 1,
          EUR: 1
        }
      };

      await Promise.all([
        RedisClientService.Instance.set("fxRates", FX_RATES),
        RedisClientService.Instance.set("fmp:today:latest_tickers", latestTickers),
        ...CONFIG.flatMap(({ assetId, close30DaysAgo, close1DayAgo }) => [
          RedisClientService.Instance.set(`eod:price_30d_ago:${assetId}`, close30DaysAgo),
          RedisClientService.Instance.set(`eod:price_1d_ago:${assetId}`, close1DayAgo),
          buildInvestmentProduct(false, { assetId })
        ])
      ]);

      // run method
      await IntraDayTickerCronService.storeAssetPrices();

      // assertions
      const intraDayAssetTickers = await IntraDayAssetTicker.find({}).populate("investmentProduct");
      expect(intraDayAssetTickers.length).toBe(2);
      expect(intraDayAssetTickers).toMatchObject(
        expect.arrayContaining([
          expect.objectContaining({
            pricePerCurrency: expect.objectContaining({
              EUR: CONFIG[0].close / 2,
              GBP: CONFIG[0].close / 2,
              USD: CONFIG[0].close
            }),
            currency: ASSET_CONFIG["equities_apple"].tradedCurrency,
            dailyReturnPercentage: 0.05,
            monthlyReturnPercentage: 0.05,
            timestamp: new Date(CONFIG[0].timestamp),
            investmentProduct: expect.objectContaining({ commonId: "equities_apple" })
          }),
          expect.objectContaining({
            pricePerCurrency: expect.objectContaining({
              EUR: CONFIG[1].close / 2,
              GBP: CONFIG[1].close / 2,
              USD: CONFIG[1].close
            }),
            currency: ASSET_CONFIG["equities_microsoft"].tradedCurrency,
            dailyReturnPercentage: 0,
            monthlyReturnPercentage: 0,
            timestamp: new Date(CONFIG[1].timestamp),
            investmentProduct: expect.objectContaining({ commonId: "equities_microsoft" })
          })
        ])
      );
    });

    it("should create asset ticker documents for today's tickers", async () => {
      // set conditions
      // 15:30 New York time - 20:30 London time
      const newYorkMarketTime = DateTime.utc(2024, 6, 26)
        .setZone(MARKET_TRADING_HOURS.US.timeZone)
        .set({
          hour: 15,
          minute: 30
        })
        .toJSDate()
        .valueOf();
      Date.now = jest.fn(() => newYorkMarketTime);

      const CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        close: number;
        close30DaysAgo: number;
        close1DayAgo: number;
        timestamp: number;
      }[] = [
        {
          assetId: "equities_apple",
          close: 2100,
          close30DaysAgo: 2000, // A 5% increase.
          close1DayAgo: 2000, // A 5% increase.
          timestamp: new Date(newYorkMarketTime).getTime()
        },
        {
          assetId: "equities_microsoft",
          close: 1000,
          close30DaysAgo: 1000, // No change (0%)
          close1DayAgo: 1000, // No change (0%)
          timestamp: new Date(newYorkMarketTime).getTime() - 1
        },
        {
          assetId: "equities_coinbase",
          close: 50,
          close30DaysAgo: 100,
          close1DayAgo: 100,
          timestamp: DateUtil.getDateOfDaysAgo(new Date(newYorkMarketTime), 3).getTime()
        }
      ];
      const latestTickers: PartialRecord<
        investmentUniverseConfig.AssetType,
        { close: number; timestamp: number }
      > = Object.fromEntries(CONFIG.map(({ assetId, close, timestamp }) => [assetId, { close, timestamp }]));
      const FX_RATES = {
        USD: {
          USD: 1,
          EUR: 0.5,
          GBP: 0.5
        },
        EUR: {
          USD: 2,
          EUR: 1,
          GBP: 1
        },
        GBP: {
          USD: 2,
          GBP: 1,
          EUR: 1
        }
      };

      await Promise.all([
        RedisClientService.Instance.set("fxRates", FX_RATES),
        RedisClientService.Instance.set("fmp:today:latest_tickers", latestTickers),
        ...CONFIG.flatMap(({ assetId, close30DaysAgo, close1DayAgo }) => [
          RedisClientService.Instance.set(`eod:price_30d_ago:${assetId}`, close30DaysAgo),
          RedisClientService.Instance.set(`eod:price_1d_ago:${assetId}`, close1DayAgo),
          buildInvestmentProduct(false, { assetId })
        ])
      ]);

      // run method
      await IntraDayTickerCronService.storeAssetPrices();

      // assertions
      const intraDayAssetTickers = await IntraDayAssetTicker.find({}).populate("investmentProduct");
      expect(intraDayAssetTickers.length).toBe(2);
      expect(intraDayAssetTickers).toMatchObject(
        expect.arrayContaining([
          expect.objectContaining({
            pricePerCurrency: expect.objectContaining({
              EUR: CONFIG[0].close / 2,
              GBP: CONFIG[0].close / 2,
              USD: CONFIG[0].close
            }),
            currency: ASSET_CONFIG["equities_apple"].tradedCurrency,
            dailyReturnPercentage: 0.05,
            monthlyReturnPercentage: 0.05,
            timestamp: new Date(CONFIG[0].timestamp),
            investmentProduct: expect.objectContaining({ commonId: "equities_apple" })
          }),
          expect.objectContaining({
            pricePerCurrency: expect.objectContaining({
              EUR: CONFIG[1].close / 2,
              GBP: CONFIG[1].close / 2,
              USD: CONFIG[1].close
            }),
            currency: ASSET_CONFIG["equities_microsoft"].tradedCurrency,
            dailyReturnPercentage: 0,
            monthlyReturnPercentage: 0,
            timestamp: new Date(CONFIG[1].timestamp),
            investmentProduct: expect.objectContaining({ commonId: "equities_microsoft" })
          })
        ])
      );
    });
  });

  describe("storePortfolioPrices", () => {
    describe("when there is a portfolio with an owner that has not made an investment yet", () => {
      beforeEach(async () => {
        const owner = await buildUser({ portfolioConversionStatus: "notStarted" });
        await buildPortfolio({
          owner: owner.id,
          initialHoldingsAllocation: [],
          holdings: []
        });
        const intraDayPortfolioTickers = await IntraDayPortfolioTicker.find();
        expect(intraDayPortfolioTickers.length).toBe(0);

        await IntraDayTickerCronService.storePortfolioPrices();
      });

      it("should not create intra-day portfolio tickers", async () => {
        const intraDayPortfolioTickers = await IntraDayPortfolioTicker.find();
        expect(intraDayPortfolioTickers.length).toBe(0);
      });
    });

    describe("when there is a portfolio that belongs to a deleted user", () => {
      let portfolio: PortfolioDocument;
      let owner: UserDocument;

      beforeEach(async () => {
        // create investment products & daily asset tickers
        const ASSET_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          close: number;
          timestamp: number;
          quantity: number;
          percentage: number;
        }[] = [
          {
            assetId: "equities_apple",
            close: 2000,
            timestamp: new Date().getTime(),
            quantity: 1,
            percentage: 40
          },
          {
            assetId: "equities_microsoft",
            close: 1000,
            timestamp: new Date().getTime() - 1,
            quantity: 1,
            percentage: 30
          },
          {
            assetId: "equities_coinbase",
            close: 1000,
            timestamp: DateUtil.getDateOfDaysAgo(new Date(), 3).getTime(),
            quantity: 1,
            percentage: 30
          }
        ];
        const holdings: HoldingsType[] = [];
        const initialHoldingsAllocation: InitialHoldingsAllocationType[] = [];
        for (let i = 0; i < ASSET_CONFIG.length; i++) {
          const { assetId, close, timestamp, quantity, percentage } = ASSET_CONFIG[i];
          const investmentProduct = await buildInvestmentProduct(false, { commonId: assetId });
          await buildIntraDayAssetTicker({
            investmentProduct: investmentProduct.id,
            pricePerCurrency: {
              EUR: close / 2,
              GBP: close / 2,
              USD: close
            },
            timestamp: new Date(timestamp)
          });
          holdings.push({ asset: investmentProduct.id, assetCommonId: assetId, quantity });
          initialHoldingsAllocation.push({ asset: investmentProduct.id, assetCommonId: assetId, percentage });
        }

        // create user & portfolio
        owner = await buildUser({
          email: "<EMAIL>",
          portfolioConversionStatus: "completed",
          currency: "GBP"
        });
        expect(owner.isDeleted).toEqual(true);
        portfolio = await buildPortfolio({
          owner: owner.id,
          initialHoldingsAllocation,
          holdings
        });

        // cache fx rates
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.5,
            GBP: 0.5
          },
          EUR: {
            USD: 2,
            EUR: 1,
            GBP: 1
          },
          GBP: {
            USD: 2,
            GBP: 1,
            EUR: 1
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const intraDayPortfolioTickers = await IntraDayPortfolioTicker.find();
        expect(intraDayPortfolioTickers.length).toBe(0);

        await IntraDayTickerCronService.storePortfolioPrices();
      });

      it("should not create intra-day portfolio tickers", async () => {
        const intraDayPortfolioTickers = await IntraDayPortfolioTicker.find({ portfolio: portfolio.id });
        expect(intraDayPortfolioTickers.length).toBe(0);
      });
    });

    describe("when there are portfolios with portfolio-converted owners and no intra-day ticker for the day", () => {
      let portfolio: PortfolioDocument;
      let owner: UserDocument;

      beforeEach(async () => {
        // create investment products & daily asset tickers
        const ASSET_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          close: number;
          timestamp: number;
          quantity: number;
          percentage: number;
        }[] = [
          {
            assetId: "equities_apple",
            close: 2000,
            timestamp: new Date().getTime(),
            quantity: 1,
            percentage: 40
          },
          {
            assetId: "equities_microsoft",
            close: 1000,
            timestamp: new Date().getTime() - 1,
            quantity: 1,
            percentage: 30
          },
          {
            assetId: "equities_coinbase",
            close: 1000,
            timestamp: DateUtil.getDateOfDaysAgo(new Date(), 3).getTime(),
            quantity: 1,
            percentage: 30
          }
        ];
        const holdings: HoldingsType[] = [];
        const initialHoldingsAllocation: InitialHoldingsAllocationType[] = [];
        for (let i = 0; i < ASSET_CONFIG.length; i++) {
          const { assetId, close, timestamp, quantity, percentage } = ASSET_CONFIG[i];
          const investmentProduct = await buildInvestmentProduct(false, { commonId: assetId });
          await buildIntraDayAssetTicker({
            investmentProduct: investmentProduct.id,
            pricePerCurrency: {
              EUR: close / 2,
              GBP: close / 2,
              USD: close
            },
            timestamp: new Date(timestamp)
          });
          holdings.push({ asset: investmentProduct.id, assetCommonId: assetId, quantity });
          initialHoldingsAllocation.push({ asset: investmentProduct.id, assetCommonId: assetId, percentage });
        }

        // create user & portfolio
        owner = await buildUser({ portfolioConversionStatus: "completed", currency: "GBP" });
        portfolio = await buildPortfolio({
          owner: owner.id,
          initialHoldingsAllocation,
          holdings
        });

        // cache fx rates
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.5,
            GBP: 0.5
          },
          EUR: {
            USD: 2,
            EUR: 1,
            GBP: 1
          },
          GBP: {
            USD: 2,
            GBP: 1,
            EUR: 1
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const intraDayPortfolioTickers = await IntraDayPortfolioTicker.find();
        expect(intraDayPortfolioTickers.length).toBe(0);

        await IntraDayTickerCronService.storePortfolioPrices();
      });

      it("should create intra-day portfolio tickers", async () => {
        const intraDayPortfolioTickers = await IntraDayPortfolioTicker.find({ portfolio: portfolio.id });
        expect(intraDayPortfolioTickers.length).toBe(1);

        const EXPECTED_USD_PRICE = 4000;
        expect(intraDayPortfolioTickers[0]).toMatchObject(
          expect.objectContaining({
            pricePerCurrency: expect.objectContaining({
              EUR: Decimal.div(EXPECTED_USD_PRICE, 2).toNumber(),
              GBP: Decimal.div(EXPECTED_USD_PRICE, 2).toNumber(),
              USD: EXPECTED_USD_PRICE
            }),
            currency: owner.currency,
            timestamp: expect.any(Date),
            portfolio: portfolio._id
          })
        );
      });
    });

    describe("when there are portfolios with portfolio-converted owners and intra-day tickers exist for the day", () => {
      let existingTicker: IntraDayPortfolioTickerDocument;
      let portfolio: PortfolioDocument;
      let owner: UserDocument;
      const EXPECTED_USD_PRICE = 4000;

      beforeEach(async () => {
        // create investment products & daily asset tickers
        const ASSET_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          close: number;
          timestamp: number;
          quantity: number;
          percentage: number;
        }[] = [
          {
            assetId: "equities_apple",
            close: 2000,
            timestamp: new Date().getTime(),
            quantity: 1,
            percentage: 40
          },
          {
            assetId: "equities_microsoft",
            close: 1000,
            timestamp: new Date().getTime() - 1,
            quantity: 1,
            percentage: 30
          },
          {
            assetId: "equities_coinbase",
            close: 1000,
            timestamp: DateUtil.getDateOfDaysAgo(new Date(), 3).getTime(),
            quantity: 1,
            percentage: 30
          }
        ];
        const holdings: HoldingsType[] = [];
        const initialHoldingsAllocation: InitialHoldingsAllocationType[] = [];
        for (let i = 0; i < ASSET_CONFIG.length; i++) {
          const { assetId, close, timestamp, quantity, percentage } = ASSET_CONFIG[i];
          const investmentProduct = await buildInvestmentProduct(false, { commonId: assetId });
          await buildIntraDayAssetTicker({
            investmentProduct: investmentProduct.id,
            pricePerCurrency: {
              EUR: close / 2,
              GBP: close / 2,
              USD: close
            },
            timestamp: new Date(timestamp)
          });
          holdings.push({ asset: investmentProduct.id, assetCommonId: assetId, quantity });
          initialHoldingsAllocation.push({ asset: investmentProduct.id, assetCommonId: assetId, percentage });
        }

        // create user & portfolio
        owner = await buildUser({ portfolioConversionStatus: "completed", currency: "GBP" });
        portfolio = await buildPortfolio({
          owner: owner.id,
          initialHoldingsAllocation,
          holdings
        });
        existingTicker = await buildIntraDayPortfolioTicker({
          currency: owner.currency,
          portfolio: portfolio.id,
          pricePerCurrency: {
            EUR: Decimal.div(EXPECTED_USD_PRICE, 2).toNumber(),
            GBP: Decimal.div(EXPECTED_USD_PRICE, 2).toNumber(),
            USD: EXPECTED_USD_PRICE
          },
          timestamp: new Date(Date.now() - 10 * 60 * 1000) // 10 minutes ago
        });

        // cache fx rates
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.5,
            GBP: 0.5
          },
          EUR: {
            USD: 2,
            EUR: 1,
            GBP: 1
          },
          GBP: {
            USD: 2,
            GBP: 1,
            EUR: 1
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const intraDayPortfolioTickers = await IntraDayPortfolioTicker.find();
        expect(intraDayPortfolioTickers.length).toBe(1);

        await IntraDayTickerCronService.storePortfolioPrices();
      });

      it("should create additional portfolio tickers", async () => {
        const intraDayPortfolioTickers = await IntraDayPortfolioTicker.find({ portfolio: portfolio.id });
        expect(intraDayPortfolioTickers.length).toBe(2);

        expect(intraDayPortfolioTickers).toMatchObject(
          expect.arrayContaining([
            expect.objectContaining({
              pricePerCurrency: expect.objectContaining({
                EUR: Decimal.div(EXPECTED_USD_PRICE, 2).toNumber(),
                GBP: Decimal.div(EXPECTED_USD_PRICE, 2).toNumber(),
                USD: EXPECTED_USD_PRICE
              }),
              currency: owner.currency,
              timestamp: existingTicker.timestamp,
              portfolio: portfolio._id
            }),
            expect.objectContaining({
              pricePerCurrency: expect.objectContaining({
                EUR: Decimal.div(EXPECTED_USD_PRICE, 2).toNumber(),
                GBP: Decimal.div(EXPECTED_USD_PRICE, 2).toNumber(),
                USD: EXPECTED_USD_PRICE
              }),
              currency: owner.currency,
              timestamp: expect.any(Date),
              portfolio: portfolio._id
            })
          ])
        );
      });
    });

    describe("when there are portfolios with with portfolio-converted owners and intra-day tickers exist for the day with timestamp within 1 minute", () => {
      let existingTicker: IntraDayPortfolioTickerDocument;
      let portfolio: PortfolioDocument;
      let owner: UserDocument;
      const EXPECTED_USD_PRICE = 4000;

      beforeEach(async () => {
        // create investment products & daily asset tickers
        const ASSET_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          close: number;
          timestamp: number;
          quantity: number;
          percentage: number;
        }[] = [
          {
            assetId: "equities_apple",
            close: 2000,
            timestamp: new Date().getTime(),
            quantity: 1,
            percentage: 40
          },
          {
            assetId: "equities_microsoft",
            close: 1000,
            timestamp: new Date().getTime() - 1,
            quantity: 1,
            percentage: 30
          },
          {
            assetId: "equities_coinbase",
            close: 1000,
            timestamp: DateUtil.getDateOfDaysAgo(new Date(), 3).getTime(),
            quantity: 1,
            percentage: 30
          }
        ];
        const holdings: HoldingsType[] = [];
        const initialHoldingsAllocation: InitialHoldingsAllocationType[] = [];
        for (let i = 0; i < ASSET_CONFIG.length; i++) {
          const { assetId, close, timestamp, quantity, percentage } = ASSET_CONFIG[i];
          const investmentProduct = await buildInvestmentProduct(false, { commonId: assetId });
          await buildIntraDayAssetTicker({
            investmentProduct: investmentProduct.id,
            pricePerCurrency: {
              EUR: close / 2,
              GBP: close / 2,
              USD: close
            },
            timestamp: new Date(timestamp)
          });
          holdings.push({ asset: investmentProduct.id, assetCommonId: assetId, quantity });
          initialHoldingsAllocation.push({ asset: investmentProduct.id, assetCommonId: assetId, percentage });
        }

        // create user & portfolio
        owner = await buildUser({ portfolioConversionStatus: "completed", currency: "GBP" });
        portfolio = await buildPortfolio({
          owner: owner.id,
          initialHoldingsAllocation,
          holdings
        });
        existingTicker = await buildIntraDayPortfolioTicker({
          currency: owner.currency,
          portfolio: portfolio.id,
          pricePerCurrency: {
            EUR: Decimal.div(EXPECTED_USD_PRICE, 2).toNumber(),
            GBP: Decimal.div(EXPECTED_USD_PRICE, 2).toNumber(),
            USD: EXPECTED_USD_PRICE
          },
          timestamp: new Date() // now
        });

        // cache fx rates
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.5,
            GBP: 0.5
          },
          EUR: {
            USD: 2,
            EUR: 1,
            GBP: 1
          },
          GBP: {
            USD: 2,
            GBP: 1,
            EUR: 1
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const intraDayPortfolioTickers = await IntraDayPortfolioTicker.find();
        expect(intraDayPortfolioTickers.length).toBe(1);

        await IntraDayTickerCronService.storePortfolioPrices();
      });

      it("should not create additional portfolio tickers", async () => {
        const intraDayPortfolioTickers = await IntraDayPortfolioTicker.find({ portfolio: portfolio.id });
        expect(intraDayPortfolioTickers.length).toBe(1);

        expect(intraDayPortfolioTickers).toMatchObject(
          expect.arrayContaining([
            expect.objectContaining({
              pricePerCurrency: expect.objectContaining({
                EUR: Decimal.div(EXPECTED_USD_PRICE, 2).toNumber(),
                GBP: Decimal.div(EXPECTED_USD_PRICE, 2).toNumber(),
                USD: EXPECTED_USD_PRICE
              }),
              currency: owner.currency,
              timestamp: existingTicker.timestamp,
              portfolio: portfolio._id
            })
          ])
        );
      });
    });
  });

  describe("checkStaleTickers", () => {
    describe("When there are tickers for today, but the asset is NOT currently traded", () => {
      // 16:30 New York time - 21:30 London time
      const newYorkMarketTime = DateTime.utc(2024, 6, 26)
        .setZone(MARKET_TRADING_HOURS.US.timeZone)
        .set({
          hour: 16,
          minute: 30
        })
        .toJSDate()
        .valueOf();

      beforeAll(async () => {
        jest.resetAllMocks();

        Date.now = jest.fn(() => newYorkMarketTime);

        const CONFIG: { assetId: investmentUniverseConfig.AssetType; close: number; timestamp: number }[] = [
          {
            assetId: "equities_apple",
            close: 2000,
            timestamp: new Date(newYorkMarketTime).getTime()
          },
          {
            assetId: "equities_microsoft",
            close: 1000,
            timestamp: new Date(newYorkMarketTime).getTime() - 1
          },
          {
            assetId: "equities_coinbase",
            close: 50,
            timestamp: DateUtil.getDateOfDaysAgo(new Date(newYorkMarketTime), 3).getTime()
          }
        ];

        await Promise.all(
          CONFIG.map(({ assetId, timestamp }) =>
            buildInvestmentProduct(true, { assetId }, { timestamp: timestamp })
          )
        );

        await IntraDayTickerCronService.checkStaleTickers();
      });

      it("should NOT emit an event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.investmentProduct.staleTicker.eventId,
          expect.anything()
        );
      });
    });

    describe("when an asset ticker was updated 1 hour ago", () => {
      const newYorkMarketTime = DateTime.utc(2024, 6, 26)
        .setZone(MARKET_TRADING_HOURS.US.timeZone)
        .set({
          hour: 15,
          minute: 30
        })
        .toJSDate()
        .valueOf();
      const newYorkMarketTimeMinus1Hour = newYorkMarketTime - 1 * 60 * 60 * 1000;

      beforeAll(async () => {
        jest.resetAllMocks();

        Date.now = jest.fn(() => newYorkMarketTime);
        jest.spyOn(eventEmitter, "emit");

        const ASSET_ID: investmentUniverseConfig.AssetType = "equities_apple";

        await buildInvestmentProduct(true, { assetId: ASSET_ID }, { timestamp: newYorkMarketTimeMinus1Hour });

        await IntraDayTickerCronService.checkStaleTickers();
      });
      afterAll(async () => {
        await clearDb();
      });

      it("should NOT emit an event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.investmentProduct.staleTicker.eventId,
          expect.anything()
        );
      });
    });

    describe("when an asset ticker was updated 3 hour ago", () => {
      const newYorkMarketTime = DateTime.utc(2024, 6, 26)
        .setZone(MARKET_TRADING_HOURS.US.timeZone)
        .set({
          hour: 15,
          minute: 30
        })
        .toJSDate()
        .valueOf();
      const newYorkMarketTimeMinus3Hours = newYorkMarketTime - 3 * 60 * 60 * 1000;

      beforeAll(async () => {
        jest.resetAllMocks();

        Date.now = jest.fn(() => newYorkMarketTime);
        jest.spyOn(eventEmitter, "emit");

        const ASSET_ID: investmentUniverseConfig.AssetType = "equities_apple";

        await buildInvestmentProduct(true, { assetId: ASSET_ID }, { timestamp: newYorkMarketTimeMinus3Hours });

        await IntraDayTickerCronService.checkStaleTickers();
      });
      afterAll(async () => {
        await clearDb();
      });

      it("should emit an event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(events.investmentProduct.staleTicker.eventId, [
          {
            assetName: "Apple",
            lastUpdateDate: new Date(newYorkMarketTimeMinus3Hours)
          }
        ]);
      });
    });

    describe("when multiple asset tickers were updated 3 hour ago", () => {
      const newYorkMarketTime = DateTime.utc(2024, 6, 26)
        .setZone(MARKET_TRADING_HOURS.US.timeZone)
        .set({
          hour: 15,
          minute: 30
        })
        .toJSDate()
        .valueOf();
      const newYorkMarketTimeMinus3Hours = newYorkMarketTime - 3 * 60 * 60 * 1000;

      beforeAll(async () => {
        jest.resetAllMocks();

        Date.now = jest.fn(() => newYorkMarketTime);
        jest.spyOn(eventEmitter, "emit");

        const APPLE_ASSET_ID: investmentUniverseConfig.AssetType = "equities_apple";
        const MICROSOFT_ASSET_ID: investmentUniverseConfig.AssetType = "equities_microsoft";

        await buildInvestmentProduct(
          true,
          { assetId: APPLE_ASSET_ID },
          { timestamp: newYorkMarketTimeMinus3Hours }
        );
        await buildInvestmentProduct(
          true,
          { assetId: MICROSOFT_ASSET_ID },
          { timestamp: newYorkMarketTimeMinus3Hours }
        );

        await IntraDayTickerCronService.checkStaleTickers();
      });
      afterAll(async () => {
        await clearDb();
      });

      it("should emit only 1 event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(events.investmentProduct.staleTicker.eventId, [
          {
            assetName: "Apple",
            lastUpdateDate: new Date(newYorkMarketTimeMinus3Hours)
          },
          {
            assetName: "Microsoft",
            lastUpdateDate: new Date(newYorkMarketTimeMinus3Hours)
          }
        ]);
      });
    });

    describe("when an asset ticker is in the LENIENT list and was updated 10 hour ago", () => {
      const lseMarketTime = DateTime.utc(2024, 6, 28) // Friday
        .setZone(MARKET_TRADING_HOURS.LSE.timeZone)
        .set({
          hour: 15,
          minute: 30
        })
        .toJSDate()
        .valueOf();
      const lseMarketTimeMinus10Hours = lseMarketTime - 10 * 60 * 60 * 1000;

      beforeAll(async () => {
        jest.resetAllMocks();

        Date.now = jest.fn(() => lseMarketTime);
        jest.spyOn(eventEmitter, "emit");

        const ASSET_ID: investmentUniverseConfig.AssetType = "equities_global_financials_broad";

        await buildInvestmentProduct(true, { assetId: ASSET_ID }, { timestamp: lseMarketTimeMinus10Hours });

        await IntraDayTickerCronService.checkStaleTickers();
      });
      afterAll(async () => {
        await clearDb();
      });

      it("should NOT emit an event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.investmentProduct.staleTicker.eventId,
          expect.anything()
        );
      });
    });

    describe("when an asset ticker is in the LENIENT list and was updated 50 hour ago", () => {
      const lseMarketTime = DateTime.utc(2024, 6, 28) // Friday
        .setZone(MARKET_TRADING_HOURS.LSE.timeZone)
        .set({
          hour: 15,
          minute: 30
        })
        .toJSDate()
        .valueOf();
      const lseMarketTimeMinus50Hours = lseMarketTime - 50 * 60 * 60 * 1000;

      beforeAll(async () => {
        jest.resetAllMocks();

        Date.now = jest.fn(() => lseMarketTime);
        jest.spyOn(eventEmitter, "emit");

        const ASSET_ID: investmentUniverseConfig.AssetType = "equities_global_financials_broad";

        await buildInvestmentProduct(true, { assetId: ASSET_ID }, { timestamp: lseMarketTimeMinus50Hours });

        await IntraDayTickerCronService.checkStaleTickers();
      });
      afterAll(async () => {
        await clearDb();
      });

      it("should emit an event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(events.investmentProduct.staleTicker.eventId, [
          {
            assetName: "SPDR World Financials UCITS ETF",
            lastUpdateDate: new Date(lseMarketTimeMinus50Hours)
          }
        ]);
      });
    });
  });

  describe("broadcastAssetPrices", () => {
    describe("when there are no tickers with timestamp in the last 20 minutes", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(SocketEmitter.Instance, "emit");

        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => +TODAY);

        const investmentProduct = await buildInvestmentProduct(false, { assetId: "equities_apple" });
        await buildIntraDayAssetTicker({
          investmentProduct: investmentProduct.id,
          pricePerCurrency: {
            EUR: 100,
            GBP: 100,
            USD: 100
          },
          timestamp: DateUtil.getDateOfMinutesAgo(21)
        });

        await IntraDayTickerCronService.broadcastAssetPrices();
      });
      afterAll(async () => {
        await clearDb();
      });

      it("should not emit any socket events", async () => {
        expect(SocketEmitter.Instance.emit).not.toHaveBeenCalled();
      });
    });

    describe("when there are tickers with timestamp in the last 10 minutes", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(SocketEmitter.Instance, "emit");

        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => +TODAY);

        const microsoftProduct = await buildInvestmentProduct(false, { assetId: "equities_microsoft" });
        await buildIntraDayAssetTicker({
          investmentProduct: microsoftProduct.id,
          pricePerCurrency: {
            GBP: 100
          },
          monthlyReturnPercentage: 0.02,
          timestamp: DateUtil.getDateOfMinutesAgo(5)
        });

        await IntraDayTickerCronService.broadcastAssetPrices();
      });
      afterAll(async () => {
        await clearDb();
      });

      it("should emit socket events with pricing data", async () => {
        expect(SocketEmitter.Instance.emit).toHaveBeenCalledWith(
          {
            key: `${RoomEnum.PRICING}:${RoomActionEnum.UPDATE}`,
            value: JSON.stringify([
              {
                assetId: "equities_microsoft",
                pricePerCurrency: {
                  GBP: 100
                },
                monthlyReturnPercentage: 0.02
              }
            ])
          },
          RoomEnum.PRICING
        );
      });

      it("should emit a update completed socket event", async () => {
        expect(SocketEmitter.Instance.emit).toHaveBeenLastCalledWith(
          {
            key: `${RoomEnum.PRICING}:${RoomActionEnum.UPDATE_COMPLETED}`
          },
          RoomEnum.PRICING
        );
      });
    });
  });
});
