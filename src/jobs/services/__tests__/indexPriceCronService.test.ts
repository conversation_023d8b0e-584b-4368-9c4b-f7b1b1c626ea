import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import { jest } from "@jest/globals";
import IndexPriceCronService from "../indexPriceCronService";
import { IndexPrice } from "../../../models/IndexPrice";
import { RedisClientService } from "../../../loaders/redis";
import { PartialRecord } from "../../../types/utils";
import { indexesConfig } from "@wealthyhood/shared-configs";
import DateUtil from "../../../utils/dateUtil";
import { buildIndexPrice } from "../../../tests/utils/generateModels";
import { FMPService } from "../../../external-services/fmpService";
import Decimal from "decimal.js";

describe("IndexPriceCronService", () => {
  beforeAll(async () => await connectDb("IndexPriceCronService"));
  afterAll(async () => await closeDb());

  describe("storeIndexPricesFromRealtimeCache", () => {
    describe("when there is a cached value for that index", () => {
      const INDEX_ID = "sp500";
      const PRICE = 1000;
      const DAILY_RETURNS = 0.01;
      const TODAY = new Date("2024-12-02T11:00:00Z");

      beforeAll(async () => {
        jest.resetAllMocks();
        const latestTickers: PartialRecord<
          indexesConfig.IndexType,
          { close: number; timestamp: number; dailyReturnPercentage: number }
        > = {
          [INDEX_ID]: {
            close: PRICE,
            timestamp: DateUtil.getDateOfHoursAgo(TODAY, 1).valueOf(),
            dailyReturnPercentage: DAILY_RETURNS
          }
        };
        Date.now = jest.fn(() => TODAY.valueOf());

        // We already have an index price for yesterday.
        await buildIndexPrice({
          index: "sp500",
          price: 990,
          date: DateUtil.getDateOfDaysAgo(TODAY, 1),
          dailyReturnPercentage: 0.01
        });

        await RedisClientService.Instance.set("fmp:today:latest_index_prices", latestTickers);

        await IndexPriceCronService.storeIndexPricesFromRealtimeCache();
      });
      afterAll(async () => {
        await Promise.all([RedisClientService.Instance.del("fmp:today:latest_index_prices"), clearDb()]);
      });

      it("should create an index price document", async () => {
        const indexPrices = await IndexPrice.find({ index: "sp500" });
        expect(indexPrices.length).toBe(2);

        const indexPrice = await IndexPrice.findOne({
          index: "sp500",
          date: { $gte: DateUtil.getStartOfDay(TODAY) }
        });
        expect(indexPrice.toObject()).toEqual(
          expect.objectContaining({
            date: DateUtil.getDateOfHoursAgo(TODAY, 1),
            index: "sp500",
            price: PRICE,
            dailyReturnPercentage: DAILY_RETURNS
          })
        );
      });
    });
  });

  describe("cacheCurrentIndexPrices", () => {
    describe("when FMP returns an error", () => {
      const NOW = 1732897740000; // Epoch timestamp in milliseconds

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(FMPService.Instance, "getBatchQuote").mockImplementation(() => {
          throw new Error();
        });
        Date.now = jest.fn(() => NOW);

        await IndexPriceCronService.cacheCurrentIndexPrices();
      });
      afterAll(async () => {
        await Promise.all([RedisClientService.Instance.del("fmp:today:latest_index_prices"), clearDb()]);
      });

      it("should NOT cache an index price", async () => {
        const cachedData = await RedisClientService.Instance.get<
          PartialRecord<
            indexesConfig.IndexType,
            { close: number; timestamp: number; dailyReturnPercentage: number }
          >
        >("fmp:today:latest_index_prices");

        expect(cachedData).toBeUndefined();
      });
    });

    describe("when we do not have any prices stored for that index", () => {
      const PRICE = 6034.5898;
      const DAILY_RETURNS = 0.22; // 0.22%
      const NOW = 1732897740; // Epoch timestamp in seconds

      beforeAll(async () => {
        jest.resetAllMocks();
        jest
          .spyOn(FMPService.Instance, "getBatchQuote")
          .mockResolvedValue([{ symbol: "^FCHI", price: PRICE, timestamp: NOW, changePercentage: DAILY_RETURNS }]);
        Date.now = jest.fn(() => NOW);

        await IndexPriceCronService.cacheCurrentIndexPrices();
      });
      afterAll(async () => {
        await Promise.all([RedisClientService.Instance.del("fmp:today:latest_index_prices"), clearDb()]);
      });

      it("should cache the index price", async () => {
        const cachedData = await RedisClientService.Instance.get<
          PartialRecord<
            indexesConfig.IndexType,
            { close: number; timestamp: number; dailyReturnPercentage: number }
          >
        >("fmp:today:latest_index_prices");
        expect(cachedData).toMatchObject(
          expect.objectContaining({
            cac40: {
              close: PRICE,
              dailyReturnPercentage: Decimal.div(DAILY_RETURNS, 100).toNumber(),
              timestamp: Decimal.mul(NOW, 1000).toNumber() // Timestamp in milliseconds
            }
          })
        );
      });
    });
  });
});
