import { cloudflareConfig } from "@wealthyhood/shared-configs";
import MockCloudflareService from "../../../external-services/__mocks__/cloudflareService";
import { closeDb, connectDb, clearDb } from "../../../tests/utils/db";
import SavingsProductsDataCronService, { SavingsProductsDataType } from "../savingsProductsDataCronService";
import { SavingsProductDocument } from "../../../models/SavingsProduct";
import { buildDailySavingsProductTicker, buildSavingsProduct } from "../../../tests/utils/generateModels";
import Decimal from "decimal.js";
import { DailySavingsProductTickerDocument } from "../../../models/DailyTicker";

const { KvNamespaceKeys } = cloudflareConfig;

describe("SavingsProductsDataCronService", () => {
  beforeAll(async () => await connectDb("SavingsProductsDataCronService"));
  afterAll(async () => await closeDb());

  describe("updateSavingsProductsData", () => {
    // Wednesday
    const TODAY = new Date("2024-01-10");

    describe("when there are no savings products data saved in cloudflare", () => {
      let savingsProduct: SavingsProductDocument;
      const FUND_MANAGER_FEE_PERCENTAGE = 0.1;

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => +TODAY);
        savingsProduct = await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
        await SavingsProductsDataCronService.updateSavingsProductsData();
      });
      afterAll(async () => await clearDb());

      it("should save the correct savings products data in cloudflare", async () => {
        const savingsProductsData = MockCloudflareService.kvStorage[
          KvNamespaceKeys.SAVINGS_PRODUCTS_DATA
        ] as SavingsProductsDataType;

        expect(savingsProductsData.mmf_dist_gbp).toEqual({
          feeDetails: [
            {
              fundManagerAnnualFeePercentage: `${FUND_MANAGER_FEE_PERCENTAGE.toFixed(2)}%`,
              netInterestRate: `${Decimal.sub(savingsProduct.currentTicker.oneDayYield, 0.6).toFixed(2)}% p.a.`,
              netInterestRateValue: Decimal.sub(savingsProduct.currentTicker.oneDayYield, 0.6).toDP(2).toNumber(),
              plan: "free",
              wealthyhoodAnnualFeePercentage: "0.60%"
            },
            {
              fundManagerAnnualFeePercentage: `${FUND_MANAGER_FEE_PERCENTAGE.toFixed(2)}%`,
              netInterestRate: `${Decimal.sub(savingsProduct.currentTicker.oneDayYield, 0.2).toFixed(2)}% p.a.`,
              netInterestRateValue: Decimal.sub(savingsProduct.currentTicker.oneDayYield, 0.2).toDP(2).toNumber(),
              plan: "paid_low",
              wealthyhoodAnnualFeePercentage: "0.20%"
            },
            {
              fundManagerAnnualFeePercentage: `${FUND_MANAGER_FEE_PERCENTAGE.toFixed(2)}%`,
              netInterestRate: `${Decimal.sub(savingsProduct.currentTicker.oneDayYield, 0.1).toFixed(2)}% p.a.`,
              netInterestRateValue: Decimal.sub(savingsProduct.currentTicker.oneDayYield, 0.1).toDP(2).toNumber(),
              plan: "paid_mid",
              wealthyhoodAnnualFeePercentage: "0.10%"
            }
          ],
          fundManagerFeeColumnLabel: "Blackrock fee annually",
          grossInterestRate: `${Decimal.add(
            savingsProduct.currentTicker.oneDayYield,
            FUND_MANAGER_FEE_PERCENTAGE
          ).toFixed(2)}%`,
          netInterestRateColumnLabel: "Your 1-day yield (net)",
          planColumnLabel: "Your plan",
          wealthyhoodFeeColumnLabel: "Our fee annually"
        });
      });
    });

    describe("when there are savings products data already saved in cloudflare", () => {
      let savingsProduct: SavingsProductDocument;
      const FUND_MANAGER_FEE_PERCENTAGE = 0.1;
      let oldTicker: DailySavingsProductTickerDocument;
      let newTicker: DailySavingsProductTickerDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => +TODAY);
        savingsProduct = await buildSavingsProduct(false, { commonId: "mmf_dist_gbp" });
        oldTicker = await buildDailySavingsProductTicker({
          savingsProduct: savingsProduct.id
        });

        await SavingsProductsDataCronService.updateSavingsProductsData();
      });
      afterAll(async () => await clearDb());

      it("should have the old savings products data in cloudflare before the update", async () => {
        const savingsProductsData = MockCloudflareService.kvStorage[
          KvNamespaceKeys.SAVINGS_PRODUCTS_DATA
        ] as SavingsProductsDataType;

        expect(savingsProductsData.mmf_dist_gbp).toEqual({
          feeDetails: [
            {
              fundManagerAnnualFeePercentage: `${FUND_MANAGER_FEE_PERCENTAGE.toFixed(2)}%`,
              netInterestRate: `${Decimal.sub(oldTicker.oneDayYield, 0.6).toFixed(2)}% p.a.`,
              netInterestRateValue: Decimal.sub(oldTicker.oneDayYield, 0.6).toDP(2).toNumber(),
              plan: "free",
              wealthyhoodAnnualFeePercentage: "0.60%"
            },
            {
              fundManagerAnnualFeePercentage: `${FUND_MANAGER_FEE_PERCENTAGE.toFixed(2)}%`,
              netInterestRate: `${Decimal.sub(oldTicker.oneDayYield, 0.2).toFixed(2)}% p.a.`,
              netInterestRateValue: Decimal.sub(oldTicker.oneDayYield, 0.2).toDP(2).toNumber(),
              plan: "paid_low",
              wealthyhoodAnnualFeePercentage: "0.20%"
            },
            {
              fundManagerAnnualFeePercentage: `${FUND_MANAGER_FEE_PERCENTAGE.toFixed(2)}%`,
              netInterestRate: `${Decimal.sub(oldTicker.oneDayYield, 0.1).toFixed(2)}% p.a.`,
              netInterestRateValue: Decimal.sub(oldTicker.oneDayYield, 0.1).toDP(2).toNumber(),
              plan: "paid_mid",
              wealthyhoodAnnualFeePercentage: "0.10%"
            }
          ],
          fundManagerFeeColumnLabel: "Blackrock fee annually",
          grossInterestRate: `${Decimal.add(oldTicker.oneDayYield, FUND_MANAGER_FEE_PERCENTAGE).toFixed(2)}%`,
          netInterestRateColumnLabel: "Your 1-day yield (net)",
          planColumnLabel: "Your plan",
          wealthyhoodFeeColumnLabel: "Our fee annually"
        });
      });

      it("should update the savings products data in cloudflare", async () => {
        newTicker = await buildDailySavingsProductTicker({
          savingsProduct: savingsProduct.id
        });

        await SavingsProductsDataCronService.updateSavingsProductsData();

        const savingsProductsData = MockCloudflareService.kvStorage[
          KvNamespaceKeys.SAVINGS_PRODUCTS_DATA
        ] as SavingsProductsDataType;
        expect(savingsProductsData.mmf_dist_gbp).toEqual({
          feeDetails: [
            {
              fundManagerAnnualFeePercentage: `${FUND_MANAGER_FEE_PERCENTAGE.toFixed(2)}%`,
              netInterestRate: `${Decimal.sub(newTicker.oneDayYield, 0.6).toFixed(2)}% p.a.`,
              netInterestRateValue: Decimal.sub(newTicker.oneDayYield, 0.6).toDP(2).toNumber(),
              plan: "free",
              wealthyhoodAnnualFeePercentage: "0.60%"
            },
            {
              fundManagerAnnualFeePercentage: `${FUND_MANAGER_FEE_PERCENTAGE.toFixed(2)}%`,
              netInterestRate: `${Decimal.sub(newTicker.oneDayYield, 0.2).toFixed(2)}% p.a.`,
              netInterestRateValue: Decimal.sub(newTicker.oneDayYield, 0.2).toDP(2).toNumber(),
              plan: "paid_low",
              wealthyhoodAnnualFeePercentage: "0.20%"
            },
            {
              fundManagerAnnualFeePercentage: `${FUND_MANAGER_FEE_PERCENTAGE.toFixed(2)}%`,
              netInterestRate: `${Decimal.sub(newTicker.oneDayYield, 0.1).toFixed(2)}% p.a.`,
              netInterestRateValue: Decimal.sub(newTicker.oneDayYield, 0.1).toDP(2).toNumber(),
              plan: "paid_mid",
              wealthyhoodAnnualFeePercentage: "0.10%"
            }
          ],
          fundManagerFeeColumnLabel: "Blackrock fee annually",
          grossInterestRate: `${Decimal.add(newTicker.oneDayYield, FUND_MANAGER_FEE_PERCENTAGE).toFixed(2)}%`,
          netInterestRateColumnLabel: "Your 1-day yield (net)",
          planColumnLabel: "Your plan",
          wealthyhoodFeeColumnLabel: "Our fee annually"
        });
      });
    });
  });
});
