import { TransactionMonitorCronService } from "./../transactionMonitorCronService";
import events from "../../../event-handlers/events";
import eventEmitter from "../../../loaders/eventEmitter";
import { MonitorType, TransactionMonitor } from "../../../models/TransactionMonitor";
import { KycStatusEnum, UserDocument } from "../../../models/User";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import { buildDepositCashTransaction, buildRiskAssessment, buildUser } from "../../../tests/utils/generateModels";

describe("TransactionMonitorCronService", () => {
  beforeAll(async () => await connectDb("TransactionMonitorCronService"));
  afterAll(async () => await closeDb());

  describe("checkUsersForSuspiciousTransactionActivity", () => {
    describe("when two users have suspicous activity", () => {
      let user: UserDocument;
      let anotherUser: UserDocument;

      beforeAll(async () => {
        user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        anotherUser = await buildUser({ kycStatus: KycStatusEnum.PASSED });

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 990001, currency: "GBP" }, status: "Settled" },
            user
          ),
          buildDepositCashTransaction(
            { consideration: { amount: 990001, currency: "GBP" }, status: "Settled" },
            user
          ),
          buildDepositCashTransaction(
            { consideration: { amount: 990001, currency: "GBP" }, status: "Settled" },
            anotherUser
          ),
          buildDepositCashTransaction(
            { consideration: { amount: 990001, currency: "GBP" }, status: "Settled" },
            anotherUser
          )
        ]);

        await TransactionMonitorCronService.checkUsersForSuspiciousTransactionActivity();
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should create a transaction monitor documents", async () => {
        const transactionMonitors = await TransactionMonitor.find();

        expect(transactionMonitors).toHaveLength(2);
        expect(transactionMonitors).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              owner: user._id,
              type: MonitorType.HIGH_VOLUME_DEPOSITS
            }),
            expect.objectContaining({
              owner: anotherUser._id,
              type: MonitorType.HIGH_VOLUME_DEPOSITS
            })
          ])
        );
      });

      it("should emit an event for each user", () => {
        expect(eventEmitter.emit).toHaveBeenCalledTimes(2);
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transactionMonitoring.highVolumeDeposits.eventId,
          expect.objectContaining({ email: user.email })
        );
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transactionMonitoring.highVolumeDeposits.eventId,
          expect.objectContaining({ email: anotherUser.email })
        );
      });
    });

    describe("when a user is prohibited", () => {
      let user: UserDocument;

      beforeAll(async () => {
        user = await buildUser({ kycStatus: KycStatusEnum.PASSED });

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 990001, currency: "GBP" }, status: "Settled" },
            user
          ),
          buildDepositCashTransaction(
            { consideration: { amount: 990001, currency: "GBP" }, status: "Settled" },
            user
          ),
          buildRiskAssessment({
            owner: user.id,
            totalScore: 200
          })
        ]);

        await TransactionMonitorCronService.checkUsersForSuspiciousTransactionActivity();
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should not process the user", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id });

        expect(transactionMonitors).toHaveLength(0);
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("when a user has suspicious activity and the cron runs twice", () => {
      let user: UserDocument;

      beforeAll(async () => {
        user = await buildUser({ kycStatus: KycStatusEnum.PASSED });

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 990001, currency: "GBP" }, status: "Settled" },
            user
          ),
          buildDepositCashTransaction(
            { consideration: { amount: 990001, currency: "GBP" }, status: "Settled" },
            user
          )
        ]);

        await TransactionMonitorCronService.checkUsersForSuspiciousTransactionActivity();
        await TransactionMonitorCronService.checkUsersForSuspiciousTransactionActivity();
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should create only one transaction monitor", async () => {
        const transactionMonitors = await TransactionMonitor.find({ owner: user._id });

        expect(transactionMonitors).toHaveLength(1);
        expect(transactionMonitors[0]).toMatchObject({
          owner: user._id,
          type: MonitorType.HIGH_VOLUME_DEPOSITS
        });
      });

      it("should emit an event once", () => {
        expect(eventEmitter.emit).toHaveBeenNthCalledWith(
          1,
          events.transactionMonitoring.highVolumeDeposits.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when a user is not kyc passed", () => {
      beforeAll(async () => {
        const user = await buildUser({ kycStatus: KycStatusEnum.PENDING });
        const anotherUser = await buildUser({ kycStatus: KycStatusEnum.FAILED });

        await Promise.all([
          buildDepositCashTransaction(
            { consideration: { amount: 990001, currency: "GBP" }, status: "Settled" },
            user
          ),
          buildDepositCashTransaction(
            { consideration: { amount: 990001, currency: "GBP" }, status: "Settled" },
            user
          ),
          buildDepositCashTransaction(
            { consideration: { amount: 990001, currency: "GBP" }, status: "Settled" },
            anotherUser
          ),
          buildDepositCashTransaction(
            { consideration: { amount: 990001, currency: "GBP" }, status: "Settled" },
            anotherUser
          )
        ]);

        await TransactionMonitorCronService.checkUsersForSuspiciousTransactionActivity();
      });
      afterAll(async () => {
        await clearDb();
        jest.clearAllMocks();
      });

      it("should not process the user", async () => {
        const transactionMonitors = await TransactionMonitor.find();

        expect(transactionMonitors).toHaveLength(0);
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });
  });
});
