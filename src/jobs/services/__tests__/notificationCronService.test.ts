import { faker } from "@faker-js/faker";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import { Notification, NotificationDocument, NotificationMetadataTypeEnum } from "../../../models/Notification";
import { jest } from "@jest/globals";
import { NotificationCronService } from "../notificationCronService";
import {
  buildNotification,
  buildNotificationSettings,
  buildPortfolio,
  buildSundownDigest,
  buildUser,
  buildHoldingDTO
} from "../../../tests/utils/generateModels";
import DateUtil from "../../../utils/dateUtil";
import { UserDocument } from "../../../models/User";
import {
  AppNotificationSettingEnum,
  AppNotificationSettings,
  EmailNotificationSettingEnum,
  EmailNotificationSettings
} from "../../../models/NotificationSettings";
import {
  LearningNotificationEventEnum,
  TransactionalNotificationEventEnum
} from "../../../event-handlers/notificationEvents";
import logger from "../../../external-services/loggerService";
import OneSignalService from "../../../external-services/onesignalService";
import { RedisClientService } from "../../../loaders/redis";
import { SundownDigestDocument } from "../../../models/SundownDigest";
import { TenorEnum } from "../../../configs/durationConfig";

const originalDateNow = Date.now;

describe("NotificationCronService", () => {
  beforeAll(async () => await connectDb("NotificationCronService"));
  afterAll(async () => await closeDb());

  describe("sendPendingAppContentNotifications", () => {
    describe("when there are no notifications", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

        await NotificationCronService.sendPendingAppContentNotifications();
      });

      it("should NOT send the notification via OneSignal", async () => {
        expect(OneSignalService.sendPushNotification).not.toHaveBeenCalled();
      });
    });

    describe("when there is a pending notification but it is planned to be sent 2 hours from now", () => {
      let user: UserDocument;
      let notification: NotificationDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        notification = await buildNotification({
          owner: user.id,
          status: "Pending",
          notifyAt: DateUtil.getDateOfMinutesAgo(120)
        });

        await NotificationCronService.sendPendingAppContentNotifications();
      });

      it("should NOT update notification to 'Sent'", async () => {
        const updatedNotification = await Notification.findById(notification.id);
        expect(updatedNotification.status).toEqual("Pending");
      });

      it("should NOT send the notification via OneSignal", async () => {
        expect(OneSignalService.sendPushNotification).not.toHaveBeenCalled();
      });
    });

    describe("when there is a pending notification but the user has not enabled that notification", () => {
      let user: UserDocument;
      let notification: NotificationDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

        user = await buildUser();
        await buildNotificationSettings({
          owner: user.id,
          app: {
            deviceNotificationsEnabled: true,
            settings: new Map(
              Object.entries({
                [AppNotificationSettingEnum.TRANSACTIONAL]: true,
                [AppNotificationSettingEnum.LEARNING_GUIDE]: true,
                [AppNotificationSettingEnum.ANALYST_INSIGHT]: true,
                [AppNotificationSettingEnum.QUICK_TAKE]: false,
                [AppNotificationSettingEnum.WEEKLY_REVIEW]: true,
                [AppNotificationSettingEnum.PROMOTIONAL]: true
              })
            ) as AppNotificationSettings
          },
          email: {
            settings: new Map(
              Object.entries({
                [EmailNotificationSettingEnum.TRANSACTIONAL]: true,
                [EmailNotificationSettingEnum.PROMOTIONAL]: true,
                [EmailNotificationSettingEnum.WEALTHYBITES]: true
              })
            ) as EmailNotificationSettings
          }
        });
        notification = await buildNotification({
          owner: user.id,
          status: "Pending",
          createdAt: DateUtil.getDateOfHoursAgo(new Date(), 1),
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.QUICK_TAKE_CREATED,
              properties: new Map(
                Object.entries({
                  title: "Great quick take!"
                })
              )
            }
          }
        });

        await NotificationCronService.sendPendingAppContentNotifications();
      });

      it("should NOT update notification to 'Sent'", async () => {
        const updatedNotification = await Notification.findById(notification.id);
        expect(updatedNotification.status).toEqual("Pending");
      });

      it("should NOT send the notification via OneSignal", async () => {
        expect(OneSignalService.sendPushNotification).not.toHaveBeenCalled();
      });
    });

    describe("when there is a pending notification but the user has already been sent notification today", () => {
      let user: UserDocument;
      let notification: NotificationDocument;

      const TODAY = new Date("2022-08-31T11:00:00Z");

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        notification = await buildNotification({
          owner: user.id,
          status: "Pending",
          createdAt: DateUtil.getDateOfHoursAgo(new Date(), 1),
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.QUICK_TAKE_CREATED,
              properties: new Map(
                Object.entries({
                  title: "Great quick take!"
                })
              )
            }
          }
        });

        // User has been sent another notification one hour ago.
        await buildNotification({
          owner: user.id,
          status: "Sent",
          sentAt: DateUtil.getDateOfHoursAgo(TODAY, 1),
          createdAt: DateUtil.getDateOfHoursAgo(new Date(), 1),
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.QUICK_TAKE_CREATED,
              properties: new Map(
                Object.entries({
                  title: "Great quick take!"
                })
              )
            }
          }
        });

        await NotificationCronService.sendPendingAppContentNotifications();
      });

      it("should NOT update notification to 'Sent'", async () => {
        const updatedNotification = await Notification.findById(notification.id);
        expect(updatedNotification.status).toEqual("Pending");
      });

      it("should NOT send the notification via OneSignal", async () => {
        expect(OneSignalService.sendPushNotification).not.toHaveBeenCalled();
      });
    });

    describe("when there is a pending notification that should be sent", () => {
      let user: UserDocument;
      let notification: NotificationDocument;

      const TODAY = new Date("2022-08-31T11:00:00Z");

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ kycStatus: "passed" });
        await buildNotificationSettings({ owner: user.id });
        notification = await buildNotification({
          owner: user.id,
          status: "Pending",
          method: "app",
          createdAt: DateUtil.getDateOfHoursAgo(TODAY, 1),
          notifyAt: DateUtil.getDateOfMinutesAgo(20),
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.QUICK_TAKE_CREATED,
              properties: new Map(
                Object.entries({
                  title: "Great quick take!"
                })
              ),
              metadata: {
                documentId: faker.string.uuid(),
                notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT
              }
            }
          }
        });

        await NotificationCronService.sendPendingAppContentNotifications();
      });

      it("should update notification to 'Sent' and add the sentAt timestamp", async () => {
        const updatedNotification = await Notification.findById(notification.id);
        expect(updatedNotification.toObject()).toEqual(
          expect.objectContaining({
            status: "Sent",
            sentAt: TODAY
          })
        );
      });

      it("should send the notification via OneSignal", async () => {
        expect(OneSignalService.sendPushNotification).toHaveBeenCalledWith(
          expect.arrayContaining([user.id]),
          notification?.providers?.onesignal?.notificationId,
          Object.fromEntries(notification?.providers?.onesignal?.properties.entries()),
          notification?.providers?.onesignal?.metadata
        );
      });
    });

    describe("when there is a pending notification that should be sent and the method is called twice", () => {
      let user: UserDocument;
      let notification: NotificationDocument;

      const TODAY = new Date("2022-08-31T11:00:00Z");

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();
        await buildNotificationSettings({ owner: user.id });
        notification = await buildNotification({
          owner: user.id,
          status: "Pending",
          createdAt: DateUtil.getDateOfHoursAgo(TODAY, 1),
          notifyAt: DateUtil.getDateOfMinutesAgo(20),
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.QUICK_TAKE_CREATED,
              properties: new Map(
                Object.entries({
                  title: "Great quick take!"
                })
              ),
              metadata: {
                notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT,
                documentId: faker.string.uuid()
              }
            }
          }
        });

        await NotificationCronService.sendPendingAppContentNotifications();
        await NotificationCronService.sendPendingAppContentNotifications();
      });

      it("should update notification to 'Sent'", async () => {
        const updatedNotification = await Notification.findById(notification.id);
        expect(updatedNotification.status).toEqual("Sent");
      });

      it("should send the notification via OneSignal only once", async () => {
        expect(OneSignalService.sendPushNotification).toHaveBeenCalledTimes(1);
      });
    });

    describe("when there are multiple notifications of different types for different users", () => {
      let user1: UserDocument;
      let user2: UserDocument;
      let quickTakeNotification: NotificationDocument;
      let weeklyReviewNotification: NotificationDocument;

      const TODAY = new Date("2022-08-31T11:00:00Z");

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });
        Date.now = jest.fn(() => TODAY.valueOf());

        user1 = await buildUser({ kycStatus: "passed" });
        user2 = await buildUser({ kycStatus: "passed" });
        await buildNotificationSettings({ owner: user1.id });
        await buildNotificationSettings({ owner: user2.id });

        // Create two notifications of different types
        quickTakeNotification = await buildNotification({
          owner: user1.id,
          status: "Pending",
          method: "app",
          notifyAt: DateUtil.getDateOfMinutesAgo(20),
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.QUICK_TAKE_CREATED,
              properties: new Map(Object.entries({ title: "Quick Take!" })),
              metadata: {
                documentId: faker.string.uuid(),
                notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT
              }
            }
          }
        });

        weeklyReviewNotification = await buildNotification({
          owner: user2.id,
          status: "Pending",
          method: "app",
          notifyAt: DateUtil.getDateOfMinutesAgo(20),
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.WEEKLY_REVIEW_CREATED,
              properties: new Map(Object.entries({ title: "Weekly Review!" })),
              metadata: {
                documentId: faker.string.uuid(),
                notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT
              }
            }
          }
        });

        await NotificationCronService.sendPendingAppContentNotifications();
      });

      it("should process notifications of different types separately", () => {
        expect(OneSignalService.sendPushNotification).toHaveBeenCalledTimes(2);

        expect(OneSignalService.sendPushNotification).toHaveBeenNthCalledWith(
          1,
          [user1.id],
          LearningNotificationEventEnum.QUICK_TAKE_CREATED,
          { title: "Quick Take!" },
          {
            notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT,
            documentId: quickTakeNotification.providers?.onesignal?.metadata?.documentId
          }
        );

        expect(OneSignalService.sendPushNotification).toHaveBeenNthCalledWith(
          2,
          [user2.id],
          LearningNotificationEventEnum.WEEKLY_REVIEW_CREATED,
          { title: "Weekly Review!" },
          {
            notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT,
            documentId: weeklyReviewNotification.providers?.onesignal?.metadata?.documentId
          }
        );
      });

      it("should mark both notifications as sent", async () => {
        const updatedQuickTake = await Notification.findById(quickTakeNotification.id);
        const updatedWeeklyReview = await Notification.findById(weeklyReviewNotification.id);

        expect(updatedQuickTake?.status).toBe("Sent");
        expect(updatedWeeklyReview?.status).toBe("Sent");
      });
    });

    describe("when there are multiple notifications of the same type but with different properties", () => {
      let user1: UserDocument;
      let user2: UserDocument;
      let notification1: NotificationDocument;
      let notification2: NotificationDocument;

      const TODAY = new Date("2022-08-31T11:00:00Z");

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });
        Date.now = jest.fn(() => TODAY.valueOf());

        user1 = await buildUser({ kycStatus: "passed" });
        user2 = await buildUser({ kycStatus: "passed" });
        await buildNotificationSettings({ owner: user1.id });
        await buildNotificationSettings({ owner: user2.id });

        // Create two notifications of same type but different properties
        notification1 = await buildNotification({
          owner: user1.id,
          status: "Pending",
          method: "app",
          notifyAt: DateUtil.getDateOfMinutesAgo(20),
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.QUICK_TAKE_CREATED,
              properties: new Map(Object.entries({ title: "Quick Take 1!" })),
              metadata: {
                documentId: faker.string.uuid(),
                notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT
              }
            }
          }
        });

        notification2 = await buildNotification({
          owner: user2.id,
          status: "Pending",
          method: "app",
          notifyAt: DateUtil.getDateOfMinutesAgo(20),
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.QUICK_TAKE_CREATED,
              properties: new Map(Object.entries({ title: "Quick Take 2!" })),
              metadata: {
                documentId: faker.string.uuid(),
                notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT
              }
            }
          }
        });

        await NotificationCronService.sendPendingAppContentNotifications();
      });

      it("should process notifications with different properties separately", () => {
        expect(OneSignalService.sendPushNotification).toHaveBeenCalledTimes(2);

        expect(OneSignalService.sendPushNotification).toHaveBeenNthCalledWith(
          1,
          [user1.id],
          LearningNotificationEventEnum.QUICK_TAKE_CREATED,
          { title: "Quick Take 1!" },
          {
            notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT,
            documentId: notification1.providers?.onesignal?.metadata?.documentId
          }
        );

        expect(OneSignalService.sendPushNotification).toHaveBeenNthCalledWith(
          2,
          [user2.id],
          LearningNotificationEventEnum.QUICK_TAKE_CREATED,
          { title: "Quick Take 2!" },
          {
            notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT,
            documentId: notification2.providers?.onesignal?.metadata?.documentId
          }
        );
      });

      it("should mark both notifications as sent", async () => {
        const updatedNotification1 = await Notification.findById(notification1.id);
        const updatedNotification2 = await Notification.findById(notification2.id);

        expect(updatedNotification1.status).toBe("Sent");
        expect(updatedNotification2.status).toBe("Sent");
      });
    });

    describe("when there is a pending notification but method is not 'app'", () => {
      let user: UserDocument;
      let notification: NotificationDocument;

      const TODAY = new Date("2022-08-31T11:00:00Z");

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ kycStatus: "passed" });
        await buildNotificationSettings({ owner: user.id });
        notification = await buildNotification({
          owner: user.id,
          status: "Pending",
          method: "email", // Set method to email instead of app
          notifyAt: DateUtil.getDateOfMinutesAgo(20),
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.QUICK_TAKE_CREATED,
              properties: new Map(Object.entries({ title: "Quick Take!" })),
              metadata: {
                documentId: faker.string.uuid(),
                notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT
              }
            }
          }
        });

        await NotificationCronService.sendPendingAppContentNotifications();
      });

      it("should NOT update notification status", async () => {
        const updatedNotification = await Notification.findById(notification.id);
        expect(updatedNotification.status).toEqual("Pending");
      });

      it("should NOT send the notification via OneSignal", () => {
        expect(OneSignalService.sendPushNotification).not.toHaveBeenCalled();
      });
    });

    describe("when there is a pending notification but notificationId is not a LearningNotificationEventEnum", () => {
      let user: UserDocument;
      let notification: NotificationDocument;

      const TODAY = new Date("2022-08-31T11:00:00Z");

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ kycStatus: "passed" });
        await buildNotificationSettings({ owner: user.id });
        notification = await buildNotification({
          owner: user.id,
          status: "Pending",
          method: "app",
          notifyAt: DateUtil.getDateOfMinutesAgo(20),
          providers: {
            onesignal: {
              notificationId: TransactionalNotificationEventEnum.DEPOSIT_SUCCESS, // Not a LearningNotificationEventEnum
              properties: new Map(Object.entries({ title: "Deposit Success!" })),
              metadata: {
                documentId: faker.string.uuid(),
                notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT
              }
            }
          }
        });

        await NotificationCronService.sendPendingAppContentNotifications();
      });

      it("should NOT update notification status", async () => {
        const updatedNotification = await Notification.findById(notification.id);
        expect(updatedNotification.status).toEqual("Pending");
      });

      it("should NOT send the notification via OneSignal", () => {
        expect(OneSignalService.sendPushNotification).not.toHaveBeenCalled();
      });
    });

    describe("when there are multiple pending notifications with content notificationId and close notifyAt times (one minute apart) for the same user", () => {
      let user: UserDocument;
      let notification1: NotificationDocument;
      let notification2: NotificationDocument;
      const documentId1 = faker.string.uuid();
      const documentId2 = faker.string.uuid();
      const TODAY = new Date("2022-08-31T11:00:00Z");

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

        Date.now = jest.fn(() => TODAY.valueOf());

        const TWENTY_MINUTES_LATER = DateUtil.getDateOfMinutesAgo(20);
        const TWENTY_ONE_MINUTES_LATER = DateUtil.getDateOfMinutesAgo(21);

        user = await buildUser({ kycStatus: "passed" });
        await buildNotificationSettings({ owner: user.id });

        // notification with analyst insight notificationId
        notification1 = await buildNotification({
          owner: user.id,
          status: "Pending",
          method: "app",
          createdAt: DateUtil.getDateOfHoursAgo(TODAY, 1),
          notifyAt: TWENTY_MINUTES_LATER,
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.ANALYSIS_CREATED,
              properties: new Map(
                Object.entries({
                  title: "Analyst Insight"
                })
              ),
              metadata: {
                notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT,
                documentId: documentId1
              }
            }
          }
        });

        // notification with weekly review notificationId
        notification2 = await buildNotification({
          owner: user.id,
          status: "Pending",
          method: "app",
          createdAt: DateUtil.getDateOfHoursAgo(TODAY, 1),
          notifyAt: TWENTY_ONE_MINUTES_LATER,
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.WEEKLY_REVIEW_CREATED,
              properties: new Map(
                Object.entries({
                  title: "Weekly Review"
                })
              ),
              metadata: {
                notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT,
                documentId: documentId2
              }
            }
          }
        });

        await NotificationCronService.sendPendingAppContentNotifications();
      });

      it("should mark only one notification to 'Sent'", async () => {
        const updatedNotification1 = await Notification.findById(notification1.id);
        const updatedNotification2 = await Notification.findById(notification2.id);

        expect(updatedNotification1.status).toEqual("Sent");
        expect(updatedNotification2.status).toEqual("Pending");
      });

      it("should send the notification via OneSignal only once", async () => {
        // The function should call OneSignal only once for both notifications
        expect(OneSignalService.sendPushNotification).toHaveBeenCalledTimes(1);

        // Verify the arguments
        expect(OneSignalService.sendPushNotification).toHaveBeenCalledWith(
          expect.arrayContaining([user.id]),
          LearningNotificationEventEnum.ANALYSIS_CREATED,
          { title: "Analyst Insight" },
          {
            notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT,
            documentId: documentId1
          }
        );
      });
    });

    describe("when there are two pending notifications for the same user but the method is run at different times", () => {
      let user: UserDocument;
      let notification1: NotificationDocument;
      let notification2: NotificationDocument;
      const documentId1 = faker.string.uuid();
      const documentId2 = faker.string.uuid();

      const TODAY = new Date("2022-08-31T11:00:00Z");

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

        // First run time
        Date.now = jest.fn(() => TODAY.valueOf());

        const FIRST_RUN_TIME = DateUtil.getDateOfMinutesAgo(20);
        const SECOND_RUN_TIME = DateUtil.getDateOfMinutesAgo(35); // 15 minutes later

        user = await buildUser({ kycStatus: "passed" });
        await buildNotificationSettings({ owner: user.id });

        // Create two notifications for the same user
        notification1 = await buildNotification({
          owner: user.id,
          status: "Pending",
          method: "app",
          notifyAt: FIRST_RUN_TIME,
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.ANALYSIS_CREATED,
              properties: new Map(
                Object.entries({
                  title: "Analyst Insight 1"
                })
              ),
              metadata: {
                notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT,
                documentId: documentId1
              }
            }
          }
        });

        notification2 = await buildNotification({
          owner: user.id,
          status: "Pending",
          method: "app",
          notifyAt: FIRST_RUN_TIME,
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.WEEKLY_REVIEW_CREATED,
              properties: new Map(
                Object.entries({
                  title: "Weekly Review"
                })
              ),
              metadata: {
                notificationType: NotificationMetadataTypeEnum.ANALYST_INSIGHT,
                documentId: documentId2
              }
            }
          }
        });

        // First run of the method
        await NotificationCronService.sendPendingAppContentNotifications();

        // Check first run results
        const afterFirstRunNotification1 = await Notification.findById(notification1.id);
        const afterFirstRunNotification2 = await Notification.findById(notification2.id);

        expect(afterFirstRunNotification1.status).toEqual("Sent");
        expect(afterFirstRunNotification2.status).toEqual("Pending");
        expect(OneSignalService.sendPushNotification).toHaveBeenCalledTimes(1);

        // Reset the mock before second run
        jest.clearAllMocks();

        // Set time to 15 minutes later
        Date.now = jest.fn(() => SECOND_RUN_TIME.valueOf());

        // Second run of the method
        await NotificationCronService.sendPendingAppContentNotifications();
      });

      it("should not send the second notification in the second run", async () => {
        // Verify the second notification is still pending
        const updatedNotification2 = await Notification.findById(notification2.id);
        expect(updatedNotification2.status).toEqual("Pending");

        // Verify that no calls to OneSignal were made in the second run
        expect(OneSignalService.sendPushNotification).not.toHaveBeenCalled();
      });

      it("should not affect the first notification", async () => {
        const updatedNotification1 = await Notification.findById(notification1.id);
        expect(updatedNotification1.status).toEqual("Sent");
      });
    });
  });

  describe("sendPendingDailyRecapNotifications", () => {
    beforeAll(() => {
      Date.now = originalDateNow;
    });

    it("should NOT send any notifications if there are no pending daily recap notifications", async () => {
      jest.resetAllMocks();
      jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

      await NotificationCronService.sendPendingDailyRecapNotifications();

      expect(OneSignalService.sendPushNotification).not.toHaveBeenCalled();
    });

    it("should send the daily recap notification if the user has the setting enabled", async () => {
      jest.resetAllMocks();
      jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

      const user = await buildUser({
        deviceTokens: {
          ios: faker.string.uuid()
        }
      });
      await buildNotificationSettings({
        owner: user.id,
        app: {
          settings: new Map([[AppNotificationSettingEnum.DAILY_RECAP, true]]),
          deviceNotificationsEnabled: true
        }
      });

      const title = "Daily Recap Title";
      const body = "This is the daily recap body.";
      const notification = await buildNotification({
        owner: user.id,
        status: "Pending",
        method: "app",
        providers: {
          onesignal: {
            notificationId: LearningNotificationEventEnum.DAILY_MARKET_SUMMARY,
            properties: new Map([
              ["title", title],
              ["body", body]
            ]),
            metadata: {
              notificationType: NotificationMetadataTypeEnum.OTHER,
              documentId: faker.string.uuid()
            }
          }
        },
        notifyAt: new Date()
      });

      await NotificationCronService.sendPendingDailyRecapNotifications();

      expect(OneSignalService.sendPushNotification).toHaveBeenCalledWith(
        [user.id],
        LearningNotificationEventEnum.DAILY_MARKET_SUMMARY,
        { title, body },
        expect.anything()
      );

      const updatedNotification = await Notification.findById(notification.id);
      expect(updatedNotification.status).toBe("Sent");
      expect(updatedNotification.sentAt).toBeInstanceOf(Date);
    });

    it("should NOT send the daily recap notification if the user has the setting disabled", async () => {
      jest.resetAllMocks();
      jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

      const user = await buildUser({
        deviceTokens: {
          ios: faker.string.uuid()
        }
      });
      await buildNotificationSettings({
        owner: user.id,
        app: {
          settings: new Map([[AppNotificationSettingEnum.DAILY_RECAP, false]]),
          deviceNotificationsEnabled: true
        }
      });

      const title = "Daily Recap Title";
      const body = "This is the daily recap body.";
      const notification = await buildNotification({
        owner: user.id,
        status: "Pending",
        method: "app",
        providers: {
          onesignal: {
            notificationId: LearningNotificationEventEnum.DAILY_MARKET_SUMMARY,
            properties: new Map([
              ["title", title],
              ["body", body]
            ]),
            metadata: {
              notificationType: NotificationMetadataTypeEnum.OTHER,
              documentId: faker.string.uuid()
            }
          }
        },
        notifyAt: new Date()
      });

      await NotificationCronService.sendPendingDailyRecapNotifications();

      expect(OneSignalService.sendPushNotification).not.toHaveBeenCalled();

      const updatedNotification = await Notification.findById(notification.id);
      expect(updatedNotification.status).toBe("Pending");
      expect(updatedNotification.sentAt).toBeUndefined();
    });

    it("should NOT send the daily recap notification if notifyAt is outside the time window", async () => {
      jest.resetAllMocks();
      jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

      const user = await buildUser({
        deviceTokens: {
          ios: faker.string.uuid()
        }
      });
      await buildNotificationSettings({
        owner: user.id,
        app: {
          settings: new Map([[AppNotificationSettingEnum.DAILY_RECAP, true]]),
          deviceNotificationsEnabled: true
        }
      });

      const title = "Daily Recap Title";
      const body = "This is the daily recap body.";
      const notification = await buildNotification({
        owner: user.id,
        status: "Pending",
        method: "app",
        providers: {
          onesignal: {
            notificationId: LearningNotificationEventEnum.DAILY_MARKET_SUMMARY,
            properties: new Map([
              ["title", title],
              ["body", body]
            ]),
            metadata: {
              notificationType: NotificationMetadataTypeEnum.OTHER,
              documentId: faker.string.uuid()
            }
          }
        },
        notifyAt: DateUtil.getDateOfMinutesAgo(200)
      });

      await NotificationCronService.sendPendingDailyRecapNotifications();

      expect(OneSignalService.sendPushNotification).not.toHaveBeenCalled();

      const updatedNotification = await Notification.findById(notification.id);
      expect(updatedNotification.status).toBe("Pending");
      expect(updatedNotification.sentAt).toBeUndefined();
    });

    it("should only send notifications to users with the setting enabled", async () => {
      jest.resetAllMocks();
      jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

      const userEnabled = await buildUser({
        deviceTokens: {
          ios: faker.string.uuid()
        }
      });
      const userDisabled = await buildUser({
        deviceTokens: {
          ios: faker.string.uuid()
        }
      });

      await buildNotificationSettings({
        owner: userEnabled.id,
        app: {
          settings: new Map([[AppNotificationSettingEnum.DAILY_RECAP, true]]),
          deviceNotificationsEnabled: true
        }
      });
      await buildNotificationSettings({
        owner: userDisabled.id,
        app: {
          settings: new Map([[AppNotificationSettingEnum.DAILY_RECAP, false]]),
          deviceNotificationsEnabled: true
        }
      });

      const title = "Daily Recap Title";
      const body = "This is the daily recap body.";
      const notificationEnabled = await buildNotification({
        owner: userEnabled.id,
        status: "Pending",
        method: "app",
        providers: {
          onesignal: {
            notificationId: LearningNotificationEventEnum.DAILY_MARKET_SUMMARY,
            properties: new Map([
              ["title", title],
              ["body", body]
            ]),
            metadata: {
              notificationType: NotificationMetadataTypeEnum.OTHER,
              documentId: faker.string.uuid()
            }
          }
        },
        notifyAt: new Date()
      });
      const notificationDisabled = await buildNotification({
        owner: userDisabled.id,
        status: "Pending",
        method: "app",
        providers: {
          onesignal: {
            notificationId: LearningNotificationEventEnum.DAILY_MARKET_SUMMARY,
            properties: new Map([
              ["title", title],
              ["body", body]
            ]),
            metadata: {
              notificationType: NotificationMetadataTypeEnum.OTHER,
              documentId: faker.string.uuid()
            }
          }
        },
        notifyAt: new Date()
      });

      await NotificationCronService.sendPendingDailyRecapNotifications();

      expect(OneSignalService.sendPushNotification).toHaveBeenCalledTimes(1);

      const updatedNotificationEnabled = await Notification.findById(notificationEnabled.id);
      expect(updatedNotificationEnabled.status).toBe("Sent");
      expect(updatedNotificationEnabled.sentAt).toBeInstanceOf(Date);

      const updatedNotificationDisabled = await Notification.findById(notificationDisabled.id);
      expect(updatedNotificationDisabled.status).toBe("Pending");
      expect(updatedNotificationDisabled.sentAt).toBeUndefined();
    });
  });

  describe("createDailyMarketRecapNotifications", () => {
    const MOCK_DATE = new Date("2023-05-10T01:00:00Z");

    // Mock the expected notification time (7:00 UTC / 9:00 EET)
    const EXPECTED_NOTIFICATION_TIME = new Date("2023-05-10T07:00:00Z");
    let sundownDigest: SundownDigestDocument;

    beforeEach(async () => {
      jest.resetAllMocks();
      await clearDb();

      // Set fixed date for all tests
      Date.now = jest.fn(() => MOCK_DATE.valueOf());

      // Mock the Redis client
      jest.spyOn(RedisClientService.Instance, "get").mockImplementation(async (key) => {
        if (key.startsWith("portfolios:mwrr:")) {
          return {
            [TenorEnum.ONE_DAY]: 0.0231 // 2.31% return
          };
        }
        return null;
      });

      // Create a valid sundown digest for yesterday
      sundownDigest = await buildSundownDigest({
        date: DateUtil.getYesterday(),
        content: "Raw content for digest",
        formattedContent: {
          overview: "Markets were up yesterday with tech leading gains"
        }
      });
    });

    describe("when no sundown digest is available", () => {
      beforeAll(async () => {
        await NotificationCronService.createDailyMarketRecapNotifications();
      });

      it("should not create any notifications", async () => {
        const createdNotifications = await Notification.find({});
        expect(createdNotifications.length).toBe(0);
      });
    });

    describe("when sundown digest is not from yesterday", () => {
      beforeAll(async () => {
        // Create a digest from two days ago
        await buildSundownDigest({
          date: new Date("2023-05-08"), // 2 days ago
          content: "Old digest content",
          formattedContent: {
            overview: "Markets were mixed two days ago"
          }
        });

        await NotificationCronService.createDailyMarketRecapNotifications();
      });

      it("should not create any notifications", async () => {
        const createdNotifications = await Notification.find({});
        expect(createdNotifications.length).toBe(0);
      });
    });

    describe("when processing uninvested users", () => {
      let user1: UserDocument;
      let user2: UserDocument;
      let userWithoutDevice: UserDocument;

      beforeEach(async () => {
        // Create actual users with different conversion statuses
        user1 = await buildUser({
          kycStatus: "passed",
          portfolioConversionStatus: "notStarted",
          deviceTokens: {
            android: "test-android-token"
          }
        });

        user2 = await buildUser({
          kycStatus: "passed",
          portfolioConversionStatus: "inProgress",
          deviceTokens: {
            ios: "test-ios-token"
          }
        });

        userWithoutDevice = await buildUser({
          kycStatus: "passed",
          portfolioConversionStatus: "fullyWithdrawn",
          deviceTokens: {}
        });

        await NotificationCronService.createDailyMarketRecapNotifications();
      });

      it("should create notifications for uninvested users with device tokens", async () => {
        // Get created notifications
        const createdNotifications = await Notification.find({}).lean();

        // Should create 2 notifications (for user1 and user2 with device tokens)
        expect(createdNotifications.length).toBe(2);

        // Check notifications are for the right users
        const userIds = createdNotifications.map((n) => n.owner.toString());
        expect(userIds).toContain(user1.id);
        expect(userIds).toContain(user2.id);
        expect(userIds).not.toContain(userWithoutDevice.id); // User without device token shouldn't get notification

        // Verify the notification properties
        const notification = createdNotifications.find((n) => n.owner.toString() === user1.id);
        expect(notification.method).toBe("app");
        expect(notification.status).toBe("Pending");
        expect(DateUtil.datesAreEqual(notification.notifyAt, EXPECTED_NOTIFICATION_TIME)).toBe(true);

        // Check onesignal properties
        expect(notification.providers.onesignal.notificationId).toBe(
          LearningNotificationEventEnum.DAILY_MARKET_SUMMARY
        );
        expect(notification.providers.onesignal.metadata.notificationType).toBe(
          NotificationMetadataTypeEnum.OTHER
        );
        expect(notification.providers.onesignal.metadata.documentId).toBe(sundownDigest.id);

        // Check title and body content
        const properties = new Map(Object.entries(notification.providers.onesignal.properties));
        expect(properties.get("title")).toBe("Daily Market Recap");
        expect(properties.get("body")).toContain(sundownDigest.formattedContent.overview);
      });
    });

    describe("when processing invested users", () => {
      let investedUser1: UserDocument;
      let investedUser2: UserDocument;

      beforeEach(async () => {
        // Create invested users with portfolios
        investedUser1 = await buildUser({
          kycStatus: "passed",
          portfolioConversionStatus: "completed",
          deviceTokens: {
            android: faker.string.uuid(),
            ios: faker.string.uuid()
          }
        });

        // Create a portfolio with holdings for user1
        const holding = await buildHoldingDTO(
          true,
          faker.helpers.arrayElement(investmentUniverseConfig.AssetArrayConst),
          10
        );
        await buildPortfolio({
          owner: investedUser1.id,
          holdings: [holding]
        });
        await investedUser1.populate("portfolios");

        investedUser2 = await buildUser({
          kycStatus: "passed",
          portfolioConversionStatus: "completed",
          deviceTokens: {
            android: faker.string.uuid(),
            ios: faker.string.uuid()
          }
        });

        // Create a portfolio with holdings for user2
        const holding2 = await buildHoldingDTO(
          true,
          faker.helpers.arrayElement(investmentUniverseConfig.AssetArrayConst),
          5
        );
        await buildPortfolio({
          owner: investedUser2.id,
          holdings: [holding2]
        });
        await investedUser2.populate("portfolios");

        await NotificationCronService.createDailyMarketRecapNotifications();
      });

      it("should create notifications for invested users with portfolio returns", async () => {
        // Get created notifications
        const createdNotifications = await Notification.find({}).lean();

        // Should create 2 notifications (one for each invested user)
        expect(createdNotifications.length).toBe(2);

        // Check notifications are for the right users
        const userIds = createdNotifications.map((n) => n.owner.toString());
        expect(userIds).toContain(investedUser1.id);
        expect(userIds).toContain(investedUser2.id);

        // Verify the notification properties for investedUser1
        const notification = createdNotifications.find((n) => n.owner.toString() === investedUser1.id);

        // Check structure
        expect(notification.method).toBe("app");
        expect(notification.status).toBe("Pending");
        expect(DateUtil.datesAreEqual(notification.notifyAt, EXPECTED_NOTIFICATION_TIME)).toBe(true);

        // Check onesignal properties
        expect(notification.providers.onesignal.notificationId).toBe(
          LearningNotificationEventEnum.DAILY_MARKET_SUMMARY
        );

        // Check title contains portfolio gain information (2.31%)
        const properties = new Map(Object.entries(notification.providers.onesignal.properties));
        expect(properties.get("title")).toBe("📈 Daily recap: +2.31%");
        expect(properties.get("body")).toContain(`${sundownDigest.formattedContent.overview}`);
      });
    });

    describe("when portfolio returns are negative", () => {
      let investedUser: UserDocument;

      beforeEach(async () => {
        // Override Redis mock to return negative returns
        jest.spyOn(RedisClientService.Instance, "get").mockImplementation(async (key) => {
          if (key.startsWith("portfolios:mwrr:")) {
            return {
              [TenorEnum.ONE_DAY]: -0.0184 // -1.84% return
            };
          }
          return null;
        });

        // Create an invested user with portfolio
        investedUser = await buildUser({
          kycStatus: "passed",
          portfolioConversionStatus: "completed",
          deviceTokens: {
            ios: faker.string.uuid()
          }
        });

        // Create a portfolio with holdings
        const holding = await buildHoldingDTO(
          true,
          faker.helpers.arrayElement(investmentUniverseConfig.AssetArrayConst),
          10
        );
        await buildPortfolio({
          owner: investedUser.id,
          holdings: [holding]
        });
        await investedUser.populate("portfolios");

        await NotificationCronService.createDailyMarketRecapNotifications();
      });

      it("should create notifications with portfolio dip information", async () => {
        // Get created notifications
        const createdNotifications = await Notification.find({}).lean();

        // Should create 1 notification
        expect(createdNotifications.length).toBe(1);

        // Verify it's for the right user
        expect(createdNotifications[0].owner.toString()).toBe(investedUser.id);

        // Check title contains portfolio dip information
        const properties = new Map(Object.entries(createdNotifications[0].providers.onesignal.properties));
        expect(properties.get("title")).toBe("📉 Daily recap: -1.84%");
      });
    });

    describe("when notifications already exist for today", () => {
      const MOCK_DATE = new Date("2023-05-10T01:00:00Z");
      let existingNotification: NotificationDocument;
      let sundownDigest: SundownDigestDocument;

      beforeEach(async () => {
        jest.resetAllMocks();

        // Set fixed date for test
        Date.now = jest.fn(() => MOCK_DATE.valueOf());

        // Create a valid sundown digest for yesterday
        sundownDigest = await buildSundownDigest({
          date: DateUtil.getYesterday(),
          content: "Raw content for digest",
          formattedContent: {
            overview: "Markets were up yesterday with tech leading gains"
          }
        });

        // Create a user
        const user = await buildUser({
          deviceTokens: {
            ios: faker.string.uuid()
          }
        });

        // Create an existing notification for today
        existingNotification = await buildNotification({
          owner: user.id,
          status: "Pending",
          method: "app",
          notifyAt: new Date(MOCK_DATE),
          providers: {
            onesignal: {
              notificationId: LearningNotificationEventEnum.DAILY_MARKET_SUMMARY,
              properties: new Map([
                ["title", "Daily Market Recap"],
                ["body", "Market overview for today"]
              ]),
              metadata: {
                notificationType: NotificationMetadataTypeEnum.OTHER,
                documentId: sundownDigest.id
              }
            }
          }
        });

        // Mock logger to verify it was called
        jest.spyOn(logger, "info");

        // Call the method under test
        await NotificationCronService.createDailyMarketRecapNotifications();
      });

      afterEach(async () => await clearDb());

      it("should not create any new notifications", async () => {
        // Get all notifications after the method call
        const notifications = await Notification.find({
          "providers.onesignal.notificationId": LearningNotificationEventEnum.DAILY_MARKET_SUMMARY
        });

        // Should only have the one we created before
        expect(notifications).toHaveLength(1);
        expect(notifications[0].id).toEqual(existingNotification.id);
      });

      it("should log that notifications already exist", async () => {
        expect(logger.info).toHaveBeenCalledWith(
          expect.stringContaining("Daily market recap notification already exists for today"),
          expect.anything()
        );
      });
    });

    describe("when Redis cache is unavailable for portfolio returns", () => {
      let investedUser: UserDocument;

      beforeEach(async () => {
        // Override Redis mock to return null
        jest.spyOn(RedisClientService.Instance, "get").mockResolvedValue(null);

        // Create an invested user with portfolio
        investedUser = await buildUser({
          kycStatus: "passed",
          portfolioConversionStatus: "completed",
          deviceTokens: {
            ios: faker.string.uuid()
          }
        });

        // Create a portfolio with holdings
        const holding = await buildHoldingDTO(
          true,
          faker.helpers.arrayElement(investmentUniverseConfig.AssetArrayConst),
          10
        );
        await buildPortfolio({
          owner: investedUser.id,
          holdings: [holding]
        });
        await investedUser.populate("portfolios");

        await NotificationCronService.createDailyMarketRecapNotifications();
      });

      it("should fall back to generic notification format", async () => {
        // Get created notifications
        const createdNotifications = await Notification.find({}).lean();

        // Should create 1 notification
        expect(createdNotifications.length).toBe(1);

        // Verify it's for the right user
        expect(createdNotifications[0].owner.toString()).toBe(investedUser.id);

        // Check title is generic
        const properties = new Map(Object.entries(createdNotifications[0].providers.onesignal.properties));
        expect(properties.get("title")).toBe("Daily Market Recap");
      });
    });
  });

  describe("markStagnantNotificationsAsSkipped", () => {
    describe("when there is a notification that has to be sent today", () => {
      let notification: NotificationDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        notification = await buildNotification({
          status: "Pending",
          notifyAt: DateUtil.getDateOfHoursAgo(TODAY, 10)
        });

        await NotificationCronService.markStagnantNotificationsAsSkipped();
      });

      it("should NOT update notification to 'Skipped'", async () => {
        const updatedNotification = await Notification.findById(notification.id);
        expect(updatedNotification.status).toEqual("Pending");
      });
    });

    describe("when there is a notification that had to be sent yesterday", () => {
      let notification: NotificationDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(OneSignalService, "sendPushNotification").mockResolvedValue({ id: faker.string.uuid() });

        const TODAY = new Date("2022-08-31T11:00:00Z");
        const YESTERDAY = new Date("2022-08-30T23:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        notification = await buildNotification({
          status: "Pending",
          notifyAt: YESTERDAY
        });

        await NotificationCronService.markStagnantNotificationsAsSkipped();
      });

      it("should update notification to 'Skipped'", async () => {
        const updatedNotification = await Notification.findById(notification.id);
        expect(updatedNotification.status).toEqual("Skipped");
      });
    });
  });
});
