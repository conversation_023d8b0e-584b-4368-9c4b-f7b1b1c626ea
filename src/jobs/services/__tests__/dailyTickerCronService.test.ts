import BlackrockService from "../../../external-services/blackrockService";
import {
  DailyPortfolioSavingsTicker,
  DailyPortfolioSavingsTickerDocument,
  DailySavingsProductTicker,
  DailySavingsProductTickerDocument
} from "../../../models/DailyTicker";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import {
  buildDailyPortfolioSavingsTicker,
  buildPortfolio,
  buildSavingsProduct,
  buildSubscription,
  buildUser
} from "../../../tests/utils/generateModels";
import DailyTickerCronService from "../dailyTickerCronService";
import { buildBlackrockFundData } from "../../../tests/utils/generateBlackrock";
import DateUtil from "../../../utils/dateUtil";
import { SavingsProductDocument } from "../../../models/SavingsProduct";
import Decimal from "decimal.js";
import { SubscriptionDocument } from "../../../models/Subscription";
import { PortfolioDocument } from "../../../models/Portfolio";
import eventEmitter from "../../../loaders/eventEmitter";
import events from "../../../event-handlers/events";
import { RedisClientService } from "../../../loaders/redis";
import { TrackSavingsProductDataUpdatePropertiesType } from "../../../external-services/segmentAnalyticsService";
import { savingsUniverseConfig } from "@wealthyhood/shared-configs";

describe("DailyTickerCronService", () => {
  beforeAll(async () => await connectDb("DailyTickerCronService"));
  afterAll(async () => await closeDb());

  describe("createDailySavingsProductTickers", () => {
    describe("when there are not savings products to create tickers for", () => {
      beforeAll(async () => {
        jest.clearAllMocks();
        await DailyTickerCronService.createDailySavingsProductTickers();
      });
      afterAll(async () => await clearDb());

      it("should not create any daily savings product tickers", async () => {
        const savingsProductTickers: DailySavingsProductTickerDocument[] = await DailySavingsProductTicker.find();
        expect(savingsProductTickers).toHaveLength(0);
      });
    });

    describe("when there is a savings product outside of our savingsUniverse", () => {
      beforeAll(async () => {
        jest.clearAllMocks();
        await buildSavingsProduct(false, {
          commonId: "mmf_wealthyhood" as any
        });

        await DailyTickerCronService.createDailySavingsProductTickers();
      });
      afterAll(async () => await clearDb());

      it("should not create any daily savings product tickers", async () => {
        const savingsProductTickers: DailySavingsProductTickerDocument[] = await DailySavingsProductTicker.find();
        expect(savingsProductTickers).toHaveLength(0);
      });
    });

    describe("when there is a savings product and yesterday is a week day", () => {
      let savingsProduct: SavingsProductDocument;

      afterEach(async () => await clearDb());

      [
        new Date("2024-03-19"), // Tuesday
        new Date("2024-03-20"), // Wednesday
        new Date("2024-03-21"), // Thursday
        new Date("2024-03-22") // Friday
      ].map((TODAY) =>
        it("should create a savings product ticker for yesterday's date", async () => {
          jest.clearAllMocks();
          Date.now = jest.fn(() => +TODAY);
          const yesterday = DateUtil.getDateOfDaysAgo(TODAY, 1);
          const TICKER_DATE = DateUtil.getDateAfterNHours(yesterday, 12); // Yesterday in the middle of the day, to avoid timezone issues
          const FIXING_DATE = yesterday; // Fixing date is yesterday
          const FUND_DATA = buildBlackrockFundData({
            fixingDate: FIXING_DATE
          });
          savingsProduct = await buildSavingsProduct(false, {
            commonId: "mmf_dist_gbp"
          });

          jest.spyOn(BlackrockService, "getDistributingMoneyMarketFundData").mockResolvedValue(FUND_DATA);

          await DailyTickerCronService.createDailySavingsProductTickers();
          const tickers = await DailySavingsProductTicker.find({
            savingsProduct: savingsProduct._id
          });

          expect(tickers).toHaveLength(1);
          expect(tickers[0]).toMatchObject({
            date: TICKER_DATE,
            dailyDistributionFactor: FUND_DATA.dailyDistributionFactor,
            oneDayYield: FUND_DATA.oneDayYield,
            fixingDate: FUND_DATA.fixingDate
          });
        })
      );
    });

    describe("when there is a savings product and the fixing date is from 2 days ago", () => {
      let savingsProduct: SavingsProductDocument;
      const TODAY = new Date("2024-04-04"); // Today is Thursday, yesterday is Wednesday
      const TICKER_DATE = new Date("2024-04-04T12:00:00.000Z"); // Today in the middle of the day, to avoid timezone issues
      // Fixing date from 2 days ago, without being a holiday or weekend in between
      const FIXING_DATE = new Date("2024-04-02");
      const FUND_DATA = buildBlackrockFundData({
        fixingDate: FIXING_DATE
      });

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        savingsProduct = await buildSavingsProduct(false, {
          commonId: "mmf_dist_gbp"
        });

        jest.spyOn(BlackrockService, "getDistributingMoneyMarketFundData").mockResolvedValue(FUND_DATA);

        await DailyTickerCronService.createDailySavingsProductTickers();
      });
      afterAll(async () => await clearDb());

      it("should not create a savings product ticker for today's date", async () => {
        const savingsProductTickers: DailySavingsProductTickerDocument[] = await DailySavingsProductTicker.find();
        expect(savingsProductTickers).toHaveLength(0);
      });
    });

    describe("when there is a savings product and yesterday belongs to a 4-day break (Holiday + Weekend + Holiday)", () => {
      afterEach(async () => await clearDb());
      const FUND_DATA = buildBlackrockFundData({
        fixingDate: new Date("2024-03-28"), // Fixing date is from the last workday (Thursday)
        dailyDistributionFactor: 0.********,
        oneDayYield: 5.27
      });
      const dailyDistributionFactorSplit = Decimal.div(FUND_DATA.dailyDistributionFactor, 5).toNumber();

      [
        new Date("2024-03-29"), // Friday UK bank holiday
        new Date("2024-03-30"), // Saturday
        new Date("2024-03-31"), // Sunday
        new Date("2024-04-01") // Monday Bank holiday
      ].map((TICKER_DATE) =>
        it("should not create a savings product ticker for yesterdays's date", async () => {
          jest.clearAllMocks();
          Date.now = jest.fn(() => +DateUtil.getDateAfterNdays(TICKER_DATE, 1));
          await buildSavingsProduct(false, {
            commonId: "mmf_dist_gbp"
          });

          jest.spyOn(BlackrockService, "getDistributingMoneyMarketFundData").mockResolvedValue(FUND_DATA);

          await DailyTickerCronService.createDailySavingsProductTickers();

          const savingsProductTickers: DailySavingsProductTickerDocument[] =
            await DailySavingsProductTicker.find();
          expect(savingsProductTickers).toHaveLength(0);
        })
      );

      it("should create a savings product ticker for each day (5 total)", async () => {
        jest.clearAllMocks();
        const TICKER_DATE = new Date("2024-03-28T12:00:00.000Z"); // Thursday normal workday
        Date.now = jest.fn(() => +DateUtil.getDateAfterNdays(TICKER_DATE, 1));
        await buildSavingsProduct(false, {
          commonId: "mmf_dist_gbp"
        });

        jest.spyOn(BlackrockService, "getDistributingMoneyMarketFundData").mockResolvedValue(FUND_DATA);

        await DailyTickerCronService.createDailySavingsProductTickers();

        const tickers: DailySavingsProductTickerDocument[] = await DailySavingsProductTicker.find();

        expect(tickers).toHaveLength(5);
        expect(tickers).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              date: TICKER_DATE,
              dailyDistributionFactor: dailyDistributionFactorSplit,
              oneDayYield: FUND_DATA.oneDayYield,
              fixingDate: FUND_DATA.fixingDate
            }),
            expect.objectContaining({
              date: new Date("2024-03-29T12:00:00.000Z"),
              dailyDistributionFactor: dailyDistributionFactorSplit,
              oneDayYield: FUND_DATA.oneDayYield,
              fixingDate: FUND_DATA.fixingDate
            }),
            expect.objectContaining({
              date: new Date("2024-03-30T12:00:00.000Z"),
              dailyDistributionFactor: dailyDistributionFactorSplit,
              oneDayYield: FUND_DATA.oneDayYield,
              fixingDate: FUND_DATA.fixingDate
            }),
            expect.objectContaining({
              date: new Date("2024-03-31T12:00:00.000Z"),
              dailyDistributionFactor: dailyDistributionFactorSplit,
              oneDayYield: FUND_DATA.oneDayYield,
              fixingDate: FUND_DATA.fixingDate
            }),
            expect.objectContaining({
              date: new Date("2024-04-01T12:00:00.000Z"),
              dailyDistributionFactor: dailyDistributionFactorSplit,
              oneDayYield: FUND_DATA.oneDayYield,
              fixingDate: FUND_DATA.fixingDate
            })
          ])
        );
      });
    });

    describe("when there is a savings product and yesterday is Friday", () => {
      afterEach(async () => await clearDb());

      let savingsProduct: SavingsProductDocument;
      const TODAY = new Date("2024-03-23"); // Today is Saturday, yesterday is Friday
      const FRIDAY_TICKER_DATE = new Date("2024-03-22T12:00:00.000Z"); // Yesterday in the middle of the day, to avoid timezone issues
      const SATURDAY_TICKER_DATE = new Date("2024-03-23T12:00:00.000Z");
      const SUNDAY_TICKER_DATE = new Date("2024-03-24T12:00:00.000Z");
      // Fixing date from 2 days ago, without being a holiday or weekend in between
      const FIXING_DATE = new Date("2024-03-22");
      const FUND_DATA = buildBlackrockFundData({
        fixingDate: FIXING_DATE,
        dailyDistributionFactor: 0.000428505,
        oneDayYield: 5.21
      });

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        savingsProduct = await buildSavingsProduct(false, {
          commonId: "mmf_dist_gbp"
        });

        jest.spyOn(BlackrockService, "getDistributingMoneyMarketFundData").mockResolvedValue(FUND_DATA);

        await DailyTickerCronService.createDailySavingsProductTickers();
      });
      afterAll(async () => await clearDb());

      it("should create a savings product ticker for friday AND the weekend", async () => {
        const tickers: DailySavingsProductTickerDocument[] = await DailySavingsProductTicker.find({
          savingsProduct: savingsProduct._id
        });

        const dailyDistributionFactorSplit = Decimal.div(FUND_DATA.dailyDistributionFactor, 3).toNumber();

        expect(tickers).toHaveLength(3);
        expect(tickers).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              date: FRIDAY_TICKER_DATE,
              dailyDistributionFactor: dailyDistributionFactorSplit,
              oneDayYield: FUND_DATA.oneDayYield,
              fixingDate: FUND_DATA.fixingDate
            }),
            expect.objectContaining({
              date: SATURDAY_TICKER_DATE,
              dailyDistributionFactor: dailyDistributionFactorSplit,
              oneDayYield: FUND_DATA.oneDayYield,
              fixingDate: FUND_DATA.fixingDate
            }),
            expect.objectContaining({
              date: SUNDAY_TICKER_DATE,
              dailyDistributionFactor: dailyDistributionFactorSplit,
              oneDayYield: FUND_DATA.oneDayYield,
              fixingDate: FUND_DATA.fixingDate
            })
          ])
        );
      });
    });

    describe("when there is a savings product and the ticker already exists for yesterday", () => {
      let savingsProduct: SavingsProductDocument;
      const TODAY = new Date("2024-04-04"); // Today is Thursday, yesterday is Wednesday
      const FIXING_DATE = new Date("2024-04-02");
      const FUND_DATA = buildBlackrockFundData({
        fixingDate: FIXING_DATE
      });

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        savingsProduct = await buildSavingsProduct(
          true,
          {
            commonId: "mmf_dist_gbp"
          },
          {
            date: DateUtil.getDateOfDaysAgo(TODAY, 1),
            fixingDate: DateUtil.getDateOfDaysAgo(TODAY, 1)
          }
        );

        jest.spyOn(BlackrockService, "getDistributingMoneyMarketFundData").mockResolvedValue(FUND_DATA);

        await DailyTickerCronService.createDailySavingsProductTickers();
      });
      afterAll(async () => await clearDb());

      it("should not create a second ticker", async () => {
        const tickers: DailySavingsProductTickerDocument[] = await DailySavingsProductTicker.find({
          savingsProduct: savingsProduct._id
        });
        expect(tickers).toHaveLength(1);
        // It's the same ticker
        expect(tickers[0].id).toEqual(savingsProduct.currentTicker.id);
      });
    });
  });

  describe("createDailyPortfolioSavingsTicker", () => {
    describe("when ticker already exists for yesterday", () => {
      let portfolioSavingsTicker: DailyPortfolioSavingsTickerDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        const user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          savings: new Map([
            [
              "mmf_dist_gbp",
              {
                amount: 100000,
                currency: "GBP"
              }
            ]
          ])
        });
        const subscription = await buildSubscription({
          owner: user.id
        });
        const savingsProduct = await buildSavingsProduct(true, {
          commonId: "mmf_dist_gbp"
        });

        portfolioSavingsTicker = await buildDailyPortfolioSavingsTicker({
          portfolio: portfolio.id,
          planPrice: subscription.price,
          savingsProduct,
          date: DateUtil.getYesterday()
        });

        await DailyTickerCronService.createDailyPortfolioSavingsTicker();
      });
      afterAll(async () => await clearDb());

      it("should not create a second ticker", async () => {
        const tickers = await DailyPortfolioSavingsTicker.find({
          portfolio: portfolioSavingsTicker.portfolio,
          savingsProduct: portfolioSavingsTicker.savingsProduct
        });

        expect(tickers).toHaveLength(1);
        expect(tickers[0].id).toEqual(portfolioSavingsTicker.id);
      });
    });

    describe("when the user has no portfolio savings", () => {
      beforeAll(async () => {
        jest.clearAllMocks();
        const user = await buildUser();
        await Promise.all([
          buildSubscription({
            owner: user.id
          }),
          buildPortfolio({
            owner: user.id,
            savings: undefined
          }),
          buildSavingsProduct(true, {
            commonId: "mmf_dist_gbp"
          })
        ]);

        await DailyTickerCronService.createDailyPortfolioSavingsTicker();
      });
      afterAll(async () => await clearDb());

      it("should not create a ticker", async () => {
        const tickers = await DailyPortfolioSavingsTicker.find();

        expect(tickers).toHaveLength(0);
      });
    });

    describe("when the user has portfolio savings amount equal to 0", () => {
      beforeAll(async () => {
        jest.clearAllMocks();
        const user = await buildUser();
        await Promise.all([
          buildSubscription({
            owner: user.id
          }),
          buildPortfolio({
            owner: user.id,
            savings: new Map([
              [
                "mmf_dist_gbp",
                {
                  amount: 0,
                  currency: "GBP"
                }
              ]
            ])
          }),
          buildSavingsProduct(true, {
            commonId: "mmf_dist_gbp"
          })
        ]);

        await DailyTickerCronService.createDailyPortfolioSavingsTicker();
      });
      afterAll(async () => await clearDb());

      it("should not create a ticker", async () => {
        const tickers = await DailyPortfolioSavingsTicker.find();

        expect(tickers).toHaveLength(0);
      });
    });

    describe("when the user has portfolio savings amount but a savings product ticker doesn't exist", () => {
      beforeAll(async () => {
        jest.clearAllMocks();
        const user = await buildUser();
        await Promise.all([
          buildSubscription({
            owner: user.id
          }),
          buildPortfolio({
            owner: user.id,
            savings: new Map([
              [
                "mmf_dist_gbp",
                {
                  amount: 0,
                  currency: "GBP"
                }
              ]
            ])
          }),
          buildSavingsProduct(
            true,
            {
              commonId: "mmf_dist_gbp"
            },
            {
              date: DateUtil.getDateOfDaysAgo(new Date(), 3)
            }
          )
        ]);

        await DailyTickerCronService.createDailyPortfolioSavingsTicker();
      });
      afterAll(async () => await clearDb());

      it("should not create a ticker", async () => {
        const tickers = await DailyPortfolioSavingsTicker.find();

        expect(tickers).toHaveLength(0);
      });
    });

    describe("when the user has portfolio savings", () => {
      let portfolio: PortfolioDocument;
      let subscription: SubscriptionDocument;
      const SAVINGS_PRD_ID = "mmf_dist_gbp";
      const SAVINGS_AMOUNT = 100000; // £1000
      const PLAN_FEE = 0.006;
      const WLTH_FEE = Decimal.div(PLAN_FEE, 365); // One day fee
      const DDF = 0.001; // Daily Distribution Factor
      /**
       * Daily accrual post fees:
       * (DDF - Daily plan fee) * Savings amount
       */
      const DAILY_ACCRUAL_POST_FEES = Decimal.mul(SAVINGS_AMOUNT, Decimal.sub(DDF, WLTH_FEE)).toNumber();
      const TODAY = new Date("2024-03-25");
      const TICKER_DATE = new Date("2024-03-24T12:00:00.000Z"); // Yesterday in the middle of the day, to avoid timezone issues

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        const user = await buildUser();
        [subscription, portfolio] = await Promise.all([
          buildSubscription({
            owner: user.id
          }),
          buildPortfolio({
            owner: user.id,
            savings: new Map([
              [
                SAVINGS_PRD_ID,
                {
                  amount: SAVINGS_AMOUNT,
                  currency: "GBP"
                }
              ]
            ])
          }),
          buildSavingsProduct(
            true,
            {
              commonId: SAVINGS_PRD_ID
            },
            {
              dailyDistributionFactor: DDF,
              oneDayYield: Decimal.mul(DDF, 365).toNumber(),
              date: TICKER_DATE
            }
          )
        ]);

        await DailyTickerCronService.createDailyPortfolioSavingsTicker();
      });
      afterAll(async () => await clearDb());

      it("should create a ticker with the correct daily accrual", async () => {
        const tickers = await DailyPortfolioSavingsTicker.find({ portfolio: portfolio.id });

        expect(tickers).toHaveLength(1);
        expect(tickers[0]).toMatchObject({
          date: TICKER_DATE,
          planPrice: subscription.price,
          holdingAmount: SAVINGS_AMOUNT,
          planFee: PLAN_FEE,
          dailyAccrual: DAILY_ACCRUAL_POST_FEES
        });
      });
    });
  });

  describe("checkSavingsProductDataUpdate", () => {
    describe("When the saving product data have not yet been updated", () => {
      const TODAY = new Date("2024-04-04"); // Thursday
      const YESTERDAY = new Date("2024-04-03"); // Wednesday
      const SAVINGS_PRODUCT_COMMON_ID = "mmf_dist_gbp";

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => +TODAY);

        await buildSavingsProduct(true, {
          commonId: SAVINGS_PRODUCT_COMMON_ID
        });
        const FUND_DATA = buildBlackrockFundData({
          fixingDate: YESTERDAY
        });

        jest.spyOn(BlackrockService, "getDistributingMoneyMarketFundData").mockResolvedValue(FUND_DATA);
        RedisClientService.Instance.set(`savings:data:${SAVINGS_PRODUCT_COMMON_ID}`, FUND_DATA);
        await DailyTickerCronService.checkSavingsProductDataUpdate();
      });

      afterAll(async () => {
        await RedisClientService.Instance.del(`savings:data:${SAVINGS_PRODUCT_COMMON_ID}`);
        await clearDb();
      });

      it("should NOT emit an event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.savingsProduct.savingProductDataUpdate.eventId,
          expect.anything()
        );
      });
    });

    describe("When the saving product data have already been updated for the day", () => {
      const TODAY = new Date("2024-04-04");
      const SAVINGS_PRODUCT_COMMON_ID = "mmf_dist_gbp";

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => +TODAY);

        await buildSavingsProduct(true, {
          commonId: SAVINGS_PRODUCT_COMMON_ID
        });
        const FUND_DATA = buildBlackrockFundData({
          fixingDate: TODAY
        });
        RedisClientService.Instance.set(`savings:data:${SAVINGS_PRODUCT_COMMON_ID}`, FUND_DATA);
        await DailyTickerCronService.checkSavingsProductDataUpdate();
      });
      afterAll(async () => await clearDb());

      it("should NOT emit an event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.savingsProduct.savingProductDataUpdate.eventId,
          expect.anything()
        );
      });
    });

    describe("When the saving product data have been updated and we haven't cached it yet", () => {
      const TODAY = new Date("2024-04-04"); // Thursday
      const YESTERDAY = new Date("2024-04-03"); // Wednesday
      const SAVINGS_PRODUCT_COMMON_ID = "mmf_dist_gbp";

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => +TODAY);

        await buildSavingsProduct(true, {
          commonId: SAVINGS_PRODUCT_COMMON_ID
        });
        const FUND_DATA_OLD = buildBlackrockFundData({
          fixingDate: YESTERDAY
        });
        const FUND_DATA_NEW = buildBlackrockFundData({
          fixingDate: TODAY,
          oneDayYield: 1,
          dailyDistributionFactor: 1
        });

        jest.spyOn(BlackrockService, "getDistributingMoneyMarketFundData").mockResolvedValue(FUND_DATA_NEW);
        RedisClientService.Instance.set(`savings:data:${SAVINGS_PRODUCT_COMMON_ID}`, FUND_DATA_OLD);

        await DailyTickerCronService.checkSavingsProductDataUpdate();
      });

      afterAll(async () => {
        await RedisClientService.Instance.del(`savings:data:${SAVINGS_PRODUCT_COMMON_ID}`);
        await clearDb();
      });

      it("should emit an event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(events.savingsProduct.savingProductDataUpdate.eventId, {
          savingsProductLabel:
            savingsUniverseConfig.SAVINGS_PRODUCT_CONFIG_GLOBAL[SAVINGS_PRODUCT_COMMON_ID].label,
          lastUpdateDate: TODAY,
          oneDayYield: 1,
          dailyDistributionFactor: 1
        } as TrackSavingsProductDataUpdatePropertiesType);
      });
    });
  });
});
