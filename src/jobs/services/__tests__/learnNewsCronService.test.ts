import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import { FMPService } from "../../../external-services/fmpService";
import LearnNewsCronService from "../learnNewsCronService";
import { LearnNews } from "../../../models/LearnNews";
import { buildLearnNews } from "../../../tests/utils/generateModels";

describe("LearnNewsCronService", () => {
  beforeAll(async () => await connectDb("LearnNewsCronService"));
  afterAll(async () => await closeDb());

  describe("createLearnNews", () => {
    describe("when <PERSON><PERSON> throws an error", () => {
      const NOW = new Date("2024-10-11");

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        jest.spyOn(FMPService.Instance, "getNewsArticles").mockRejectedValue(new Error("FMP error"));

        await LearnNewsCronService.createLearnNews();
      });
      afterAll(async () => await clearDb());

      it("should not ingest any new news", async () => {
        const news = await LearnNews.find();
        expect(news).toHaveLength(0);
      });
    });

    describe("when FMP does not give us any news", () => {
      const NOW = new Date("2024-10-11");

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        jest.spyOn(FMPService.Instance, "getNewsArticles").mockResolvedValue([]);

        await LearnNewsCronService.createLearnNews();
      });
      afterAll(async () => await clearDb());

      it("should not ingest any new news", async () => {
        const news = await LearnNews.find();
        expect(news).toHaveLength(0);
      });
    });

    describe("when FMP does give us news but we already have them ingested", () => {
      const NOW = new Date("2024-10-11");

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        const article = {
          title: "Stock Market Update",
          date: "2024-10-10 02:47:21",
          content: "Stocks rallied today.",
          tickers: "STOCK:AAPL",
          image: "market.jpg"
        };

        jest.spyOn(FMPService.Instance, "getNewsArticles").mockResolvedValue([article]);

        await buildLearnNews({
          hash: "e2ac2ae89d09991935a9b886c631913447fc8a22ad676a2491a6ff3556ed9d84", // The calculated hash of the above data
          imageUrl: article.image,
          title: article.title,
          htmlContent: article.content,
          date: new Date(article.date),
          readingTime: "1 min"
        });

        await LearnNewsCronService.createLearnNews();
      });
      afterAll(async () => await clearDb());

      it("should not ingest any new news", async () => {
        const news = await LearnNews.find();
        expect(news).toHaveLength(1);
      });
    });

    describe("when FMP does give us news and we don't have them ingested", () => {
      const NOW = new Date("2024-10-10");
      const TITLE = "Stock Market Update";
      const IMAGE_URL = "market.jpg";

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        const article = {
          title: TITLE,
          date: "2024-10-10 02:47:21",
          content: "Stocks rallied today.",
          tickers: "STOCK:AAPL",
          image: IMAGE_URL
        };

        jest.spyOn(FMPService.Instance, "getNewsArticles").mockResolvedValue([article]);

        await LearnNewsCronService.createLearnNews();
      });
      afterAll(async () => await clearDb());

      it("should ingest one news article", async () => {
        const news = await LearnNews.find();
        expect(news).toHaveLength(1);

        expect(news[0].toObject()).toEqual(
          expect.objectContaining({
            hash: "e2ac2ae89d09991935a9b886c631913447fc8a22ad676a2491a6ff3556ed9d84", // The calculated hash of the above data
            title: TITLE,
            htmlContent: "<p>Main content</p>",
            imageUrl: IMAGE_URL,
            date: new Date("2024-10-10T06:47:21Z"),
            tickers: "STOCK:AAPL"
          })
        );
      });
    });

    describe("when FMP does give us news with different reading times", () => {
      const NOW = new Date("2024-10-10");
      const ARTICLES = [
        {
          title: "Short News",
          date: "2024-10-10 02:47:21",
          content: "Short news.",
          tickers: "STOCK;AAPL",
          image: "short.jpg"
        },
        {
          title: "Medium News",
          date: "2024-10-10 02:47:21",
          content: "This is a medium length news article with a few more words to test reading time estimation.",
          tickers: "STOCK;GOOG",
          image: "medium.jpg"
        },
        {
          title: "Long News",
          date: "2024-10-10 02:47:21",
          content: Array(1000).fill("word").join(" "), // 1000 words
          tickers: "STOCK;MSFT",
          image: "long.jpg"
        }
      ];

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        jest.spyOn(FMPService.Instance, "getNewsArticles").mockResolvedValue(ARTICLES);

        await LearnNewsCronService.createLearnNews();
      });
      afterAll(async () => await clearDb());

      it("should ingest 3 new news entries with correct data", async () => {
        const news = await LearnNews.find().sort({ date: -1 });
        expect(news).toHaveLength(3);

        const shortNews = news.find((n) => n.title === "Short News");
        const mediumNews = news.find((n) => n.title === "Medium News");
        const longNews = news.find((n) => n.title === "Long News");

        expect(shortNews!.readingTime).toBe("1 min");
        expect(mediumNews!.readingTime).toBe("1 min");
        expect(longNews!.readingTime).toBe("5 mins"); // 1000 words / 200 = 5 mins
      });
    });
  });
});
