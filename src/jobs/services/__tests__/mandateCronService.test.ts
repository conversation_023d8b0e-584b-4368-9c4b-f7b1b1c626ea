import { faker } from "@faker-js/faker";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import { UserDocument } from "../../../models/User";
import { BankAccountDocument } from "../../../models/BankAccount";
import {
  buildAddress,
  buildAssetTransaction,
  buildBankAccount,
  buildDepositCashTransaction,
  buildMandate,
  buildSavingsTopUpAutomation,
  buildTopUpAutomation,
  buildUser
} from "../../../tests/utils/generateModels";
import { Mandate, MandateDocument } from "../../../models/Mandate";
import MandateCronService from "../mandateCronService";
import { Automation, AutomationDocument } from "../../../models/Automation";
import { DepositCashTransaction, DepositCashTransactionDocument } from "../../../models/Transaction";
import AutomationService from "../../../services/automationService";
import { ProviderEnum } from "../../../configs/providersConfig";
import { GoCardlessPaymentsService } from "../../../external-services/goCardlessPaymentsService";
import { AddressDocument } from "../../../models/Address";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { buildGoCardlessFulfiledBillingRequest } from "../../../tests/utils/generateGoCardless";
import { WealthkernelService } from "../../../external-services/wealthkernelService";

describe("MandateCronService", () => {
  beforeAll(async () => await connectDb("MandateCronService"));
  afterAll(async () => await closeDb());

  describe("createAllWkMandates", () => {
    describe("when mandate owner does not have a Wealthkernel party", () => {
      let user: UserDocument;
      let mandate: MandateDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createMandate");

        user = await buildUser();
        mandate = await buildMandate({
          owner: user.id,
          category: "Top-Up",
          bankAccount: user.bankAccounts[0].id
        });
        await buildAddress({
          owner: user.id
        });

        await MandateCronService.createAllWkMandates();
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.createMandate).not.toHaveBeenCalled();
      });

      it("should not update the mandate document", async () => {
        const updatedMandate = (await Mandate.findById(mandate.id)) as MandateDocument;
        expect(updatedMandate.providers?.wealthkernel?.id).toBe(undefined);
      });
    });

    describe("when mandate owner does not have a Wealthkernel bank account", () => {
      let user: UserDocument;
      let mandate: MandateDocument;
      let bankAccount: BankAccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createMandate");

        user = await buildUser({ providers: { wealthkernel: { id: "some-party" } } }, false);
        bankAccount = await buildBankAccount({
          owner: user.id,
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {}
        });
        mandate = await buildMandate({
          owner: user.id,
          category: "Top-Up",
          bankAccount: bankAccount.id
        });
        await buildAddress({
          owner: user.id
        });

        await MandateCronService.createAllWkMandates();
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.createMandate).not.toHaveBeenCalled();
      });

      it("should not update the mandate document", async () => {
        const updatedMandate = (await Mandate.findById(mandate.id)) as MandateDocument;
        expect(updatedMandate.providers?.wealthkernel?.id).toBe(undefined);
      });
    });

    describe("when mandate owner has Wealthkernel bank account with status Pending", () => {
      let user: UserDocument;
      let mandate: MandateDocument;
      let bankAccount: BankAccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createMandate");

        user = await buildUser({ providers: { wealthkernel: { id: "some-party" } } }, false);
        bankAccount = await buildBankAccount({
          owner: user.id,
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending" } }
        });
        mandate = await buildMandate({
          owner: user.id,
          category: "Top-Up",
          bankAccount: bankAccount.id,
          activeProviders: [ProviderEnum.WEALTHKERNEL]
        });
        await buildAddress({
          owner: user.id
        });

        await MandateCronService.createAllWkMandates();
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.createMandate).not.toHaveBeenCalled();
      });

      it("should not update the mandate document", async () => {
        const updatedMandate = (await Mandate.findById(mandate.id)) as MandateDocument;
        expect(updatedMandate.providers?.wealthkernel?.id).toBe(undefined);
      });
    });

    describe("when mandate has category Subscription", () => {
      let user: UserDocument;
      let mandate: MandateDocument;
      let bankAccount: BankAccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createMandate");

        user = await buildUser({ providers: { wealthkernel: { id: "party-id" } } }, false);
        bankAccount = await buildBankAccount({
          owner: user.id,
          providers: { wealthkernel: { id: "bank-account-id" } }
        });
        mandate = await buildMandate({
          owner: user.id,
          category: "Subscription",
          bankAccount: bankAccount.id
        });
        await buildAddress({
          owner: user.id
        });

        await MandateCronService.createAllWkMandates();
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.createMandate).not.toHaveBeenCalled();
      });

      it("should not update the mandate document", async () => {
        const updatedMandate = (await Mandate.findById(mandate.id)) as MandateDocument;
        expect(updatedMandate.providers?.wealthkernel?.id).toBe(undefined);
      });
    });

    describe("when mandate has already been created in Wealthkernel", () => {
      let user: UserDocument;
      let mandate: MandateDocument;
      let bankAccount: BankAccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createMandate");

        user = await buildUser({ providers: { wealthkernel: { id: "party-id" } } }, false);
        bankAccount = await buildBankAccount({
          owner: user.id,
          providers: { wealthkernel: { id: "bank-account-id" } }
        });
        mandate = await buildMandate({
          owner: user.id,
          category: "Top-Up",
          bankAccount: bankAccount.id,
          providers: { wealthkernel: { id: "mandate-id", status: "Pending" } }
        });
        await buildAddress({
          owner: user.id
        });

        await MandateCronService.createAllWkMandates();
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.createMandate).not.toHaveBeenCalled();
      });

      it("should not update the mandate document", async () => {
        const updatedMandate = (await Mandate.findById(mandate.id)) as MandateDocument;
        expect(updatedMandate.providers?.wealthkernel?.id).toBe("mandate-id");
      });
    });

    describe("when mandate has not been created in Wealthkernel and WK bank account is Active", () => {
      let user: UserDocument;
      let mandate: MandateDocument;
      let bankAccount: BankAccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createMandate").mockResolvedValue({ id: "mandate-id" });

        user = await buildUser({ providers: { wealthkernel: { id: "party-id" } } }, false);
        bankAccount = await buildBankAccount({
          owner: user.id,
          providers: { wealthkernel: { id: "bank-account-id", status: "Active" } }
        });
        mandate = await buildMandate({
          owner: user.id,
          category: "Top-Up",
          bankAccount: bankAccount.id,
          activeProviders: [ProviderEnum.WEALTHKERNEL]
        });
        await buildAddress({
          owner: user.id
        });

        await MandateCronService.createAllWkMandates();
      });
      afterAll(async () => await clearDb());

      it("should call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.createMandate).toHaveBeenCalledWith(
          {
            bankAccountId: bankAccount.providers.wealthkernel?.id,
            partyId: user.providers.wealthkernel.id
          },
          mandate.id
        );
      });

      it("should update the mandate document with the newly created WK mandate", async () => {
        const updatedMandate = (await Mandate.findById(mandate.id)) as MandateDocument;
        expect(updatedMandate.providers?.wealthkernel?.id).toBe("mandate-id");
        expect(updatedMandate.providers?.wealthkernel?.status).toBe("Pending");
      });
    });
  });

  describe("createAllGoCardlessMandates", () => {
    describe("when mandate owner does not have a GoCardless customer", () => {
      let user: UserDocument;
      let mandate: MandateDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(GoCardlessPaymentsService.Instance, "createMandateOnlyBillingRequest");
        jest.spyOn(GoCardlessPaymentsService.Instance, "collectCustomerDetails");
        jest.spyOn(GoCardlessPaymentsService.Instance, "confirmPayerDetails");
        jest.spyOn(GoCardlessPaymentsService.Instance, "fulfilBillingRequest");
        jest.spyOn(GoCardlessPaymentsService.Instance, "retrieveMandate");

        user = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        });
        mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0].id });
        await buildAddress({
          owner: user.id
        });

        await MandateCronService.createAllGoCardlessMandates();
      });
      afterAll(async () => await clearDb());

      it("should not call GoCardless", async () => {
        expect(GoCardlessPaymentsService.Instance.createMandateOnlyBillingRequest).not.toHaveBeenCalled();
      });

      it("should not update the mandate document", async () => {
        const updatedMandate = await Mandate.findById(mandate.id);
        expect(updatedMandate.providers?.gocardless?.id).toBeUndefined();
      });
    });

    describe("when mandate owner does not have a GoCardless bank account", () => {
      let user: UserDocument;
      let mandate: MandateDocument;
      let bankAccount: BankAccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(GoCardlessPaymentsService.Instance, "createMandateOnlyBillingRequest");
        jest.spyOn(GoCardlessPaymentsService.Instance, "collectCustomerDetails");
        jest.spyOn(GoCardlessPaymentsService.Instance, "confirmPayerDetails");
        jest.spyOn(GoCardlessPaymentsService.Instance, "fulfilBillingRequest");
        jest.spyOn(GoCardlessPaymentsService.Instance, "retrieveMandate");

        user = await buildUser(
          {
            providers: { gocardless: { id: "CU-123" } },
            companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
          },
          false
        );
        bankAccount = await buildBankAccount({ owner: user.id });
        mandate = await buildMandate({
          owner: user.id,
          bankAccount: bankAccount.id
        });
        await buildAddress({
          owner: user.id
        });

        await MandateCronService.createAllGoCardlessMandates();
      });
      afterAll(async () => await clearDb());

      it("should not call GoCardless", async () => {
        expect(GoCardlessPaymentsService.Instance.createMandateOnlyBillingRequest).not.toHaveBeenCalled();
      });

      it("should not update the mandate document", async () => {
        const updatedMandate = await Mandate.findById(mandate.id);
        expect(updatedMandate?.providers?.gocardless?.id).toBeUndefined();
      });
    });

    describe("when mandate has already been created in GoCardless", () => {
      let user: UserDocument;
      let mandate: MandateDocument;
      let bankAccount: BankAccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(GoCardlessPaymentsService.Instance, "createMandateOnlyBillingRequest");
        jest.spyOn(GoCardlessPaymentsService.Instance, "collectCustomerDetails");
        jest.spyOn(GoCardlessPaymentsService.Instance, "confirmPayerDetails");
        jest.spyOn(GoCardlessPaymentsService.Instance, "fulfilBillingRequest");
        jest.spyOn(GoCardlessPaymentsService.Instance, "retrieveMandate");

        user = await buildUser(
          {
            providers: { gocardless: { id: "CU-123" } },
            companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
          },
          false
        );
        bankAccount = await buildBankAccount({ owner: user.id, providers: { gocardless: { id: "BA-123" } } });
        mandate = await buildMandate({
          owner: user.id,
          bankAccount: bankAccount.id,
          providers: { gocardless: { id: "MA-123", status: "pending_submission" } }
        });
        await buildAddress({
          owner: user.id
        });

        await MandateCronService.createAllGoCardlessMandates();
      });
      afterAll(async () => await clearDb());

      it("should not call GoCardless", async () => {
        expect(GoCardlessPaymentsService.Instance.createMandateOnlyBillingRequest).not.toHaveBeenCalled();
      });

      it("should not update the mandate document", async () => {
        const updatedMandate = await Mandate.findById(mandate.id);
        expect(updatedMandate?.providers?.gocardless?.id).toBe("MA-123");
      });
    });

    describe("when mandate has not been created in GoCardless", () => {
      let user: UserDocument;
      let mandate: MandateDocument;
      let bankAccount: BankAccountDocument;
      let address: AddressDocument;

      const MANDATE_GOCARDLESS_ID = "MA-123";
      const MANDATE_BILLING_REQUEST_ID = "MA-123";
      const TODAY = new Date("2022-08-31T11:00:00Z");

      beforeAll(async () => {
        jest.resetAllMocks();

        Date.now = jest.fn(() => TODAY.valueOf());

        jest.spyOn(GoCardlessPaymentsService.Instance, "createMandateOnlyBillingRequest").mockResolvedValue({
          id: MANDATE_BILLING_REQUEST_ID
        });
        jest.spyOn(GoCardlessPaymentsService.Instance, "collectCustomerDetails");
        jest.spyOn(GoCardlessPaymentsService.Instance, "confirmPayerDetails");
        jest.spyOn(GoCardlessPaymentsService.Instance, "fulfilBillingRequest").mockResolvedValue(
          buildGoCardlessFulfiledBillingRequest({
            id: MANDATE_BILLING_REQUEST_ID,
            mandate_request: { links: { mandate: MANDATE_GOCARDLESS_ID } }
          })
        );
        jest.spyOn(GoCardlessPaymentsService.Instance, "retrieveMandate").mockResolvedValue({
          id: MANDATE_GOCARDLESS_ID,
          status: "pending_submission",
          next_possible_charge_date: "2022-09-01"
        });

        user = await buildUser(
          {
            providers: { gocardless: { id: "CU-123" } },
            companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
          },
          false
        );
        bankAccount = await buildBankAccount({ owner: user.id, providers: { gocardless: { id: "BA-123" } } });
        mandate = await buildMandate({
          category: "Top-Up",
          owner: user.id,
          bankAccount: bankAccount.id
        });
        address = await buildAddress({
          owner: user.id
        });

        await MandateCronService.createAllGoCardlessMandates();
      });
      afterAll(async () => await clearDb());

      it("should call GoCardless to create a mandate only billing request", async () => {
        expect(GoCardlessPaymentsService.Instance.createMandateOnlyBillingRequest).toHaveBeenNthCalledWith(1, {
          bankAccountId: bankAccount.providers.gocardless.id,
          customerId: user.providers.gocardless.id,
          metadata: { wealthyhoodId: mandate.id }
        });
        expect(GoCardlessPaymentsService.Instance.collectCustomerDetails).toHaveBeenNthCalledWith(
          1,
          MANDATE_BILLING_REQUEST_ID,
          {
            customer_billing_detail: {
              address_line1: address.line1,
              city: address.city,
              postal_code: address.postalCode,
              country_code: address.countryCode
            }
          }
        );
        expect(GoCardlessPaymentsService.Instance.confirmPayerDetails).toHaveBeenNthCalledWith(
          1,
          MANDATE_BILLING_REQUEST_ID
        );
        expect(GoCardlessPaymentsService.Instance.fulfilBillingRequest).toHaveBeenNthCalledWith(
          1,
          MANDATE_BILLING_REQUEST_ID
        );
        expect(GoCardlessPaymentsService.Instance.retrieveMandate).toHaveBeenNthCalledWith(
          1,
          MANDATE_GOCARDLESS_ID
        );
      });

      it("should update the mandate document to have the new GoCardless ID", async () => {
        const updatedMandate = await Mandate.findById(mandate.id);
        expect(updatedMandate.toObject()).toEqual(
          expect.objectContaining({
            providers: expect.objectContaining({
              gocardless: expect.objectContaining({
                id: MANDATE_GOCARDLESS_ID,
                status: "pending_submission"
              })
            })
          })
        );
      });
    });
  });

  describe("syncPendingWkMandates", () => {
    describe("when mandate is created in Wealthkernel and status has not been updated", () => {
      let user: UserDocument;
      let mandate: MandateDocument;
      let bankAccount: BankAccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "retrieveMandate").mockResolvedValue({
          status: "Pending"
        } as any);

        user = await buildUser({ providers: { wealthkernel: { id: "party-id" } } }, false);
        bankAccount = await buildBankAccount({
          owner: user.id,
          providers: { wealthkernel: { id: "bank-account-id" } }
        });
        mandate = await buildMandate({
          owner: user.id,
          category: "Top-Up",
          bankAccount: bankAccount.id,
          providers: {
            wealthkernel: {
              id: "mandate-id",
              status: "Pending"
            }
          }
        });

        await MandateCronService.syncPendingWkMandates();
      });
      afterAll(async () => await clearDb());

      it("should call Wealthkernel to retrieve the mandate", async () => {
        expect(WealthkernelService.UKInstance.retrieveMandate).toHaveBeenCalledWith(
          mandate.providers.wealthkernel.id
        );
      });

      it("should not update the mandate document", async () => {
        const updatedMandate = (await Mandate.findById(mandate.id)) as MandateDocument;
        expect(updatedMandate.providers?.wealthkernel?.status).toBe("Pending");
      });
    });

    describe("when mandate is created in Wealthkernel and status has been updated", () => {
      let user: UserDocument;
      let mandate: MandateDocument;
      let bankAccount: BankAccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "retrieveMandate").mockResolvedValue({
          status: "Active"
        } as any);

        user = await buildUser({ providers: { wealthkernel: { id: "party-id" } } }, false);
        bankAccount = await buildBankAccount({
          owner: user.id,
          providers: { wealthkernel: { id: "bank-account-id" } }
        });
        mandate = await buildMandate({
          owner: user.id,
          category: "Top-Up",
          bankAccount: bankAccount.id,
          providers: {
            wealthkernel: {
              id: "mandate-id",
              status: "Pending"
            }
          }
        });

        await MandateCronService.syncPendingWkMandates();
      });
      afterAll(async () => await clearDb());

      it("should call Wealthkernel to retrieve the mandate", async () => {
        expect(WealthkernelService.UKInstance.retrieveMandate).toHaveBeenCalledWith(
          mandate.providers.wealthkernel.id
        );
      });

      it("should update the mandate document", async () => {
        const updatedMandate = (await Mandate.findById(mandate.id)) as MandateDocument;
        expect(updatedMandate.providers?.wealthkernel?.status).toBe("Active");
      });
    });
  });

  describe("syncActiveWkMandates", () => {
    describe("when mandate is active in Wealthkernel and status has been updated to cancelled", () => {
      let user: UserDocument;
      let mandate: MandateDocument;
      let bankAccount: BankAccountDocument;
      let topUpAutomation: AutomationDocument;
      let savingsAutomation: AutomationDocument;
      let anotherAutomation: AutomationDocument;
      let depositCashTransaction: DepositCashTransactionDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "retrieveMandate").mockResolvedValue({
          status: "Cancelled"
        } as any);

        user = await buildUser({ providers: { wealthkernel: { id: "party-id" } } }, false);
        bankAccount = await buildBankAccount({
          owner: user.id,
          providers: { wealthkernel: { id: "bank-account-id" } }
        });
        mandate = await buildMandate({
          owner: user.id,
          category: "Top-Up",
          bankAccount: bankAccount.id,
          providers: {
            wealthkernel: {
              id: "mandate-id",
              status: "Active"
            }
          }
        });
        topUpAutomation = await buildTopUpAutomation({
          owner: user.id,
          mandate: mandate.id,
          category: "TopUpAutomation",
          active: true
        });
        savingsAutomation = await buildSavingsTopUpAutomation({
          owner: user.id,
          mandate: mandate.id,
          active: true
        });

        // User has another automation that should not be deactivated
        anotherAutomation = await buildTopUpAutomation({
          owner: user.id,
          category: "TopUpAutomation",
          active: true
        });

        depositCashTransaction = await buildDepositCashTransaction(
          {
            owner: user.id,
            linkedAutomation: topUpAutomation.id,
            status: "Pending"
          },
          user
        );
        await buildAssetTransaction({
          owner: user.id,
          status: "PendingDeposit",
          pendingDeposit: depositCashTransaction.id,
          linkedAutomation: topUpAutomation.id
        });

        await MandateCronService.syncActiveWkMandates();
      });
      afterAll(async () => await clearDb());

      it("should call Wealthkernel to retrieve the mandate", async () => {
        expect(WealthkernelService.UKInstance.retrieveMandate).toHaveBeenCalledWith(
          mandate.providers.wealthkernel.id
        );
      });

      it("should update the mandate document", async () => {
        const updatedMandate = (await Mandate.findById(mandate.id)) as MandateDocument;
        expect(updatedMandate.providers?.wealthkernel?.status).toBe("Cancelled");
      });

      it("should cancel both linked automations", async () => {
        const updatedTopUpAutomation = (await Automation.findById(topUpAutomation.id)) as AutomationDocument;
        expect(updatedTopUpAutomation.status).toBe("Inactive");

        const updatedSavingsAutomation = (await Automation.findById(savingsAutomation.id)) as AutomationDocument;
        expect(updatedSavingsAutomation.status).toBe("Inactive");
      });

      it("should NOT cancel automation that is linked to another mandate", async () => {
        const updatedTopUpAutomation = (await Automation.findById(anotherAutomation.id)) as AutomationDocument;
        expect(updatedTopUpAutomation.status).toBe("Active");
      });

      it("should not cancel the deposit transaction", async () => {
        const updatedDeposit = await DepositCashTransaction.findById(depositCashTransaction.id).populate(
          "linkedAutomation"
        );
        expect(updatedDeposit?.status).toBe("Pending");
      });
    });

    describe("when mandate is active in Wealthkernel and status has been updated to cancelled and there is no linked automation", () => {
      let user: UserDocument;
      let mandate: MandateDocument;
      let bankAccount: BankAccountDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "retrieveMandate").mockResolvedValue({
          status: "Cancelled"
        } as any);

        user = await buildUser({ providers: { wealthkernel: { id: "party-id" } } }, false);
        bankAccount = await buildBankAccount({
          owner: user.id,
          providers: { wealthkernel: { id: "bank-account-id" } }
        });
        mandate = (await buildMandate({
          owner: user.id,
          category: "Top-Up",
          bankAccount: bankAccount.id,
          providers: {
            wealthkernel: {
              id: "mandate-id",
              status: "Active"
            }
          }
        })) as MandateDocument;

        await MandateCronService.syncActiveWkMandates();
      });
      afterAll(async () => await clearDb());

      it("should update the mandate document", async () => {
        const updatedMandate = (await Mandate.findById(mandate.id)) as MandateDocument;
        expect(updatedMandate.providers?.wealthkernel?.status).toBe("Cancelled");
      });

      it("should not try to cancel any automation documents", async () => {
        const automations = (await AutomationService.getAutomations()).data;
        expect(automations).toEqual([]);
      });
    });
  });
});
