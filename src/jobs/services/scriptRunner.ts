import "../../loaders/environment";
import "../../event-handlers";
import { envIsDev, envIsProd } from "../../utils/environmentUtil";
import mongoose from "mongoose";
import * as Sentry from "@sentry/node";
import * as SentryProfiling from "@sentry/profiling-node";
import logger from "../../external-services/loggerService";
import MongoLoader from "../../loaders/mongo";
import { MongoReadPreferenceEnum } from "../../utils/dbUtil";

export default abstract class ScriptRunner {
  abstract scriptName: string;

  constructor(
    options: { connectDb: boolean; overrideMongoReadPreference?: MongoReadPreferenceEnum } = { connectDb: true }
  ) {
    if (options.connectDb) {
      const mongoLoader = new MongoLoader(
        options?.overrideMongoReadPreference ?? MongoReadPreferenceEnum.SECONDARY
      );
      mongoLoader.init();
    }
  }

  run(): void {
    Sentry.init({
      enabled: !envIsDev(),
      dsn: process.env.SENTRY_DNS,
      environment: process.env.NODE_ENV,
      integrations: [
        SentryProfiling.nodeProfilingIntegration(),
        Sentry.extraErrorDataIntegration(),
        // enable HTTP calls tracing
        Sentry.httpIntegration({
          ignoreOutgoingRequests: (url) => {
            if (url.includes("datadog")) {
              return true;
            }
            return false;
          }
        }),
        Sentry.mongoIntegration(),
        Sentry.mongooseIntegration()
      ],
      profilesSampler: () => {
        if (envIsProd()) {
          return 0.2;
        } else {
          return false;
        }
      },
      tracesSampler: () => {
        if (envIsProd()) {
          return 0.2;
        } else {
          return false;
        }
      }
    });

    mongoose.connection.once("open", async () => {
      let exitCode: number;

      try {
        await this.processFn();
        exitCode = 0;
      } catch (err) {
        exitCode = 1;
        logger.error("Task failed", { module: `script:${this.scriptName}`, data: { error: err } });
        Sentry.captureException(err);
      }

      // This is a workaround to delay process exiting, until all logs have been submitted, otherwise some logs sent to datadog may be skipped
      setTimeout(() => process.exit(exitCode), 5000);
    });
  }

  abstract processFn(): Promise<void>;
}
