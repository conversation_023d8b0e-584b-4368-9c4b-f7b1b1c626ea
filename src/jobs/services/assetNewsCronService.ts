import InvestmentProductService from "../../services/investmentProductService";
import AssetNewsService from "../../services/assetNewsService";
import { captureException } from "@sentry/node";
import logger from "../../external-services/loggerService";
import { StockNewsDaysEnum } from "../../external-services/stockNewsService";

export default class AssetNewsCronService {
  /**
   * @description Iterates over all available investment products,fetches the news, and stores it in the database if not already present.
   */
  public static async createAssetNews(): Promise<void> {
    const investmentProducts = await InvestmentProductService.getInvestmentProducts({
      populateTicker: false,
      useCache: true,
      listedOnly: true
    });

    for (let index = 0; index < investmentProducts.length; index++) {
      const investmentProduct = investmentProducts[index];

      try {
        await AssetNewsService.retrieveAndStoreAssetNews(investmentProduct, 100, StockNewsDaysEnum.LAST_7_DAYS);
      } catch (err) {
        captureException(err);
        logger.error(`Failed while retrieving and storing asset news for asset ${investmentProduct.commonId}`, {
          module: "AssetNewsCronService",
          method: "createAssetNews"
        });
      }
    }
  }
}
