import { cloudflareConfig, savingsUniverseConfig } from "@wealthyhood/shared-configs";
import CloudflareService from "../../external-services/cloudflareService";
import SavingsProductService, { SavingsProductFeeDetailsType } from "../../services/savingsProductService";

const { KvNamespaceKeys } = cloudflareConfig;

/**
 * TYPES
 */
export type SavingsProductsDataType = Record<
  savingsUniverseConfig.SavingsProductType,
  SavingsProductFeeDetailsType
>;

export default class SavingsProductsDataCronService {
  public static async updateSavingsProductsData(): Promise<void> {
    const savingsProductsData = {} as SavingsProductsDataType;

    const savingsProducts = await SavingsProductService.getSavingsProducts({ currentTicker: true });

    for (const product of savingsProducts) {
      savingsProductsData[product.commonId] = SavingsProductService.getSavingsProductFeeDetails(product, "en");
    }

    // Serialize the complete data object and update the Cloudflare KV store
    await CloudflareService.Instance.updateKeyValuePair(
      KvNamespaceKeys.SAVINGS_PRODUCTS_DATA,
      JSON.stringify(savingsProductsData)
    );
  }
}
