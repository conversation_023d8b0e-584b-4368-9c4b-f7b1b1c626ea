import { captureException } from "@sentry/node";
import { savingsUniverseConfig } from "@wealthyhood/shared-configs";
import DailyTickerService from "../../services/dailyTickerService";
import logger from "../../external-services/loggerService";
import { SavingsProduct } from "../../models/SavingsProduct";
import PortfolioService from "../../services/portfolioService";
import { PortfolioDocument } from "../../models/Portfolio";
import SavingsProductService from "../../services/savingsProductService";
import Decimal from "decimal.js";
import { RedisClientService } from "../../loaders/redis";
import { BlackrockFundDataType } from "../../external-services/blackrockService";
import DateUtil from "../../utils/dateUtil";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import { TrackSavingsProductDataUpdatePropertiesType } from "../../external-services/segmentAnalyticsService";

const { SavingsProductArray, SAVINGS_PRODUCT_CONFIG_GLOBAL } = savingsUniverseConfig;

export default class DailyTickerCronService {
  public static async createDailySavingsProductTickers(): Promise<void> {
    const savingsProducts = await SavingsProduct.find();

    for (let i = 0; i < savingsProducts.length; i++) {
      const savingsProduct = savingsProducts[i];

      try {
        await DailyTickerService.fetchAndStoreSavingsProductData(savingsProduct);
      } catch (err) {
        captureException(err);
        logger.error(
          `Could not store daily savings product ticker for ${savingsProduct.id} (${savingsProduct.commonId})`,
          {
            module: "DailyTickerCronService",
            method: "createDailySavingsProductTickers"
          }
        );
      }
    }
  }

  public static async createDailyPortfolioSavingsTicker(): Promise<void> {
    const commonIdToSavingsProductDict = await SavingsProductService.getSavingsProductsDict("commonId", false);

    // We want to query all the portfolios where the savings has any of the MMFs in the universe with
    // amount larger than 0.
    const orOperatorQuery = SavingsProductArray.map((savingsProductId) => ({
      [`savings.${savingsProductId}.amount`]: { $gt: 0 }
    }));

    await PortfolioService.getPortfoliosStreamed(
      {
        $or: orOperatorQuery
      },
      { owner: true, currentTicker: false }
    ).eachAsync(
      async (portfolios: PortfolioDocument[]) => {
        const promises = portfolios.map(async (portfolio) => {
          try {
            for (const savings of portfolio.savings) {
              const [savingsProductId, portfolioSavings] = savings;
              if (new Decimal(portfolioSavings.amount).gt(0)) {
                await DailyTickerService.setDailyPortfolioSavingsTicker(
                  portfolio,
                  portfolioSavings,
                  commonIdToSavingsProductDict[savingsProductId]
                );
              }
            }
          } catch (err) {
            captureException(err);
            logger.error(`Could not store user savings product ticker for ${portfolio.id} portfolio`, {
              module: "DailyTickerCronService",
              method: "createDailyPortfolioSavingsTicker"
            });
          }
        });
        await Promise.all(promises);
      },
      { batchSize: 20 }
    );
  }

  public static async checkSavingsProductDataUpdate() {
    const savingsProducts = await SavingsProduct.find();

    const startOfToday = DateUtil.getStartOfDay(new Date(Date.now()));
    for (const savingsProduct of savingsProducts) {
      try {
        const lastAvailableData = await RedisClientService.Instance.get<BlackrockFundDataType>(
          `savings:data:${savingsProduct.commonId}`
        );
        // If its already updated for the day, exit
        if (lastAvailableData?.fixingDate >= startOfToday) {
          continue;
        }

        const latestData = await DailyTickerService.fetchSavingsProductData(savingsProduct.commonId);

        if (!lastAvailableData || latestData.fixingDate > new Date(lastAvailableData.fixingDate)) {
          await RedisClientService.Instance.set(`savings:data:${savingsProduct.commonId}`, latestData);

          eventEmitter.emit(events.savingsProduct.savingProductDataUpdate.eventId, {
            savingsProductLabel: SAVINGS_PRODUCT_CONFIG_GLOBAL[savingsProduct.commonId].label,
            lastUpdateDate: new Date(Date.now()),
            dailyDistributionFactor: latestData.dailyDistributionFactor,
            oneDayYield: latestData.oneDayYield
          } as TrackSavingsProductDataUpdatePropertiesType);

          logger.info(`Updated latest data for ${savingsProduct.commonId} savings product`, {
            module: "DailyTickerCronService",
            method: "checkSavingsProductDataUpdate",
            data: {
              latestData,
              savingsProductId: savingsProduct.commonId
            }
          });
        }
      } catch (err) {
        captureException(err);
        logger.error(
          `Could not finish the check for updated saving product data for  ${savingsProduct.id} (${savingsProduct.commonId})`,
          {
            module: "DailyTickerCronService",
            method: "checkSavingsProductDataUpdate"
          }
        );
      }
    }
  }
}
