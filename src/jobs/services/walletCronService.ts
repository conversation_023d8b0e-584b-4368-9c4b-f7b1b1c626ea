import { Wallet } from "../../models/Wallet";
import { UserDocument } from "../../models/User";
import { ProviderEnum } from "../../configs/providersConfig";
import DateUtil from "../../utils/dateUtil";
import WalletService from "../../services/walletService";

export default class WalletCronService {
  public static async createAllDevengoWallets(): Promise<void> {
    const walletsPendingDevengoSubmission = await Wallet.find({
      activeProviders: ProviderEnum.DEVENGO,
      $or: [{ "providers.devengo.id": { $exists: false } }, { "providers.devengo.id": { $eq: undefined } }]
    }).populate("owner");

    for (let i = 0; i < walletsPendingDevengoSubmission.length; i++) {
      const wallet = walletsPendingDevengoSubmission[i];

      if (wallet.createdAt < DateUtil.getDateOfMinutesAgo(10)) {
        await WalletService.createDevengoEntry(wallet.owner as UserDocument);
      }
    }
  }

  public static async syncAllDevengoWallets(): Promise<void> {
    const pendingDevengoWallets = await Wallet.find({
      activeProviders: ProviderEnum.DEVENGO,
      "providers.devengo.status": { $in: ["created", "delayed"] }
    });

    for (let i = 0; i < pendingDevengoWallets.length; i++) {
      const wallet = pendingDevengoWallets[i];

      if (wallet.createdAt < DateUtil.getDateOfMinutesAgo(10)) {
        await WalletService.syncDevengoEntry(wallet);
      }
    }
  }
}
