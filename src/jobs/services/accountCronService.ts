import { Account } from "../../models/Account";
import { UserDocument } from "../../models/User";
import DateUtil from "../../utils/dateUtil";
import { countriesConfig } from "@wealthyhood/shared-configs";
import UserService from "../../services/userService";
import AccountService from "../../services/accountService";
import { ProviderEnum } from "../../configs/providersConfig";

export default class AccountCronService {
  public static async createAllWkAccounts(): Promise<void> {
    const accountsPendingWkEntry = await Account.find({
      activeProviders: ProviderEnum.WEALTHKERNEL,
      "providers.wealthkernel.id": {
        $exists: false
      },
      updatedAt: {
        $gt: DateUtil.getDateOfDaysAgo(new Date(Date.now()), 30)
      }
    }).populate("owner");

    const filteredAccounts = accountsPendingWkEntry.filter(({ owner }) => {
      const user = owner as UserDocument;

      return (
        user?.providers?.wealthkernel?.id &&
        user.nationalities.some(
          (nationality) => !countriesConfig.forbiddenNationalitiesArray.includes(nationality)
        ) &&
        user.submittedRequiredInfoAt < DateUtil.getDateOfMinutesAgo(10) &&
        UserService.isUserAdult(user)
      );
    });

    for (let i = 0; i < filteredAccounts.length; i++) {
      const account = filteredAccounts[i];
      const user = account.owner as UserDocument;
      await AccountService.createOrSyncBrokerageAccount(user, account);
    }
  }
}
