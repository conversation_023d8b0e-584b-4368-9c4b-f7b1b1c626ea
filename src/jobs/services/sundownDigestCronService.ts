import Decimal from "decimal.js";
import { captureException } from "@sentry/node";
import { publicInvestmentUniverseConfig } from "@wealthyhood/shared-configs";
import eodService from "../../external-services/eodService";
import logger from "../../external-services/loggerService";
import StockNewsService from "../../external-services/stockNewsService";
import MarketSummaryFormatter from "../../lib/marketSummaryFormatter";
import TickerFinder from "../../lib/tickerFinder";
import AssetIdResolver from "../../lib/assetIdResolver";
import SectionTagger from "../../lib/sectionTagger";
import { SundownDigest, SundownDigestDTOInterface } from "../../models/SundownDigest";
import * as InvestmentUniverseUtil from "../../utils/investmentUniverseUtil";
import DateUtil from "../../utils/dateUtil";
import PortfolioUtil from "../../utils/portfolioUtil";

export default class SundownDigestCronService {
  /**
   * @description Fetches available sundown digests and stores them in the database if not already present.
   */
  public static async createAllSundownDigests(): Promise<void> {
    const latestSundownDigests = await StockNewsService.getSundownDigests();

    for (let i = 0; i < latestSundownDigests.length; i++) {
      const sundownDigest = latestSundownDigests[i];

      const existingSundownDigest = await SundownDigest.findOne({ "providers.stockNews.id": sundownDigest.id });
      if (!existingSundownDigest) {
        // STEP 1: Process the digest content with AI to format it in a structured way
        const formattedContent = await MarketSummaryFormatter.format(sundownDigest.text);

        const processedSections = await Promise.all(
          (formattedContent.sections || []).map(async (section) => {
            const processedSection: {
              companyName?: string;
              companyTicker?: string;
              content?: string;
              title?: string;
              assetId?: publicInvestmentUniverseConfig.PublicAssetType;
              assetReturnPercentage?: number;
              tag?: string;
            } = { ...section };

            // STEP 2: Find ticker if not available
            if (!processedSection.companyTicker && processedSection.companyName) {
              const ticker = await TickerFinder.findTickerForCompany(processedSection.companyName);
              if (ticker) {
                processedSection.companyTicker = ticker;
              }
            }

            // STEP 3: If no ticker is available, tag the section with a category
            if (!processedSection.companyTicker && processedSection.content) {
              const tag = await SectionTagger.tagSection(processedSection.content);
              processedSection.tag = tag;
            }

            // STEP 4: Resolve ticker symbol to asset ID
            if (processedSection.companyTicker && processedSection.companyName) {
              const assetId = await SundownDigestCronService._resolveTickerToAssetId({
                companyName: processedSection.companyName,
                companyTicker: processedSection.companyTicker
              });
              processedSection.assetId = assetId;

              // STEP 5: Fetch asset return percentage
              if (assetId) {
                const assetReturnPercentage = await SundownDigestCronService._fetchAssetReturnPercentage(assetId);
                processedSection.assetReturnPercentage = assetReturnPercentage;
              }
            }

            return processedSection;
          })
        );

        const data: SundownDigestDTOInterface = {
          content: SundownDigestCronService._formatTickerToBold(sundownDigest.text),
          formattedContent: {
            overview: formattedContent.overview,
            sections: processedSections
          },
          date: new Date(sundownDigest.date),
          providers: {
            stockNews: {
              id: sundownDigest.id.toString()
            }
          }
        };

        await new SundownDigest(data).save();
      }
    }
  }

  /**
   * Resolves a ticker symbol to its corresponding asset ID, handling cases of companies with same ticker symbol
   * @param tickerCompanyPair The company name and ticker pair
   * @returns The resolved asset ID or undefined
   * @private
   */
  private static async _resolveTickerToAssetId(tickerCompanyPair: {
    companyName: string;
    companyTicker: string;
  }): Promise<publicInvestmentUniverseConfig.PublicAssetType | undefined> {
    const { companyTicker } = tickerCompanyPair;

    // First try to get a direct match
    const matchingAssetIds = InvestmentUniverseUtil.getAllAssetIdsFromTicker(companyTicker);

    if (matchingAssetIds.length === 0) {
      // No asset IDs found
      return undefined;
    } else if (matchingAssetIds.length === 1) {
      // Exactly one asset ID found, use it directly
      return matchingAssetIds[0];
    } else {
      // Multiple potential matches, use AI to resolve the ambiguity
      const resolvedAssetId = await AssetIdResolver.resolveTickerToAssetId(tickerCompanyPair);

      // Only return if a valid asset ID was found
      if (resolvedAssetId) {
        return resolvedAssetId as publicInvestmentUniverseConfig.PublicAssetType;
      }
    }

    return undefined;
  }

  /**
   * Fetch return percentage for a single asset ID
   * @param assetId Asset ID to fetch return percentage for
   * @returns Return percentage or undefined if fetch fails
   * @private
   */
  private static async _fetchAssetReturnPercentage(
    assetId: publicInvestmentUniverseConfig.PublicAssetType
  ): Promise<number | undefined> {
    try {
      // Define a date range for historical prices (last 7 days to ensure we have enough data)
      const fromDate = DateUtil.getYearAndMonthAndDay(DateUtil.getDateOfDaysAgo(new Date(), 7));

      const historicalPrices = await eodService.getHistoricalPrices(assetId, {
        from: fromDate,
        period: "d"
      });

      // Get the last two elements of the array (the two most recent prices)
      const [previousPrice, currentPrice] = historicalPrices.slice(-2);

      const dailyReturnPercentage = PortfolioUtil.getReturns({
        startValue: new Decimal(previousPrice.close),
        endValue: new Decimal(currentPrice.close)
      }).toNumber();

      return dailyReturnPercentage;
    } catch (err) {
      logger.error(`Failed to fetch historical prices for asset ${assetId}`, {
        module: "SundownDigestCronService",
        method: "_fetchAssetReturnPercentage",
        data: { error: err }
      });
      captureException(err);
      return undefined;
    }
  }

  /**
   * Finds all text within parentheses and replaces it with a bold version e.g. (AAPL) -> **(AAPL)**
   * @param text
   * @private
   */
  private static _formatTickerToBold(text: string): string {
    return text.replace(/\((.*?)\)/g, "**($1)**");
  }
}
