import logger from "../../external-services/loggerService";
import AccountingLedgerStorageService, {
  CashBalanceSnapshot
} from "../../external-services/accountingLedgerStorageService";
import { CASH_ACCOUNTS_WK_MAPPING } from "../../configs/accountingConfig";
import { LedgerAccounts } from "../../types/accounting";
import ProviderService from "../../services/providerService";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { captureException } from "@sentry/node";
import Decimal from "decimal.js";
import { Portfolio } from "../../models/Portfolio";
import DateUtil from "../../utils/dateUtil";

export default class AccountingCronService {
  /**
   * @description Captures daily cash balance snapshots from WealthKernel for reconciliation
   * This method fetches cash balances from WK for all configured accounts and stores them as snapshots
   */
  public static async captureWealthyhoodAccountsCashBalanceSnapshots(): Promise<void> {
    logger.info("Starting cash balance snapshot capture", {
      module: "AccountingCronService",
      method: "captureCashBalanceSnapshots"
    });

    const snapshots: CashBalanceSnapshot[] = [];

    try {
      // Iterate through all configured accounts
      for (const [accountCode, portfolioId] of Object.entries(CASH_ACCOUNTS_WK_MAPPING)) {
        if (portfolioId === undefined) {
          // Skip other undefined mappings (they're not used for cash reconciliation)
          continue;
        }

        // Handle specific portfolio accounts
        const balance = await AccountingCronService._getPortfolioCashBalance(portfolioId);
        snapshots.push({
          accountCode: accountCode as LedgerAccounts,
          balance,
          asOfDate: DateUtil.getYearAndMonthAndDay(new Date(Date.now()))
        });

        logger.info(`Captured cash balance for account ${accountCode}: ${balance}`, {
          module: "AccountingCronService",
          method: "captureCashBalanceSnapshots",
          data: { accountCode, portfolioId, balance }
        });
      }

      // Store all snapshots in batch
      const result = await AccountingLedgerStorageService.addCashBalanceSnapshots(snapshots);

      if (result.success) {
        logger.info(`Successfully stored ${snapshots.length} cash balance snapshots`, {
          module: "AccountingCronService",
          method: "captureCashBalanceSnapshots",
          data: {
            snapshotCount: snapshots.length,
            accounts: snapshots.map((s) => ({ account: s.accountCode, balance: s.balance }))
          }
        });
      } else {
        throw new Error(`Failed to store snapshots: ${result.error}`);
      }
    } catch (err) {
      captureException(err);
      logger.error("Failed to capture cash balance snapshots", {
        module: "AccountingCronService",
        method: "captureCashBalanceSnapshots",
        data: { error: err }
      });
    }
  }

  /**
   * @description Gets aggregated cash balance across all client portfolios and stores it as a snapshot
   * @returns The total EUR cash balance across all client portfolios
   */
  public static async captureAggregatedEUClientsAccountsCashBalance(): Promise<number> {
    logger.info("Calculating aggregated client cash balance", {
      module: "AccountingCronService",
      method: "getAggregatedEUClientsAccountsCashBalance"
    });

    let totalBalance = new Decimal(0);

    try {
      // Query all client portfolios from the database
      // We look for portfolios that have WealthKernel integration and are active
      const portfolios = await Portfolio.find({
        "providers.wealthkernel.id": { $exists: true },
        "providers.wealthkernel.status": "Active",
        currency: "EUR"
      }).populate("owner");

      logger.info(`Found ${portfolios.length} client portfolios for aggregation`, {
        module: "AccountingCronService",
        method: "getAggregatedEUClientsAccountsCashBalance",
        data: { portfolioCount: portfolios.length }
      });

      // Iterate through each portfolio and sum up the EUR balances
      for (const portfolio of portfolios) {
        const wkPortfolioId = portfolio.providers?.wealthkernel?.id;
        const balance = await AccountingCronService._getPortfolioCashBalance(wkPortfolioId);

        if (balance > 0) {
          totalBalance = totalBalance.plus(balance);
        }
      }

      const finalBalance = totalBalance.toNumber();

      // Store the aggregated balance as a snapshot in the ledger
      const snapshot: CashBalanceSnapshot = {
        accountCode: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        balance: finalBalance,
        asOfDate: DateUtil.getYearAndMonthAndDay(new Date(Date.now()))
      };

      const result = await AccountingLedgerStorageService.addCashBalanceSnapshots([snapshot]);

      if (result.success) {
        logger.info("Aggregated client cash balance calculation and storage complete", {
          module: "AccountingCronService",
          method: "getAggregatedEUClientsAccountsCashBalance",
          data: {
            totalBalance: finalBalance,
            portfoliosProcessed: portfolios.length,
            snapshotStored: true
          }
        });
      } else {
        logger.warn("Failed to store aggregated cash balance snapshot", {
          module: "AccountingCronService",
          method: "getAggregatedEUClientsAccountsCashBalance",
          data: {
            totalBalance: finalBalance,
            error: result.error
          }
        });
      }

      return finalBalance;
    } catch (err) {
      captureException(err);
      logger.error("Failed to calculate aggregated client cash balance", {
        module: "AccountingCronService",
        method: "getAggregatedEUClientsAccountsCashBalance",
        data: { error: err }
      });
    }
  }

  /**
   * @description Gets cash balance for a specific WealthKernel portfolio
   * @param portfolioId The WealthKernel portfolio ID
   * @returns The EUR cash balance amount
   */
  private static async _getPortfolioCashBalance(portfolioId: string): Promise<number> {
    // Get brokerage service - assuming EU entity for now
    const brokerageService = ProviderService.getBrokerageService(
      entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    );

    // Fetch cash balances from WealthKernel
    const balances = await brokerageService.listCashBalances(portfolioId);

    // Find EUR balance and convert to number
    const eurBalance = balances.find((balance) => balance.value.currency === "EUR");

    if (!eurBalance) {
      logger.warn(`No EUR balance found for portfolio ${portfolioId}`, {
        module: "AccountingCronService",
        method: "_getPortfolioCashBalance",
        data: { portfolioId, availableBalances: balances.map((b) => b.value.currency) }
      });
      return 0;
    }

    return eurBalance.value.amount;
  }
}
