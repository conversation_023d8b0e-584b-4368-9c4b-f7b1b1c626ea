import { UserDocument } from "../../models/User";
import DateUtil from "../../utils/dateUtil";
import logger from "../../external-services/loggerService";
import UserService from "../../services/userService";
import Decimal from "decimal.js";
import DailySummarySnapshotService from "../../services/dailySummarySnapshotService";
import InvestmentProductService from "../../services/investmentProductService";
import { RedisClientService } from "../../loaders/redis";
import { InvestmentProductDocument } from "../../models/InvestmentProduct";
import eodService, {
  AssetSentimentsType,
  EodAssetAnalystRatingSentimentType,
  EodStockFundamentalsResponseType
} from "../../external-services/eodService";
import MathUtil from "../../utils/mathUtil";
import EodUtil from "../../utils/eodUtil";
import * as CacheUtil from "../../utils/cacheUtil";
import { captureException } from "@sentry/node";
import PortfolioService from "../../services/portfolioService";
import { PortfolioDocument } from "../../models/Portfolio";
import { TransactionService } from "../../services/transactionService";

const DB_SUMMARY_SNAPSHOT_BATCH_SIZE = 100;

const BASE_TOTAL_ANALYST_INSIGHT_WEIGHTS: Record<EodAssetAnalystRatingSentimentType, number> = {
  StrongBuy: 1,
  Buy: 0.75,
  Hold: 0.5,
  Sell: 0.25,
  StrongSell: 0
};

const NEWS_BAYESIAN_K = 20;
const ANALYST_BAYESIAN_K = 15;

const MINIMUM_MOMENTUM = -0.2;
const MAXIMUM_MOMENTUM = 0.1;

const LOW_CAP = 0.01;
const HIGH_CAP = 0.99;

const THIRTY_DAYS = 30;
const SEVEN_DAYS = 7;

const ASSET_NEWS_SENTIMENTS_TTL_SECONDS = 60 * 60 * 24; // 24 hours;

export default class DailySummarySnapshotCronService {
  /**
   * @description Method for creating daily summary snapshots for all converted users.
   */
  public static async createDailySummarySnapshotsForConvertedUsers(
    date: Date = new Date(Date.now())
  ): Promise<void> {
    logger.info("Storing daily summary snapshots for converted users...", {
      module: "DailySummarySnapshotCronService",
      method: "createDailySummarySnapshotsForConvertedUsers"
    });

    const investmentProductsDict = await InvestmentProductService.getInvestmentProductsDict("commonId", true, {
      listedOnly: true
    });

    await UserService.getUsersStreamed({ portfolioConversionStatus: "completed", hasRequestedDeletion: false }, [
      {
        path: "portfolios",
        populate: { path: "currentTicker" }
      }
    ]).eachAsync(
      async (users: UserDocument[]) => {
        logger.info(`Processing batch of ${users.length} users...`, {
          module: "DailySummarySnapshotCronService",
          method: "createDailySummarySnapshotsForConvertedUsers"
        });

        return DailySummarySnapshotService.createDailySummarySnapshotForUsers(users, investmentProductsDict, date);
      },
      { batchSize: DB_SUMMARY_SNAPSHOT_BATCH_SIZE }
    );
  }

  /**
   * @description Method for creating daily summary snapshots for non-converted users who have:
   * - Pending or settled savings top-ups (they see an aggregated savings amount
   *   in the app and therefore should also see it in their daily summary)
   * - Settled cash deposits
   */
  public static async createDailySummarySnapshotsForUsersWithCashOrSavings(
    date: Date = new Date(Date.now())
  ): Promise<void> {
    logger.info("Storing daily summary snapshots for users with cash/savings/pending transactions...", {
      module: "DailySummarySnapshotCronService",
      method: "createDailySummarySnapshotsForUsersWithCashOrSavings"
    });

    const investmentProductsDict = await InvestmentProductService.getInvestmentProductsDict("commonId", true, {
      listedOnly: true
    });

    await PortfolioService.getPortfoliosStreamed(
      {
        holdings: { $size: 0 },
        _id: {
          $in: await TransactionService.getPortfolioIdsOfDailySnapshotTransactions()
        }
      },
      { currentTicker: false, owner: true }
    ).eachAsync(
      async (portfolios: PortfolioDocument[]) => {
        const users = portfolios
          .map((portfolio) => {
            const owner = portfolio.owner as UserDocument;
            return { ...owner.toObject(), portfolios: [portfolio] };
          })
          .filter((user) => !user.isDeleted && user.portfolioConversionStatus !== "completed");

        logger.info(`Processing batch of ${users.length} users...`, {
          module: "DailySummarySnapshotCronService",
          method: "createDailySummarySnapshotsForUsersWithCashOrSavings"
        });

        await DailySummarySnapshotService.createDailySummarySnapshotForUsers(users, investmentProductsDict, date);
      },
      { batchSize: DB_SUMMARY_SNAPSHOT_BATCH_SIZE }
    );
  }

  /**
   * This method calculates & stores in our Redis cache the sentiment scores for each asset.
   *
   * If the asset is an ETF, we are only interested in the price momentum, whereas for stocks, we also
   * store the analyst & news sentiment scores.
   */
  public static async updateSentimentScoresCache(): Promise<void> {
    const investmentProducts = await InvestmentProductService.getInvestmentProducts({
      populateTicker: false,
      useCache: true,
      listedOnly: true
    });

    for (let i = 0; i < investmentProducts.length; i++) {
      const investmentProduct = investmentProducts[i];

      if (investmentProduct.isETF) {
        const priceMomentum =
          await DailySummarySnapshotCronService._calculatePriceMomentumScore(investmentProduct);

        if (priceMomentum) {
          await RedisClientService.Instance.set(
            `sentiment_scores:${investmentProduct.commonId}:price_momentum`,
            priceMomentum
          );
        }
      } else if (investmentProduct.isStock) {
        const [priceMomentum, news, analyst] = await Promise.all([
          DailySummarySnapshotCronService._calculatePriceMomentumScore(investmentProduct),
          DailySummarySnapshotCronService._calculateNewsSentimentScore(investmentProduct),
          DailySummarySnapshotCronService._calculateAnalystSentimentScore(investmentProduct)
        ]);

        await RedisClientService.Instance.mSet(
          Object.fromEntries(
            Object.entries({
              [`sentiment_scores:${investmentProduct.commonId}:price_momentum`]: priceMomentum,
              [`sentiment_scores:${investmentProduct.commonId}:news`]: news,
              [`sentiment_scores:${investmentProduct.commonId}:analyst`]: analyst
            }).filter(([, value]) => !!value)
          )
        );
      }
    }
  }

  private static async _calculateNewsSentimentScore(asset: InvestmentProductDocument): Promise<number> {
    try {
      const sentiments = await CacheUtil.getCachedDataWithFallback<AssetSentimentsType>(
        `eod:sentiments:${asset.commonId}`,
        async () => eodService.getAssetSentiments(asset.commonId),
        (_) => ASSET_NEWS_SENTIMENTS_TTL_SECONDS
      );

      if (sentiments) {
        const totalCount = sentiments
          .map(({ count }) => count)
          .reduce((sum, value) => sum.plus(value), new Decimal(0))
          .toNumber();

        // Get the weighted average of the scores by "count" and then normalize it to be between [0, 1] by doing (X+1)/2
        const score = sentiments
          .reduce(
            (sum, { normalized, count }) => Decimal.div(count, totalCount).mul(normalized).add(sum),
            new Decimal(0)
          )
          .add(1)
          .div(2)
          .toNumber();

        return MathUtil.adjustBayesian(totalCount, score, NEWS_BAYESIAN_K);
      }
    } catch (err) {
      captureException(err);
      logger.error(`Failed to calculate news sentiment score for ${asset.commonId}`, {
        module: "DailySummarySnapshotCronService",
        method: "_calculateNewsSentimentScore"
      });
    }
  }

  private static async _calculateAnalystSentimentScore(asset: InvestmentProductDocument): Promise<number> {
    try {
      const fundamentals = await CacheUtil.getCachedDataWithFallback<EodStockFundamentalsResponseType>(
        `eod:fundamentals:${asset.commonId}`,
        async () =>
          eodService.getAssetFundamentalsData(asset.commonId) as Promise<EodStockFundamentalsResponseType>
      );

      const analystRatings = fundamentals?.AnalystRatings;

      if (analystRatings) {
        const totalAnalysts = EodUtil.getTotalAnalysts(analystRatings);

        // If any of the analyst views sums are null or undefined, then EOD has missing data for this section.
        if (Object.values(analystRatings).some((value) => value === null || value === undefined)) {
          return;
        }

        const score = Object.entries(analystRatings)
          .map(([key, value]) =>
            Decimal.mul(BASE_TOTAL_ANALYST_INSIGHT_WEIGHTS[key as EodAssetAnalystRatingSentimentType] ?? 0, value)
          )
          .reduce((sum, amount) => sum.plus(amount), new Decimal(0))
          .div(totalAnalysts)
          .toNumber();

        return MathUtil.adjustBayesian(totalAnalysts, score, ANALYST_BAYESIAN_K);
      }
    } catch (err) {
      captureException(err);
      logger.error(`Failed to calculate analyst sentiment score for ${asset.commonId}`, {
        module: "DailySummarySnapshotCronService",
        method: "_calculateAnalystSentimentScore"
      });
    }
  }

  private static async _calculatePriceMomentumScore(asset: InvestmentProductDocument): Promise<number> {
    try {
      const historicalPrices = await CacheUtil.getCachedDataWithFallback<{ date: string; close: number }[]>(
        `eod:historical:${asset.commonId}`,
        async () => {
          const today = new Date(Date.now());
          const historicalPriceAll = await eodService.getHistoricalPrices(asset.commonId, {
            from: DateUtil.getYearAndMonthAndDay(DateUtil.getDateOfYearsAgo(today, 10)),
            period: "d"
          });
          return historicalPriceAll;
        }
      );

      const sevenDayMovingAverage = DailySummarySnapshotCronService._calculateMovingAverage(
        historicalPrices.filter(({ date }) =>
          DateUtil.isSameOrFutureDate(new Date(date), DateUtil.getDateOfDaysAgo(new Date(Date.now()), SEVEN_DAYS))
        )
      );
      const thirtyDayMovingAverage = DailySummarySnapshotCronService._calculateMovingAverage(
        historicalPrices.filter(({ date }) =>
          DateUtil.isSameOrFutureDate(new Date(date), DateUtil.getDateOfDaysAgo(new Date(Date.now()), THIRTY_DAYS))
        )
      );

      return Decimal.sub(sevenDayMovingAverage, thirtyDayMovingAverage)
        .div(thirtyDayMovingAverage)
        .sub(MINIMUM_MOMENTUM)
        .div(Decimal.sub(MAXIMUM_MOMENTUM, MINIMUM_MOMENTUM))
        .clampedTo(LOW_CAP, HIGH_CAP)
        .toNumber();
    } catch (err) {
      captureException(err);
      logger.error(`Failed to calculate price momentum score for ${asset.commonId}`, {
        module: "DailySummarySnapshotCronService",
        method: "_calculatePriceMomentumScore"
      });
    }
  }

  private static _calculateMovingAverage(prices: { close: number }[]): number {
    return Decimal.sum(...prices.map((p) => p.close))
      .div(prices.length)
      .toNumber();
  }
}
