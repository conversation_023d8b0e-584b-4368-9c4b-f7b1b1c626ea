import { Automation } from "../../models/Automation";

export default class AutomationCronService {
  /**
   * @description The method clears the initialiseAt field on all automation documents
   * with a past value.
   */
  public static async clearPastInitialisedAt(): Promise<void> {
    await Automation.updateMany(
      {
        initialiseAt: { $lte: new Date() }
      },
      { $unset: { initialiseAt: true } }
    );
  }
}
