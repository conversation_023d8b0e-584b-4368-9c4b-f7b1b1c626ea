import eodService from "../../external-services/eodService";
import { RedisClientService } from "../../loaders/redis";

export default class FXRateCacheCronService {
  public static async updateFXRateCache(): Promise<void> {
    const currencyRates = await eodService.getLatestFXRates();

    if (eodService.currencyRatesAreValid()) {
      await RedisClientService.Instance.set("fxRates", currencyRates);
    }
  }
}
