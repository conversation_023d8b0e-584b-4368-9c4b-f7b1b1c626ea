import logger from "../../external-services/loggerService";
import <PERSON><PERSON><PERSON><PERSON> from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";
import DailySummarySnapshotCronService from "../services/dailySummarySnapshotCronService";
import DateUtil from "../../utils/dateUtil";

class SnapshotCronJob extends CronJob {
  cronName = CronJobNameEnum.DAILY_SUMMARY_SNAPSHOT;

  /**
   * @description Cron for daily portfolio snapshot creation.
   *
   * We have split daily snapshot creation into two batches:
   * A) Users that are converted (i.e. either invested or fully withdrawn users).
   * B) Non-converted users that have either:
   *    - Available cash or savings
   *    - Pending/settled savings top-ups
   *    - Settled cash deposits
   */
  async processFn(): Promise<void> {
    // We use yesterday's date, because we create the snapshots after the day's end
    const yesterday = DateUtil.getYesterday();

    logger.info("💼 Creating daily snapshots for converted users...", { module: `cron:${this.cronName}` });
    await DailySummarySnapshotCronService.createDailySummarySnapshotsForConvertedUsers(yesterday);
    logger.info("✅ Created daily snapshots for converted users", { module: `cron:${this.cronName}` });

    logger.info("💼 Creating daily snapshots for users with cash/savings...", {
      module: `cron:${this.cronName}`
    });
    await DailySummarySnapshotCronService.createDailySummarySnapshotsForUsersWithCashOrSavings(yesterday);
    logger.info("✅ Created daily snapshots for users with cash/savings", { module: `cron:${this.cronName}` });
  }
}

new SnapshotCronJob().run();
