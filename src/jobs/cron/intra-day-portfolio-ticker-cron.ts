import logger from "../../external-services/loggerService";
import IntraDayTickerCronService from "../services/intraDayTickerCronService";
import <PERSON>ron<PERSON>ob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";

class IntraDayPortfolioTicker<PERSON>ronJob extends CronJob {
  cronName = CronJobNameEnum.INTRA_DAY_PORTFOLIO_TICKER;

  /**
   * @description Cron for intra-day portfolio ticker calculation.
   */
  async processFn(): Promise<void> {
    logger.info("📈 Creating intra-day portfolio tickers...", { module: `cron:${this.cronName}` });
    await IntraDayTickerCronService.storePortfolioPrices();
    logger.info("✅ Created intra-day portfolio tickers", { module: `cron:${this.cronName}` });
  }
}

new IntraDayPortfolioTickerCronJob().run();
