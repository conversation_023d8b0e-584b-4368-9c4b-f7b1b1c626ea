import logger from "../../external-services/loggerService";
import <PERSON>ronJ<PERSON> from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";
import CorporateEventCronService from "../services/corporateEventCronService";
import { TransactionCronService } from "../services/transactionCronService";

class StockSplitCronJob extends CronJob {
  cronName = CronJobNameEnum.STOCK_SPLIT;

  /**
   * @description Cron related to stock split tasks.
   */
  async processFn(): Promise<void> {
    logger.info("🤑 Creating all new stock splits...", {
      module: `cron:${this.cronName}`
    });
    await CorporateEventCronService.createAllStockSplits();
    logger.info("✅ Completed creating all new stock splits.", {
      module: `cron:${this.cronName}`
    });

    logger.info("😵‍💫 Creating transactions for today's stock splits...", {
      module: `cron:${this.cronName}`
    });
    await TransactionCronService.createStockSplitTransactions();
    logger.info("✅ Completed creating transactions for today's stock splits.", {
      module: `cron:${this.cronName}`
    });

    logger.info("🖖 Processing transactions for today's stock splits...", {
      module: `cron:${this.cronName}`
    });
    await TransactionCronService.processStockSplitTransactions();
    logger.info("✅ Completed processing transactions for today's stock splits.", {
      module: `cron:${this.cronName}`
    });
  }
}

new StockSplitCronJob().run();
