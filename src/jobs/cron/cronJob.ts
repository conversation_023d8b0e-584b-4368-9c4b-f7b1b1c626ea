import Mongo<PERSON>oader from "../../loaders/mongo";
import mongoose from "mongoose";
import "../../event-handlers";
import { CronJobNameEnum } from "../configs/cronNames";
import { CRON_NAME_TO_PRIORITY_NODE } from "../configs/dbConfig";
import { envIsDemo, envIsProd } from "../../utils/environmentUtil";
import * as Sentry from "@sentry/node";
import * as SentryProfiling from "@sentry/profiling-node";
import logger from "../../external-services/loggerService";
import { RedisClientService } from "../../loaders/redis";
import { workerData } from "worker_threads";
import PerformanceUtil from "../../utils/performanceUtil";
// NOTE: this import should probably not be removed, otherwise the event functionality will break
import eventEmitter from "../../loaders/eventEmitter";
import { delay } from "../../utils/scriptUtil";

/**
 * A CronJob component represents cron tasks that will run as specified by cron file.
 * A sentry check-in is sent when a cronJob runs and another one with status 'ok' or 'error'
 * when the execution is finalised.
 *
 * A mongoose connection opens when the cron job runs and it is closed when the execution is
 * completed.
 *
 * Lock mechanism:
 * In order to avoid having concurrent runs of the same cron job, which can happen when the cron
 * job service on render is deployed, we have implemented a redis cache-based lock mechanism.
 * When the job is initiated a lock is acquired and it's released when the job has finished running.
 * Both lock acquisition & release takes place atomically with redis scripts. During acquisition the
 * script checks whether the value for the lock key exists and if it doesn't, it sets it to "locked".
 * Release attempts to atomically delete the cache entry if it exists. Locks have a cache expiration of
 * 1h, in order to avoid future run of the cron job if something goes wrong.
 *
 */
export default abstract class CronJob {
  abstract cronName: CronJobNameEnum;

  private get _cacheKey(): string {
    return `cron:${this.cronName}:lock`;
  }

  async run(): Promise<void> {
    const acquiredLock = await this._acquireLock();
    if (!acquiredLock) {
      logger.warn(`Cron job ${this.cronName} skipped because lock has already been acquired`, {
        module: `cron:${this.cronName}`,
        method: "run"
      });

      setTimeout(() => process.exit(1), 5000);
      return;
    }

    Sentry.init({
      enabled: envIsProd(),
      dsn: process.env.SENTRY_DNS,
      environment: process.env.NODE_ENV,
      integrations: [
        SentryProfiling.nodeProfilingIntegration(),
        Sentry.extraErrorDataIntegration(),
        // enable HTTP calls tracing
        Sentry.httpIntegration({
          ignoreOutgoingRequests: (url) => {
            return url.includes("datadog");
          }
        }),
        Sentry.mongoIntegration(),
        Sentry.mongooseIntegration()
      ],
      profilesSampler: () => {
        if (envIsProd()) {
          return 0.2;
        } else {
          return false;
        }
      },
      tracesSampler: () => {
        if (envIsProd()) {
          return 0.2;
        } else {
          return false;
        }
      }
    });

    const mongoLoader = new MongoLoader(CRON_NAME_TO_PRIORITY_NODE[this.cronName]);
    mongoLoader.init();

    mongoose.connection.once("open", async () => {
      let exitCode: number;

      const checkInId = Sentry.captureCheckIn({
        monitorSlug: this.cronName,
        status: "in_progress"
      });

      try {
        await PerformanceUtil.withPerformance(() => this.processFn(), this.cronName);
        Sentry.captureCheckIn({
          // Make sure this variable is named `checkInId`
          checkInId,
          monitorSlug: this.cronName,
          status: "ok"
        });
        exitCode = 0;
      } catch (err) {
        try {
          Sentry.captureCheckIn({
            // Make sure this variable is named `checkInId`
            checkInId,
            monitorSlug: this.cronName,
            status: "error"
          });
        } catch (err) {
          logger.error("Failed to capture error check in", {
            module: `cron:${this.cronName}`,
            data: { error: err }
          });
        }

        exitCode = 1;
        Sentry.captureException(err);
        logger.error("Cron jobs failed", { module: `cron:${this.cronName}`, data: { error: err } });
      }

      await this._cleanExit(exitCode);
    });
  }

  abstract processFn(): Promise<void>;

  // ===============
  // PRIVATE METHODS
  // ===============

  private async _acquireLock() {
    const acquiredLock = await RedisClientService.Instance.atomicSetIfNotExists(this._cacheKey, "locked", {
      ex: workerData.lockExpirationSec
    });
    if (!acquiredLock) {
      logger.warn(`Failed to acquire lock ${this._cacheKey}`, { module: `cron:${this.cronName}` });
    }
    return acquiredLock;
  }

  private async _releaseLock() {
    const deletedLock = await RedisClientService.Instance.atomicDel(this._cacheKey);
    if (!deletedLock) {
      logger.warn(`Failed to release lock ${this._cacheKey} or lock key is expired already`, {
        module: `cron:${this.cronName}`
      });
    }
  }

  /**
   * In staging, we always monitor memory unless specified otherwise in environment variables.
   *
   * For production, we sample memory monitoring based on settings from CRON_CONFIG.
   * @private
   */
  private _shouldMonitorMemory() {
    if (process.env.MONITOR_MEMORY !== "true") {
      return false;
    }

    if (envIsDemo()) {
      return true;
    } else if (envIsProd()) {
      return workerData.memoryMonitoringSampleRate ? Math.random() < workerData.memoryMonitoringSampleRate : true;
    }
  }

  private async _cleanExit(exitCode: number): Promise<void> {
    try {
      // This is a workaround to delay process exiting, until all event handlers have finished handling events.
      await delay(20000);

      await this._releaseLock();
      await mongoose.connection.close();

      if (this._shouldMonitorMemory()) {
        await PerformanceUtil.recordHeapDump(`cron:${this.cronName}`);
      }
    } catch (err) {
      logger.error("Cron job clean up before exiting failed", {
        module: `cron:${this.cronName}`,
        data: { error: err }
      });
    } finally {
      // Before exiting we add some additional delay until all logs have been submitted.
      await delay(5000);
      process.exit(exitCode);
    }
  }
}
