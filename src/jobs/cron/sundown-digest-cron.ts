import logger from "../../external-services/loggerService";
import { CronJobNameEnum } from "../configs/cronNames";
import CronJob from "./cronJob";
import SundownDigestCronService from "../services/sundownDigestCronService";

class SundownDigestCron extends CronJob {
  cronName = CronJobNameEnum.SUNDOWN_DIGEST;

  /**
   * @description Cron creating sundown digests.
   */
  async processFn(): Promise<void> {
    logger.info("🎁 Creating sundown digests...", { module: `cron:${this.cronName}` });
    await SundownDigestCronService.createAllSundownDigests();
    logger.info("✅ Completed creating sundown digests", { module: `cron:${this.cronName}` });
  }
}

new SundownDigestCron().run();
