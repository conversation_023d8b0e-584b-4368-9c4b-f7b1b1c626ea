import logger from "../../external-services/loggerService";
import IntraDayTickerCronService from "../services/intraDayTickerCronService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";

class IntraDayAssetTickerCronJob extends CronJob {
  cronName = CronJobNameEnum.INTRA_DAY_ASSET_TICKER;

  /**
   * @description Cron for asset ticker calculation.
   */
  async processFn(): Promise<void> {
    logger.info("💾 Caching intra-day asset tickers...", { module: `cron:${this.cronName}` });
    await IntraDayTickerCronService.cacheTodaysAssetPrices();
    logger.info("✅ Cached intra-day asset tickers", { module: `cron:${this.cronName}` });

    logger.info("📈 Creating intra-day asset tickers...", { module: `cron:${this.cronName}` });
    await IntraDayTickerCronService.storeAssetPrices();
    logger.info("✅ Created intra-day asset tickers", { module: `cron:${this.cronName}` });

    logger.info("📣 Broadcasting pricing updates to clients...", { module: `cron:${this.cronName}` });
    await IntraDayTickerCronService.broadcastAssetPrices();
    logger.info("✅ Broadcast pricing updates to clients", { module: `cron:${this.cronName}` });
  }
}

new IntraDayAssetTickerCronJob().run();
