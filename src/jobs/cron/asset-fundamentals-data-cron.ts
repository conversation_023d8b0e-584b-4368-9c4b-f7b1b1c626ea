import logger from "../../external-services/loggerService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";
import InvestmentProductService from "../../services/investmentProductService";

class AssetFundamentalsDataCronJob extends CronJob {
  cronName = CronJobNameEnum.ASSET_FUNDAMENTALS_DATA;

  /**
   * @description Cron for caching EOD fundamentals asset data.
   */
  async processFn(): Promise<void> {
    logger.info("Caching fundamental asset data...", {
      module: `cron:${this.cronName}`
    });
    await InvestmentProductService.cacheAssetFundamentalsData();
    logger.info("✅ Completed caching fundamental asset data!", {
      module: `cron:${this.cronName}`
    });
  }
}

new AssetFundamentalsDataCronJob().run();
