import logger from "../../external-services/loggerService";
import { TransactionService } from "../../services/transactionService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";

class InvestmentDividendsCronJob extends CronJob {
  cronName = CronJobNameEnum.INVESTMENT_DIVIDENDS;

  /**
   * @description Cron job that fetches dividend transactions from Wealthkernel,
   * creates the corresponding dividend and asset dividend documents that are linked to investments (ETFs/Stocks) and updates portfolios cash accordingly.
   *
   * The cron should be running at night, so that the db cash has been updated by morning when
   * the cash mismatch checks run.
   */
  async processFn(): Promise<void> {
    logger.info("🛍️ Initiating Wealthkernel investment dividend tasks...", {
      module: `cron:${this.cronName}`
    });

    logger.info("🛍️ Creating Wealthkernel investment dividends...", {
      module: `cron:${this.cronName}`
    });
    await TransactionService.createWealthkernelInvestmentDividends();

    logger.info("🛍️ Creating Wealthkernel investment asset dividends...", {
      module: `cron:${this.cronName}`
    });
    await TransactionService.createWealthkernelInvestmentAssetDividends();

    logger.info("✅ Completed Wealthkernel investment dividend tasks", { module: `cron:${this.cronName}` });
  }
}

new InvestmentDividendsCronJob().run();
