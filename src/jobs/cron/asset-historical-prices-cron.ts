import logger from "../../external-services/loggerService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";
import InvestmentProductService from "../../services/investmentProductService";

class AssetHistoricalPricesCronJob extends CronJob {
  cronName = CronJobNameEnum.ASSET_HISTORICAL_PRICES;

  /**
   * @description Cron for caching EOD historical asset prices.
   */
  async processFn(): Promise<void> {
    logger.info("Caching historical asset prices...", {
      module: `cron:${this.cronName}`
    });
    await InvestmentProductService.cacheAssetHistoricalPrices();
    logger.info("✅ Completed caching historical asset prices!", {
      module: `cron:${this.cronName}`
    });
  }
}

new AssetHistoricalPricesCronJob().run();
