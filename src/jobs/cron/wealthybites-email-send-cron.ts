import logger from "../../external-services/loggerService";
import <PERSON><PERSON>Job from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";
import WealthybitesService from "../../services/wealthybitesService";

class WealthybitesEmailSendCronJob extends CronJob {
  cronName = CronJobNameEnum.WEALTHYBITES_EMAIL_SEND;

  /**
   * @description Cron for sending Wealthybites emails.
   */
  async processFn(): Promise<void> {
    logger.info("📧 Sending Wealthybites emails...", { module: `cron:${this.cronName}` });
    await WealthybitesService.processAllWealthybitesEmails();
    logger.info("✅ Wealthybites emails sent", { module: `cron:${this.cronName}` });
  }
}

new WealthybitesEmailSendCronJob().run();
