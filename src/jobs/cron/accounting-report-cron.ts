import logger from "../../external-services/loggerService";
import CronJob from "./cronJob";
import { AccountingReportingService } from "../../services/accountingReportingService";
import { CronJobNameEnum } from "../configs/cronNames";

class AccountingReportCronJob extends CronJob {
  cronName = CronJobNameEnum.ACCOUNTING_REPORT;

  /**
   * @description Cron job for generating daily accounting reports in Epsilon XML and CSV formats
   */
  async processFn(): Promise<void> {
    logger.info("📊 Starting accounting report generation...", { module: `cron:${this.cronName}` });

    // Generate XML report and upload to Cloudflare
    await AccountingReportingService.generateXMLAccountingReportFile({
      uploadToCloud: true
    });

    // Generate CSV report and upload to Cloudflare
    await AccountingReportingService.generateCSVAccountingReportFile({
      uploadToCloud: true
    });

    // Generate invoice CSV report and upload to Cloudflare
    await AccountingReportingService.generateInvoiceCsvReportFile({
      uploadToCloud: true
    });

    logger.info("✅ Completed accounting report generation", { module: `cron:${this.cronName}` });
  }
}

new AccountingReportCronJob().run();
