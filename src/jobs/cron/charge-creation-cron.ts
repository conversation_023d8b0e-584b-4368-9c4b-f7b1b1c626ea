import logger from "../../external-services/loggerService";
import { TransactionService } from "../../services/transactionService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";

class AssetTransactionCronJob extends CronJob {
  cronName = CronJobNameEnum.CHARGE_CREATION;

  /**
   * @description Cron for creation of charges. This cron creates only the charge transaction documents.
   * Submissions to WK are handled in separate charge submission cron jobs.
   */
  async processFn(): Promise<void> {
    logger.info("👩‍🚀 Initiating charge transactions tasks...", { module: `cron:${this.cronName}` });
    logger.info("🤑 Creating commission charges for transactions...", { module: `cron:${this.cronName}` });
    await TransactionService.chargeCommissionsForTransactions();
    logger.info("✅ Created commission charges for transactions!", { module: `cron:${this.cronName}` });

    logger.info("🤑 Creating commission charges for rewards...", { module: `cron:${this.cronName}` });
    await TransactionService.chargeCommissionsForRewards();
    logger.info("✅ Created commission charges for rewards!", { module: `cron:${this.cronName}` });

    logger.info("🤑 Creating execution spread charges for transactions...", {
      module: `cron:${this.cronName}`
    });
    await TransactionService.chargeExecutionSpreadForTransactions();
    logger.info("✅ Created execution spread charges for transactions!", { module: `cron:${this.cronName}` });

    logger.info("🤑 Creating execution spread charges for rewards...", { module: `cron:${this.cronName}` });
    await TransactionService.chargeExecutionSpreadForRewards();
    logger.info("✅ Created execution spread charges for rewards!", { module: `cron:${this.cronName}` });

    logger.info("🤑 Creating fx charges for transactions...", { module: `cron:${this.cronName}` });
    await TransactionService.chargeFxFeesForTransactions();
    logger.info("✅ Created fx charges for transactions!", { module: `cron:${this.cronName}` });

    logger.info("🤑 Creating fx charges for rewards...", { module: `cron:${this.cronName}` });
    await TransactionService.chargeFxFeesForRewards();
    logger.info("✅ Created fx charges for rewards!", { module: `cron:${this.cronName}` });

    logger.info("🤑 Creating remainder charges for transactions...", { module: `cron:${this.cronName}` });
    await TransactionService.chargeRemainderFeesForTransactions();
    logger.info("✅ Created remainder charges for transactions!", { module: `cron:${this.cronName}` });

    logger.info("🤑 Creating realtime execution charges for transactions...", { module: `cron:${this.cronName}` });
    await TransactionService.chargeRealtimeExecutionForTransactions();
    logger.info("✅ Created realtime execution charges for transactions!", { module: `cron:${this.cronName}` });

    logger.info("✅ Completed charge transactions tasks!", { module: `cron:${this.cronName}` });
  }
}

new AssetTransactionCronJob().run();
