import logger from "../../external-services/loggerService";
import { CronJobNameEnum } from "../configs/cronNames";
import CronJob from "./cronJob";
import IndexPriceCronService from "../services/indexPriceCronService";

class StoreIndexPricesCron extends CronJob {
  cronName = CronJobNameEnum.STORE_INDEX_PRICES;

  /**
   * @description @description Cron creating index price documents with prices & returns.
   */
  async processFn(): Promise<void> {
    logger.info("🎁 Creating index prices from realtime cache...", { module: `cron:${this.cronName}` });
    await IndexPriceCronService.storeIndexPricesFromRealtimeCache();
    logger.info("✅ Completed creating index prices from realtime cache", { module: `cron:${this.cronName}` });
  }
}

new StoreIndexPricesCron().run();
