import logger from "../../external-services/loggerService";
import { envIsProd } from "../../utils/environmentUtil";
import { TransactionService } from "../../services/transactionService";
import OrderService from "../../services/orderService";
import MandateService from "../../services/mandateService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";

class CleanTransactionsCronJob extends CronJob {
  cronName = CronJobNameEnum.CLEAN_TRANSACTIONS;

  /**
   * @description <PERSON>ron running in staging ONlY to find and clean orphaned transactions & orders from our database.
   */
  async processFn(): Promise<void> {
    if (envIsProd()) {
      logger.error("Cannot run cleaning job in production!", {
        module: `cron:${this.cronName}`
      });
      return;
    }

    logger.info("🧹 Cleaning orphaned transactions & orders...", {
      module: `cron:${this.cronName}`
    });
    await TransactionService.deleteOrphanedTransactions();
    await OrderService.deleteOrphanedOrders();
    await MandateService.deleteOrphanedMandates();
    logger.info("🧹 Cleaned orphaned transactions & orders!", {
      module: `cron:${this.cronName}`
    });
  }
}

new CleanTransactionsCronJob().run();
