import logger from "../../external-services/loggerService";
import DateUtil from "../../utils/dateUtil";
import { TransactionService } from "../../services/transactionService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";
import { entitiesConfig } from "@wealthyhood/shared-configs";

class ChargeSubmissionEUCronJob extends CronJob {
  cronName = CronJobNameEnum.CHARGE_SUBMISSION_EU;

  /**
   * @description Cron for submission of charges (as specified by the created charge transaction documents) to WK.
   */
  async processFn(): Promise<void> {
    logger.info("💰 Initiating charges creation task...", { module: `cron:${this.cronName}` });
    await TransactionService.submitChargesToWK(
      DateUtil.getFirstDayOfLastMonth(),
      new Date(Date.now()),
      entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    );
    logger.info("✅ Completed charges creation task", { module: `cron:${this.cronName}` });
  }
}

new ChargeSubmissionEUCronJob().run();
