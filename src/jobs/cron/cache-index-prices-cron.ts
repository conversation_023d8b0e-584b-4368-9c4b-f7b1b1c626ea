import logger from "../../external-services/loggerService";
import { CronJobNameEnum } from "../configs/cronNames";
import CronJob from "./cronJob";
import IndexPriceCronService from "../services/indexPriceCronService";

class CacheIndexPricesCron extends CronJob {
  cronName = CronJobNameEnum.CACHE_INDEX_PRICES;

  /**
   * @description @description Cron caching index prices.
   */
  async processFn(): Promise<void> {
    logger.info("💾 Caching current index prices...", { module: `cron:${this.cronName}` });
    await IndexPriceCronService.cacheCurrentIndexPrices();
    logger.info("✅ Cached current index prices", { module: `cron:${this.cronName}` });
  }
}

new CacheIndexPricesCron().run();
