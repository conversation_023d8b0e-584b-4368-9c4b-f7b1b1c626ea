import logger from "../../external-services/loggerService";
import { TransactionService } from "../../services/transactionService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";

class WealthyhoodDividendBonusCronJob extends CronJob {
  cronName = CronJobNameEnum.WH_DIVIDEND_BONUS;

  /**
   * @description Cron running Wealthyhood dividend bonus creation for the first 10 days of the month.
   */
  async processFn(): Promise<void> {
    logger.info("🎁 Initiating Wealthyhood dividend bonus tasks...", { module: `cron:${this.cronName}` });
    // We sync bonuses first because if we create them and then immediately try to sync them, we receive 404s from WK.
    await TransactionService.syncWealthyhoodDividendDeposits();
    await TransactionService.createWealthyhoodDividendDeposits();
    logger.info("✅ Completed Wealthyhood dividend bonus tasks", { module: `cron:${this.cronName}` });
  }
}

new WealthyhoodDividendBonusCronJob().run();
