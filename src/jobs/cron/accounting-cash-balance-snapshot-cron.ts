import logger from "../../external-services/loggerService";
import <PERSON><PERSON><PERSON><PERSON> from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";
import AccountingCronService from "../services/accountingCronService";
import DateUtil from "../../utils/dateUtil";

class CashBalanceSnapshotCronJob extends CronJob {
  cronName = CronJobNameEnum.ACCOUNTING_CASH_BALANCE_SNAPSHOT;

  /**
   * @description Cron job for capturing daily cash balance snapshots from WealthKernel
   *
   * This job runs daily at 02:00 UTC (after end-of-day processing) and:
   * 1. Fetches cash balances from WealthKernel for configured accounts
   * 2. Stores the snapshots for later reconciliation against ledger entries
   *
   * The snapshots include:
   * - Individual portfolio balances for fee accounts (custody, commission, MMF dividend)
   * - Aggregated balance across all client portfolios for omnibus account
   */
  async processFn(): Promise<void> {
    // Use today's date for the snapshot since we run after end-of-day
    const today = DateUtil.getYearAndMonthAndDay(new Date());

    logger.info("🏦 Starting cash balance snapshot capture...", {
      module: `cron:${this.cronName}`,
      data: { asOfDate: today }
    });

    await AccountingCronService.captureWealthyhoodAccountsCashBalanceSnapshots();
    await AccountingCronService.captureAggregatedEUClientsAccountsCashBalance();

    logger.info("✅ Cash balance snapshot capture completed", {
      module: `cron:${this.cronName}`,
      data: { asOfDate: today }
    });
  }
}

new CashBalanceSnapshotCronJob().run();
