import logger from "../../external-services/loggerService";
import DateUtil from "../../utils/dateUtil";
import { TransactionService } from "../../services/transactionService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";

const MONTH_OVERRIDE = process.env.MONTH_OVERRIDE;

class WealthyhoodDividendCreationCronJob extends CronJob {
  cronName = CronJobNameEnum.WH_DIVIDEND_CREATION;

  /**
   * @description Cron creating Wealthyhood dividends for users on the last work day of the month.
   *
   * We only create Wealthyhood dividends for UK users.
   *
   */
  async processFn(): Promise<void> {
    if (DateUtil.isLastUKWorkDayOfThisMonth() || MONTH_OVERRIDE) {
      logger.info("✅ It is the last work day of the month, proceeding with creating Wealthyhood dividends...", {
        module: `cron:${this.cronName}`
      });

      logger.info("💰 Creating Wealthyhood dividends...", { module: `cron:${this.cronName}` });
      await TransactionService.createWealthyhoodDividends(MONTH_OVERRIDE);
      logger.info("✅ Completed creating Wealthyhood dividends!", {
        module: `cron:${this.cronName}`
      });
    } else {
      logger.info("💔 It is not the last work day of the month, we're not creating Wealthyhood dividends yet!", {
        module: `cron:${this.cronName}`
      });
    }
  }
}

new WealthyhoodDividendCreationCronJob().run();
