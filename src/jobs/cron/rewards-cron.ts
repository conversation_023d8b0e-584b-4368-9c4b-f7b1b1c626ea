import logger from "../../external-services/loggerService";
import RewardService from "../../services/rewardService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";

class RewardsCronJob extends CronJob {
  cronName = CronJobNameEnum.REWARDS;

  /**
   * @description Cron running reward creation and reward deposit creation & syncing (Mon - Fri).
   */
  async processFn(): Promise<void> {
    logger.info("🎁 Initiating reward tasks...", { module: `cron:${this.cronName}` });
    await RewardService.createAllCRMCampaignRewards();

    // We sync bonuses first because if we create them and then immediately try to sync them, we receive 404s from WK.
    await RewardService.syncPendingRewardDeposits();
    await RewardService.createRewardDeposits();
    logger.info("✅ Completed reward tasks", { module: `cron:${this.cronName}` });
  }
}

new RewardsCronJob().run();
