import { ContentIngestionCronService } from "../services/contentIngestionCronService";
import CronJob from "./cronJob";
import logger from "../../external-services/loggerService";
import { CronJobNameEnum } from "../configs/cronNames";
import WealthyhubService from "../../services/wealthyhubService";

class ContentIngestionCronJob extends CronJob {
  cronName = CronJobNameEnum.CONTENT_INGESTION;

  /**
   * @description Stores content from Finimize to Contentful and updates our database
   */
  async processFn(): Promise<void> {
    logger.info("📚 Ingesting analyst insights from finimize...", {
      module: `cron:${this.cronName}`
    });
    await ContentIngestionCronService.createAnalystInsightContentEntries();
    logger.info("✅ Finished ingesting analyst insights from finimize.", {
      module: `cron:${this.cronName}`
    });

    logger.info("📚 Uploading analyst insights to contentful...", {
      module: `cron:${this.cronName}`
    });
    await ContentIngestionCronService.uploadAnalystInsightsToContentful();
    logger.info("✅ Finished uploading analyst insights to contentful.", {
      module: `cron:${this.cronName}`
    });

    logger.info("📚 Deleting analyst insights cache...", {
      module: `cron:${this.cronName}`
    });
    await WealthyhubService.deleteAnalystInsightsCache();
    logger.info("✅ Deleting analyst insights cache", {
      module: `cron:${this.cronName}`
    });
  }
}

new ContentIngestionCronJob().run();
