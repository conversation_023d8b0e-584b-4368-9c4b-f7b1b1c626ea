import logger from "../../external-services/loggerService";
import CronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";
import DailyTickerCronService from "../services/dailyTickerCronService";

class SavingsProductDataUpdateCheckCron extends CronJob {
  cronName = CronJobNameEnum.SAVINGS_PRODUCT_DATA_UPDATE_CHECK;

  /**
   * @description Checks if savings product data have been updated
   */
  async processFn(): Promise<void> {
    logger.info("🔍 Checking for savings products updates...", { module: `cron:${this.cronName}` });
    await DailyTickerCronService.checkSavingsProductDataUpdate();
    logger.info("✅ Completed checking for savings products updates", { module: `cron:${this.cronName}` });
  }
}

new SavingsProductDataUpdateCheckCron().run();
