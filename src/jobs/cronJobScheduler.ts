import "../loaders/environment";
import MongoLoader from "../loaders/mongo";
import { MongoReadPreferenceEnum } from "../utils/dbUtil";

const mongoLoader = new MongoLoader(MongoReadPreferenceEnum.SECONDARY);
mongoLoader.init();

import "../event-handlers";

import Bree from "bree";
import fs from "fs";
import mongoose from "mongoose";
import path from "path";
import Grace<PERSON> from "@ladjs/graceful";
import STAGING_CRON_CONFIG from "./configs/staging.config";
import PRODUCTION_CRON_CONFIG from "./configs/prod.config";
import { CronJobNameEnum } from "./configs/cronNames";
import logger from "../external-services/loggerService";
import DateUtil from "../utils/dateUtil";
import { envIsProd } from "../utils/environmentUtil";
import PerformanceUtil from "../utils/performanceUtil";
import * as Seg<PERSON>ult<PERSON>andler from "node-segfault-handler";

enum CronActionEnum {
  START = "start",
  STOP = "stop"
}

const JOB_NAME_PATH = "/tmp/bree_job_name";
const JOB_EXECUTION_TIMEOUT_MINUTES = 30;

/**
 * @description This is a job scheduler for all our cron jobs.
 *
 * Execution of cron jobs in each env is defined by the prod.config.ts
 * or staging.config.ts configuration file.
 *
 * In order to manually start/stop a job a signal has to be sent:
 * - For start: SIGUR1
 * - For stop: SIGUR2
 *
 * Example:
 * ```
 * ps aux | grep 'node dist/jobs/cronJobScheduler.js'
 * echo "asset-data-cron" > /tmp/bree_job_name && kill -SIGUSR1 <pid>
 * ```
 */
class CronJobScheduler {
  private _bree: Bree;

  constructor() {
    const jobsConfig = envIsProd() ? PRODUCTION_CRON_CONFIG : STAGING_CRON_CONFIG;

    this._bree = new Bree({
      root: path.join(__dirname, "cron"),
      jobs: jobsConfig.map(
        ({ name, cron, closeWorkerAfterMs, lockExpirationSec, memoryMonitoringSampleRate }) => ({
          name: `${name}-cron`,
          cron,
          // NOTE: Due to an issue in bree.js if we set this option for some cron jobs, we should be passing a default
          // value for the rest, which should not be 0 (as stated in the docs), otherwise we'll get an error
          // "'closeWorkersAfterMs' cannot have a value undefined".
          closeWorkerAfterMs:
            closeWorkerAfterMs ?? DateUtil.convertMinutesToMilliseconds(JOB_EXECUTION_TIMEOUT_MINUTES),
          worker: {
            workerData: {
              // This is being used by the worker to define when the cron lock should expire.
              lockExpirationSec:
                lockExpirationSec ?? DateUtil.convertMinutesToSeconds(JOB_EXECUTION_TIMEOUT_MINUTES),
              memoryMonitoringSampleRate
            }
          }
        })
      )
    });

    const graceful = new Graceful({
      brees: [this._bree],
      mongooses: [mongoose]
    });
    graceful.listen();
  }

  /**
   * @description Initialize Bree and set up signal handling.
   *
   * As part of initialization all jobs will be stopped.
   *
   */
  public async init() {
    await this._bree.stop();

    this._bree.start();

    // Set up periodic memory monitoring (every 10 minutes)
    if (process.env.MONITOR_MEMORY === "true") {
      setInterval(
        async () => {
          await PerformanceUtil.recordHeapDump("main");
        },
        10 * 60 * 1000
      ); // Take heap dump every ten minutes
    }

    // Listen for specific signals
    process.on("SIGUSR1", () => this._handleSignal(CronActionEnum.START));
    process.on("SIGUSR2", () => this._handleSignal(CronActionEnum.STOP));
  }

  // ===============
  // PRIVATE METHODS
  // ===============

  /**
   *
   * @param cronName
   */
  private _getCronLockFilePath(cronName: string): string {
    const parsedCronName = cronName.split("-cron")[0] as CronJobNameEnum;
    return path.join(__dirname, "cron", `job-${parsedCronName}.lock`);
  }

  /**
   *
   * @param jobName is in the format `${CronJobNameEnum}-cron`
   */
  private _releaseLock(jobName: string) {
    const lockFilePath = this._getCronLockFilePath(jobName);
    if (fs.existsSync(lockFilePath)) {
      fs.unlinkSync(lockFilePath);
    }
  }

  /**
   *
   * @param jobName is in the format `${CronJobNameEnum}-cron`
   */
  private _runJob(jobName: string) {
    if (this._bree.config.jobs.find((job) => job.name === jobName)) {
      logger.info(`✅ Manually starting job ${jobName}`, { module: "CronJobScheduler", method: "_runJob" });

      this._bree.run(jobName);
    } else {
      // this is left here intentionally for shell debugging
      console.error(`Cron job ${jobName} was not found in the list job names`);
    }
  }

  /**
   *
   * @param jobName is in the format `${CronJobNameEnum}-cron`
   */
  private async _stopJob(jobName: string): Promise<void> {
    this._releaseLock(jobName);

    if (this._bree.workers.get(jobName)) {
      logger.info(`✅ Manually stopping job ${jobName}`, { module: "CronJobScheduler", method: "_stopJob" });
      await this._bree.stop(jobName);
    } else {
      // this is left here intentionally for shell debugging
      console.error(`Cron job ${jobName} cannot be stopped because it is not currently running`);
    }
  }

  private async _handleSignal(action: CronActionEnum): Promise<void> {
    try {
      const jobName = fs.readFileSync(JOB_NAME_PATH, "utf8").trim() as CronJobNameEnum;
      if (jobName) {
        if (action === CronActionEnum.START) {
          this._runJob(jobName);
        } else if (action === CronActionEnum.STOP) {
          await this._stopJob(jobName);
        }
      }
    } catch (err) {
      // this is left here intentionally for shell debugging
      console.error("Error reading the temporary file", err);
    }
  }
}

mongoose.connection.once("open", async () => {
  const scheduler = new CronJobScheduler();
  scheduler.init();

  logger.info("✅ Cron Job Scheduler has been started.", { module: "CronJobScheduler" });
});

SegfaultHandler.registerHandler();
