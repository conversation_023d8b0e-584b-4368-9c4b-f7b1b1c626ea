import axios from "axios";
import Decimal from "decimal.js";
import { DateTime } from "luxon";
import {
  currenciesConfig,
  investmentUniverseConfig,
  publicInvestmentUniverseConfig
} from "@wealthyhood/shared-configs";
import { BadRequestError, InternalServerError, NotFoundError } from "../models/ApiErrors";
import { addBreadcrumb, captureException } from "@sentry/node";
import logger from "./loggerService";
import CurrencyUtil from "../utils/currencyUtil";
import DateUtil from "../utils/dateUtil";
import { PartialRecord } from "utils";

type ForexPairType = `${currenciesConfig.MainCurrencyType}${currenciesConfig.MainCurrencyType}`;

const { ASSET_CONFIG } = investmentUniverseConfig;
const { PUBLIC_ASSET_CONFIG } = publicInvestmentUniverseConfig;

export type EodETFFundamentalsResponseType = {
  General: {
    CurrencyCode: string;
    Exchange: string;
    Description: string;
    Name: string;
  };
  ETF_Data: {
    ISIN: string;
    Company_Name: string;
    ETF_URL: string;
    Company_URL: string;
    TotalAssets: string;
    Dividend_Paying_Frequency: string;
    Valuations_Growth: {
      Valuations_Rates_Portfolio: {
        "Price/Prospective Earnings": string;
        "Price/Sales": string;
        "Price/Book": string;
        "Price/Cash Flow": string;
        "Dividend-Yield Factor": string;
      };
      Growth_Rates_Portfolio: {
        "Long-Term Projected Earnings Growth": string;
        "Historical Earnings Growth": string;
        "Sales Growth": string;
        "Cash-Flow Growth": string;
        "Book-Value Growth": string;
      };
    };
    Ongoing_Charge: string;
    Holdings_Count: number;
    Top_10_Holdings: Record<string, EodETFHoldingDataType>;
    World_Regions: Record<EodWorldRegionType, { "Equity_%": string }>;
    Sector_Weights: Record<EodSectorType, { "Equity_%": string }>;
    Performance: {
      Returns_YTD: string;
      Returns_1Y: string;
      Returns_3Y: string;
      Returns_5Y: string;
      Returns_10Y: string;
      "1y_Volatility": string;
      "3y_Volatility": string;
      "3y_ExpReturn": string;
      "3y_SharpRatio": string;
    };
  };
  Technicals: {
    "52WeekHigh": number;
    "52WeekLow": number;
    "50DayMA": number;
    "200DayMA": number;
  };
};

export type EodETFHoldingDataType = {
  Name: string;
  "Assets_%": number;
  Code: string;
  Exchange: string;
};

export type EodWorldRegionType =
  | "North America"
  | "United Kingdom"
  | "Europe Developed"
  | "Europe Emerging"
  | "Africa/Middle East"
  | "Japan"
  | "Australasia"
  | "Asia Developed"
  | "Asia Emerging"
  | "Latin America";

export type EodSectorType =
  | "Basic Materials"
  | "Consumer Cyclicals"
  | "Financial Services"
  | "Real Estate"
  | "Communication Services"
  | "Energy"
  | "Industrials"
  | "Technology"
  | "Consumer Defensive"
  | "Healthcare"
  | "Utilities";

export type EodFundamentalsResponseType = EodStockFundamentalsResponseType | EodETFFundamentalsResponseType;

export type EodStockFundamentalsResponseType = {
  General: EodAssetGeneralFundamentalsType;
  Highlights: EodAssetHighlightsFundamentalsType;
  Valuation: EodAssetValuationFundamentalsType;
  Technicals: EodAssetTechnicalsFundamentalsType;
  AnalystRatings: EodAssetAnalystRatingsFundamentalsType;
};

type EodAssetGeneralFundamentalsType = {
  Description: string;
  WebURL: string;
  FullTimeEmployees: number;
  GicIndustry: string;
  Exchange: string;
  Officers: Record<number, EodOfficerType>;
  AddressData: EodAddressDataType;
  LogoURL: string;
  ISIN: string;
};
type EodAssetHighlightsFundamentalsType = {
  DividendYield: number;
  EarningsShare: number;
  PERatio: number;
  MarketCapitalization: number;
  WallStreetTargetPrice: number;
};
type EodAssetValuationFundamentalsType = {
  ForwardPE: number;
};
type EodAssetTechnicalsFundamentalsType = {
  Beta: number;
};
export type EodAssetAnalystRatingSentimentType = "StrongBuy" | "Buy" | "Hold" | "Sell" | "StrongSell";
export type EodAssetAnalystRatingsFundamentalsType = Record<EodAssetAnalystRatingSentimentType, number>;
type EodOfficerType = {
  Name: string;
  Title: string;
};
type EodAddressDataType = {
  City: string;
  State: string;
  Country: string;
};

export type StockSplitType = { code: string; exchange: string; date: string; split: string };

type SentimentsDataType = Record<string, AssetSentimentsType>;

export type AssetSentimentsType = AssetSentimentType[];
type AssetSentimentType = {
  date: string; // e.g. 2024-11-27
  count: number;
  normalized: number;
};

export type HistoricalDataType = {
  date: string; // "2024-05-29"
  open: number;
  high: number;
  low: number;
  close: number;
  adjusted_close: number;
  volume: number;
};

export type HistoricalVolumeDataType = {
  date: string; // "2024-05-29"
  volume: number;
};

export type IntraDayDataType = {
  timestamp: number; // in seconds
  gmtoffset: number;
  datetime: string; // SQL date "2024-05-13 13:35:00"
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
};

export type RealTimeDataType = {
  code: string;
  timestamp: number;
  gmtoffset: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  previousClose: number;
  change: number;
  change_p: number;
};

export type ExchangeRates = Record<
  currenciesConfig.MainCurrencyType,
  Record<currenciesConfig.MainCurrencyType, number>
>;

/**
 * CONFIG
 */
export const EOD_BASE_URL = "https://eodhistoricaldata.com";

const DAYS_TO_RETRIEVE_SENTIMENTS_FOR = 30;

/**
 * To learn more about the API used, check
 * https://eodhistoricaldata.com/financial-apis/api-for-historical-data-and-volumes/
 */
export class EodService {
  private _currencyRates: ExchangeRates = {
    USD: {
      GBP: undefined,
      EUR: undefined,
      USD: 1
    },
    EUR: {
      GBP: undefined,
      USD: undefined,
      EUR: 1
    },
    GBP: {
      EUR: undefined,
      USD: undefined,
      GBP: 1
    }
  };

  public currencyRatesAreValid(): boolean {
    return Object.values(this._currencyRates).every((currencies) =>
      Object.values(currencies).every((rate) => rate > 0)
    );
  }

  public async getLatestFXRates(): Promise<ExchangeRates> {
    await this._fetchAllCurrencyRates();

    if (this.currencyRatesAreValid()) {
      return this._currencyRates;
    }
  }

  public async getRealTimeAssetsData(assetsCommonId: investmentUniverseConfig.AssetType[]): Promise<
    {
      assetCommonId: investmentUniverseConfig.AssetType;
      close: number;
      timestamp: number;
    }[]
  > {
    addBreadcrumb({
      type: "default",
      category: "EodService.getRealTimeAssetsData",
      level: "info",
      data: {
        assetsCommonId
      }
    });

    const realTimeTickersJoined = assetsCommonId
      .map((assetId) => ASSET_CONFIG[assetId])
      .map((assetConfig) => `${assetConfig.formalTicker}.${assetConfig.formalExchange}`)
      .join();

    const url = `${EOD_BASE_URL}/api/real-time/${realTimeTickersJoined}`;

    const eodResponse: RealTimeDataType[] = await EodService._fetch(url, {
      fmt: "json"
    });

    const symbolToAssetIdMap: Record<string, investmentUniverseConfig.AssetType> = Object.fromEntries(
      assetsCommonId.map((assetId) => {
        const { formalTicker, formalExchange } = ASSET_CONFIG[assetId];
        return [`${formalTicker}.${formalExchange}`, assetId];
      })
    );

    const mappedTickers: {
      assetCommonId: investmentUniverseConfig.AssetType;
      close: number;
      timestamp: number;
    }[] = [];

    if (assetsCommonId.length !== eodResponse.length) {
      logger.warn("EOD response size does not match request size!", {
        module: "EodService",
        method: "getRealTimeAssetsData",
        data: {
          requested: assetsCommonId,
          received: eodResponse.map((ticker) => ticker.code)
        }
      });
    }

    eodResponse.forEach((realTimeTicker) => {
      if (!realTimeTicker) return;

      const { code, close, timestamp } = realTimeTicker;
      const assetId = symbolToAssetIdMap[code];

      try {
        mappedTickers.push({
          assetCommonId: assetId,
          close: CurrencyUtil.convertAmountToMainCurrency(ASSET_CONFIG[assetId].tradedCurrency, close),
          timestamp: Decimal.mul(timestamp, 1000).toNumber()
        });
      } catch (err) {
        logger.error(`Bad real-time EOD response for asset ${assetId}`, {
          module: "EodService",
          method: "getRealTimeAssetsData",
          data: {
            assetId,
            realTimeTicker
          }
        });
        captureException(err);
      }
    });

    return mappedTickers;
  }

  public async getStockSplits(
    exchange: investmentUniverseConfig.ExchangeType,
    date: Date
  ): Promise<StockSplitType[]> {
    const bulkEodUrl = EodService._getEndpoint({ exchange }).BULK;

    return EodService._fetch(bulkEodUrl, {
      fmt: "json",
      type: "splits",
      date: DateUtil.getYearAndMonthAndDay(date)
    });
  }

  public async getAssetFundamentalsData(
    assetCommonId: publicInvestmentUniverseConfig.PublicAssetType,
    options?: {
      useFormalTicker?: boolean;
    }
  ): Promise<EodFundamentalsResponseType> {
    const { formalTicker, formalExchange } = PUBLIC_ASSET_CONFIG[assetCommonId];
    const fundamentalsUrl = options?.useFormalTicker
      ? EodService._getEndpoint({
          ticker: formalTicker,
          exchange: formalExchange
        }).FUNDAMENTALS
      : EodService._getEndpoint({ assetId: assetCommonId }).FUNDAMENTALS;

    return EodService._fetch(fundamentalsUrl);
  }

  public async getAssetFundamentalsDataByFundamentalsTicker(
    fundamentalsTicker: string
  ): Promise<EodFundamentalsResponseType> {
    const fundamentalsUrl = EodService._getEndpoint({ fundamentalsTicker: fundamentalsTicker }).FUNDAMENTALS;

    return EodService._fetch(fundamentalsUrl);
  }

  /**
   * Returns historical data for an asset between two dates. For each day,
   * it returns its closing price. Daily, weekly and monthly intervals are available.
   *
   * @param assetCommonId The corresponding asset id of the investment product
   * @param options
   * - period – use ‘d’ for daily, ‘w’ for weekly, ‘m’ for monthly prices. By default, daily prices will be shown.
   * - from and to – the format is ‘YYYY-MM-DD’. If you need data from Jan 5, 2017, to Feb 10, 2017,
   * you should use from=2017-01-05 and to=2017-02-10. If any is missing or not formatted properly,
   * then it defaults to "the-beginning-of-time" and "today" respectively.
   *
   * @returns
   * - [{date: string, close: number},...] - "date" is formatted "YYYY-MM-DD".
   */
  public async getHistoricalPrices(
    assetCommonId: publicInvestmentUniverseConfig.PublicAssetType,
    options: {
      from?: string;
      to?: string;
      period?: "d" | "w" | "m";
    }
  ): Promise<{ date: string; close: number }[]> {
    const { from, to, period } = options;

    const historyFrom = /\d{4}-\d{1,2}-\d{1,2}/.test(from as string) ? from : undefined;
    const historyTo = /\d{4}-\d{1,2}-\d{1,2}/.test(to as string) ? to : undefined;
    const historyPeriod = /d|w|m/.test(period as string) ? period : "d";

    const eodUrl = EodService._getEndpoint({ assetId: assetCommonId }).EOD;
    const response: HistoricalDataType[] = await EodService._fetch(eodUrl, {
      period: historyPeriod,
      from: historyFrom,
      to: historyTo,
      fmt: "json"
    });

    return response
      .filter(({ adjusted_close, close }) => adjusted_close > 0 || close > 0)
      .map(({ date, close, adjusted_close }: { date: string; close: number; adjusted_close: number }) => ({
        date,
        close: CurrencyUtil.convertAmountToMainCurrency(
          PUBLIC_ASSET_CONFIG[assetCommonId].tradedCurrency,
          adjusted_close ?? close
        )
      }));
  }

  /**
   * Returns historical data for an asset between two dates. For each day,
   * it returns its volume. Daily, weekly and monthly intervals are available.
   *
   * @param assetCommonId The corresponding asset id of the investment product
   * @param options
   * - period – use ‘d’ for daily, ‘w’ for weekly, ‘m’ for monthly prices. By default, daily prices will be shown.
   * - from and to – the format is ‘YYYY-MM-DD’. If you need data from Jan 5, 2017, to Feb 10, 2017,
   * you should use from=2017-01-05 and to=2017-02-10. If any is missing or not formatted properly,
   * then it defaults to "the-beginning-of-time" and "today" respectively.
   *
   * @returns
   * - [{date: string, volume: number},...] - "date" is formatted "YYYY-MM-DD".
   */
  public async getHistoricalVolume(
    assetCommonId: publicInvestmentUniverseConfig.PublicAssetType,
    options: {
      from?: string;
      to?: string;
      period?: "d" | "w" | "m";
    }
  ): Promise<HistoricalVolumeDataType[]> {
    const { from, to, period } = options;

    const historyFrom = /\d{4}-\d{1,2}-\d{1,2}/.test(from as string) ? from : undefined;
    const historyTo = /\d{4}-\d{1,2}-\d{1,2}/.test(to as string) ? to : undefined;
    const historyPeriod = /d|w|m/.test(period as string) ? period : "d";

    const eodUrl = EodService._getEndpoint({ assetId: assetCommonId }).EOD;
    const response: HistoricalDataType[] = await EodService._fetch(eodUrl, {
      period: historyPeriod,
      from: historyFrom,
      to: historyTo,
      fmt: "json"
    });

    return response
      .filter(({ adjusted_close, close }) => adjusted_close > 0 || close > 0)
      .map(({ date, volume }: { date: string; volume: number }) => ({
        date,
        volume: CurrencyUtil.convertAmountToMainCurrency(PUBLIC_ASSET_CONFIG[assetCommonId].tradedCurrency, volume)
      }));
  }

  /**
   * @description Returns intra day pricing for the requested asset, starting from the requested date.
   * The 'from' date should be sent to EOD in UNIX timestamp in seconds.
   * The result timestamp from EOD is also in UNIX timestamp in seconds and is converted to milliseconds.
   *
   * @param assetCommonId
   * @param options
   * @returns
   */
  public async getIntradayPrices(
    assetCommonId: publicInvestmentUniverseConfig.PublicAssetType,
    options: {
      from: Date;
    }
  ): Promise<{ timestamp: number; close: number }[]> {
    const intradayUrl = EodService._getEndpoint({ assetId: assetCommonId }).INTRADAY;
    const response: IntraDayDataType[] = await EodService._fetch(intradayUrl, {
      interval: "5m",
      from: new Date(options.from).getTime() / 1000, // unit timestamp in seconds
      fmt: "json"
    });

    return response
      .filter(({ close }) => close > 0)
      .map(({ datetime, close }: { datetime: string; close: number }) => ({
        // datetime is in format "2024-05-14 20:00:00" which is SQL date
        // and we convert it to UNIX timestamp in msec
        timestamp: DateTime.fromSQL(datetime, { zone: "GMT" }).toJSDate().getTime(),
        // In the app we display the traded currency, this conversion is applied to convert GBX to GBP
        close: CurrencyUtil.convertAmountToMainCurrency(
          PUBLIC_ASSET_CONFIG[assetCommonId].tradedCurrency,
          assetCommonId === "equities_nvidia" && close > 800 ? Decimal.div(close, 10).toNumber() : close
        )
      }));
  }

  /**
   * Retrieves the sentiment response for the given asset for the last 30 days.
   * @param assetCommonId
   * @param from
   * @param to
   */
  public async getAssetSentiments(
    assetCommonId: publicInvestmentUniverseConfig.PublicAssetType,
    from?: Date,
    to?: Date
  ): Promise<AssetSentimentsType> {
    const sentimentsUrl = EodService._getEndpoint({ assetId: assetCommonId }).SENTIMENTS;
    const response: SentimentsDataType = await EodService._fetch(sentimentsUrl, {
      from: from
        ? DateUtil.getYearAndMonthAndDay(from)
        : DateUtil.getYearAndMonthAndDay(
            DateUtil.getDateOfDaysAgo(new Date(Date.now()), DAYS_TO_RETRIEVE_SENTIMENTS_FOR)
          ),
      to: to ? DateUtil.getYearAndMonthAndDay(to) : DateUtil.getYearAndMonthAndDay(new Date(Date.now())),
      fmt: "json"
    });

    return Object.values(response)?.[0];
  }

  private static _getEndpoint(options: {
    assetId?: publicInvestmentUniverseConfig.PublicAssetType;
    forexPair?: ForexPairType;
    fundamentalsTicker?: string;
    ticker?: string;
    exchange?: string;
  }): PartialRecord<"REAL_TIME" | "FUNDAMENTALS" | "EOD" | "INTRADAY" | "BULK" | "SENTIMENTS", string> {
    if (options.assetId) {
      const { formalTicker, formalExchange } = PUBLIC_ASSET_CONFIG[options.assetId];
      const fundamentalsTicker =
        (PUBLIC_ASSET_CONFIG[options.assetId] as investmentUniverseConfig.ETFAssetConfigType).fundamentalsTicker ??
        `${formalTicker}.${formalExchange}`;

      return {
        REAL_TIME: `${EOD_BASE_URL}/api/real-time/${formalTicker}.${formalExchange}`,
        FUNDAMENTALS: `${EOD_BASE_URL}/api/fundamentals/${fundamentalsTicker}`,
        EOD: `${EOD_BASE_URL}/api/eod/${formalTicker}.${formalExchange}`,
        INTRADAY: `${EOD_BASE_URL}/api/intraday/${formalTicker}.${formalExchange}`,
        SENTIMENTS: `${EOD_BASE_URL}/api/sentiments?s=${formalTicker}.${formalExchange}`
      };
    } else if (options.exchange && options.ticker) {
      return {
        EOD: `${EOD_BASE_URL}/api/eod/${options.ticker}.${options.exchange}`,
        REAL_TIME: `${EOD_BASE_URL}/api/real-time/${options.ticker}.${options.exchange}`,
        FUNDAMENTALS: `${EOD_BASE_URL}/api/fundamentals/${options.ticker}.${options.exchange}`
      };
    } else if (options.exchange) {
      return {
        BULK: `${EOD_BASE_URL}/api/eod-bulk-last-day/${options.exchange}`
      };
    } else if (options.forexPair) {
      return {
        REAL_TIME: `${EOD_BASE_URL}/api/real-time/${options.forexPair}.FOREX`,
        EOD: `${EOD_BASE_URL}/api/eod/${options.forexPair}.FOREX`,
        INTRADAY: `${EOD_BASE_URL}/api/intraday/${options.forexPair}.FOREX`
      };
    } else {
      return {
        FUNDAMENTALS: `${EOD_BASE_URL}/api/fundamentals/${options.fundamentalsTicker}`
      };
    }
  }

  private async _fetchAllCurrencyRates(): Promise<void> {
    for (const from of Object.keys(this._currencyRates)) {
      for (const to of Object.keys(this._currencyRates[from as currenciesConfig.MainCurrencyType])) {
        if (from !== to) {
          try {
            await this._updateCurrencyRate(
              from as currenciesConfig.MainCurrencyType,
              to as currenciesConfig.MainCurrencyType
            );
          } catch (err) {
            logger.error(`Retrieval of EOD forex rate failed for ${from}${to} pair`, {
              module: "EodService",
              method: "_fetchAllCurrencyRates"
            });
            captureException(err);
          }
        }
      }
    }
  }

  private async _updateCurrencyRate(
    from: currenciesConfig.MainCurrencyType,
    to: currenciesConfig.MainCurrencyType
  ): Promise<void> {
    const realTimeUrl = EodService._getEndpoint({ forexPair: `${from}${to}` }).REAL_TIME;
    const response = await EodService._fetch(realTimeUrl, {
      period: "d",
      fmt: "json"
    });

    if (response?.close > 0) {
      this._currencyRates[from][to] = new Decimal(response.close).toDecimalPlaces(2).toNumber();
    }
  }

  private static async _fetch(url: string, params: any = {}): Promise<any> {
    logger.info("making http request", {
      module: "EodService",
      method: "_fetch",
      data: {
        url,
        params
      }
    });

    try {
      const response = await axios.get(url, {
        params: {
          ...params,
          api_token: process.env.EOD_DATA_TOKEN
        }
      });
      return response.data;
    } catch (err) {
      logger.error("http request failed", {
        module: "EodService",
        method: "_fetch",
        data: {
          url,
          params,
          error: err
        }
      });
      addBreadcrumb({
        type: "http",
        category: "http",
        level: "error",
        data: {
          url,
          // eslint-disable-next-line camelcase
          status_code: err.response && err.response.status,
          reason: err.response && JSON.stringify(err.response.data, null, 4)
        }
      });
      if (err?.response?.status >= 500) {
        throw new InternalServerError("Something went wrong with communication with EOD");
      } else if (err?.response?.status == 404) {
        throw new NotFoundError("Not found, EOD returned 404");
      } else {
        throw new BadRequestError(err?.response?.data?.error);
      }
    }
  }
}

const eodService = new EodService();

export default eodService;
