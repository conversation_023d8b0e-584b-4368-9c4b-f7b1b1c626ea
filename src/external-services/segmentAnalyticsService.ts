import { Analytics } from "@segment/analytics-node";
import { TrackingSourceType } from "../models/Participant";
import { KycStatusEnum, UserDocument } from "../models/User";
import { ReferredStatus } from "referral";
import { GiftedStatus } from "gift";
import logger from "./loggerService";
import {
  countriesConfig,
  currenciesConfig,
  entitiesConfig,
  investmentUniverseConfig,
  plansConfig
} from "@wealthyhood/shared-configs";
import { AllocationCreationFlowEnum } from "../models/Portfolio";
import { ChargeMethodType } from "../models/Transaction";
import { EmploymentStatusType, OrderSideType, SourceOfWealthType } from "./wealthkernelService";
import { RiskScoreClassificationEnum, SourceOfFundsEnum } from "../models/RiskAssessment";
import { UserDataRequestReasonEnum } from "../models/UserDataRequest";
import { AmlScreeningResultEnum } from "../configs/riskAssessmentConfig";
import { ReviewAnswerType } from "./sumsubService";

const INTEGRATIONS_MAPPING: Record<keyof IntegrationsType, string> = {
  All: "All",
  Intercom: "Intercom Cloud Mode (Actions)",
  MailChimp: "MailChimp",
  Slack: "Slack",
  // This is the Slack Actions integration that is currently being used
  // for failed verifications only. As a next step we'll migrate all events
  // to this one.
  SlackActions: "Slack (Actions)",
  Mixpanel: "Mixpanel",
  Facebook: "Facebook Conversions API (Actions)"
};

export enum MixpanelAccountStatusEnum {
  Closed = "Closed",
  Closing = "Closing",
  WkSuspendedClosed = "WKSuspenedClosed",
  Active = "Active",
  VerificationFailed = "VerificationFailed",
  Pending = "Pending"
}

type IntegrationsType = {
  All?: boolean;
  Intercom?: boolean;
  MailChimp?: boolean;
  Slack?: boolean;
  SlackActions?: boolean;
  Mixpanel?: boolean;
  Facebook?: boolean;
};
export type UserTraitType = {
  appDownld?: Date;
  appsflyerId?: string;
  cash?: number;
  currency?: currenciesConfig.MainCurrencyType;
  companyEntity?: entitiesConfig.CompanyEntityEnum;
  companyEnt?: entitiesConfig.CompanyEntityEnum; // companyEntity Mailchimp-shortened version
  savingsGBP?: number;
  savingsEUR?: number;
  dateOfBirth?: Date;
  deletionFeedback?: string;
  email?: string;
  emailVerif?: "True" | "";
  financeAdsId?: string;
  firstName?: string;
  kyc?: KycStatusEnum;
  lastName?: string;
  referred?: ReferredStatus;
  referredBy?: string;
  pageUserLanded?: string;
  pageLanded?: string; // pageUserLanded Mailchimp-shortened version
  gifted?: GiftedStatus;
  giftedBy?: string;
  status?: string;
  accountStatus?: MixpanelAccountStatusEnum;
  UKResident?: string;
  bankLinked?: string;
  hasInvstd?: "Created" | "Succeeded";
  mobileApp?: "android" | "ios";
  portfolioValue?: number;
  signedupAt?: Date;
  source?: TrackingSourceType;
  sourceCampaign?: string;
  repInvestm?: "True" | "False";
  repSavings?: "True" | "False";
  residencyCountry?: string;
  residencyC?: string; // Residency country Mailchimp-shortened version
  autRebalan?: "True" | "False";
  banks?: string[];
  plan?: plansConfig.PlanType;
  planRecurrence?: plansConfig.PriceRecurrenceType;
  return?: number;
  mover1?: string;
  mover1ret?: number;
  mover2?: string;
  mover2ret?: number;
  mover3?: string;
  mover3ret?: number;
  nation?: countriesConfig.CountryCodesType;
  reportStat?: string;
  CRAScore?: number;
  CRAClassification?: RiskScoreClassificationEnum;
  wealthybit?: boolean; // field for wealthybites subscription
  promo?: boolean; // field for promotional email subscription
  submissionTechClickId?: string;
  waiting?: boolean;
};

export type TrackPropertiesType =
  | {
      appsflyerId?: string;
      wlthdId?: string;
      grsfId?: string;
      pageUserLanded?: string;
      sId?: string;
      referredBy?: string;
      giftedBy?: string;
      deletionFeedback?: string;
      platform?: "ios" | "android" | "web";
      decision?: ReviewAnswerType;
    }
  | TrackTransactionInfoType
  | TrackPortfolioPropertiesType
  | TrackUserDisassociationPropertiesType
  | TrackGiftPropertiesType
  | TrackRewardInvitationPropertiesType
  | TrackBankAccountPropertiesType
  | TrackPlanType
  | TrackNotificationOrderSettled
  | TrackNotificationGiftReceived
  | TrackSubscriptionChargeSuccess
  | TrackSavingsDividendChargeSuccess
  | TrackCustodyChargeSuccess
  | TrackWealthyhoodDividendPropertiesType
  | TrackRiskAssessmentType
  | TrackAssetDividendSuccessPropertiesType
  | TrackAppRatingPropertiesType;

export type TrackAppRatingPropertiesType = {
  starRating: number;
  feedback?: string;
};

export type TrackDepositPropertiesType = {
  amount: number;
  currency: currenciesConfig.MainCurrencyType;
  noAssetTransactionPending: boolean;
};

export type TrackOrderPropertiesType = {
  amount: number;
  currency: currenciesConfig.MainCurrencyType;
  quantity: number;
  asset: investmentUniverseConfig.AssetType;
  side: OrderSideType;
  rejectionReason?: string;
};

export type TrackPortfolioPropertiesType = {
  currency?: currenciesConfig.MainCurrencyType;
  allocationCreationFlow?: AllocationCreationFlowEnum;
  cash?: number;
  savingsGBP?: number;
  savingsEUR?: number;
  portfolioValue?: number;
  hasRepeatingTopup?: boolean;
  hasRepeatingSavingsTopup?: boolean;
  repeatingTopupAmount?: number;
  repeatingSavingsTopupAmount?: number;
  hasRepeatingRebalance?: boolean;
};

export type TrackUserDisassociationPropertiesType = {
  reason: UserDataRequestReasonEnum;
};

export type TrackWithdrawalPropertiesType = {
  amount: number;
  currency: currenciesConfig.MainCurrencyType;
};

export type TrackWealthyhoodDividendPropertiesType = {
  amount: number;
  currency: currenciesConfig.MainCurrencyType;
  plan: plansConfig.PlanType;
};

export type TrackAssetDividendSuccessPropertiesType = {
  quantity: number;
  asset: investmentUniverseConfig.AssetType;
};

export type TrackGiftPropertiesType = {
  targetUserEmail: string;
  amount: number;
  currency: currenciesConfig.MainCurrencyType;
};

export type TrackRewardInvitationPropertiesType = {
  invitedEmail: string;
};

export type TrackBankAccountPropertiesType = {
  truelayerId: string;
};

export type TrackStockSplitPropertiesType = {
  assetName: string;
  date: Date;
};

export type TrackInvestmentProductStaleTickerPropertiesType = {
  assetName: string;
  lastUpdateDate: Date;
}[];

export type TrackCreditTicketReportedPropertiesType = {
  totalCreditedAmount: number;
  oldOpenTickets: number;
  depositVsCreditedMatch: boolean;
  totalRecentDeposits: number;
  totalRecentCredited: number;
};

export type TrackAssetIsinChangePropertiesType = {
  assetName: string;
  oldISIN: string;
  newISIN: string;
  date: Date;
};

export type TrackSavingsProductDataUpdatePropertiesType = {
  savingsProductLabel: string;
  lastUpdateDate: Date;
  dailyDistributionFactor: number;
  oneDayYield: number;
};

export type TrackTransactionInfoCategoryType = "etf" | "portfolio" | "stock" | "reward" | "savings";

export type TrackTransactionInfoType = {
  isFirst?: boolean; // Applies only to Stocks and ETFs
  side?: "buy" | "sell";
  category?: TrackTransactionInfoCategoryType;
  assetName?: string; // if category is ETF, stock or savings, then we also add the asset name
  frequency?: "one-off" | "repeating";
  amount?: number;
  cashbackAmount?: number;
  fxFees?: number;
  commissionFees?: number;
  executionSpreadFees?: number;
  redeemedGift?: boolean;
  currency?: currenciesConfig.MainCurrencyType;

  // Display properties for Slack channels
  displayCurrencySymbol?: string;
};

export type TrackRiskAssessmentType = {
  nationality: countriesConfig.CountryCodesType;
  nationalityRiskScore: number;
  sourcesOfFunds: SourceOfFundsEnum[];
  sourcesOfFundsRiskScore: number;
  employmentStatus: EmploymentStatusType;
  employmentStatusRiskScore: number;
  volumeOfTransactions: number;
  volumeOfTransactionsRiskScore: number;
  amlScreening: AmlScreeningResultEnum;
  amlScreeningRiskScore: number;
  sourcesOfWealth: SourceOfWealthType[];
  sourcesOfWealthRiskScore: number;
  totalRiskScore: number;
  classification: RiskScoreClassificationEnum;
};

export type TrackNotificationOrderSettled = {
  side: "buy" | "sell";
  asset: "your portfolio" | string;
  /**
   * Amount can be "£X.XX" or "XXX shares" depending on the asset and "buy" or "sell" side
   */
  amount: string;
};

export type TrackNotificationWealthyhoodDividendSuccess = {
  /**
   * Amount is string because it includes currency i.e. "£X.XX"
   */
  amount: string;
  plan: string; // string and not PlanType because the first letter of plan is capitalized when emitting
};

export type TrackNotificationDividendSuccess = {
  /**
   * Amount is string because it includes currency i.e. "£X.XX"
   */
  amount: string;
  /**
   * This `etf_name` is outdated, it could refer to Stocks, ETFs, or Savings Products.
   */
  etf_name: string;
};

export type TrackNotificationSavingsDividendCreation = {
  /**
   * Amount is string because it includes currency i.e. "£X.XX"
   */
  amount: string;
};

export type TrackNotificationGiftReceived = {
  gifted_by: string;
};

export type TrackPlanType = {
  from: plansConfig.PlanType | "";
  fromRecurrence: plansConfig.PriceRecurrenceType | "";
  to: plansConfig.PlanType;
  toRecurrence: plansConfig.PriceRecurrenceType;
  category: "upgrade" | "downgrade";

  // Display properties for Slack channels
  displayFrom?: string;
  displayTo?: string;
};

export type TrackSubscriptionChargeSuccess = {
  nominalCharge: number;
  actualCharge: number;
  currency: currenciesConfig.MainCurrencyType;
  settledAt: Date; // YYYY-MM-DD
  chargeMethod: Omit<ChargeMethodType, "orders">;
  plan: plansConfig.PlanType;
  recurrence: plansConfig.PriceRecurrenceType;
};

export type TrackSavingsDividendChargeSuccess = {
  chargedAmount: number;
  originalDividendAmount: number;
  currency: currenciesConfig.MainCurrencyType;
};

export type TrackCustodyChargeSuccess = {
  amount: number;
  currency: currenciesConfig.MainCurrencyType;
};

class SegmentAnalyticsService {
  private _analytics: Analytics;

  constructor() {
    this._analytics = new Analytics({ writeKey: process.env.SEGMENT_WRITE_KEY, maxEventsInBatch: 1 });

    // Log all requests to Segment
    this._analytics.on("http_request", (event) => {
      logger.info("HTTP request for segment", {
        module: "SegmentAnalyticsService",
        data: { event }
      });
    });
  }

  public alias({ previousId, userId }: { previousId: string; userId: string }): void {
    this._analytics.alias({ previousId, userId, integrations: { All: false, Mixpanel: true } });
  }

  public delay(duration: number): Promise<any> {
    return new Promise((resolve) => setTimeout(resolve, duration));
  }

  public identify(
    user: UserDocument,
    traits: UserTraitType,
    integrations: IntegrationsType = { All: true }
  ): void {
    const userId = user._id.toString();
    this._analytics.identify({
      userId,
      traits: {
        // we're also adding userId in the traits so that Mailchimp adds it as a field in the user
        userId,
        // we're always sending the email because it's required by mailchimp
        email: user.email,
        ...traits
      },
      integrations: SegmentAnalyticsService._mapIntegrations(integrations)
    });
  }

  public rawIdentify(data: any = {}, integrations: IntegrationsType = { All: true }): void {
    this._analytics.identify({
      ...data,
      integrations: SegmentAnalyticsService._mapIntegrations(integrations)
    });
  }

  public track(
    user: UserDocument,
    event: string,
    integrations: IntegrationsType = {},
    properties: TrackPropertiesType = {}
  ): void {
    // ignore track for mailchimp
    const userId = user._id.toString();
    const email = user.email;
    this._analytics.track({
      userId,
      event,
      properties: {
        email,
        appsflyerId: user?.participant?.appsflyerId,
        lastLoginPlatform: user?.lastLoginPlatform,
        platform: user?.participant?.appInstallInfo?.platform,
        ...properties
      },
      // Mailchimp only access identify events
      integrations: {
        Intercom: false,
        ...SegmentAnalyticsService._mapIntegrations(integrations),
        MailChimp: false
      }
    });
  }

  public rawTrack(data: any = {}, integrations: IntegrationsType = {}): void {
    // ignore track for mailchimp
    this._analytics.track({
      ...data,
      // Mailchimp only access identify events
      integrations: { ...SegmentAnalyticsService._mapIntegrations(integrations), MailChimp: false }
    });
  }

  private static _mapIntegrations(integrations: IntegrationsType): Record<string, boolean> {
    return Object.fromEntries(
      Object.entries(integrations).map(([integration, enabled]: [keyof IntegrationsType, boolean]) => [
        INTEGRATIONS_MAPPING[integration],
        enabled
      ])
    );
  }
}

const analytics = new SegmentAnalyticsService();
export default analytics;
