import { Method } from "axios";
import HttpUtil from "../utils/httpUtil";
import DateUtil from "../utils/dateUtil";
import crypto from "crypto";
import { BadRequestError, ForbiddenError } from "../models/ApiErrors";
import { hashSHA256utf8 } from "../utils/cryptoUtil";

const SUMSUB_API_URL = "https://api.sumsub.com";

/**
 * ENUMS
 */
export enum SumsubLevelEnum {
  BASIC_KYC = "Wealthyhood Basic KYC",
  BASIC_KYC_EU = "Wealthyhood Basic KYC (EU)"
}

/**
 * TYPES
 */
export const EventTypeArray = ["applicantCreated", "applicantReviewed", "applicantPersonalInfoChanged"] as const;
type EventTypeType = (typeof EventTypeArray)[number];

export type EventType = {
  applicantId: string;
  externalUserId: string;
  type: EventTypeType;
};

export type AccessTokenType = {
  token: string;
};

export const ReviewStatusArray = ["init", "pending", "prechecked", "queued", "completed", "onHold"] as const;
export type ReviewStatusType = (typeof ReviewStatusArray)[number];

export const ReviewAnswerArray = ["GREEN", "RED"] as const;
export type ReviewAnswerType = (typeof ReviewAnswerArray)[number];

type ReviewType = {
  reviewId: string;
  reviewStatus: ReviewStatusType;
  reviewResult?: ReviewResultType; // Present only if review status is completed
};

type ReviewResultType = {
  reviewAnswer: ReviewAnswerType;
};

type ApplicantInfoType = {
  firstName: string;
  lastName: string;
  dob: string; // YYYY-mm-dd
  nationality: string; // alpha-3 country code e.g.GBR
  idDocs: IdDocType[];
};

type IdDocType = {
  idDocType: string; // Can be 'PASSPORT', 'ID_CARD', etc.
  mrzLine1?: string;
};

export type ApplicantType = {
  id: string;
  info: ApplicantInfoType;
  review: ReviewType;
};

export class SumsubService {
  private static _instance: SumsubService;
  private static _clientToken = process.env.SUMSUB_TOKEN;
  private static _clientSecretKey = process.env.SUMSUB_SECRET_KEY;
  private static _webhookKey = process.env.SUMSUB_WEBHOOK_KEY;

  private constructor() {
    SumsubService._verifyCredentialsExist();
  }

  // ===============
  // PUBLIC METHODS
  // ===============
  public static get Instance(): SumsubService {
    return this._instance || (this._instance = new this());
  }

  /**
   * Validates if wealthkernel signature is valid. If not it throws a ForbiddenError.
   *
   * @param signature
   * @param body
   */
  public static validateWebhookSignature(signature: string, body: string) {
    if (!signature) {
      throw new BadRequestError("No signature found", "Invalid header");
    }

    const contentHmac = hashSHA256utf8(body, SumsubService._webhookKey);

    if (signature != contentHmac) {
      throw new ForbiddenError("Received webhook from Sumsub with invalid signature!");
    }
  }

  public async generateAccessToken(
    externalUserId: string,
    levelName: SumsubLevelEnum = SumsubLevelEnum.BASIC_KYC
  ): Promise<AccessTokenType> {
    const res = await this._fetch({
      method: "POST",
      url: `/resources/accessTokens?userId=${externalUserId}&levelName=${HttpUtil.extendedEncodeURIComponent(levelName)}`
    });

    return res as AccessTokenType;
  }

  public async retrieveApplicant(externalUserId: string): Promise<ApplicantType> {
    const res = await this._fetch({
      method: "GET",
      url: `/resources/applicants/-;externalUserId=${externalUserId}/one`
    });

    return res as ApplicantType;
  }

  public async runAMLCheck(applicantId: string): Promise<void> {
    await this._fetch({
      method: "POST",
      url: `/resources/applicants/${applicantId}/recheck/aml`
    });
  }

  // ===============
  // PRIVATE METHODS
  // ===============

  private static _verifyCredentialsExist() {
    if (!SumsubService._clientSecretKey) {
      throw new Error("SUMSUB_SECRET_KEY env variable is not set");
    } else if (!SumsubService._clientToken) {
      throw new Error("SUMSUB_TOKEN env variable is not set");
    } else if (!SumsubService._webhookKey) {
      throw new Error("SUMSUB_WEBHOOK_KEY env variable is not set");
    }
  }

  /**
   * @description This is the method for making any requests to access the Sumsub API.
   * @param config
   */
  private async _fetch(config: { method: Method; url: string; data?: any; headers?: any }): Promise<any> {
    const secondsSinceEpoch = DateUtil.getSecondsSinceEpoch();

    // All requests made to Sumsub need to include a signed request in the headers.
    const signature = SumsubService._getRequestSignature(
      secondsSinceEpoch,
      config.method,
      config.url,
      config.data
    );

    const headers = {
      "X-App-Token": SumsubService._clientToken,
      "X-App-Access-Ts": secondsSinceEpoch,
      "X-App-Access-Sig": signature,
      ...config.headers
    };

    return await HttpUtil.fetch(
      { ...config, url: `${SUMSUB_API_URL}${config.url}`, headers },
      {
        throwError: true,
        addSentryBreadcrumb: true,
        captureSentryException: true,
        logOptions: {
          active: true,
          module: "SumsubService",
          method: "_fetch"
        }
      }
    );
  }

  private static _getRequestSignature(secondsSinceEpoch: number, method: Method, url: string, data?: any): string {
    const signature = crypto.createHmac("sha256", SumsubService._clientSecretKey);

    signature.update(secondsSinceEpoch + method.toUpperCase() + url);

    if (data) {
      signature.update(data);
    }

    return signature.digest("hex");
  }
}
