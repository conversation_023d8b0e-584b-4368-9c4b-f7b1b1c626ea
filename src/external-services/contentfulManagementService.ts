import { ContentfulAccountEnum } from "../configs/contentfulConfig";
import { createClient, PlainClientAPI } from "contentful-management";
import { ContentEntryCategoryEnum, ContentEntryContentTypeEnum } from "../models/ContentEntry";

export type RequestOptions = {
  limit: number;
};

type Localized<T> = {
  [P in keyof T]: {
    [locale: string]: T[P];
  };
};

enum ContentTypeEnum {
  CONTENT_ENTRY = "contentEntry"
}

export type ContentEntryContentfulType = {
  slug: string;
  title: string;
  publishedAt: string;
  subtitle: string;
  readingTime: string;
  summary: string;
  headerImage: string;
  bannerImage: string;
  content: string;
  category: ContentEntryCategoryEnum;
  contentType: ContentEntryContentTypeEnum;
  notificationTitle?: string;
  notificationBody?: string;
};

class ContentfulManagementService {
  private _client: PlainClientAPI;
  private static _instances: Record<ContentfulAccountEnum, ContentfulManagementService> = {
    [ContentfulAccountEnum.LANDING_PAGE]: null,
    [ContentfulAccountEnum.LEARN_HUB]: null
  };

  constructor(options: { account: ContentfulAccountEnum }) {
    const space = process.env[`CONTENTFUL_SPACE_${options?.account}`];
    const environment = process.env[`CONTENTFUL_ENVIRONMENT_${options?.account}`];
    const accessToken = process.env[`CONTENTFUL_MANAGEMENT_ACCESS_TOKEN_${options?.account}`] as string;

    this._client = createClient(
      {
        accessToken
      },
      {
        type: "plain",
        defaults: {
          spaceId: space,
          environmentId: environment
        }
      }
    );
  }

  // ====================
  // Statics - Utils
  // ====================
  public static get LandingPageInstance(): ContentfulManagementService {
    return (
      this._instances[ContentfulAccountEnum.LANDING_PAGE] ||
      (this._instances[ContentfulAccountEnum.LANDING_PAGE] = new this({
        account: ContentfulAccountEnum.LANDING_PAGE
      }))
    );
  }

  public static get LearnHubInstance(): ContentfulManagementService {
    return (
      this._instances[ContentfulAccountEnum.LEARN_HUB] ||
      (this._instances[ContentfulAccountEnum.LEARN_HUB] = new this({ account: ContentfulAccountEnum.LEARN_HUB }))
    );
  }

  // ====================
  // Public methods
  // ====================
  public async createContentEntry(
    data: ContentEntryContentfulType,
    options: { publishAfterCreation: boolean }
  ): Promise<any> {
    const entry = await this._client.entry.create(
      { contentTypeId: ContentTypeEnum.CONTENT_ENTRY },
      {
        fields: this._localiseFields(data)
      }
    );

    if (options.publishAfterCreation && entry.sys?.id) {
      await this._client.entry.publish(
        {
          entryId: entry.sys.id
        },
        entry
      );
    }

    return { id: entry.sys.id, spaceId: entry.sys.space.sys.id, environmentId: entry.sys.environment.sys.id };
  }

  // ====================
  // Private methods
  // ====================
  private _localiseFields<T>(fields: T): Localized<T> {
    return Object.fromEntries(
      Object.entries(fields).map(([key, value]) => {
        return [
          key,
          {
            "en-US": value
          }
        ];
      })
    ) as unknown as Localized<T>;
  }
}

export default ContentfulManagementService;
