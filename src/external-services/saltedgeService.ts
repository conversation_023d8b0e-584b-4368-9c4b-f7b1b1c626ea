import { Method } from "axios";
import HttpUtil from "../utils/httpUtil";
import { faker } from "@faker-js/faker";
import { CustomRequest } from "custom";
import { ForbiddenError } from "../models/ApiErrors";
import { banksConfig, countriesConfig } from "@wealthyhood/shared-configs";
import BanksUtil from "../utils/banksUtil";
import * as crypto from "crypto";
import { PartialRecord } from "utils";

/**
 * DATA TYPES
 */
type LeadDataType = {
  fullName: string;
  email: string;
  identifier: string;
};

type PaymentSessionDataType = {
  customerId: string;
  returnTo: string;
  fullName: string;
  amount: string;
  reference: string;
};

type LeadType = {
  data: {
    customer_id: string;
  };
};

type PaymentType = {
  data: {
    id: string;
    status: PaymentStatusType;
    provider_code: string;
    payment_attributes: {
      end_to_end_id: string; // The custom ID we set for the payment
      debtor_iban?: string;
    };
  };
};

type PaymentSessionType = {
  data: {
    connect_url: string;
  };
};

export type SaltedgeEventPayloadType = {
  data: {
    payment_id: string;
  };
};

const REQUIRED_FIELDS: PartialRecord<banksConfig.BankType, string[]> = {
  alphabank: ["creditor_agent"]
};

export const PaymentStatusArray = ["processing", "accepted", "rejected", "failed", "unknown", "deleted"] as const;
export type PaymentStatusType = (typeof PaymentStatusArray)[number];

export class SaltedgeService {
  private static _url = process.env.SALTEDGE_URL;
  private static _appId = process.env.SALTEDGE_APP_ID;
  private static _secret = process.env.SALTEDGE_SECRET;

  private static _instance: SaltedgeService;

  private constructor() {
    SaltedgeService._verifyCredentialsExist();
  }

  // ===============
  // PUBLIC METHODS
  // ===============
  public static get Instance(): SaltedgeService {
    return this._instance || (this._instance = new this());
  }

  public static validateWebhookSignature(req: CustomRequest) {
    const signature = req.headers["signature"] as string;
    const publicKey = Buffer.from(process.env.SALTEDGE_WEBHOOK_SECRET_BASE64, "base64").toString();

    const fullUrl = req.protocol + "://" + req.get("host") + req.originalUrl;
    const signatureData = `${fullUrl}|${JSON.stringify(req.body)}`;

    const verifier = crypto.createVerify("sha256");

    verifier.update(signatureData);

    const isVerified = verifier.verify(publicKey, signature, "base64");

    if (!isVerified) {
      throw new ForbiddenError("Received webhook from Saltedge with invalid signature!");
    }
  }

  public async createLead(leadData: LeadDataType): Promise<LeadType> {
    return this._fetch({
      method: "POST",
      url: `${SaltedgeService._url}/leads`,
      data: {
        data: {
          ...leadData,
          kyc: {
            full_name: leadData.fullName,
            type_of_account: "own"
          }
        }
      }
    });
  }

  public async createPaymentSession(
    paymentSessionData: PaymentSessionDataType,
    bankId: banksConfig.BankType,
    customId: string
  ): Promise<PaymentSessionType> {
    return this._fetch({
      method: "POST",
      url: `${SaltedgeService._url}/payments/sessions`,
      data: {
        data: {
          customer_id: paymentSessionData.customerId,
          return_to: paymentSessionData.returnTo,
          provider_code: banksConfig.BANKS_CONFIG[bankId].saltedgeInstitutionId,
          skip_provider_select: true,
          show_consent_confirmation: false,
          disable_provider_search: true,
          return_payment_id: true,
          return_error_class: true,
          template_identifier: banksConfig.BANKS_CONFIG[bankId].defaultPaymentScheme ?? "SEPA",
          locale: "en",
          kyc: {
            type_of_account: "own"
          },
          payment_attributes: {
            creditor_name: banksConfig.EU_BANK_DETAILS.accountName,
            creditor_agent: REQUIRED_FIELDS[bankId]?.includes("creditor_agent") && "Devengo S.L.",
            creditor_address: BanksUtil.getEuropeanBankFullAddress(),
            creditor_street_name: banksConfig.EU_BANK_DETAILS.bank.address.street,
            creditor_building_number: banksConfig.EU_BANK_DETAILS.bank.address.number,
            creditor_post_code: banksConfig.EU_BANK_DETAILS.bank.address.postCode,
            creditor_town: banksConfig.EU_BANK_DETAILS.bank.address.city,
            creditor_country_code: countriesConfig.countries.find(
              ({ name }) => name === banksConfig.EU_BANK_DETAILS.bank.address.country
            ).code,
            creditor_iban: "***************************", // For testing purposes ONLY.
            customer_ip_address: faker.internet.ip(),
            debtor_name: paymentSessionData.fullName,
            debtor_phone: "69" + faker.string.numeric({ allowLeadingZeros: false, length: { min: 8, max: 8 } }),
            end_to_end_id: customId,
            currency_code: "EUR",
            amount: paymentSessionData.amount,
            description: "Wealthyhood Deposit",
            reference: paymentSessionData.reference
          }
        }
      }
    });
  }

  public async getPayment(paymentId: string): Promise<PaymentType> {
    return this._fetch({
      method: "GET",
      url: `${SaltedgeService._url}/payments/${paymentId}`
    });
  }

  // ===============
  // PRIVATE METHODS
  // ===============

  private static _verifyCredentialsExist(): void {
    if (!SaltedgeService._appId) {
      throw new Error("SALTEDGE_APP_ID env variable has not been set");
    } else if (!SaltedgeService._secret) {
      throw new Error("SALTEDGE_SECRET env variable has not been set");
    } else if (!SaltedgeService._url) {
      throw new Error("SALTEDGE_URL env variable has not been set");
    }
  }

  /**
   * @description This is the method for making any requests to access the GoCardless API.
   * @param config
   */
  private async _fetch(config: { method: Method; url: string; data?: any; headers?: any }): Promise<any> {
    const headers = {
      "App-id": SaltedgeService._appId,
      Secret: SaltedgeService._secret,
      ...config.headers
    };

    return await HttpUtil.fetch(
      { ...config, headers },
      {
        throwError: true,
        addSentryBreadcrumb: true,
        captureSentryException: true,
        logOptions: {
          active: true,
          module: "SaltedgeService",
          method: "_fetch"
        }
      }
    );
  }
}
