/**
 * Accounting Ledger Storage Service
 *
 * This service implements the "Accounting Ledger Storage" layer.
 * It handles all CRUD operations for accounting ledger entries and enforces double-entry
 * accounting entries. It uses the generic TursoService for database operations.
 *
 * Ledger Entry Fields:
 * - aa: number - Accounting record index from AccountingRecordIndex
 * - account_code: string - Chart of accounts code (e.g., "30-00-00-0000")
 * - side: "debit" | "credit" - The accounting side of the entry
 * - amount: number - The monetary amount for this entry
 * - reference_number?: string - Optional reference number for tracking
 * - article_date: string - Date when the entry is posted (YYYY-MM-DD format)
 * - description: string - Entry description
 *   (format: "user_id | transaction_id | event_type" or "user_id | transaction_id | isin | event_type")
 *
 * Additional fields returned from queries:
 * - id?: number - Auto-generated database ID
 * - created_at?: string - Timestamp when the entry was created
 *
 * Cash Balance Snapshot Fields:
 * - account_code: string - Chart of accounts code
 * - balance: number - The cash balance amount for this account
 * - as_of_date: string - Date of the snapshot (YYYY-MM-DD format)
 * - created_at?: string - Timestamp when the snapshot was created
 *
 * Usage Examples:
 *
 * // Adding a single ledger entry
 * const result = await accountingLedgerStorageService.addLedgerEntry({
 *   aa: 1001,
 *   account_code: "30-00-00-0000",
 *   side: "debit",
 *   amount: 100.00,
 *   article_date: "2024-01-15",
 *   description: "user123 | txn456 | deposit",
 * });
 *
 * // Adding validated double-entry transactions
 * const entries = [
 *   {
 *     aa: 1001,
 *     account_code: "30-00-00-0000",
 *     side: "credit",
 *     amount: 100.00,
 *     article_date: "2024-01-15",
 *     description: "user123 | txn456 | deposit",
 *   },
 *   {
 *     aa: 1001,
 *     account_code: "38-03-00-0001",
 *     side: "debit",
 *     amount: 100.00,
 *     article_date: "2024-01-15",
 *     description: "user123 | txn456 | deposit",
 *   }
 * ];
 *
 * const result = await accountingLedgerStorageService.addValidatedLedgerEntries(entries);
 *
 * // Querying ledger entries
 * const entries = await accountingLedgerStorageService.queryLedgerEntries({
 *   user_id: "user123",
 *   article_date_from: "2024-01-01",
 *   article_date_to: "2024-01-31",
 *   limit: 100
 * });
 *
 * // Getting account balance
 * const balance = await accountingLedgerStorageService.getAccountBalance("30-00-00-0000");
 *
 * // Adding cash balance snapshots
 * const snapshots = [
 *   {
 *     accountCode: "38-03-02-0000",
 *     balance: 1000.50,
 *     asOfDate: "2024-01-15"
 *   }
 * ];
 * const result = await accountingLedgerStorageService.addCashBalanceSnapshots(snapshots);
 *
 * // Querying cash balance snapshots
 * const snapshots = await accountingLedgerStorageService.queryCashBalances({
 *   accountCodes: ["38-03-02-0000"],
 *   fromDate: "2024-01-01",
 *   toDate: "2024-01-31"
 * });
 */

import TursoService, { TursoInsertResult } from "../loaders/turso";
import { LedgerAccounts } from "../types/accounting";
import { delay } from "../utils/scriptUtil";

export type AccountingLedgerEntry = {
  aa: number; // Accounting record index from AccountingRecordIndex
  account_code: string;
  side: "debit" | "credit";
  amount: number;
  reference_number?: string;
  article_date: string;
  description: string; // Format: "user_id | transaction_id | event_type"
};

export type LedgerQueryResult = AccountingLedgerEntry & {
  id?: number;
  created_at?: string;
};

export type CashBalanceSnapshot = {
  accountCode: LedgerAccounts;
  balance: number;
  asOfDate: string;
};

export type CashBalanceQueryResult = CashBalanceSnapshot & {
  id?: number;
  created_at?: string;
};

type ReportingCheckpoint = {
  reportType: string;
  lastProcessedId: number;
  processedAt: string; // ISO date string
};

type ReportingCheckpointQueryResult = ReportingCheckpoint & {
  id?: number;
  created_at?: string;
};

export default class AccountingLedgerStorageService {
  private static readonly _tableName = "accountingledger";
  private static readonly _cashBalancesTableName = "cashbalances";
  private static readonly _reportingCheckpointsTableName = "reportingcheckpoints";

  /**
   * Add a single ledger entry to the accounting ledger
   */
  public static async addLedgerEntry(entry: AccountingLedgerEntry): Promise<TursoInsertResult> {
    const data = {
      aa: entry.aa,
      account_code: entry.account_code,
      side: entry.side,
      amount: entry.amount,
      reference_number: entry.reference_number || null,
      article_date: entry.article_date,
      description: entry.description
    };

    return await TursoService.Instance.addRow(AccountingLedgerStorageService._tableName, data);
  }

  /**
   * Add multiple ledger entries in a batch transaction
   * Ensures ACID compliance for double-entry accounting
   */
  public static async addLedgerEntries(entries: AccountingLedgerEntry[]): Promise<TursoInsertResult> {
    const dataArray = entries.map((entry) => ({
      aa: entry.aa,
      account_code: entry.account_code,
      side: entry.side,
      amount: entry.amount,
      reference_number: entry.reference_number || null,
      article_date: entry.article_date,
      description: entry.description
    }));

    return await TursoService.Instance.addRows(AccountingLedgerStorageService._tableName, dataArray);
  }

  /**
   * Validate double-entry accounting entries
   * Ensures debits equal credits for a given transaction group
   */
  public static validateDoubleEntry(entries: AccountingLedgerEntry[]): { isValid: boolean; error?: string } {
    let totalDebits = 0;
    let totalCredits = 0;

    for (const entry of entries) {
      if (entry.side === "debit") {
        totalDebits += entry.amount;
      } else if (entry.side === "credit") {
        totalCredits += entry.amount;
      } else {
        return {
          isValid: false,
          error: `Invalid side value: ${entry.side}. Must be 'debit' or 'credit'`
        };
      }
    }

    const difference = Math.abs(totalDebits - totalCredits);
    const tolerance = 0.01; // Allow for minor rounding differences

    if (difference > tolerance) {
      return {
        isValid: false,
        error: `Double-entry validation failed. Debits: ${totalDebits}, Credits: ${totalCredits}, Difference: ${difference}`
      };
    }

    return { isValid: true };
  }

  /**
   * Add validated double-entry ledger entries
   * Validates before inserting to ensure accounting integrity
   */
  public static async addValidatedLedgerEntries(entries: AccountingLedgerEntry[]): Promise<TursoInsertResult> {
    const validation = AccountingLedgerStorageService.validateDoubleEntry(entries);

    if (!validation.isValid) {
      return {
        success: false,
        error: validation.error
      };
    }

    return await AccountingLedgerStorageService.addLedgerEntries(entries);
  }

  /**
   * Query ledger entries by various criteria
   */
  public static async queryLedgerEntries(filters: {
    account_code?: string;
    reference_number?: string;
    user_id?: string;
    transaction_id?: string;
    article_date_from?: string;
    article_date_to?: string;
    limit?: number;
  }): Promise<LedgerQueryResult[]> {
    let sql = `SELECT * FROM ${AccountingLedgerStorageService._tableName} WHERE 1=1`;
    const args: any[] = [];

    if (filters.account_code) {
      sql += " AND account_code = ?";
      args.push(filters.account_code);
    }

    if (filters.reference_number) {
      sql += " AND reference_number = ?";
      args.push(filters.reference_number);
    }

    if (filters.user_id) {
      sql += " AND user_id = ?";
      args.push(filters.user_id);
    }

    if (filters.transaction_id) {
      sql += " AND transaction_id = ?";
      args.push(filters.transaction_id);
    }

    if (filters.article_date_from) {
      sql += " AND article_date >= ?";
      args.push(filters.article_date_from);
    }

    if (filters.article_date_to) {
      sql += " AND article_date <= ?";
      args.push(filters.article_date_to);
    }

    sql += " ORDER BY article_date ASC, id ASC";

    if (filters.limit) {
      sql += " LIMIT ?";
      args.push(filters.limit);
    }

    const rows = await TursoService.Instance.queryRows(sql, args);

    return rows.map((row) => ({
      id: row.id as number,
      aa: row.aa as number,
      account_code: row.account_code as string,
      side: row.side as "debit" | "credit",
      amount: row.amount as number,
      reference_number: row.reference_number as string,
      article_date: row.article_date as string,
      description: row.description as string,
      created_at: row.created_at as string
    }));
  }

  /**
   * Get account balance for a specific account code
   */
  public static async getAccountBalance(
    accountCode: string
  ): Promise<{ balance: number; debitTotal: number; creditTotal: number }> {
    const sql = `
      SELECT 
        side,
        SUM(amount) as total
      FROM ${AccountingLedgerStorageService._tableName} 
      WHERE account_code = ?
      GROUP BY side
    `;

    const rows = await TursoService.Instance.queryRows(sql, [accountCode]);

    let debitTotal = 0;
    let creditTotal = 0;

    for (const row of rows) {
      if (row.side === "debit") {
        debitTotal = row.total as number;
      } else if (row.side === "credit") {
        creditTotal = row.total as number;
      }
    }

    const balance = debitTotal - creditTotal;

    return {
      balance,
      debitTotal,
      creditTotal
    };
  }

  /**
   * Query ledger entries by event type (extracted from description field)
   * Description format: "user_id|transaction_id|event_type" or "user_id|transaction_id|isin|event_type"
   */
  public static async queryLedgerEntriesByEventType(
    eventType: string,
    fromDate?: string
  ): Promise<LedgerQueryResult[]> {
    let sql = `
      SELECT * FROM ${AccountingLedgerStorageService._tableName}
      WHERE description LIKE ?
    `;

    const args: any[] = [`%|${eventType}`];

    if (fromDate) {
      sql += " AND article_date >= ?";
      args.push(fromDate);
    }

    sql += " ORDER BY article_date ASC, id ASC";

    const rows = await TursoService.Instance.queryRows(sql, args);

    return rows.map((row) => ({
      id: row.id as number,
      aa: row.aa as number,
      account_code: row.account_code as string,
      side: row.side as "debit" | "credit",
      amount: row.amount as number,
      reference_number: row.reference_number as string,
      article_date: row.article_date as string,
      description: row.description as string,
      created_at: row.created_at as string
    }));
  }

  /**
   * Query ledger entries by transaction ID (extracted from description field)
   * Description format: "user_id|transaction_id|event_type" or "user_id|transaction_id|isin|event_type"
   */
  public static async queryLedgerEntriesByTransactionId(transactionId: string): Promise<LedgerQueryResult[]> {
    const sql = `
      SELECT * FROM ${AccountingLedgerStorageService._tableName}
      WHERE description LIKE ?
      ORDER BY article_date ASC, id ASC
    `;

    // Create pattern to match descriptions containing the transaction ID
    const pattern = `%|${transactionId}|%`;

    const rows = await TursoService.Instance.queryRows(sql, [pattern]);

    return rows.map((row) => ({
      id: row.id as number,
      aa: row.aa as number,
      account_code: row.account_code as string,
      side: row.side as "debit" | "credit",
      amount: row.amount as number,
      reference_number: row.reference_number as string,
      article_date: row.article_date as string,
      description: row.description as string,
      created_at: row.created_at as string
    }));
  }

  /**
   * Get total amounts for a specific event type, grouped by transaction
   * Returns a map of transaction ID to total amount for that transaction
   */
  public static async getTotalsByTransactionForEventType(eventType: string): Promise<Map<string, number>> {
    const entries = await AccountingLedgerStorageService.queryLedgerEntriesByEventType(eventType);
    const totals = new Map<string, number>();

    for (const entry of entries) {
      const transactionId = AccountingLedgerStorageService._extractTransactionIdFromDescription(entry.description);
      if (transactionId) {
        const currentTotal = totals.get(transactionId) || 0;
        totals.set(transactionId, currentTotal + entry.amount);
      }
    }

    return totals;
  }

  /**
   * Extract transaction ID from description field
   * Description format: "user_id|transaction_id|event_type" or "user_id|transaction_id|isin|event_type"
   */
  private static _extractTransactionIdFromDescription(description: string): string | null {
    const parts = description.split("|");
    return parts.length >= 2 ? parts[1] : null;
  }

  /**
   * Add cash balance snapshots to the storage
   */
  public static async addCashBalanceSnapshots(snapshots: CashBalanceSnapshot[]): Promise<TursoInsertResult> {
    const dataArray = snapshots.map((snapshot) => ({
      account_code: snapshot.accountCode,
      balance: snapshot.balance,
      as_of_date: snapshot.asOfDate
    }));

    return await TursoService.Instance.addRows(AccountingLedgerStorageService._cashBalancesTableName, dataArray);
  }

  /**
   * Query cash balance snapshots by various criteria
   */
  public static async queryCashBalances(
    filters: {
      accountCodes?: LedgerAccounts[];
      fromDate?: string;
      toDate?: string;
      limit?: number;
    } = {}
  ): Promise<CashBalanceQueryResult[]> {
    let sql = `SELECT * FROM ${AccountingLedgerStorageService._cashBalancesTableName} WHERE 1=1`;
    const args: any[] = [];

    if (filters.accountCodes && filters.accountCodes.length > 0) {
      sql += ` AND account_code IN (${filters.accountCodes.map(() => "?").join(", ")})`;
      args.push(...filters.accountCodes);
    }

    if (filters.fromDate) {
      sql += " AND as_of_date >= ?";
      args.push(filters.fromDate);
    }

    if (filters.toDate) {
      sql += " AND as_of_date <= ?";
      args.push(filters.toDate);
    }

    sql += " ORDER BY as_of_date DESC, account_code ASC";

    if (filters.limit) {
      sql += " LIMIT ?";
      args.push(filters.limit);
    }

    const rows = await TursoService.Instance.queryRows(sql, args);

    return rows.map((row) => ({
      id: row.id as number,
      accountCode: row.account_code as LedgerAccounts,
      balance: row.balance as number,
      asOfDate: row.as_of_date as string,
      created_at: row.created_at as string
    }));
  }

  /**
   * Get the latest cash balance snapshot for a specific account
   */
  public static async getLatestCashBalance(accountCode: LedgerAccounts): Promise<CashBalanceQueryResult | null> {
    const sql = `
      SELECT * FROM ${AccountingLedgerStorageService._cashBalancesTableName}
      WHERE account_code = ?
      ORDER BY as_of_date DESC
      LIMIT 1
    `;

    const rows = await TursoService.Instance.queryRows(sql, [accountCode]);

    if (rows.length === 0) {
      return null;
    }

    const row = rows[0];
    return {
      id: row.id as number,
      accountCode: row.account_code as LedgerAccounts,
      balance: row.balance as number,
      asOfDate: row.as_of_date as string,
      created_at: row.created_at as string
    };
  }

  /**
   * Get cash balance snapshot for a specific account on or after a specific date
   */
  public static async getCashBalanceOnOrAfterDate(
    accountCode: LedgerAccounts,
    date: string
  ): Promise<CashBalanceQueryResult | null> {
    const sql = `
      SELECT * FROM ${AccountingLedgerStorageService._cashBalancesTableName}
      WHERE account_code = ? AND as_of_date >= ?
      ORDER BY as_of_date ASC
      LIMIT 1
    `;

    const rows = await TursoService.Instance.queryRows(sql, [accountCode, date]);

    if (rows.length === 0) {
      return null;
    }

    const row = rows[0];
    return {
      id: row.id as number,
      accountCode: row.account_code as LedgerAccounts,
      balance: row.balance as number,
      asOfDate: row.as_of_date as string,
      created_at: row.created_at as string
    };
  }

  /**
   * Get the last processed ID for a specific report type
   */
  public static async getLastProcessedId(reportType: string): Promise<number | null> {
    const sql = `
      SELECT last_processed_id FROM ${AccountingLedgerStorageService._reportingCheckpointsTableName}
      WHERE report_type = ?
      ORDER BY processed_at DESC
      LIMIT 1
    `;

    const rows = await TursoService.Instance.queryRows(sql, [reportType]);

    if (rows.length === 0) {
      return null;
    }

    return rows[0].last_processed_id as number;
  }

  /**
   * Update the last processed ID for a specific report type
   */
  public static async updateLastProcessedId(
    reportType: string,
    lastProcessedId: number
  ): Promise<TursoInsertResult> {
    // Add a small delay to ensure unique timestamps when called in rapid succession
    await delay(1);

    const processedAt = new Date().toISOString();

    const data = {
      report_type: reportType,
      last_processed_id: lastProcessedId,
      processed_at: processedAt
    };

    return await TursoService.Instance.addRow(AccountingLedgerStorageService._reportingCheckpointsTableName, data);
  }

  /**
   * Query ledger entries starting from a specific ID (for incremental processing)
   */
  public static async queryLedgerEntriesFromId(filters: {
    fromId?: number;
    article_date_from?: string;
    article_date_to?: string;
    limit?: number;
  }): Promise<LedgerQueryResult[]> {
    let sql = `SELECT * FROM ${AccountingLedgerStorageService._tableName} WHERE 1=1`;
    const args: any[] = [];

    if (filters.fromId) {
      sql += " AND id > ?";
      args.push(filters.fromId);
    }

    if (filters.article_date_from) {
      sql += " AND article_date >= ?";
      args.push(filters.article_date_from);
    }

    if (filters.article_date_to) {
      sql += " AND article_date <= ?";
      args.push(filters.article_date_to);
    }

    sql += " ORDER BY id ASC";

    if (filters.limit) {
      sql += " LIMIT ?";
      args.push(filters.limit);
    }

    const rows = await TursoService.Instance.queryRows(sql, args);

    return rows.map((row) => ({
      id: row.id as number,
      aa: row.aa as number,
      account_code: row.account_code as string,
      side: row.side as "debit" | "credit",
      amount: row.amount as number,
      reference_number: row.reference_number as string,
      article_date: row.article_date as string,
      description: row.description as string,
      created_at: row.created_at as string
    }));
  }

  /**
   * Get all reporting checkpoints for debugging purposes
   */
  public static async getAllReportingCheckpoints(): Promise<ReportingCheckpointQueryResult[]> {
    const sql = `SELECT * FROM ${AccountingLedgerStorageService._reportingCheckpointsTableName} ORDER BY processed_at DESC`;

    const rows = await TursoService.Instance.queryRows(sql, []);

    return rows.map((row) => ({
      id: row.id as number,
      reportType: row.report_type as string,
      lastProcessedId: row.last_processed_id as number,
      processedAt: row.processed_at as string,
      created_at: row.created_at as string
    }));
  }
}
