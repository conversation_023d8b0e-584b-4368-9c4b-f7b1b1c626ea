import { Method } from "axios";
import HttpUtil from "../utils/httpUtil";

type ResponseTemplate<T> = {
  status: string;
  message: string;
  content: T;
};

export type HitType = {
  doc: {
    id: string;
    types: string[];
  };
  is_whitelisted: boolean;
};

export type SearchWithDetailsType = {
  hits: HitType[];
};
type SearchWithDetailsResponseContentType = {
  data: SearchWithDetailsType;
};

export type SearchType = {
  ref: string;
};
type SearchesResponseContentType = {
  data: SearchType[];
};

type UpdateSearchEntitiesDataType = {
  entities: string[];
  is_whitelisted: true;
};

const COMPLY_ADVANTAGE_API_URL = "https://api.complyadvantage.com";

export enum HitCategoryType {
  PEP = "pep",
  AdverseMedia = "adverse-media",
  Warning = "warning",
  FitnessAndProbity = "fitness-probity",
  Sanction = "sanction"
}

export class ComplyAdvantageService {
  private static _accessToken = process.env.COMPLY_ADVANTAGE_API_KEY;

  constructor() {
    ComplyAdvantageService._verifyCredentialsExist();
  }

  // ===============
  // PUBLIC METHODS
  // ===============
  public static async retrieveSearches(clientReference: string): Promise<SearchType[]> {
    const responseContent = await this._fetch<SearchesResponseContentType>({
      method: "GET",
      url: `${COMPLY_ADVANTAGE_API_URL}/searches?client_ref=${clientReference}`
    });
    return responseContent.data;
  }

  public static async retrieveSearchWithDetails(id: string): Promise<SearchWithDetailsType> {
    const responseContent = await this._fetch<SearchWithDetailsResponseContentType>({
      method: "GET",
      url: `${COMPLY_ADVANTAGE_API_URL}/searches/${id}/details`
    });
    return responseContent.data;
  }

  public static async updateSearchEntities(searchId: string, data: UpdateSearchEntitiesDataType): Promise<void> {
    return this._fetch({
      method: "PATCH",
      url: `${COMPLY_ADVANTAGE_API_URL}/searches/${searchId}/entities`,
      data
    });
  }

  // ===============
  // PRIVATE METHODS
  // ===============
  private static _verifyCredentialsExist() {
    if (!ComplyAdvantageService._accessToken) {
      throw new Error("Comply Advantage API key is missing");
    }
  }

  /**
   * @description This is the method for making any requests to access the Comply Advantage API.
   * @returns the response content if the request is successful
   * @param method get or post
   * @param url the Comply Advantage API endpoint
   * @param headers
   * @param data any data that may be posted with the request
   */
  private static async _fetch<T>(config: { method: Method; url: string; data?: any; headers?: any }): Promise<T> {
    const headers = {
      Authorization: `Token ${ComplyAdvantageService._accessToken}`,
      "User-Agent": "Wealthyhood-app-api",
      ...config.headers
    };

    const response: ResponseTemplate<T> = await HttpUtil.fetch(
      { ...config, headers },
      {
        throwError: true,
        addSentryBreadcrumb: true,
        captureSentryException: true,
        logOptions: {
          active: true,
          module: "ComplyAdvantageService",
          method: "_fetch"
        }
      }
    );

    if (response.status !== "success") {
      throw new Error(response.message);
    }

    return response.content;
  }
}

new ComplyAdvantageService();
