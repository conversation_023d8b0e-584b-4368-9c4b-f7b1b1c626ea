import { AxiosRequestConfig } from "axios";
import logger from "./loggerService";
import HttpUtil from "../utils/httpUtil";
import { envIsProd } from "../utils/environmentUtil";
import { NotificationEventType } from "../configs/notificationSettingsConfig";
import { NotificationMetadataTypeEnum } from "../models/Notification";

const NOTIFICATION_TO_TEMPLATE_ID: Record<NotificationEventType, string | undefined> = {
  learning_analysis_created: "f952ed2b-4aab-47dc-bcf3-633fadc70da8",
  learning_quick_take_created: "55b07b00-081a-4afa-a6fb-fd88cdcbe14c",
  learning_weekly_review_created: "3e991e4a-f603-4a90-b2c7-6935f1d3a23e",
  learning_guide_created: "6ed4480f-eb2c-4147-9c59-5137529abb51",
  learning_daily_market_recap: "0d156430-166f-4b9e-a68d-4a81fd0a4a1a",
  prompt_autopilot_1a: "d35e8a83-432c-41a6-80fc-ca61b6d9ec79",
  prompt_autopilot_1b: "b01f4ff4-bf79-4cca-a8f4-2774ff9da90a",
  prompt_free_etf_campaign_1a: "dc1c3072-43b3-47e9-9ef4-1a95026dfc34",
  prompt_invest_4a: "a34632ea-708f-476f-ab41-33107aa7bf47",
  prompt_invest_4b: "9f33f9e6-6972-4af7-a238-08dc27d6e153",
  prompt_referrals_1a: "0bc186e6-1d8b-4701-ad47-3292fc6c4c5f",
  prompt_send_gift: "96c06287-ffee-4190-afab-56c4e0757928",
  prompt_verify_3a: "47c1411b-33cb-483f-bbed-631626b31606",
  prompt_verify_3b: "d707a930-dd51-4f6e-a089-23ad0ba4f37e",
  prompt_receive_gift_unverified: "5056e23b-c50e-48c1-8ddd-ee44a3bae3c0",
  prompt_receive_gift_verified: "b5791039-3021-4632-887e-56e00d45f9dd",
  prompt_referrals_2a: "5dce7eee-6b14-45dc-ba95-d7b0f8c52819",
  prompt_referrals_2b: "28876f1d-192d-4c6a-b3fb-7dc4698b54d8",
  prompt_referrals_3: "75c689e0-9a5b-4538-b01e-e99de81205ee",
  transactional_automated_rebalancing_settled: "4515bfef-c3ce-4f4c-9d7c-3dd1ec9f67f4",
  transactional_deposit_failed: "a26b42ee-0e12-4b4f-ab9e-f1fa80c5f95f",
  transactional_deposit_success: "4903cf2e-4ef0-4234-8da5-7044363af213",
  transactional_dividend_received: "506c1e4e-7578-4ebe-bdd9-32a1dcf0e702",
  transactional_kyc_success: "7d3eddfc-66be-4d1a-9356-f85d8e34039f",
  transactional_order_settled: "6f8b6c3f-7b32-43dc-af56-b72c874e8fd2",
  transactional_rebalance_completed: "9d2f37ae-6705-4e9f-b526-1fb6cfa04a1b",
  transactional_repeating_investment_settled: "bcea3e5a-20be-484b-b977-7439a44a2267",
  transactional_savings_dividend_received: "6dc51297-ee1d-4df1-8173-fb78fc1b78e7",
  transactional_wealthyhood_dividend_created: "36d57002-5cc2-4c22-8631-e807cb1a9366"
};

export default class OneSignalService {
  private static readonly APP_ID = "9917dbe0-5498-4f06-a4e0-b108411b9da0";

  public static async sendPushNotification(
    userIds: string[],
    notificationType: NotificationEventType,
    customData?: Record<string, string | number>,
    metadata?: {
      notificationType: NotificationMetadataTypeEnum;
      documentId: string;
    }
  ): Promise<{ id: string }> {
    if (!envIsProd()) {
      return;
    }

    const templateId = NOTIFICATION_TO_TEMPLATE_ID[notificationType];

    if (!templateId) {
      logger.warn("No template ID found for notification type", {
        module: "OneSignalService",
        method: "sendPushNotification",
        data: { notificationType }
      });
      return;
    }

    const httpConfig: AxiosRequestConfig = {
      method: "POST",
      url: "https://api.onesignal.com/notifications",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Basic ${process.env.ONESIGNAL_API_KEY}`
      },
      data: {
        app_id: this.APP_ID,
        target_channel: "push",
        include_aliases: {
          external_id: userIds
        },
        template_id: templateId,
        custom_data: customData,
        data: metadata
      }
    };

    logger.info("Sending OneSignal notification", {
      module: "OneSignalService",
      method: "sendPushNotification",
      data: JSON.stringify(httpConfig, null, 4)
    });

    return await HttpUtil.fetch(httpConfig, {
      throwError: false,
      addSentryBreadcrumb: true,
      captureSentryException: true,
      logOptions: {
        active: true,
        module: "OneSignalService",
        method: "sendPushNotification"
      }
    });
  }
}
