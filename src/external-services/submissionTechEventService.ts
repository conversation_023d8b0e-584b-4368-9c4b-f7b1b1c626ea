import axios from "axios";
import logger from "./loggerService";

export enum SubmissionTechEventEnum {
  SIGNUP = "signup",
  PLUS_SUBSCRIPTION = "plusSubscription",
  GOLD_SUBSCRIPTION = "goldSubscription"
}

const URL_MAPPING = {
  [SubmissionTechEventEnum.SIGNUP]: "https://clickwork7secure.com/p.ashx?o=25990&e=2224&f=pb&r=",
  [SubmissionTechEventEnum.PLUS_SUBSCRIPTION]: "https://clickwork7secure.com/p.ashx?o=25990&e=2226&f=pb&r=",
  [SubmissionTechEventEnum.GOLD_SUBSCRIPTION]: "https://clickwork7secure.com/p.ashx?o=25990&e=2227&f=pb&r="
};

export default class SubmissionTechEventService {
  public static async trackEvent(
    eventName: SubmissionTechEventEnum,
    submissionTechClickId?: string
  ): Promise<void> {
    const url = URL_MAPPING[eventName];
    if (!url) {
      return;
    }

    if (!submissionTechClickId) {
      logger.info(`Missing submission tech click id for event ${eventName}`, {
        module: "SubmissionTechEventService",
        method: "trackEvent"
      });
      return;
    }

    logger.info(`Tracking submission tech event ${eventName} with click id ${submissionTechClickId}`, {
      module: "SubmissionTechEventService",
      method: "trackEvent"
    });

    await axios.post(url + submissionTechClickId);
  }
}
