export class StripeService {
  public static get Instance(): StripeService {
    const globalWithStripe = global as typeof global & { StripeServiceInstance: StripeService };
    if (!globalWithStripe.StripeServiceInstance) {
      globalWithStripe.StripeServiceInstance = new StripeService();
    }
    return globalWithStripe.StripeServiceInstance;
  }

  public constructEvent(): any {}

  public async createCustomer(): Promise<any> {}

  public async createSetupIntent(): Promise<any> {}

  public async createEphemeralKey(): Promise<any> {}

  public async retrieveSetupIntent(): Promise<any> {}

  public async retrievePaymentIntent(): Promise<any> {}

  public async retrievePaymentMethod(): Promise<any> {}

  public async retrieveSubscription(): Promise<any> {}

  public async createSubscription(): Promise<any> {}

  public async updateSubscription(): Promise<any> {}

  public async cancelSubscription(): Promise<any> {}
}
