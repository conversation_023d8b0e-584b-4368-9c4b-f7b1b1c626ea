import { ContentfulContentTypeEnum, ContentfulAccountEnum } from "../../configs/contentfulConfig";

class ContentfulRetrievalService {
  private static _instances: Record<ContentfulAccountEnum, ContentfulRetrievalService | null> = {
    [ContentfulAccountEnum.LANDING_PAGE]: null,
    [ContentfulAccountEnum.LEARN_HUB]: null
  };

  constructor(options: { account: ContentfulAccountEnum }) {}

  public static get LandingPageInstance(): ContentfulRetrievalService {
    return (
      this._instances[ContentfulAccountEnum.LANDING_PAGE] ||
      (this._instances[ContentfulAccountEnum.LANDING_PAGE] = new this({
        account: ContentfulAccountEnum.LANDING_PAGE
      }))
    );
  }

  public static get LearnHubInstance(): ContentfulRetrievalService {
    return (
      this._instances[ContentfulAccountEnum.LEARN_HUB] ||
      (this._instances[ContentfulAccountEnum.LEARN_HUB] = new this({ account: ContentfulAccountEnum.LEARN_HUB }))
    );
  }

  public async getEntries(): Promise<any> {
    return;
  }

  public async getEntriesByIds(contentType: ContentfulContentTypeEnum, ids: string[]): Promise<any> {
    return;
  }

  public async getEntry(id: string): Promise<any> {
    return;
  }

  public async getEntryBySlug(contentType: ContentfulContentTypeEnum, slug: string): Promise<any> {
    return;
  }
}

export default ContentfulRetrievalService;
