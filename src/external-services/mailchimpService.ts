import axios from "axios";
import logger from "./loggerService";
import { addBreadcrumb, captureException } from "@sentry/node";
import crypto from "crypto";
import { PartialRecord } from "utils";

enum HttpMethodEnum {
  GET = "get",
  POST = "post",
  PATCH = "patch",
  PUT = "put"
}

export const MailchimpUserMergeFieldArray = [
  "FNAME",
  "LNAME",
  "REFERRED",
  "REFERREDBY",
  "STATUS",
  "REPINVESTM",
  "AUTREBALAN",
  "SENDGIFTCM",
  "CAMPLIVE",
  // Email notifications
  "PROMO",
  "WEALTHYBIT",
  // Portfolio performance merge fields
  "RETURN",
  "MOVER1",
  "MOVER1RET",
  "MOVER2",
  "MOVER2RET",
  "MOVER3",
  "MOVER3RET",
  "REPORTSTAT",
  "COMPANYENT",
  "RESIDENCYC",
  "WAITING"
] as const;
export type MailchimpUserMergeFieldType = (typeof MailchimpUserMergeFieldArray)[number];

export const MemberStatusArray = [
  "subscribed",
  "unsubscribed",
  "cleaned",
  "pending",
  "transactional",
  "archived"
] as const;
export type MemberStatusType = (typeof MemberStatusArray)[number];
type MemberMergeFieldsAndStatusType = {
  status: MemberStatusType;
  mergeFields: PartialRecord<MailchimpUserMergeFieldType, string>;
};
export type MemberType = {
  status: MemberStatusType;
  addedAt: Date;
  id: string;
};
export type RequestOptions = {
  silent: boolean;
};
export type PaginationOptions = { offset: number };
export enum AudienceIdEnum {
  WEALTHYHOOD = "189a27e0df",
  WEALTHYBITES = "4bbaa561eb"
}
type UpdateMemberData = {
  merge_fields?: PartialRecord<MailchimpUserMergeFieldType, string | number>;
  status?: MemberStatusType;
};

const PENDING_REWARDS_SEGMENT_ID = "3507122";
const PENDING_REWARDS_SEGMENT_PAGE_SIZE = 300;

const PENDING_GIFTING_CAPABILITY_SEGMENT_ID = "3502349";
const PENDING_GIFTING_CAPABILITY_SEGMENT_PAGE_SIZE = 500;

export default class MailchimpService {
  public static async getMergeFieldsAndStatusForUserEmail(
    email: string,
    fields: MailchimpUserMergeFieldType[],
    options?: RequestOptions
  ): Promise<MemberMergeFieldsAndStatusType> {
    const md5HashEmail = crypto.createHash("md5").update(email).digest("hex");

    const response = await MailchimpService._fetch(
      HttpMethodEnum.GET,
      `members/${md5HashEmail}?fields=merge_fields,status`,
      email,
      null,
      undefined,
      options
    );

    return {
      status: response?.["status"],
      mergeFields: MailchimpService._filterMergeFields(response, fields)
    };
  }

  public static async executeForAllPendingRewardMembersPaginated(
    processorFn: (users: { email: string }[]) => Promise<void>
  ): Promise<void> {
    const memberCount = await MailchimpService.getPendingRewardMemberCount();
    let offset = 0;

    while (offset < memberCount) {
      logger.info(`Processing page with offset ${offset} in pending reward members...`, {
        module: "MailchimpService",
        method: "executeForAllPendingRewardMembersPaginated"
      });
      const users = await MailchimpService.getPendingRewardMembers({ offset });
      await processorFn(users);
      offset += PENDING_REWARDS_SEGMENT_PAGE_SIZE;
    }
  }

  /**
   * Returns a list of e-mail addresses, one for each user in the Mailchimp pending gifting capability segment.
   * @param options
   */
  public static async getPendingGiftingCapabilityMembers(options?: RequestOptions): Promise<string[]> {
    const response = await MailchimpService._fetch(
      HttpMethodEnum.GET,
      `segments/${PENDING_GIFTING_CAPABILITY_SEGMENT_ID}/members?include_unsubscribed=true&include_cleaned=true&count=${PENDING_GIFTING_CAPABILITY_SEGMENT_PAGE_SIZE}`,
      null,
      null,
      undefined,
      options
    );

    return response.members.map((member: any) => {
      return member.email_address;
    });
  }

  public static async retrieveMember(
    email: string,
    audience: AudienceIdEnum,
    options?: RequestOptions
  ): Promise<MemberType> {
    const md5HashEmail = crypto.createHash("md5").update(email).digest("hex");

    return await MailchimpService._fetch(
      HttpMethodEnum.GET,
      `members/${md5HashEmail}`,
      email,
      null,
      audience,
      options
    );
  }

  public static async updateMember(
    email: string,
    data: UpdateMemberData,
    audience: AudienceIdEnum,
    options?: RequestOptions
  ): Promise<void> {
    const md5HashEmail = crypto.createHash("md5").update(email).digest("hex");
    await MailchimpService._fetch(HttpMethodEnum.PATCH, `members/${md5HashEmail}`, email, data, audience, options);
  }

  public static async deleteMember(
    email: string,
    audience: AudienceIdEnum,
    options?: RequestOptions
  ): Promise<void> {
    const md5HashEmail = crypto.createHash("md5").update(email).digest("hex");

    await MailchimpService._fetch(
      HttpMethodEnum.POST,
      `members/${md5HashEmail}/actions/delete-permanent`,
      email,
      null,
      audience,
      options
    );
  }

  public static async getPendingRewardMembers(
    paginationOptions?: PaginationOptions,
    options?: RequestOptions
  ): Promise<
    {
      email: string;
    }[]
  > {
    let url = `segments/${PENDING_REWARDS_SEGMENT_ID}/members?include_unsubscribed=true&include_cleaned=true&count=${PENDING_REWARDS_SEGMENT_PAGE_SIZE}`;
    if (paginationOptions?.offset) {
      url += `&offset=${paginationOptions.offset}`;
    }

    const response = await MailchimpService._fetch(HttpMethodEnum.GET, url, null, null, undefined, options);

    return response.members.map((member: any) => {
      return {
        email: member.email_address
      };
    });
  }

  public static async getPendingRewardMemberCount(options?: RequestOptions): Promise<number> {
    const response = await MailchimpService._fetch(
      HttpMethodEnum.GET,
      `segments/${PENDING_REWARDS_SEGMENT_ID}?include_unsubscribed=true&include_cleaned=true`,
      null,
      null,
      undefined,
      options
    );

    return response.member_count;
  }
  private static _filterMergeFields(
    response: any,
    fields: MailchimpUserMergeFieldType[]
  ): PartialRecord<MailchimpUserMergeFieldType, string> {
    return Object.fromEntries(
      Object.entries(response?.["merge_fields"]).filter((field) =>
        fields.includes(field[0] as MailchimpUserMergeFieldType)
      )
    );
  }

  private static async _fetch(
    method: HttpMethodEnum,
    url: string,
    userEmail?: string,
    data?: any,
    audience: AudienceIdEnum = AudienceIdEnum.WEALTHYHOOD,
    options: RequestOptions = { silent: false }
  ): Promise<any> {
    logger.info("making http request", {
      module: "MailchimpService",
      method: "_fetch",
      userEmail,
      data: {
        method,
        url,
        data
      }
    });

    try {
      const response = await axios({
        method,
        url: `${process.env.MAILCHIMP_API}/lists/${audience}/${url}`,
        data,
        headers: { Authorization: `Basic ${process.env.MAILCHIMP_API_KEY}` }
      });
      return response.data;
    } catch (err) {
      logger.error("http request failed", {
        module: "MailchimpService",
        method: "_fetch",
        userEmail,
        data: {
          error: err
        }
      });
      addBreadcrumb({
        type: "http",
        category: "http",
        level: "error",
        data: {
          url,
          method: method.toUpperCase(),
          // eslint-disable-next-line camelcase
          status_code: err.response && err.response.status,
          reason: err.response && JSON.stringify(err.response.data, null, 4)
        }
      });
      captureException(err);

      if (!options.silent) {
        throw err;
      }
    }
  }
}
