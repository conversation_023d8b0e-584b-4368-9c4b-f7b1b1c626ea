import qs from "qs";
import { Method } from "axios";
import HttpUtil from "../utils/httpUtil";
import DateUtil from "../utils/dateUtil";
import { NotFoundError } from "../models/ApiErrors";

export enum FinimizeContentTypeEnum {
  RESEARCH = "RESEARCH",
  // Type INSIGHT also fetches RESEARCH
  INSIGHT = "INSIGHT",
  QUICK_TAKE = "QUICK_TAKE",
  WEEKLY_BRIEF = "WEEKLY_BRIEF"
}

export type FinimizeContentPieceType = {
  contentPieceId: string;
  contentPieceTypeId: FinimizeContentTypeEnum;
  slug: string;
  title: string;
  publishedAt: string;
  subtitle: string;
  summary: string;
  headerImage: {
    full: string;
    default: string;
  };
  notificationTitleText?: string;
  notificationBodyText?: string;
  blocks: ContentBlockType[];
};

type ContentBlockType = TextContentBlockType | ImageContentBlockType | QuoteContentBlockType;

type TextContentBlockType = {
  type: "text";
  textMarkdown: string;
  textPlain: string;
  textHtml: string;
};

type ImageContentBlockType = {
  type: "image";
  image: {
    full: string;
    default: string;
  };
  alt: string;
  caption: string;
};

type QuoteContentBlockType = {
  type: "quote";
  quote: string;
  caption?: string;
};

type FinimizeContentResponseType = {
  result: string | "success";
  data: {
    pageInfo: {
      hasNextPage: boolean;
      hasPreviousPage: boolean;
      startCursor: string;
      endCursor: string;
    };
    contentPieces: FinimizeContentPieceType[];
  };
};

const FINIMIZE_BASE_URL = "https://api.finimize.com/v2";

const FINIMIZE_ROUTES = {
  CONTENT: `${FINIMIZE_BASE_URL}/content`
} as const;

export default class FinimizeService {
  private static _apiKey = process.env.FINIMIZE_API_KEY;

  /**
   * @description
   * This method iterates over all the content pieces matching the given criteria
   * and processes them using the provided processor function.
   */
  public static async executeForContent(
    data: {
      types: FinimizeContentTypeEnum[];
      publishedAfter: Date;
    },
    processorFn: (data: FinimizeContentPieceType[]) => Promise<void>
  ): Promise<void> {
    let hasNextPage = true;
    let afterCursor: string;

    while (hasNextPage) {
      const queryParams = qs.stringify(
        {
          "content-types-included": data.types,
          "start-date": data.publishedAfter.toISOString(),
          after: afterCursor
        },
        { arrayFormat: "brackets", addQueryPrefix: true }
      );

      const response: FinimizeContentResponseType = await FinimizeService._fetch({
        method: "GET",
        url: `${FINIMIZE_ROUTES.CONTENT}${queryParams}`
      });

      await processorFn(response.data.contentPieces);

      hasNextPage = response.data.pageInfo.hasNextPage; // Check if more pages exist
      afterCursor = response.data.pageInfo.endCursor; // Update the cursor for the next page
    }
  }

  public static async retrieveContentPiece(data: {
    type: FinimizeContentTypeEnum;
    publishedAt: Date;
    contentPieceId: string;
  }): Promise<FinimizeContentPieceType> {
    const { start, end } = DateUtil.getStartAndEndOfDay(data.publishedAt);

    const queryParams = qs.stringify(
      {
        "content-types-included": data.type,
        "start-date": start.toISOString(),
        "end-date": end.toISOString()
      },
      { arrayFormat: "brackets", addQueryPrefix: true }
    );

    const response: FinimizeContentResponseType = await FinimizeService._fetch({
      method: "GET",
      url: `${FINIMIZE_ROUTES.CONTENT}${queryParams}`
    });

    const contentPiece = response.data.contentPieces.find(
      (contentPiece) => contentPiece.contentPieceId === data.contentPieceId
    );

    if (!contentPiece) {
      throw new NotFoundError(`Content piece with id ${data.contentPieceId} not found`);
    }

    return contentPiece; // Return all accumulated results
  }

  private static async _fetch(config: { method: Method; url: string; data?: any; headers?: any }): Promise<any> {
    const headers = {
      "x-api-key": FinimizeService._apiKey,
      ...config.headers
    };

    return await HttpUtil.fetch(
      { ...config, headers },
      {
        throwError: true,
        addSentryBreadcrumb: true,
        captureSentryException: true,
        logOptions: {
          active: true,
          module: "FinimizeService",
          method: "_fetch"
        }
      }
    );
  }
}
