import {
  UserDataRequest,
  UserDataRequestDocument,
  UserDataRequestDTOInterface,
  UserDataRequestInterface,
  UserDataRequestReasonEnum,
  UserDataRequestStatusType,
  UserDataRequestTypeType
} from "../models/UserDataRequest";
import { BadRequestError } from "../models/ApiErrors";
import PortfolioService from "./portfolioService";
import { captureException } from "@sentry/node";
import logger from "../external-services/loggerService";
import { HoldingsType, Portfolio, PortfolioDocument } from "../models/Portfolio";
import AccountService from "./accountService";
import { Account, AccountDocument } from "../models/Account";
import MailchimpService, { AudienceIdEnum } from "../external-services/mailchimpService";
import UserService from "./userService";
import { UserDocument } from "../models/User";
import { auth0ManagementClient } from "../external-services/auth0ManagementService";
import { TransactionService } from "./transactionService";
import {
  RevertRewardTransactionDocument,
  Transaction,
  TransactionDocument,
  WithdrawalCashTransactionDocument
} from "../models/Transaction";
import { Reward } from "../models/Reward";
import SubscriptionService from "./subscriptionService";
import ParticipantService from "./participantService";
import eventEmitter from "../loaders/eventEmitter";
import events from "../event-handlers/events";
import BankAccountService from "./bankAccountService";
import MandateService from "./mandateService";
import AutomationService from "./automationService";
import ProviderService from "./providerService";
import { MixpanelAccountStatusEnum } from "../external-services/segmentAnalyticsService";
import { UserDataRequestsFilter } from "filters";
import NotificationService from "./notificationService";

export default class UserDataRequestService {
  /**
   * Returns all user data requests for a given user.
   * @param filter
   */
  public static async getUserDataRequests(
    filter: UserDataRequestsFilter = {}
  ): Promise<UserDataRequestInterface[]> {
    const dbFilter = UserDataRequestService._createUserDataRequestsDbFilter(filter);

    return UserDataRequest.find(dbFilter);
  }

  /**
   * Creates a user data request for the given user.
   * @param user
   * @param requestType
   * @param reason
   */
  public static async createUserDataRequest(
    user: UserDocument,
    requestType: UserDataRequestTypeType,
    reason: UserDataRequestReasonEnum
  ): Promise<UserDataRequestInterface> {
    const existingUserDataRequest = await UserDataRequest.findOne({
      owner: user._id
    });

    if (existingUserDataRequest) {
      throw new BadRequestError("User already has a user data request");
    }

    const requestData: UserDataRequestDTOInterface = {
      owner: user._id,
      requestType,
      reason
    };

    const request = await new UserDataRequest(requestData).save();

    if (requestType === "disassociation") {
      eventEmitter.emit(events.user.disassociation.eventId, user, { reason });
      eventEmitter.emit(events.user.whAccountStatusUpdate.eventId, user, {
        accountStatus: MixpanelAccountStatusEnum.Closing
      });
    }

    if (reason !== UserDataRequestReasonEnum.INACTIVE_USER) {
      await NotificationService.createEmailNotification(
        user.id,
        { notificationId: "deletionCreation" },
        { sendImmediately: true }
      );
    }

    return request;
  }

  public static async processAllUserDisassociations() {
    const pendingDisassociationRequests = await UserDataRequest.find({
      requestType: "disassociation",
      status: "Created"
    }).populate({
      path: "owner",
      populate: [{ path: "accounts" }, { path: "portfolios" }]
    });

    for (let i = 0; i < pendingDisassociationRequests.length; i++) {
      const request = pendingDisassociationRequests[i];
      try {
        await UserDataRequestService._processPendingDisassociationRequest(request);
      } catch (err) {
        captureException(err);
        logger.error(`Processing user pending disassociation request failed for ${request._id}`, {
          module: "UserDataRequestService",
          method: "processAllUserDisassociations",
          data: { requestId: request._id, userId: request.owner.id, error: err }
        });
      }
    }
  }

  public static async processAllUserGDPRDeletions() {
    const pendingGDPRDeletionRequests = await UserDataRequest.find({
      requestType: "gdpr-delete",
      status: "Created"
    }).populate({
      path: "owner",
      populate: [{ path: "accounts" }, { path: "portfolios" }]
    });

    for (let i = 0; i < pendingGDPRDeletionRequests.length; i++) {
      const request = pendingGDPRDeletionRequests[i];
      try {
        await UserDataRequestService._processPendingGDPRDeletionRequest(request);
      } catch (err) {
        captureException(err);
        logger.error(`Processing user pending GDPR deletion request failed for ${request._id}`, {
          module: "UserDataRequestService",
          method: "processAllUserGDPRDeletions",
          data: { requestId: request._id, userId: request.owner.id, error: err }
        });
      }
    }
  }

  private static async _processPendingDisassociationRequest(request: UserDataRequestDocument): Promise<void> {
    logger.info(`Processing user pending disassociation request ${request._id}...`, {
      module: "UserDataRequestService",
      method: "_processPendingDisassociationRequest",
      data: {
        requestId: request._id,
        userId: request.owner.id
      }
    });

    // Unsubscribe user from Mailchimp as early as possible for all audiences
    await UserDataRequestService._unsubscribeFromMailchimp(request, AudienceIdEnum.WEALTHYHOOD);
    await UserDataRequestService._unsubscribeFromMailchimp(request, AudienceIdEnum.WEALTHYBITES);

    // We start with deactivating the user's subscription (to not charge them while we empty their portfolio), and then
    // selling & withdrawing their portfolio. The latter are actions the user still see as they still have access to their account.
    await SubscriptionService.deactivateSubscription(request.owner.id, { cancelCardBasedSubscription: true });

    await UserDataRequestService._cancelAllUserAutomations(request);

    await UserDataRequestService._cancelAllUserMandates(request);

    const owner = request.owner as UserDocument;
    // if the user has suspended account but no holdings, savings or cash, it's safe to proceed with closing it
    if (
      owner.hasSuspendedAccount &&
      owner.portfolios.some((portfolio) => {
        const hasHoldings = portfolio?.holdings?.length > 0;
        const hasCash = portfolio?.cash?.[owner.currency]?.available > 0;
        const hasSavings = Array.from(portfolio?.savings?.values()).some((saving) => saving?.amount > 0);
        return hasHoldings || hasCash || hasSavings;
      })
    ) {
      logger.warn(
        `Cannot sell holdings & withdraw cash during processing of disassociation request ${request._id} due to suspended account`,
        {
          module: "UserDataRequestService",
          method: "_processPendingDisassociationRequest",
          data: {
            requestId: request._id,
            userId: owner.id,
            userEmail: owner.email
          }
        }
      );

      return;
    }

    const allPortfoliosSold = await UserDataRequestService._sellAllPortfolios(request);
    if (!allPortfoliosSold) return;
    const allPortfoliosWithdrawn = await UserDataRequestService._withdrawFromAllPortfolios(request);
    if (!allPortfoliosWithdrawn) return;

    // We continue by disassociating the e-mail from the ID so that the user is not able to log in anymore.
    // This is done in the beginning as the rest of the actions could take a few days to complete.
    const wasAssociated = await UserDataRequestService._disassociateUserFromEmail(request);
    await UserDataRequestService._disassociateParticipantFromEmail(request);

    if (wasAssociated && request.reason !== UserDataRequestReasonEnum.INACTIVE_USER) {
      await NotificationService.createEmailNotification(
        owner.id,
        { notificationId: "deletionSuccess" },
        { sendImmediately: true }
      );
    } else if (wasAssociated && request.reason === UserDataRequestReasonEnum.INACTIVE_USER) {
      await NotificationService.createEmailNotification(
        owner.id,
        { notificationId: "deletionSuccessForInactiveUser" },
        { sendImmediately: true }
      );
    }

    const allRewardsReverted = await UserDataRequestService._revertAllRewards(request);
    if (!allRewardsReverted) return;

    const allPortfoliosClosed = await UserDataRequestService._closeAllPortfolios(request);
    if (!allPortfoliosClosed) return;

    const allAccountsClosed = await UserDataRequestService._closeAllAccounts(request);
    if (!allAccountsClosed) return;

    if (allPortfoliosClosed && allAccountsClosed) {
      await UserDataRequest.findByIdAndUpdate(request.id, { status: "Completed" });
      eventEmitter.emit(events.user.whAccountStatusUpdate.eventId, owner, {
        accountStatus: MixpanelAccountStatusEnum.Closed
      });

      logger.info(`Completed user pending disassociation request ${request._id}...`, {
        module: "UserDataRequestService",
        method: "_processPendingDisassociationRequest",
        data: {
          requestId: request._id,
          userId: owner.id
        }
      });
    } else {
      logger.info(`Finished processing user pending disassociation request ${request._id}...`, {
        module: "UserDataRequestService",
        method: "_processPendingDisassociationRequest",
        data: {
          requestId: request._id,
          userId: owner.id
        }
      });
    }
  }

  private static async _processPendingGDPRDeletionRequest(request: UserDataRequestDocument) {
    logger.info(`Processing user pending GDPR deletion request ${request._id}...`, {
      module: "UserDataRequestService",
      method: "_processPendingGDPRDeletionRequest",
      data: {
        requestId: request._id,
        userId: request.owner.id
      }
    });

    // Delete user from Mailchimp as early as possible for all audiences
    await UserDataRequestService._deleteFromMailchimp(request, AudienceIdEnum.WEALTHYHOOD);
    await UserDataRequestService._deleteFromMailchimp(request, AudienceIdEnum.WEALTHYBITES);

    // We start with deactivating the user's subscription (to not charge them while we empty their portfolio), and then
    // selling & withdrawing their portfolio. The latter are actions the user still see as they still have access to their account.
    await SubscriptionService.deactivateSubscription(request.owner.id, { cancelCardBasedSubscription: true });

    await UserDataRequestService._cancelAllUserAutomations(request);

    await UserDataRequestService._cancelAllUserMandates(request);

    const owner = request.owner as UserDocument;
    // if the user has suspended account but no holdings or cash, it's safe to proceed with closing it
    if (
      owner.hasSuspendedAccount &&
      owner.portfolios.some(
        (portfolio) => portfolio?.holdings?.length > 0 || portfolio?.cash?.[owner.currency]?.available > 0
      )
    ) {
      logger.warn(
        `Cannot sell holdings & withdraw cash during processing of disassociation request ${request._id} due to suspended account`,
        {
          module: "UserDataRequestService",
          method: "_processPendingGDPRDeletionRequest",
          data: {
            requestId: request._id,
            userId: owner.id,
            userEmail: owner.email
          }
        }
      );

      return;
    }

    const allPortfoliosSold = await UserDataRequestService._sellAllPortfolios(request);
    if (!allPortfoliosSold) return;
    const allPortfoliosWithdrawn = await UserDataRequestService._withdrawFromAllPortfolios(request);
    if (!allPortfoliosWithdrawn) return;

    // We continue with deleting the user from Auth0 and disassociating the e-mail from the ID and Auth0 so that the user is not able to log in anymore.
    // This is done in the beginning as the rest of the actions could take a few days to complete.
    await UserDataRequestService._deleteFromAuth0(request);
    const wasAssociated = await UserDataRequestService._disassociateUserFromEmail(request);
    await UserDataRequestService._disassociateParticipantFromEmail(request);

    if (wasAssociated) {
      await NotificationService.createEmailNotification(
        owner.id,
        { notificationId: "deletionSuccess" },
        { sendImmediately: true }
      );
    }

    const allRewardsReverted = await UserDataRequestService._revertAllRewards(request);
    if (!allRewardsReverted) return;

    const allPortfoliosClosed = await UserDataRequestService._closeAllPortfolios(request);
    if (!allPortfoliosClosed) return;

    const allAccountsClosed = await UserDataRequestService._closeAllAccounts(request);
    if (!allAccountsClosed) return;

    if (allPortfoliosClosed && allAccountsClosed) {
      await UserDataRequestService._deleteUserDocument(request);
      await UserDataRequestService._deleteParticipantDocument(request);

      await UserDataRequest.findByIdAndUpdate(request.id, { status: "Completed" });
      logger.info(`Completed user GDPR deletion request ${request._id}...`, {
        module: "UserDataRequestService",
        method: "_processPendingGDPRDeletionRequest",
        data: {
          requestId: request._id,
          userId: owner.id
        }
      });
    } else {
      logger.info(`Finished processing user GDPR deletion request ${request._id}...`, {
        module: "UserDataRequestService",
        method: "_processPendingGDPRDeletionRequest",
        data: {
          requestId: request._id,
          userId: owner.id
        }
      });
    }
  }

  private static async _disassociateUserFromEmail(request: UserDataRequestDocument): Promise<boolean> {
    const user = request.owner as UserDocument;
    let wasAssociated = false;
    if (!user.isDeleted) {
      wasAssociated = true;
      await UserService.updateUser(user.id, { email: `deleted_${user.email}` });
      logger.info(`Disassociated user from e-mail for request ${request._id}...`, {
        module: "UserDataRequestService",
        method: "_disassociateUserFromEmail",
        data: {
          requestId: request._id,
          userId: request.owner.id
        }
      });
    }
    return wasAssociated;
  }

  private static async _disassociateParticipantFromEmail(request: UserDataRequestDocument) {
    const user = request.owner as UserDocument;
    const participant = await ParticipantService.getParticipantByEmail(user.email);

    if (!participant) return;

    if (!participant.email.startsWith("deleted_")) {
      await ParticipantService.updateParticipant(participant, { email: `deleted_${participant.email}` });
      logger.info(`Disassociated user from e-mail for request ${request._id}...`, {
        module: "UserDataRequestService",
        method: "_disassociateParticipantFromEmail",
        data: {
          requestId: request._id,
          userId: request.owner.id,
          participantId: participant.id
        }
      });
    } else {
      logger.info(`User is already disassociated from email from e-mail for request ${request._id}...`, {
        module: "UserDataRequestService",
        method: "_disassociateParticipantFromEmail",
        data: {
          requestId: request._id,
          userId: request.owner.id,
          participantId: participant.id
        }
      });
    }
  }

  /**
   * Withdraws cash from all the user's portfolios. Returns true if all portfolios are fully withdrawn, false otherwise.
   * @param request
   * @private
   */
  private static async _withdrawFromAllPortfolios(request: UserDataRequestDocument): Promise<boolean> {
    const user = request.owner as UserDocument;
    const portfolios = await PortfolioService.getRealPortfolios(user.id);

    const portfoliosWithCash = portfolios.filter(
      (portfolio) =>
        portfolio.providers?.wealthkernel?.status === "Active" && portfolio?.cash?.[user.currency]?.available > 0
    );

    const pendingLinkedWithdrawals = (await TransactionService.getWithdrawalCashTransactions({
      owner: user._id,
      statuses: ["Pending"],
      linkedUserDataRequest: request._id
    })) as WithdrawalCashTransactionDocument[];

    if (portfoliosWithCash.length > 0) {
      if (
        portfoliosWithCash.some(
          (portfolio) => portfolio.cash?.[user.currency]?.settled !== portfolio.cash?.[user.currency]?.available
        )
      ) {
        logger.info(`Could not request withdrawal for ${request.id} since the user still has unsettled cash.`, {
          module: "UserDataRequestService",
          method: "_withdrawFromAllPortfolios"
        });
        return false;
      }

      const mostRecentBankAccount = (
        await BankAccountService.getBankAccounts({ owner: user.id }, "-createdAt")
      )?.[0];

      if (!mostRecentBankAccount) {
        logger.info(
          `Could not request withdrawal for ${request._id} since the user does not have any bank account, we should reach out to them!`,
          {
            module: "UserDataRequestService",
            method: "_withdrawFromAllPortfolios",
            data: {
              requestId: request._id,
              userId: request.owner.id
            }
          }
        );
        return false;
      }

      await Promise.all(
        portfoliosWithCash.map((portfolio) =>
          TransactionService.withdraw(portfolio.id, portfolio?.cash?.[user.currency]?.available, {
            linkedUserDataRequest: request.id,
            bankAccountId: mostRecentBankAccount.id
          })
        )
      );
      logger.info(`Created withdrawals for request ${request._id}...`, {
        module: "UserDataRequestService",
        method: "_withdrawFromAllPortfolios",
        data: {
          requestId: request._id,
          userId: request.owner.id
        }
      });
      return false;
    } else if (pendingLinkedWithdrawals.length > 0) {
      logger.info(`Withdrawals for request ${request._id} are still pending, returning...`, {
        module: "UserDataRequestService",
        method: "_withdrawFromAllPortfolios",
        data: {
          requestId: request._id,
          userId: request.owner.id
        }
      });
      return false;
    } else {
      logger.info(
        `There are no portfolio with cash or active withdrawals transactions for request ${request._id}`,
        {
          module: "UserDataRequestService",
          method: "_withdrawFromAllPortfolios",
          data: {
            requestId: request._id,
            userId: request.owner.id
          }
        }
      );
      return true;
    }
  }

  /**
   * Sells all holdings from the user's portfolios. Returns true if all portfolios are fully sold, false otherwise.
   * @param request
   * @private
   */
  private static async _sellAllPortfolios(request: UserDataRequestDocument): Promise<boolean> {
    const user = request.owner as UserDocument;

    const pendingTransactions = (await Transaction.find({
      owner: user.id,
      status: "Pending",
      category: { $in: ["AssetTransaction", "SavingsWithdrawalTransaction"] },
      linkedUserDataRequest: request._id
    })) as TransactionDocument[];

    if (pendingTransactions.length > 0) {
      // We have already started the portfolio sell process but the transaction(s) is still pending.
      return false;
    }

    const portfolios = await PortfolioService.getRealPortfolios(user.id);
    const portfoliosWithAvailableHoldings = (
      await Promise.all(
        portfolios.map(async (portfolio) => {
          return {
            ...portfolio.toObject(),
            availableHoldings: await PortfolioService.getAvailableHoldings(portfolio)
          } as PortfolioDocument & { availableHoldings: HoldingsType[] };
        })
      )
    ).filter((portfolio) => portfolio.availableHoldings.length > 0);

    let allHoldingsSold = true;
    if (portfoliosWithAvailableHoldings.length > 0) {
      await Promise.all(
        portfoliosWithAvailableHoldings.map((portfolio) =>
          PortfolioService.sellWholePortfolio(portfolio.id, {
            linkedUserDataRequest: request.id,
            executeEtfOrdersInRealtime: user.isRealtimeETFExecutionEnabled
          })
        )
      );
      logger.info(`Created portfolio sell asset transactions for for request ${request._id}...`, {
        module: "UserDataRequestService",
        method: "_sellAllPortfolios",
        data: {
          requestId: request._id,
          userId: request.owner.id
        }
      });
      allHoldingsSold = false;
    }

    let allSavingsSold = true;
    const portfoliosWithSavings = portfolios.filter((portfolio) =>
      Array.from(portfolio?.savings?.values()).some((saving) => saving?.amount > 0)
    );
    if (portfoliosWithSavings.length > 0) {
      await Promise.all(
        portfolios.map((portfolio) =>
          PortfolioService.withdrawAllSavings(portfolio, { linkedUserDataRequest: request })
        )
      );
      logger.info(`Created portfolio savings withdrawal transactions for for request ${request._id}...`, {
        module: "UserDataRequestService",
        method: "_sellAllPortfolios",
        data: {
          requestId: request._id,
          userId: request.owner.id
        }
      });
      allSavingsSold = false;
    }

    return allHoldingsSold && allSavingsSold;
  }

  /**
   * Reverts all user rewards. Returns true if all rewards are fully sold & transferred to our portfolio, false otherwise.
   * @param request
   * @private
   */
  private static async _revertAllRewards(request: UserDataRequestDocument): Promise<boolean> {
    const user = request.owner as UserDocument;

    const restrictedRewards = await Reward.find({
      targetUser: user.id,
      unrestrictedAt: { $gt: new Date() },
      accepted: true
    }).populate("targetUser");

    const linkedRevertRewardTransactions = (await TransactionService.getRevertRewardTransactions({
      owner: user._id,
      linkedUserDataRequest: request._id
    })) as RevertRewardTransactionDocument[];

    const allRestrictedRewardsHaveBeenReverted = restrictedRewards.every((reward) =>
      linkedRevertRewardTransactions.some(
        (revertRewardTransaction) =>
          revertRewardTransaction.status === "Settled" && revertRewardTransaction.reward.toString() === reward.id
      )
    );

    // We return true if:
    // 1. all revert reward transactions are settled (even if they are not linked to a reward that's currently restricted,
    // as they might be linked to a reward that was restricted when the user data request was made but then got unrestricted)
    // 2. AND each restricted reward has a corresponding revert reward transaction that is settled
    if (
      linkedRevertRewardTransactions.every(
        (revertRewardTransaction) => revertRewardTransaction.status === "Settled"
      ) &&
      allRestrictedRewardsHaveBeenReverted
    ) {
      {
        logger.info(`All reward transactions have been reverted for request ${request._id}...`, {
          module: "UserDataRequestService",
          method: "_revertAllRewards",
          data: {
            requestId: request._id,
            userId: request.owner.id
          }
        });
        return true;
      }
    } else {
      // Create a revert reward transaction for each restricted reward which doesn't have one
      const restrictedRewardCreationPromises = restrictedRewards
        .filter(
          (reward) =>
            !linkedRevertRewardTransactions.some(
              (revertRewardTransaction) => revertRewardTransaction.reward.toString() === reward.id
            ) && reward.status == "Settled"
        )
        .map(async (reward) => {
          const portfolio = await PortfolioService.getGeneralInvestmentPortfolio(
            reward.targetUser as UserDocument
          );
          return TransactionService.createRevertRewardTransaction(portfolio, reward.id, request.id);
        });

      const createdTransactions = await Promise.all(restrictedRewardCreationPromises);

      logger.info(`Created revert reward transactions for for request ${request._id}...`, {
        module: "UserDataRequestService",
        method: "_revertAllRewards",
        data: {
          requestId: request._id,
          userId: request.owner.id,
          rewards: createdTransactions.map((transaction) => transaction.reward.toString())
        }
      });

      return false;
    }
  }

  /**
   * Closes all the user's portfolios by:
   * 1) Requesting closure for all portfolios that are active in our database
   * 2) If a portfolio is 'Closing' in our database, then sync with Wealthkernel until closed
   * 3) If all portfolios are 'Closed', then return true. Else, return false.
   * @param request
   * @private
   */
  private static async _closeAllPortfolios(request: UserDataRequestDocument): Promise<boolean> {
    const user = request.owner as UserDocument;
    const portfoliosToClose = (await PortfolioService.getRealPortfolios(user.id)).filter(
      (portfolio) => portfolio?.providers?.wealthkernel?.id
    );

    if (
      portfoliosToClose.length > 0 &&
      !portfoliosToClose.every((portfolio) => portfolio.providers?.wealthkernel?.status === "Closed")
    ) {
      // We want to request the closing of all active portfolios in WK.
      const activePortfolios = portfoliosToClose.filter(
        (portfolio) => portfolio.providers?.wealthkernel?.status === "Active"
      );
      await Promise.all(
        activePortfolios.map((portfolio) => UserDataRequestService._closePortfolio(portfolio, request))
      );

      // If we've already requested the closing of a portfolio, we want to sync with WK to ensure it's Closed.
      const closingPortfolios = portfoliosToClose.filter(
        (portfolio) => portfolio.providers?.wealthkernel?.status === "Closing"
      );
      await Promise.all(
        closingPortfolios.map((portfolio) => UserDataRequestService._syncClosingPortfolio(portfolio, request))
      );

      return false;
    } else {
      logger.info(`All portfolios for user request ${request._id} are closed`, {
        module: "UserDataRequestService",
        method: "_closeAllPortfolios",
        data: {
          requestId: request._id,
          userId: request.owner.id
        }
      });
      return true;
    }
  }

  private static async _syncClosingPortfolio(portfolio: PortfolioDocument, request: UserDataRequestDocument) {
    const user = request.owner as UserDocument;

    const wealthkernelPortfolio = await ProviderService.getBrokerageService(user.companyEntity).retrievePortfolio(
      portfolio.providers?.wealthkernel?.id
    );
    if (wealthkernelPortfolio.status === "Closed") {
      await Portfolio.findByIdAndUpdate(portfolio.id, { "providers.wealthkernel.status": "Closed" });
      logger.info(`Closed portfolio for user data request ${request._id}`, {
        module: "UserDataRequestService",
        method: "_syncClosingPortfolio",
        data: {
          requestId: request._id,
          userId: request.owner.id,
          wealthkernelId: portfolio.providers?.wealthkernel?.id
        }
      });
    }
  }

  /**
   * Closes all the user's accounts by:
   * 1) Requesting closure for all accounts that are active in our database
   * 2) If an account is 'Closing' in our database, then sync with Wealthkernel until closed
   * 3) If all accounts are 'Closed', then return true. Else, return false.
   * @param request
   * @private
   */
  private static async _closeAllAccounts(request: UserDataRequestDocument): Promise<boolean> {
    const user = request.owner as UserDocument;
    const accountsToClose = (await AccountService.getAccounts(user.id)).filter(
      (account) => account?.providers?.wealthkernel?.id
    );

    if (
      accountsToClose.length > 0 &&
      !accountsToClose.every((account) => account.providers.wealthkernel.status === "Closed")
    ) {
      // We want to request the closing of all active accounts in WK.
      const activeAccounts = accountsToClose.filter(
        (account) => account.providers.wealthkernel.status === "Active"
      );
      await Promise.all(activeAccounts.map((account) => UserDataRequestService._closeAccount(account, request)));

      return false;
    } else {
      logger.info(`All accounts for user request ${request._id} are closed`, {
        module: "UserDataRequestService",
        method: "_closeAllAccounts",
        data: {
          requestId: request._id,
          userId: request.owner.id
        }
      });
      return true;
    }
  }

  private static async _deleteUserDocument(request: UserDataRequestDocument) {
    const user = request.owner as UserDocument;

    await UserService.deleteUser(user.id);
    logger.info(`Deleted user document for request ${request._id}...`, {
      module: "UserDataRequestService",
      method: "_deleteUserDocument",
      data: {
        requestId: request._id,
        userId: request.owner.id
      }
    });
  }

  private static async _deleteParticipantDocument(request: UserDataRequestDocument) {
    const user = request.owner as UserDocument;
    const participant = await ParticipantService.getParticipantByEmail(user.email);

    if (participant) {
      await ParticipantService.deleteParticipant(participant.id);
      logger.info(`Deleted participant document for request ${request._id}...`, {
        module: "UserDataRequestService",
        method: "_deleteParticipantDocument",
        data: {
          requestId: request._id,
          userId: request.owner.id,
          participantId: participant.id
        }
      });
    }
  }

  private static async _closePortfolio(portfolio: PortfolioDocument, request: UserDataRequestDocument) {
    const user = request.owner as UserDocument;

    if (portfolio.holdings.length > 0) {
      throw new BadRequestError("User has holdings");
    } else if (portfolio?.cash?.[user.currency]?.available > 0) {
      throw new BadRequestError("User has available cash");
    }

    const latestWealthkernelValuation = await ProviderService.getBrokerageService(
      user.companyEntity
    ).retrieveLatestValuationByPortfolio(portfolio.providers?.wealthkernel?.id);

    if (!latestWealthkernelValuation) {
      logger.warn(`Valuation wasn't retrieved for ${portfolio.providers?.wealthkernel?.id}`, {
        module: "UserDataRequestService",
        method: "_closePortfolio",
        data: { wealthkernelPortfolioId: portfolio.providers?.wealthkernel?.id }
      });
      return;
    }

    const cashAmount = latestWealthkernelValuation.cash[0]?.value?.amount ?? 0;
    const holdingsLength = latestWealthkernelValuation.holdings.length;
    if (holdingsLength > 0) {
      throw new BadRequestError("User has holdings in Wealthkernel");
    } else if (cashAmount > 0) {
      // A user having cash in WK is not an error scenario - as they could have fees that have not been transferred
      // yet. Therefore, in this case, we just log a warning and return.
      logger.info(`Skipping user request ${request._id} as user has Wealthkernel cash`, {
        module: "UserDataRequestService",
        method: "_closePortfolio",
        data: { requestId: request._id, userId: request.owner.id, cashAmount }
      });
      return;
    }

    await ProviderService.getBrokerageService(user.companyEntity).closePortfolio(
      portfolio.providers?.wealthkernel?.id
    );

    await Portfolio.findByIdAndUpdate(portfolio.id, { "providers.wealthkernel.status": "Closing" });

    logger.info(`Requested the closing of portfolio ${portfolio.id} for user data request ${request._id}`, {
      module: "UserDataRequestService",
      method: "_closePortfolio",
      data: {
        requestId: request._id,
        userId: request.owner.id,
        wealthkernelId: portfolio.providers?.wealthkernel?.id
      }
    });
  }

  private static async _closeAccount(account: AccountDocument, request: UserDataRequestDocument) {
    const user = request.owner as UserDocument;

    await ProviderService.getBrokerageService(user.companyEntity).closeAccount(account.providers.wealthkernel.id);

    await Account.findByIdAndUpdate(account.id, { "providers.wealthkernel.status": "Closing" });

    logger.info(`Requested the closing of account ${account.id} for user data request ${request._id}`, {
      module: "UserDataRequestService",
      method: "_closeAccount",
      data: {
        requestId: request._id,
        userId: request.owner.id,
        wealthkernelId: account.providers.wealthkernel.id
      }
    });
  }

  private static async _unsubscribeFromMailchimp(request: UserDataRequestDocument, audience: AudienceIdEnum) {
    const user = request.owner as UserDocument;

    if (user.isDeleted) {
      logger.info(
        `Not unsubscribing member with user email ${user.email} for request ${request._id} as it is already disassociated and therefore unsubscribed`,
        {
          module: "UserDataRequestService",
          method: "_unsubscribeFromMailchimp",
          userEmail: user.email,
          data: {
            requestId: request._id,
            userId: request.owner.id
          }
        }
      );
      return;
    }

    const mailchimpUser = await MailchimpService.retrieveMember(user.email, audience, { silent: true });
    if (!mailchimpUser) {
      logger.info(
        `We were not able to retrieve from mailchimp the user with user email ${user.email} for request ${request._id} - has probably been deleted`,
        {
          module: "UserDataRequestService",
          method: "_unsubscribeFromMailchimp",
          userEmail: user.email,
          data: {
            requestId: request._id,
            userId: request.owner.id
          }
        }
      );
    } else if (mailchimpUser?.status === "unsubscribed") {
      logger.info(
        `Not unsubscribing member with user email ${user.email} for request ${request._id} as it is already unsubscribed`,
        {
          module: "UserDataRequestService",
          method: "_unsubscribeFromMailchimp",
          userEmail: user.email,
          data: {
            requestId: request._id,
            userId: request.owner.id
          }
        }
      );
    } else {
      await MailchimpService.updateMember(user.email, { status: "unsubscribed" }, audience);
      logger.info(
        `Unsubscribed Mailchimp member ${user.email} for request ${request._id}... for specified audiences`,
        {
          module: "UserDataRequestService",
          method: "_unsubscribeFromMailchimp",
          userEmail: user.email,
          data: {
            requestId: request._id,
            userId: request.owner.id,
            audienceId: audience
          }
        }
      );
    }
  }

  private static async _deleteFromMailchimp(request: UserDataRequestDocument, audience: AudienceIdEnum) {
    const user = request.owner as UserDocument;

    if (user.isDeleted) {
      logger.info(
        `Not deleting member with user email ${user.email} for request ${request._id} as it is already disassociated and therefore deleted in Mailchimp`,
        {
          module: "UserDataRequestService",
          method: "_deleteFromMailchimp",
          userEmail: user.email,
          data: {
            requestId: request._id,
            userId: request.owner.id,
            audienceId: audience
          }
        }
      );
      return;
    }

    const mailchimpUser = await MailchimpService.retrieveMember(user.email, audience, { silent: true });
    if (!mailchimpUser) {
      logger.info(
        `Not deleting member with user email ${user.email} for request ${request._id} as it is already deleted`,
        {
          module: "UserDataRequestService",
          method: "_deleteFromMailchimp",
          userEmail: user.email,
          data: {
            requestId: request._id,
            userId: request.owner.id
          }
        }
      );
    } else {
      await MailchimpService.deleteMember(user.email, audience);
      logger.info(`Deleted Mailchimp member ${user.email} for request ${request._id}...`, {
        module: "UserDataRequestService",
        method: "_deleteFromMailchimp",
        userEmail: user.email,
        data: {
          requestId: request._id,
          userId: request.owner.id
        }
      });
    }
  }

  private static async _deleteFromAuth0(request: UserDataRequestDocument) {
    const user = request.owner as UserDocument;

    try {
      await auth0ManagementClient.users.get({ id: user.auth0.id });
    } catch (err) {
      if (err.statusCode === 404) {
        logger.info(
          `Not deleting Auth0 user with user ID ${request.owner.id} for request ${request._id} as it is already deleted`,
          {
            module: "UserDataRequestService",
            method: "_deleteFromAuth0",
            data: {
              requestId: request._id,
              userId: request.owner.id,
              userAuthId: user.auth0.id
            }
          }
        );
        return;
      } else throw err;
    }

    await auth0ManagementClient.users.delete({ id: user.auth0.id });
    logger.info(`Deleted Auth0 user with user ID ${request.owner.id} for request ${request._id}...`, {
      module: "UserDataRequestService",
      method: "_deleteFromMailchimp",
      data: {
        requestId: request._id,
        userId: request.owner.id,
        userAuthId: user.auth0.id
      }
    });
  }

  private static async _cancelAllUserMandates(request: UserDataRequestDocument) {
    const owner = request.owner.id;
    const userMandates = await MandateService.getMandates({
      owner
    });

    if (!userMandates.length || !userMandates.filter((mandate) => mandate.status !== "Inactive").length) {
      logger.info(`There are no active mandates for request ${request._id}...`, {
        module: "UserDataRequestService",
        method: "_cancelAllUserMandates",
        data: {
          requestId: request._id,
          userId: request.owner.id
        }
      });
      return;
    }

    for (const mandate of userMandates) {
      if (mandate.status !== "Inactive") {
        await MandateService.cancelMandate(mandate);

        logger.info(`Cancelled mandate for request ${request._id}...`, {
          module: "UserDataRequestService",
          method: "_cancelAllUserMandates",
          data: {
            requestId: request._id,
            userId: request.owner.id,
            mandateId: mandate.id
          }
        });
      }
    }
  }

  private static async _cancelAllUserAutomations(request: UserDataRequestDocument): Promise<void> {
    const owner = request.owner.id;
    const { data: userAutomations } = await AutomationService.getAutomations({
      activeOnly: true,
      owner
    });

    if (!userAutomations.length) {
      logger.info(`There are no active automations for request ${request._id}...`, {
        module: "UserDataRequestService",
        method: "_cancelAllUserAutomations",
        data: {
          requestId: request._id,
          userId: request.owner.id
        }
      });
      return;
    }

    for (const automation of userAutomations) {
      if (automation.active) {
        await AutomationService.cancelAutomation(automation);

        logger.info(`Cancelled automation for request ${request._id}...`, {
          module: "UserDataRequestService",
          method: "_cancelAllUserAutomations",
          data: {
            requestId: request._id,
            userId: request.owner.id,
            automationId: automation.id
          }
        });
      }
    }
  }

  private static _createUserDataRequestsDbFilter(filter: UserDataRequestsFilter) {
    const actualFilter: {
      createdAt?: { $gte: Date; $lt: Date };
      status?: { $in: (UserDataRequestStatusType | null)[] };
      owner?: string;
    } = {
      createdAt: null,
      status: null,
      owner: null
    };

    Object.keys(actualFilter).forEach((key) => {
      if ((filter as any)[key] != null) {
        (actualFilter as any)[key] = (filter as any)[key];
      }
    });

    if (filter.creationDate) {
      actualFilter["createdAt"] = {
        $gte: filter.creationDate.startDate ?? new Date("1970-01-01T00:00:00Z"),
        $lt: filter.creationDate.endDate ?? new Date(Date.now())
      };
    }

    if (filter.statuses) {
      actualFilter["status"] = {
        $in: [...filter.statuses]
      };
    }

    return Object.fromEntries(Object.entries(actualFilter).filter(([, value]) => value != null));
  }
}
