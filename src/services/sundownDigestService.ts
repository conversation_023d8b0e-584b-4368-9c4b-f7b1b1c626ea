import { QueryOptions } from "mongoose";
import { SundownDigest, SundownDigestDocument } from "../models/SundownDigest";
import type { SundownDigestFilter } from "types/filters";
import DbUtil from "../utils/dbUtil";

export default class SundownDigestService {
  /**
   * PUBLIC METHODS
   */
  public static async getSundownDigests(
    filter: SundownDigestFilter = {},
    sort?: string
  ): Promise<SundownDigestDocument[]> {
    const dbFilter = this._createSundownDigestsDbFilter(filter);

    const options: QueryOptions = {};
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }

    return SundownDigest.find(dbFilter, null, options);
  }

  public static async getLatestSundownDigest(): Promise<SundownDigestDocument> {
    return (await SundownDigest.find().sort({ date: -1 }).limit(1))?.at(0);
  }

  /**
   * PRIVATE METHODS
   */
  private static _createSundownDigestsDbFilter(filter: SundownDigestFilter) {
    const dbFilter = {
      date: null as any
    };

    if (filter.date) {
      dbFilter["date"] = {
        $gte: filter.date.startDate,
        $lt: filter.date.endDate
      };
    }

    return dbFilter
      ? Object.fromEntries(
          Object.entries(dbFilter).filter(
            ([key, value]) => key != "status" && value !== undefined && value !== null
          )
        )
      : {};
  }
}
