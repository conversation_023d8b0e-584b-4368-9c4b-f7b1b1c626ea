import Stripe from "stripe";
import {
  Subscription,
  SubscriptionDocument,
  SubscriptionDTOInterface,
  SubscriptionInterface
} from "../models/Subscription";
import { ChargeTransactionsFilter, SubscriptionsFilter } from "filters";
import PortfolioService from "./portfolioService";
import Decimal from "decimal.js";
import { PortfolioDocument } from "../models/Portfolio";
import {
  ChargeTransaction,
  ChargeTransactionDocument,
  ChargeTransactionDTOInterface,
  TransactionStatusType
} from "../models/Transaction";
import { TransactionService } from "./transactionService";
import OrderService, { SellOrderFilteringMethodEnum } from "./orderService";
import { entitiesConfig, fees, plansConfig } from "@wealthyhood/shared-configs";
import { UserDocument } from "../models/User";
import logger from "../external-services/loggerService";
import { captureException } from "@sentry/node";
import UserService from "./userService";
import ConfigUtil from "../utils/configUtil";
import { envIsProd, getEnvironment } from "../utils/environmentUtil";
import DateUtil from "../utils/dateUtil";
import DailyTickerService from "./dailyTickerService";
import { DailyPortfolioTickerDocument } from "../models/DailyTicker";
import { BadRequestError, InternalServerError } from "../models/ApiErrors";
import DbUtil from "../utils/dbUtil";
import eventEmitter from "../loaders/eventEmitter";
import events from "../event-handlers/events";
import { TrackPlanType } from "../external-services/segmentAnalyticsService";
import {
  isDowngrade,
  isDowngradeToFree,
  isDowngradeToPaidLow,
  isOnCardSubscription,
  isOnLifetimeSubscription,
  isUpdateOfRecurrence,
  isUpdateToALifetimePrice,
  isUpgradeFromPaidLow
} from "../utils/planUtil";
import ProviderService, { ProviderScopeEnum } from "./providerService";
import { PaymentIntentStatusType, StripeService } from "../external-services/stripeService";
import PaymentMethodService from "./paymentMethodService";
import { PaymentMethod, PaymentMethodDocument, PaymentMethodTypeEnum, WalletEnum } from "../models/PaymentMethod";
import { WealthyhoodToStripePrices } from "../configs/stripeConfig";
import { OrderDTOInterface } from "../models/Order";
import { getZeroFees } from "../utils/feesUtil";
import { ProviderEnum } from "../configs/providersConfig";
import { CardBasedSubscriptionPaymentService } from "./subscriptionPaymentService";

const { CUSTODY_RATES, MINIMUM_CUSTODY_FEE } = fees;

type InitiateStripeType = InitiateStripeWithInvoiceType | InitiateStripeWithFreeTrialType;

type InitiateStripeWithInvoiceType = {
  clientSecret: string;
  paymentIntentId: string;
};

type InitiateStripeWithFreeTrialType = {
  subscription: SubscriptionDocument;
};

export interface UpdateSubscriptionData {
  category: plansConfig.SubscriptionCategoryType;
  mandate?: string;
  price?: plansConfig.PriceType;
}

export default class SubscriptionService {
  /**
   * Activates the subscription of the given user.
   * @param owner
   */
  public static async activateSubscription(owner: string): Promise<SubscriptionInterface> {
    return Subscription.findOneAndUpdate({ owner }, { active: true });
  }

  /**
   * Creates a subscription document with the given DTO data.
   * @param subscriptionData
   */
  public static async createSubscription(
    subscriptionData: SubscriptionDTOInterface
  ): Promise<SubscriptionDocument> {
    const owner = await UserService.getUser(subscriptionData.owner.toString());

    SubscriptionService._validateSubscriptionPaymentMethod(
      subscriptionData.price,
      subscriptionData.category,
      owner.companyEntity
    );

    // For single payment subscriptions, we create a free subscription initially, as this will get upgraded to a lifetime
    // subscription when the single payment succeeds.
    if (subscriptionData.category === "SinglePaymentSubscription") {
      subscriptionData = {
        ...subscriptionData,
        price: "free_monthly",
        category: "FeeBasedSubscription"
      };
    }

    const subscription = await Subscription.findOneAndUpdate({ owner: subscriptionData.owner }, subscriptionData, {
      setDefaultsOnInsert: true,
      upsert: true,
      new: true
    });

    // For single payment subscriptions, we are going to emit the event when the user completes their single payment
    if (subscriptionData.category !== "SinglePaymentSubscription") {
      await this._emitPlanEvent(events.plan.planUpgrade.eventId, subscription);
    }

    return subscription;
  }

  /**
   * Downgrades/upgrades a subscription.
   * There are several cases that fall into this flow:
   * 1. Updating to a lifetime subscription: This is a no/op flow, as the actual update of the subscription happens when
   * the Truelayer lifetime payment is successful.
   * 2. Downgrading from a lifetime subscription: Downgrades instantly
   * 3. Downgrading from a card-based subscription to beginner: Downgrades using Stripe cancellation on period end
   * 4. Downgrading from a card-based subscription to another card-based subscription: Downgrades instantly using the
   * Stripe API, which adds credit to the user's balance, that will discount their future payments.
   * 5. Upgrading from a card-based subscription to another card-based subscription: Upgrades instantly using the Stripe
   * API, which charges the user automatically a difference based on the days they've spent in their current plan.
   * 6. Updating the billing period of a subscription (e.g. from monthly -> yearly).
   * @param id
   * @param subscriptionData
   */
  public static async updateSubscription(
    id: string,
    subscriptionData: UpdateSubscriptionData
  ): Promise<SubscriptionInterface> {
    const currentSubscription = await SubscriptionService.getSubscriptionById(id);
    const owner = await UserService.getUser(currentSubscription.owner.toString());

    const PRICE_CONFIG = ConfigUtil.getPricing(owner.companyEntity);
    const PLAN_CONFIG = ConfigUtil.getPlans(owner.companyEntity);

    SubscriptionService._validateSubscriptionPaymentMethod(
      subscriptionData.price,
      subscriptionData.category,
      owner.companyEntity
    );

    const oldPlan = PRICE_CONFIG[currentSubscription.price].plan;
    const newPlan = PRICE_CONFIG[subscriptionData.price].plan;

    if (isUpdateToALifetimePrice(subscriptionData.price, PRICE_CONFIG)) {
      logger.info(
        `User requested update of subscription ${currentSubscription.id} to a lifetime ${newPlan}, will update after their payment is complete`,
        {
          module: "SubscriptionService",
          method: "updateSubscription",
          data: subscriptionData
        }
      );

      return currentSubscription;
    }

    if (isDowngrade(oldPlan, newPlan, PLAN_CONFIG)) {
      if (isOnLifetimeSubscription(currentSubscription, PRICE_CONFIG)) {
        logger.warn(`Downgrading subscription ${currentSubscription.id} FROM a lifetime ${newPlan}`, {
          module: "SubscriptionService",
          method: "updateSubscription",
          data: subscriptionData
        });

        // If the user is downgrading from a lifetime plan to free, we do that downgrade. However, if they downgrade
        // from a lifetime plan to a paid monthly plan, we switch them to the respective lifetime plan instead.
        const priceToDowngradeTo =
          subscriptionData.price === "free_monthly"
            ? "free_monthly"
            : Object.values(PRICE_CONFIG).find(
                ({ plan, recurrence }) => plan === newPlan && recurrence === "lifetime"
              ).keyName;

        const subscription = await SubscriptionService.downgradeSubscription(
          currentSubscription,
          priceToDowngradeTo
        );

        // Downgrades from lifetime plans happen immediately, so in this case, we emit both downgrade init and completion events.
        await this._emitPlanEvent(events.plan.planDowngradeInit.eventId, subscription, currentSubscription);
        await this._emitPlanEvent(events.plan.planDowngradeCompletion.eventId, subscription, currentSubscription);

        return subscription;
      }

      if (isOnCardSubscription(currentSubscription) && subscriptionData.price === "free_monthly") {
        const { current_period_end } = await ProviderService.getSubscriptionPaymentService(
          owner.companyEntity
        ).cancelCardSubscriptionOnPeriodEnd(currentSubscription.cardBasedProviderId);

        const subscription = await SubscriptionService.startDowngrade(
          currentSubscription,
          subscriptionData.price,
          { periodEndOverride: new Date(current_period_end * 1000) }
        );

        await this._emitPlanEvent(events.plan.planDowngradeInit.eventId, subscription, currentSubscription);

        return subscription;
      }

      if (isOnCardSubscription(currentSubscription) && subscriptionData.price !== "free_monthly") {
        await ProviderService.getSubscriptionPaymentService(owner.companyEntity).downgradeCardSubscription(
          currentSubscription.cardBasedProviderId,
          owner.currency,
          subscriptionData.price
        );
        const subscription = await SubscriptionService.downgradeSubscription(
          currentSubscription,
          subscriptionData.price
        );

        await this._emitPlanEvent(events.plan.planDowngradeInit.eventId, subscription, currentSubscription);
        await this._emitPlanEvent(events.plan.planDowngradeCompletion.eventId, subscription, currentSubscription);

        return subscription;
      }
    }

    if (
      isUpdateOfRecurrence(currentSubscription.price, subscriptionData.price, PRICE_CONFIG) &&
      isOnCardSubscription(currentSubscription)
    ) {
      const { current_period_end } = await ProviderService.getSubscriptionPaymentService(
        owner.companyEntity
      ).updateCardSubscription(currentSubscription.cardBasedProviderId, owner.currency, subscriptionData.price);

      const subscription = await Subscription.findByIdAndUpdate(
        currentSubscription._id,
        {
          price: subscriptionData.price,
          category: subscriptionData.category,
          nextChargeAt: new Date(current_period_end * 1000),
          $unset: { expiration: true }
        },
        {
          upsert: false,
          new: true
        }
      ).populate("owner");

      await this._emitPlanEvent(events.plan.planUpgrade.eventId, subscription, currentSubscription);

      return subscription;
    }

    if (isUpgradeFromPaidLow(oldPlan, newPlan, PLAN_CONFIG) && isOnCardSubscription(currentSubscription)) {
      await ProviderService.getSubscriptionPaymentService(owner.companyEntity).updateCardSubscription(
        currentSubscription.cardBasedProviderId,
        owner.currency,
        subscriptionData.price
      );

      const subscription = await Subscription.findByIdAndUpdate(
        currentSubscription._id,
        {
          price: subscriptionData.price,
          category: subscriptionData.category,
          $unset: { expiration: true }
        },
        {
          upsert: false,
          new: true
        }
      ).populate("owner");

      await this._emitPlanEvent(events.plan.planUpgrade.eventId, subscription, currentSubscription);

      return subscription;
    }

    // If the method has not returned yet, then the user is making an update that is not allowed.
    throw new InternalServerError(
      `The user should not be updating from ${currentSubscription.price}/${currentSubscription.category} to ${subscriptionData.price}/${subscriptionData.category}`
    );
  }

  /**
   * Updates the nextChargeAt field of the subscription based on the passed date.
   * @param subscriptionId
   * @param date
   */
  public static async updateNextChargeAt(subscriptionId: string, date: Date): Promise<SubscriptionDocument> {
    return Subscription.findByIdAndUpdate(
      subscriptionId,
      {
        nextChargeAt: date
      },
      { new: true }
    );
  }

  /**
   * Updates a subscription to lifetime after a successful payment.
   * @param subscriptionId
   * @param price
   */
  public static async redeemLifetimePayment(
    subscriptionId: string,
    price: plansConfig.PriceType
  ): Promise<SubscriptionInterface> {
    logger.info(`Updating ${subscriptionId} to a ${price}, as their lifetime payment was successful`, {
      module: "SubscriptionService",
      method: "redeemLifetimePayment"
    });

    if (!price) {
      throw new Error("Price is required when redeeming a lifetime payment");
    }

    const currentSubscription = await Subscription.findById(subscriptionId).populate("owner");
    if (currentSubscription.price === price) {
      throw new Error(
        `Subscription ${subscriptionId} already has price ${price} but we're trying to redeem a lifetime payment for the same price`
      );
    }

    if (currentSubscription.category === "CardPaymentSubscription") {
      await ProviderService.getSubscriptionPaymentService(
        (currentSubscription.owner as UserDocument).companyEntity
      ).cancelCardSubscriptionNow(currentSubscription?.cardBasedProviderId);
    }

    const updatedSubscription = await Subscription.findByIdAndUpdate(
      currentSubscription._id,
      {
        category: "SinglePaymentSubscription",
        price,
        $unset: { mandate: true, expiration: true, nextChargeAt: true, providers: true }
      },
      {
        upsert: false,
        new: true
      }
    ).populate("owner mandate");

    await this._emitPlanEvent(events.plan.planUpgrade.eventId, updatedSubscription, currentSubscription);

    return updatedSubscription;
  }

  /**
   * Renews a subscription, i.e. cancels the downgrading of a subscription, if expired is still in the future.
   * @param id
   */
  public static async renewSubscription(id: string): Promise<SubscriptionInterface> {
    const currentSubscription = await SubscriptionService.getSubscriptionById(id, { owner: true });

    if (
      !["DirectDebitSubscription", "CardPaymentSubscription"].includes(currentSubscription.category) ||
      !currentSubscription.expiration
    ) {
      throw new BadRequestError("Subscription has to be a recurring paid subscription that's expiring.");
    } else if (currentSubscription.expiration.date < new Date(Date.now())) {
      throw new BadRequestError("Subscription's expiration date has to be in the future");
    }

    logger.info(`Renewing subscription ${currentSubscription.id}`, {
      module: "SubscriptionService",
      method: "renewSubscription"
    });

    if (currentSubscription.category === "CardPaymentSubscription") {
      await ProviderService.getSubscriptionPaymentService(
        (currentSubscription.owner as UserDocument).companyEntity
      ).abortCancelCardSubscription(currentSubscription.cardBasedProviderId);
    }

    const updatedSubscription = Subscription.findByIdAndUpdate(
      currentSubscription._id,
      {
        $unset: { expiration: true }
      },
      {
        upsert: false,
        new: true
      }
    );

    logger.info(`Renewed subscription ${currentSubscription.id}!`, {
      module: "SubscriptionService",
      method: "renewSubscription"
    });

    return updatedSubscription;
  }

  /**
   * Returns the subscription of the given owner.
   * @param owner
   * @param populate
   */
  public static async getSubscription(
    owner: string,
    populate: { mandate: boolean } = { mandate: false }
  ): Promise<SubscriptionDocument> {
    const populationString = DbUtil.getPopulationString(populate);

    return Subscription.findOne({ owner }).populate(populationString);
  }

  /**
   * Returns the subscription with the given ID.
   * @param id
   * @param populate
   */
  public static async getSubscriptionById(
    id: string,
    populate: { owner: boolean } = { owner: false }
  ): Promise<SubscriptionDocument> {
    const populationString = DbUtil.getPopulationString(populate);

    return Subscription.findById(id).populate(populationString);
  }

  /**
   * Deactivates the subscription of the given user.
   * @param owner
   * @param options
   */
  public static async deactivateSubscription(
    owner: string,
    options?: { cancelCardBasedSubscription: boolean }
  ): Promise<SubscriptionInterface> {
    logger.info(`Deactivating subscription, if one exists, for user ${owner}`, {
      module: "SubscriptionService",
      method: "deactivateSubscription"
    });

    const subscription = await SubscriptionService.getSubscription(owner);

    if (subscription?.isCardBasedProviderActive && options?.cancelCardBasedSubscription) {
      await CardBasedSubscriptionPaymentService.Instance.cancelCardSubscriptionNow(
        subscription.cardBasedProviderId
      );
    }

    return Subscription.findOneAndUpdate({ owner }, { active: false });
  }

  /**
   * Returns all subscriptions passing the given filters.
   * @param filter
   * @param populate
   */
  public static async getSubscriptions(
    filter: SubscriptionsFilter = {},
    populate: { owner: boolean; mandate: boolean } = { owner: false, mandate: false }
  ): Promise<SubscriptionDocument[]> {
    const actualFilter = SubscriptionService._createDbFilter(filter);
    const populationString = DbUtil.getPopulationString(populate);

    return Subscription.find(actualFilter).populate(populationString);
  }

  /**
   * Returns a Mongoose cursor iterating through subscriptions passing the given filters.
   * @param filter
   */
  public static getSubscriptionsStreamed(filter: SubscriptionsFilter = {}) {
    const actualFilter = SubscriptionService._createDbFilter(filter);

    return Subscription.find(actualFilter).cursor().addCursorFlag("noCursorTimeout", envIsProd());
  }

  /**
   * Creates custody charge transactions for all users with a free plan active subscription.
   */
  public static async createCustodyFeeCharges(
    chargeMonth: string = DateUtil.getYearAndMonth(new Date())
  ): Promise<void> {
    await SubscriptionService.getSubscriptionsStreamed({
      active: true,
      prices: ["free_monthly"]
    }).eachAsync(async (subscription) => {
      try {
        const user = await UserService.getUser(subscription.owner.toString());
        if (!user) {
          logger.warn(`User '${subscription.owner.toString()}' not found`, {
            module: "SubscriptionService",
            method: "createCustodyFeeCharges"
          });
          return;
        } else if (!user.hasConvertedPortfolio) {
          return;
        }

        const portfolio: PortfolioDocument = await PortfolioService.getGeneralInvestmentPortfolio(user, true);

        // We retrieve existing custody charge for this month
        const existingCustodyCharges = (await TransactionService.getChargeTransactions({
          portfolio: portfolio._id,
          chargeTypes: ["custody"],
          chargeMonth
        } as ChargeTransactionsFilter)) as ChargeTransactionDocument[];

        // We only charge a portfolio if there are no custody charges for this month.
        if (existingCustodyCharges.length === 0) {
          await SubscriptionService._chargeCustodyFeesForPortfolio(portfolio, subscription.plan, chargeMonth);
        }
      } catch (err) {
        logger.error(`Could not charge subscription ${subscription.id}`, {
          module: "SubscriptionService",
          method: "createCustodyFeeCharges",
          data: subscription
        });
        captureException(err);
      }
    });
  }

  /**
   * Downgrades all subscriptions that have an expiration block with a date in the past.
   */
  public static async downgradeDirectDebitExpiredSubscriptions(): Promise<void> {
    await SubscriptionService.getSubscriptionsStreamed({
      category: "DirectDebitSubscription",
      expiredOnly: true
    }).eachAsync(async (subscription) => {
      const updatedSubscription = await SubscriptionService.downgradeSubscription(subscription);

      await this._emitPlanEvent(events.plan.planDowngradeCompletion.eventId, updatedSubscription, subscription);

      logger.info(`Downgraded subscription ${subscription.id} because it was expired.`, {
        module: "SubscriptionService",
        method: "downgradeDirectDebitExpiredSubscriptions",
        data: {
          subscription,
          updatedSubscription
        }
      });
    });
  }

  public static async initiateStripe(
    user: UserDocument,
    paymentMethodId: string,
    price: plansConfig.PriceType
  ): Promise<InitiateStripeType> {
    const customerId = user.providers?.stripe?.id;
    if (!customerId) {
      throw new InternalServerError("User does not have Stripe customer ID!");
    }

    let paymentMethod = await PaymentMethodService.getPaymentMethodByStripeId(paymentMethodId);
    if (!paymentMethod) {
      const stripePaymentMethod = await StripeService.Instance.retrievePaymentMethod(paymentMethodId);

      if (["apple_pay", "google_pay"].includes(stripePaymentMethod.card?.wallet?.type)) {
        // If Apple Pay or Google Pay is being used, then we don't have a payment method document for this.
        paymentMethod = await new PaymentMethod({
          owner: user.id,
          lastFourDigits: stripePaymentMethod.card.last4,
          brand: stripePaymentMethod.card.brand,
          type: PaymentMethodTypeEnum.CARD,
          wallet: stripePaymentMethod.card?.wallet?.type,
          providers: {
            stripe: {
              id: stripePaymentMethod.id
            }
          }
        }).save();
      } else throw new InternalServerError("There is no such payment method!");
    }

    const currentSubscription = await SubscriptionService.getSubscription(user.id);
    const shouldUseFreeTrial = !currentSubscription?.hasUsedFreeTrial;

    return shouldUseFreeTrial
      ? await SubscriptionService._initiateStripeWithFreeTrial(user, price, paymentMethod, currentSubscription)
      : await SubscriptionService._initiateStripeWithInvoice(user, price, paymentMethod, currentSubscription);
  }

  public static async completeStripe(user: UserDocument, paymentIntentId: string): Promise<SubscriptionDocument> {
    const customerId = user.providers?.stripe?.id;
    if (!customerId) {
      throw new InternalServerError("User does not have Stripe customer ID!");
    }

    const paymentIntent = await StripeService.Instance.retrievePaymentIntent(paymentIntentId, [
      "invoice.subscription"
    ]);

    const transaction = await ChargeTransaction.findOne({ "providers.stripe.id": paymentIntentId });
    if (!transaction)
      throw new InternalServerError(
        `Stripe payment intent ID ${paymentIntentId} did not match any charge transaction`
      );

    const currentSubscription = await SubscriptionService.getSubscription(user.id);
    if (!currentSubscription)
      throw new InternalServerError(`Could not find subscription ${currentSubscription.id}!`);

    // Validate that status can be updated, based on payment lifecycle
    if (
      transaction.providers?.stripe?.status &&
      !SubscriptionService._validateStripePaymentStatusUpdate(
        transaction.providers?.stripe?.status as PaymentIntentStatusType,
        paymentIntent.status
      )
    ) {
      return currentSubscription;
    }

    let updatedChargeStatus: TransactionStatusType;
    if (paymentIntent.status === "succeeded") {
      updatedChargeStatus = "Settled";
    } else if (paymentIntent.status === "requires_payment_method") {
      updatedChargeStatus = "Rejected";
    } else {
      updatedChargeStatus = "Pending";
    }

    // Use previous payment status for filter, to ensure that is updated correctly and avoid concurrency issues
    const updatedTransaction = await ChargeTransaction.findOneAndUpdate(
      {
        "providers.stripe.id": transaction.providers?.stripe?.id,
        "providers.stripe.status": transaction.providers?.stripe?.status
      },
      { status: updatedChargeStatus, "providers.stripe.status": paymentIntent.status },
      {
        runValidators: true,
        upsert: false,
        new: true
      }
    );

    if (updatedTransaction?.providers?.stripe?.status === "succeeded") {
      logger.info(
        `Updating ${currentSubscription.id} to a ${transaction.price}, as their initial card payment was successful`,
        {
          module: "SubscriptionService",
          method: "completeStripe"
        }
      );

      const stripeSubscription = (paymentIntent.invoice as Stripe.Invoice).subscription as Stripe.Subscription;

      const updatedSubscription = await Subscription.findByIdAndUpdate(
        currentSubscription.id,
        {
          category: "CardPaymentSubscription",
          price: transaction.price,
          paymentMethod: updatedTransaction.paymentMethod,
          nextChargeAt: new Date(stripeSubscription.current_period_end * 1000),
          providers: {
            stripe: {
              id: stripeSubscription.id,
              status: stripeSubscription.status
            }
          }
        },
        {
          upsert: false,
          new: true
        }
      ).populate("owner");

      await this._emitPlanEvent(events.plan.planUpgrade.eventId, updatedSubscription, currentSubscription);

      return updatedSubscription;
    }

    return currentSubscription;
  }

  /**
   * Starts the downgrading of a subscription. Since the user already paid for a whole month, their subscription remains
   * in their current plan until an expiration date which is set as the next charge date. On the date of the expiration
   * we actually move their subscription to their new selected plan.
   *
   * @param currentSubscription
   * @param newPrice
   * @param options
   */
  public static async startDowngrade(
    currentSubscription: SubscriptionDocument,
    newPrice: plansConfig.PriceType,
    options: {
      periodEndOverride?: Date;
    } = {}
  ): Promise<SubscriptionDocument> {
    return Subscription.findByIdAndUpdate(
      currentSubscription._id,
      {
        expiration: {
          date: options?.periodEndOverride ?? currentSubscription.nextChargeAt,
          downgradesTo: newPrice
        }
      },
      { upsert: false, new: true }
    );
  }

  public static async updatePaymentMethod(
    user: UserDocument,
    paymentMethodId: string
  ): Promise<SubscriptionDocument> {
    const currentSubscription = await SubscriptionService.getSubscription(user.id);
    if (!currentSubscription)
      throw new InternalServerError(`Could not find subscription ${currentSubscription.id}!`);

    if (currentSubscription.category !== "CardPaymentSubscription") {
      throw new InternalServerError(`Subscription ${currentSubscription.id} is not card-based!`);
    }

    let paymentMethod = await PaymentMethodService.getPaymentMethodByStripeId(paymentMethodId);
    if (!paymentMethod) {
      const stripePaymentMethod = await StripeService.Instance.retrievePaymentMethod(paymentMethodId);

      if (["apple_pay", "google_pay"].includes(stripePaymentMethod.card?.wallet?.type)) {
        // If Apple Pay or Google Pay is being used, then we don't have a payment method document for this.
        paymentMethod = await new PaymentMethod({
          owner: user.id,
          lastFourDigits: stripePaymentMethod.card.last4,
          brand: stripePaymentMethod.card.brand,
          type: PaymentMethodTypeEnum.CARD,
          wallet: stripePaymentMethod.card?.wallet?.type,
          providers: {
            stripe: {
              id: stripePaymentMethod.id
            }
          }
        }).save();
      } else throw new InternalServerError(`Could not find payment method ${paymentMethodId}!`);
    }

    await ProviderService.getSubscriptionPaymentService(user.companyEntity).updateCardSubscriptionPaymentMethod(
      currentSubscription.cardBasedProviderId,
      paymentMethod.providers.stripe.id
    );

    return Subscription.findByIdAndUpdate(
      currentSubscription.id,
      {
        paymentMethod: paymentMethod.id
      },
      {
        upsert: false,
        new: true
      }
    );
  }

  public static async getSubscriptionByStripeID(id: string): Promise<SubscriptionDocument> {
    return Subscription.findOne({ "providers.stripe.id": id });
  }

  /**
   * Completes the downgrading of a subscription.
   *
   * @param currentSubscription
   * @param downgradeTo
   */
  public static async downgradeSubscription(
    currentSubscription: SubscriptionDocument,
    downgradeTo?: plansConfig.PriceType
  ): Promise<SubscriptionDocument> {
    const priceToDowngradeTo = downgradeTo ?? currentSubscription.expiration?.downgradesTo;

    const owner = (
      currentSubscription.populated("owner")
        ? currentSubscription.owner
        : // we get owner separately to not be included in the http response
          await UserService.getUser(currentSubscription.owner.toString())
    ) as UserDocument;

    const PRICE_CONFIG = ConfigUtil.getPricing(owner.companyEntity);
    const PLAN_CONFIG = ConfigUtil.getPlans(owner.companyEntity);

    const oldPlan = PRICE_CONFIG[currentSubscription.price].plan;
    const newPlan = PRICE_CONFIG[priceToDowngradeTo].plan;

    let updatedSubscription: SubscriptionDocument;
    if (isDowngradeToFree(oldPlan, newPlan, PLAN_CONFIG)) {
      updatedSubscription = await Subscription.findByIdAndUpdate(
        currentSubscription._id,
        {
          category: "FeeBasedSubscription",
          price: priceToDowngradeTo,
          $unset: { mandate: true, expiration: true, nextChargeAt: true, providers: true }
        },
        {
          upsert: false,
          new: true
        }
      );
    } else if (isDowngradeToPaidLow(oldPlan, newPlan, PLAN_CONFIG)) {
      // We are downgrading to paid low from paid mid.
      updatedSubscription = await Subscription.findByIdAndUpdate(
        currentSubscription._id,
        {
          price: priceToDowngradeTo,
          $unset: { expiration: true }
        },
        {
          upsert: false,
          new: true
        }
      );
    } else throw new InternalServerError("The user should downgrading to either paid_low or free");

    return updatedSubscription;
  }

  /**
   * @description If the user is using free trial, then we don't have a charge transaction to create. Also, since there
   * is no payment to be made, we can immediately start a subscription with the new payment method & price.
   *
   * @param user
   * @param price
   * @param paymentMethod
   * @param currentSubscription
   * @private
   */
  private static async _initiateStripeWithFreeTrial(
    user: UserDocument,
    price: plansConfig.PriceType,
    paymentMethod: PaymentMethodDocument,
    currentSubscription: SubscriptionDocument
  ): Promise<InitiateStripeWithFreeTrialType> {
    const stripeSubscription = await StripeService.Instance.createSubscription(
      user.providers.stripe.id,
      WealthyhoodToStripePrices[user.currency][price][getEnvironment()],
      paymentMethod.providers.stripe.id,
      { withFreeTrial: true }
    );

    let subscription: SubscriptionDocument;
    if (!currentSubscription) {
      subscription = await SubscriptionService.createSubscription({
        // Subscription is created as active by default unless the user has failed the verification process
        active: !user?.hasFailedKyc,
        owner: user.id,
        category: "CardPaymentSubscription",
        price: price,
        paymentMethod: paymentMethod.id,
        hasUsedFreeTrial: true,
        nextChargeAt: new Date(stripeSubscription.current_period_end * 1000),
        providers: {
          stripe: {
            id: stripeSubscription.id,
            status: stripeSubscription.status
          }
        }
      });
    } else {
      subscription = await Subscription.findByIdAndUpdate(
        currentSubscription.id,
        {
          category: "CardPaymentSubscription",
          price: price,
          paymentMethod: paymentMethod.id,
          hasUsedFreeTrial: true,
          nextChargeAt: new Date(stripeSubscription.current_period_end * 1000),
          providers: {
            stripe: {
              id: stripeSubscription.id,
              status: stripeSubscription.status
            }
          }
        },
        {
          upsert: false,
          new: true
        }
      ).populate("owner");

      await this._emitPlanEvent(events.plan.planUpgrade.eventId, subscription, currentSubscription);
    }

    return {
      subscription
    };
  }

  /**
   * @description If the user is NOT using free trial, to initiate the subscription, we create a subscription
   * that required the first payment intent to be confirmed on the client-side.
   *
   * Therefore, we:
   * 1. Set up a charge transaction in pending state
   * 2. Return the payment intent data to be used in the client-side.
   * 3. Confirm the subscription update ONLY when the client calls complete-stripe with the confirmed payment.
   *
   * @param user
   * @param price
   * @param paymentMethod
   * @param currentSubscription
   * @private
   */
  private static async _initiateStripeWithInvoice(
    user: UserDocument,
    price: plansConfig.PriceType,
    paymentMethod: PaymentMethodDocument,
    currentSubscription: SubscriptionDocument
  ): Promise<InitiateStripeType> {
    const stripeSubscription = await StripeService.Instance.createSubscription(
      user.providers.stripe.id,
      WealthyhoodToStripePrices[user.currency][price][getEnvironment()],
      paymentMethod.providers.stripe.id,
      { withFreeTrial: false, isApplePay: paymentMethod.wallet === WalletEnum.APPLE_PAY }
    );

    const subscription =
      currentSubscription ??
      (await SubscriptionService.createSubscription({
        // Subscription is created as active by default unless the user has failed the verification process
        active: !user?.hasFailedKyc,
        owner: user.id,
        category: "CardPaymentSubscription",
        price: price,
        paymentMethod: paymentMethod.id,
        hasUsedFreeTrial: false,
        nextChargeAt: new Date(stripeSubscription.current_period_end * 1000),
        providers: {
          stripe: {
            id: stripeSubscription.id,
            status: stripeSubscription.status
          }
        }
      }));

    const paymentIntent = (stripeSubscription.latest_invoice as Stripe.Invoice)
      ?.payment_intent as Stripe.PaymentIntent;

    // If Stripe did not give us a payment intent, it means that the user has a credit balance and they
    // can upgrade to paid for free.
    if (!paymentIntent) {
      const updatedSubscription = await Subscription.findByIdAndUpdate(
        subscription.id,
        {
          category: "CardPaymentSubscription",
          price: price,
          paymentMethod: paymentMethod.id,
          nextChargeAt: new Date(stripeSubscription.current_period_end * 1000),
          providers: {
            stripe: {
              id: stripeSubscription.id,
              status: stripeSubscription.status
            }
          }
        },
        {
          upsert: false,
          new: true
        }
      ).populate("owner");

      await this._emitPlanEvent(events.plan.planUpgrade.eventId, updatedSubscription, currentSubscription);

      return {
        subscription: updatedSubscription
      };
    }

    const portfolio = await PortfolioService.getGeneralInvestmentPortfolio(user);
    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);
    const priceAmount = PRICE_CONFIG[price].amount;
    const transactionData: Omit<ChargeTransactionDTOInterface, "createdAt" | "originalChargeAmount"> = {
      consideration: {
        currency: user.currency,
        amount: Decimal.mul(priceAmount, 100).toNumber() // amount is stored in cents
      },
      owner: user.id,
      portfolio: portfolio.id,
      chargeMethod: "card",
      subscription: subscription.id,
      paymentMethod: paymentMethod.id,
      chargeType: "subscription",
      price,
      chargeMonth: DateUtil.getYearAndMonth(new Date(Date.now())),
      activeProviders: [ProviderEnum.STRIPE], // As this flow is Stripe-specific, we hardcode Stripe as the active provider
      providers: {
        stripe: {
          id: paymentIntent.id,
          status: paymentIntent.status
        }
      }
    };

    await TransactionService.createChargeTransaction(transactionData);

    return {
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id
    };
  }

  /**
   * Throws an error if a subscription/payment method combination is invalid (e.g. direct debit for a free subscription)
   * @param price
   * @param category
   * @param companyEntity
   * @private
   */
  private static _validateSubscriptionPaymentMethod(
    price: plansConfig.PriceType,
    category: plansConfig.SubscriptionCategoryType,
    companyEntity: entitiesConfig.CompanyEntityEnum
  ): void {
    if (!ConfigUtil.getPricing(companyEntity)[price].allowedSubscriptionCategories.includes(category)) {
      throw new InternalServerError("Invalid price/subscription category combination");
    }
  }

  /**
   * Creates custody charge transaction for user based on their holdings.
   */
  private static async _chargeCustodyFeesForPortfolio(
    portfolio: PortfolioDocument,
    plan: plansConfig.PlanType,
    chargeMonth: string
  ): Promise<void> {
    const availableHoldings = await PortfolioService.getHoldingsExcludingPendingOrders(portfolio);
    if (availableHoldings?.filter((holding) => holding.quantity > 0).length === 0) {
      return;
    }

    if (!portfolio.populated("owner")) {
      await portfolio.populate("owner");
    }

    const user = portfolio.owner as UserDocument;

    // Charge month has format YYYY-MM so using that we build the first day of the charge month.
    const firstDayOfChargeMonth = new Date(`${chargeMonth}-01T00:00:00.000+00:00`);
    const lastDayOfChargeMonth = DateUtil.getDateOfDaysAgo(
      DateUtil.getFirstDayOfNextMonth(firstDayOfChargeMonth),
      1
    );

    const numberOfWeekDaysForChargeMonth = DateUtil.getNumberOfWeekDaysBetween(
      firstDayOfChargeMonth,
      lastDayOfChargeMonth
    );

    const tickersForChargeMonth = (
      await DailyTickerService.getDailyPortfolioTickers({
        portfolio: portfolio.id,
        date: {
          startDate: firstDayOfChargeMonth,
          endDate: DateUtil.getFirstDayOfNextMonth(firstDayOfChargeMonth)
        }
      })
    ).data as DailyPortfolioTickerDocument[];

    // We don't want to charge users that were fully withdrawn for the whole month
    if (tickersForChargeMonth.every((ticker) => ticker.getPrice(user.currency) === 0)) {
      return;
    }

    const averagePortfolioTickerValue = tickersForChargeMonth
      .map((ticker) => ticker.getPrice(user.currency))
      .reduce((sum, amount) => sum.plus(amount), new Decimal(0))
      .div(numberOfWeekDaysForChargeMonth)
      .round();

    // The custody rate figure is a yearly rate that is charged monthly. Therefore, to get the monthly charge, we
    // divide the custody rates by 12.
    const monthlyCustodyRate = Decimal.div(CUSTODY_RATES[plan], 12);

    const calculatedCustodyCharge = averagePortfolioTickerValue.mul(monthlyCustodyRate).toDecimalPlaces(2);

    // If the calculated custody fee is less than the minimum, we charge the minimum instead.
    const finalCustodyCharge = calculatedCustodyCharge.greaterThanOrEqualTo(MINIMUM_CUSTODY_FEE)
      ? calculatedCustodyCharge.toNumber()
      : MINIMUM_CUSTODY_FEE;

    const transactionData: Omit<ChargeTransactionDTOInterface, "createdAt" | "originalChargeAmount"> = {
      consideration: {
        currency: user.currency,
        amount: Decimal.mul(finalCustodyCharge, 100).toNumber(), // amount is stored in cents
        holdingsAmount: Decimal.mul(finalCustodyCharge, 100).toNumber()
      },
      owner: user.id,
      portfolio: portfolio.id,
      chargeMethod: "holdings",
      chargeType: "custody",
      chargeMonth,
      activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BROKERAGE])
    };
    const transaction = await TransactionService.createChargeTransaction(transactionData);

    const ordersToCreate = await OrderService.getSellOrdersToCreate(portfolio, finalCustodyCharge, {
      orderFilteringMethod: SellOrderFilteringMethodEnum.FILTER_AND_DISTRIBUTE,
      sellRestrictedHoldings: true,
      executeEtfOrdersInRealtime: false
    });

    await Promise.all(
      ordersToCreate.map((order) => {
        const orderData = {
          ...order,
          fees: getZeroFees(user.currency), // Custody charges have no fees
          transaction: transaction.id
        };
        return OrderService.createDbOrder(orderData as OrderDTOInterface);
      })
    );
  }

  private static _createDbFilter(filter: SubscriptionsFilter): {
    category?: plansConfig.SubscriptionCategoryType;
    price?: { $in: plansConfig.PriceType[] };
    active?: boolean;
    nextChargeAt?: { $gte: Date; $lt: Date };
    expiration?: { $exists: boolean };
    "expiration.date"?: { $lte: Date };
  } {
    if (filter.expiredOnly && filter.notExpiringOnly) {
      throw new Error("Either expiredOnly or notExpiringOnly can be passed in subscriptions filter");
    }

    const actualFilter: {
      category?: plansConfig.SubscriptionCategoryType;
      price?: { $in: plansConfig.PriceType[] };
      active?: boolean;
      nextChargeAt?: { $gte: Date; $lt: Date };
      expiration?: { $exists: boolean };
      "expiration.date"?: { $lte: Date };
    } = {
      category: filter.category,
      active: filter.active
    };

    if (filter.prices) {
      actualFilter["price"] = {
        $in: [...filter.prices] as plansConfig.PriceType[]
      };
    }

    if (filter.nextChargeAt) {
      actualFilter["nextChargeAt"] = {
        $gte: filter.nextChargeAt.startDate,
        $lt: filter.nextChargeAt.endDate
      };
    }

    if (filter.notExpiringOnly) {
      actualFilter["expiration"] = {
        $exists: false
      };
    }

    if (filter.expiredOnly) {
      actualFilter["expiration.date"] = {
        $lte: new Date(Date.now())
      };
    }

    return actualFilter
      ? Object.fromEntries(
          Object.entries(actualFilter).filter(
            ([key, value]) => key != "status" && value !== undefined && value !== null
          )
        )
      : {};
  }

  private static async _emitPlanEvent(
    planEvent: string,
    newSubscription: SubscriptionDocument,
    oldSubscription?: SubscriptionDocument
  ) {
    const owner = (
      newSubscription.populated("owner")
        ? newSubscription.owner
        : // we get owner separately to not be included in the http response
          await UserService.getUser(newSubscription.owner.toString())
    ) as UserDocument;

    const PRICE_CONFIG = ConfigUtil.getPricing(owner.companyEntity);

    if (!oldSubscription) {
      eventEmitter.emit(planEvent, owner, {
        from: "",
        fromRecurrence: "",
        to: PRICE_CONFIG[newSubscription.expiration?.downgradesTo ?? newSubscription.price].plan,
        toRecurrence:
          PRICE_CONFIG[newSubscription.expiration?.downgradesTo ?? newSubscription.price].recurrence ?? "monthly"
      } as TrackPlanType);
    } else {
      eventEmitter.emit(planEvent, owner, {
        from: PRICE_CONFIG[oldSubscription?.price]?.plan,
        fromRecurrence: PRICE_CONFIG[oldSubscription?.price]?.recurrence ?? "monthly", // If no recurrence -> free_monthly
        to: PRICE_CONFIG[newSubscription.expiration?.downgradesTo ?? newSubscription.price].plan,
        toRecurrence:
          PRICE_CONFIG[newSubscription.expiration?.downgradesTo ?? newSubscription.price].recurrence ?? "monthly" // If no recurrence -> free_monthly
      } as TrackPlanType);
    }
  }

  /**
   * @description
   * This function validate status updates, by checking that new statuses
   * won't revert current status in the context of payments lifecycle.
   * For example We cannot update newStatus: "requires_action" when currentStatus is "succeeded".
   */
  public static _validateStripePaymentStatusUpdate(
    currentStatus: PaymentIntentStatusType,
    newStatus: PaymentIntentStatusType
  ): boolean {
    if (currentStatus === "requires_action" && ["succeeded", "requires_payment_method"].includes(newStatus)) {
      return true;
    } else if (currentStatus === "requires_payment_method" && newStatus === "succeeded") {
      return true;
    } else return currentStatus === newStatus;
  }
}
