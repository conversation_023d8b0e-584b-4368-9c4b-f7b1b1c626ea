import { captureException } from "@sentry/node";
import { Types } from "mongoose";
import {
  CustomNotificationProperties,
  Notification,
  NotificationDocument,
  NotificationDTOInterface,
  NotificationMetadataTypeEnum
} from "../models/Notification";
import UserService from "./userService";
import NotificationSettingsService from "./notificationSettingsService";
import {
  CONTENT_ENTRY_CATEGORY_TO_NOTIFICATION_MAPPING,
  APP_NOTIFICATIONS_CONFIG,
  EMAIL_NOTIFICATIONS_CONFIG,
  NotificationEventType
} from "../configs/notificationSettingsConfig";
import MailerService, { EmailType } from "../external-services/mailerService";
import OnesignalService from "../external-services/onesignalService";
import { ContentEntryContentTypeEnum, ContentEntryDocument } from "../models/ContentEntry";
import { ProviderEnum } from "../configs/providersConfig";
import { UserDocument } from "../models/User";
import { NotificationSettingsDocument } from "../models/NotificationSettings";
import DateUtil from "../utils/dateUtil";
import logger from "../external-services/loggerService";
import ObjectUtil from "../utils/objectUtil";
import { TransactionalNotificationEventEnum } from "../event-handlers/notificationEvents";

const DB_BATCH_SIZE = 200;

export default class NotificationService {
  /**
   * ===============
   * PUBLIC METHODS
   * ===============
   */

  /**
   * @description Creates a notification document and sends it to the user immediately if requested.
   *
   * @param userId
   * @param data
   * @param options
   */
  public static async createAppNotification(
    userId: string,
    data: { notificationId: NotificationEventType; properties?: CustomNotificationProperties },
    options: { sendImmediately: boolean } = { sendImmediately: false }
  ): Promise<void> {
    const notificationData: NotificationDTOInterface = {
      owner: new Types.ObjectId(userId),
      method: "app",
      status: "Pending",
      providers: {
        [ProviderEnum.ONESIGNAL]: {
          notificationId: data.notificationId,
          properties: data.properties
        }
      }
    };

    const notification = await new Notification({ ...notificationData }).save();

    if (options?.sendImmediately) {
      await NotificationService.sendSingleAppNotification(notification);
    }
  }

  public static async markNotificationAsSkipped(notification: NotificationDocument): Promise<void> {
    await Notification.findByIdAndUpdate(notification.id, { status: "Skipped" });
  }

  /**
   * @description Marks all notifications of the same type and same notifyAt day as skipped (for the same user).
   * This is used to ensure that we do not send multiple notifications of the same type on the same day.
   * Transactional notifications can be sent multiple times, so we don't want to skip them.
   *
   * @param notification
   */
  public static async markSameTypeAndDayContentNotificationsAsSkipped(
    notification: NotificationDocument
  ): Promise<void> {
    if (notification.status === "Sent") {
      const notificationId = notification.providers.onesignal?.notificationId;

      // Exit early if notification is a transactional notification - they can be sent multiple times
      if (
        Object.values(TransactionalNotificationEventEnum).includes(
          notificationId as TransactionalNotificationEventEnum
        )
      ) {
        return;
      }

      const notifyAtDate = notification.notifyAt;
      const { start: startOfDay, end: endOfDay } = DateUtil.getStartAndEndOfDay(notifyAtDate);

      await Notification.updateMany(
        {
          owner: notification.owner,
          "providers.onesignal.notificationId": notificationId,
          status: "Pending",
          notifyAt: {
            $gte: startOfDay,
            $lt: endOfDay
          }
        },
        { status: "Skipped" }
      );
    }
  }

  public static async createEmailNotification(
    userId: string,
    data: { notificationId: EmailType; properties?: CustomNotificationProperties },
    options: { sendImmediately: boolean } = { sendImmediately: false }
  ): Promise<void> {
    const user = await UserService.getUser(userId, { notificationSettings: true });
    const hasAllowedNotification = await NotificationSettingsService.hasAllowedEmailNotification(
      user,
      EMAIL_NOTIFICATIONS_CONFIG[data.notificationId]
    );

    // If the user has not allowed this notification, we don't create a notification document.
    if (!hasAllowedNotification) {
      return;
    }

    const notificationData: NotificationDTOInterface = {
      owner: user.id,
      method: "email",
      status: "Pending",
      providers: {
        [ProviderEnum.POSTMARK]: {
          notificationId: data.notificationId,
          properties: data.properties
        }
      }
    };

    if (options?.sendImmediately) {
      MailerService.sendEmail(
        user,
        data.notificationId,
        data.properties ? Object.fromEntries(data.properties) : {}
      );

      // If the user has selected to sent the e-mail inline with creation, then we can
      // set the status to Sent so that it does not get retried.
      notificationData.status = "Sent";
      notificationData.sentAt = new Date(Date.now());
    }

    await new Notification({ ...notificationData }).save();
  }

  public static async createContentEntryNotificationForUsers(contentEntry: ContentEntryDocument): Promise<void> {
    // This method may run for old content entries that have been uploaded to Contentful.
    // We don't want to notify users for these.
    if (!contentEntry.shouldNotifyUsers || contentEntry.publishAt < DateUtil.getDateOfMinutesAgo(30)) {
      return;
    }

    const notificationId = CONTENT_ENTRY_CATEGORY_TO_NOTIFICATION_MAPPING[contentEntry.contentType];
    const setting = APP_NOTIFICATIONS_CONFIG[notificationId];

    await NotificationSettingsService.getNotificationSettingsStreamed(
      { hasEnabledDeviceNotifications: true, hasEnabledAppNotificationSetting: setting },
      { owner: true }
    ).eachAsync(
      async (notificationSettings: NotificationSettingsDocument[]) => {
        const dbOperations = notificationSettings
          .filter((notificationSetting) => {
            const user = notificationSetting.owner as UserDocument;
            // We only want to send notifications to users who have passed KYC and have a device token.
            // After the migration from intercom to onesignal, we cannot reach out to users that we don't
            // know their device tokens.
            return user.hasPassedKyc && user.hasDeviceToken;
          })
          .map((notificationSetting) => {
            const user = notificationSetting.owner as UserDocument;

            return {
              updateOne: {
                filter: { owner: user.id, "providers.onesignal.properties.title": contentEntry.title },
                update: {
                  $setOnInsert: {
                    owner: user.id,
                    method: "app",
                    status: "Pending",
                    notifyAt: contentEntry.publishAt,
                    providers: {
                      [ProviderEnum.ONESIGNAL]: {
                        notificationId: notificationId,
                        properties: {
                          title: contentEntry.title
                        },
                        metadata: {
                          notificationType:
                            contentEntry.contentType === ContentEntryContentTypeEnum.GUIDE
                              ? NotificationMetadataTypeEnum.LEARNING_GUIDE
                              : NotificationMetadataTypeEnum.ANALYST_INSIGHT,
                          documentId: contentEntry.id
                        }
                      }
                    }
                  }
                },
                upsert: true
              }
            };
          });

        await Notification.bulkWrite(dbOperations);
      },
      { batchSize: DB_BATCH_SIZE }
    );
  }

  /**
   * Sends notifications in bulk to multiple users and updates their status.
   * This method is optimized for performance by using bulk operations.
   * All notifications must be of the same type, have the same properties and metadata.
   *
   * @param notifications Array of notifications to be sent
   * @throws Error if notifications are not of the same type, properties or metadata
   */
  public static async sendBulkAppNotifications(notifications: NotificationDocument[]): Promise<void> {
    if (notifications.length === 0) {
      return;
    }

    const firstNotification = notifications[0];
    const notificationType = firstNotification.providers.onesignal.notificationId;
    const properties = firstNotification.providers.onesignal.properties;
    const metadata = firstNotification.providers.onesignal.metadata;

    if (!NotificationService._hasMatchingNotificationData(notifications, notificationType, properties, metadata)) {
      throw new Error("All notifications in bulk send must have the same type, properties and metadata");
    }

    // Filter out notifications for users who haven't allowed them or don't have device tokens
    const validNotifications = notifications.filter((notification) => {
      const user = notification.owner as UserDocument;
      return (
        user.hasDeviceToken &&
        NotificationSettingsService.hasAllowedAppNotification(user, APP_NOTIFICATIONS_CONFIG[notificationType])
      );
    });

    if (validNotifications.length === 0) {
      return;
    }

    try {
      const userIds = validNotifications.map((notification) => (notification.owner as UserDocument).id);
      await OnesignalService.sendPushNotification(
        userIds,
        notificationType,
        properties ? Object.fromEntries(properties) : {},
        metadata
      );

      // We follow an optimistic approach and update the status to Sent.
      // If the notification fails to send, we will not retry sending it.
      await Notification.bulkWrite(
        validNotifications.map((notification) => ({
          updateOne: {
            filter: { _id: notification._id },
            update: {
              $set: {
                status: "Sent",
                sentAt: new Date(Date.now())
              }
            }
          }
        }))
      );
    } catch (error) {
      logger.error("Failed to send bulk notifications", {
        module: "NotificationService",
        method: "sendBulkAppNotifications",
        data: { notificationType, error }
      });
      captureException(error);
    }
  }

  /**
   * @description Sends a notification to the user and marks it as sent.
   *
   * This also makes sure that the user still has the appropriate setting as enabled, as the notification
   * could have been created when they did have it enabled but then later the user disabled it.
   *
   * Finally, before sending the notification, we ensure the user has not already reached today's notification limit.
   *
   * @param notification
   */
  public static async sendSingleAppNotification(notification: NotificationDocument): Promise<void> {
    let user: UserDocument;
    if (notification.populated("owner")) {
      user = notification.owner as UserDocument;
    } else {
      user = await UserService.getUser(notification.owner.toString(), { notificationSettings: true });
    }

    const hasAllowedNotification = NotificationSettingsService.hasAllowedAppNotification(
      user,
      APP_NOTIFICATIONS_CONFIG[notification.providers.onesignal?.notificationId]
    );

    if (!hasAllowedNotification || !user.hasDeviceToken) {
      return;
    }

    await OnesignalService.sendPushNotification(
      [user.id],
      notification.providers.onesignal.notificationId,
      notification.providers.onesignal.properties
        ? Object.fromEntries(notification.providers.onesignal.properties)
        : {},
      notification.providers.onesignal.metadata
    );

    await Notification.findByIdAndUpdate(notification._id, {
      status: "Sent",
      sentAt: new Date(Date.now())
    });
  }

  /**
   * ===============
   * PRIVATE METHODS
   * ===============
   */

  /**
   * Validates that all notifications have matching data (type, properties, metadata).
   * @param notifications Array of notifications to validate
   * @param notificationType The notification type to match against
   * @param properties The properties to match against
   * @param metadata The metadata to match against
   * @returns boolean indicating if all notifications have matching data
   */
  private static _hasMatchingNotificationData(
    notifications: NotificationDocument[],
    notificationType: NotificationEventType,
    properties: CustomNotificationProperties,
    metadata?: {
      notificationType: NotificationMetadataTypeEnum;
      documentId: string;
    }
  ): boolean {
    return notifications.every((notification) => {
      const currentProperties = notification.providers.onesignal.properties;
      const currentMetadata = notification.providers.onesignal.metadata;

      // Check notification type
      if (notification.providers.onesignal.notificationId !== notificationType) {
        return false;
      }

      // Check properties by converting Maps to objects
      if (
        !ObjectUtil.areObjectsEqual(ObjectUtil.mapToObject(properties), ObjectUtil.mapToObject(currentProperties))
      ) {
        return false;
      }

      // Check metadata
      if (!ObjectUtil.areObjectsEqual(metadata, currentMetadata)) {
        return false;
      }

      return true;
    });
  }
}
