import Decimal from "decimal.js";
import { captureException } from "@sentry/node";
import {
  DepositCashTransaction,
  WithdrawalCashTransaction,
  DividendTransaction,
  SavingsDividendTransaction
} from "../models/Transaction";
import { Order } from "../models/Order";
import AccountingLedgerStorageService, {
  LedgerQueryResult
} from "../external-services/accountingLedgerStorageService";
import { AccountingEventType, LedgerAccounts } from "../types/accounting";
import { CASH_ACCOUNTS_WK_MAPPING } from "../configs/accountingConfig";
import logger from "../external-services/loggerService";

export interface ValidationResult {
  transactionType:
    | "deposits_stage1"
    | "deposits_stage2"
    | "deposits_stage3"
    | "withdrawals_stage1"
    | "withdrawals_stage2"
    | "dividends_asset"
    | "dividends_mmf_receipt"
    | "dividends_mmf_commission"
    | "asset_buy"
    | "asset_sell"
    | "mmf_buy"
    | "mmf_sell";
  isValid: boolean;
  dbTotalAmount: number; // in euros
  ledgerTotalAmount: number; // in euros
  difference: number; // in euros
  transactionCount: number;
  ledgerEntryCount: number;
  discrepancies?: ValidationDiscrepancy[];
}

export interface ValidationDiscrepancy {
  transactionId: string;
  dbAmount: number; // in euros
  ledgerAmount: number; // in euros
  difference: number; // in euros
}

interface CashReconDelta {
  deltaWK: number;
  deltaLedger: number;
  difference: number;
}

export interface CashReconciliationResult {
  fromDate: string;
  perAccount: Record<LedgerAccounts, CashReconDelta>;
  aggregate: CashReconDelta;
}

export class AccountingValidationService {
  /**
   * Validates deposits, withdrawals, dividends, and orders
   */
  public static async validateAllDbWithLedger(fromDate: string): Promise<ValidationResult[]> {
    logger.info("Starting all validation", {
      module: "AccountingValidationService",
      method: "validateAllDbWithLedger",
      data: { fromDate }
    });

    const [depositsResults, withdrawalsResults, dividendsResults, ordersResults] = await Promise.all([
      AccountingValidationService.validateDepositsDbWithLedger(fromDate),
      AccountingValidationService.validateWithdrawalsDbWithLedger(fromDate),
      AccountingValidationService.validateDividendsDbWithLedger(fromDate),
      AccountingValidationService.validateOrdersDbWithLedger(fromDate)
    ]);

    logger.info("All validation completed", {
      module: "AccountingValidationService",
      method: "validateAllDbWithLedger",
      data: { depositsResults, withdrawalsResults, dividendsResults, ordersResults }
    });

    return [...depositsResults, ...withdrawalsResults, ...dividendsResults, ...ordersResults];
  }

  /**
   * Validates that deposit transaction amounts match their accounting ledger entries
   *
   * Checks for Stage 1 (External → Intermediary #1)
   * => Total db amount should match total credits on client accounts
   * => Total db amount should match total debits on intermediary deposits #1 account
   *
   * Checks for Stage 2 (Intermediary #1 → Intermediary #2) - Instant flow only
   * => Total db amount should match total credits on intermediary deposits #1 account
   * => Total db amount should match total debits on intermediary deposits #2 account
   *
   * Checks for Stage 3 (Intermediary → Omnibus)
   * => Total db amount should match total credits on intermediary deposits account (stage depends on flow type)
   * => Total db amount should match total debits on omnibus account
   */
  public static async validateDepositsDbWithLedger(fromDate: string): Promise<ValidationResult[]> {
    logger.info("Starting deposits validation", {
      module: "AccountingValidationService",
      method: "validateDepositsDbWithLedger",
      data: { fromDate }
    });

    try {
      // Get all ledger entries for bank transactions with date filter
      const ledgerEntries = await AccountingLedgerStorageService.queryLedgerEntriesByEventType(
        AccountingEventType.BANK_TRANSACTION_DEPOSIT,
        fromDate
      );

      // Stage 1 Validation: Devengo acquisition confirmed deposits (External → Intermediary #1)
      const stage1Query: any = {
        "transferWithIntermediary.acquisition.incomingPayment.providers.devengo.status": "confirmed",
        "transferWithIntermediary.acquisition.incomingPayment.providers.devengo.settledAt": {
          $gte: new Date(fromDate)
        }
      };

      const stage1Deposits = await DepositCashTransaction.find(stage1Query).populate("owner");

      const stage1DbTotalAmount = stage1Deposits
        .reduce((sum, deposit) => {
          const amount = Decimal.div(deposit.consideration.amount || 0, 100);
          return sum.plus(amount);
        }, new Decimal(0))
        .toNumber();

      const stage1DepositIds = new Set(stage1Deposits.map((d) => d._id.toString()));
      const clientAccountCodes = new Set([
        LedgerAccounts.CLIENT_DOMESTIC,
        LedgerAccounts.CLIENT_EU_EEA,
        LedgerAccounts.CLIENT_INTERNATIONAL
      ]);

      // Check credits on client accounts
      const stage1CreditAmount = ledgerEntries
        .filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return (
            stage1DepositIds.has(transactionId) &&
            entry.side === "credit" &&
            clientAccountCodes.has(entry.account_code as LedgerAccounts)
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Check debits on intermediary deposits #1 account
      const stage1DebitAmount = ledgerEntries
        .filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return (
            stage1DepositIds.has(transactionId) &&
            entry.side === "debit" &&
            entry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_1
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Both debit and credit sides should match db total
      const stage1CreditDifference = new Decimal(stage1DbTotalAmount).minus(stage1CreditAmount).toNumber();
      const stage1DebitDifference = new Decimal(stage1DbTotalAmount).minus(stage1DebitAmount).toNumber();
      const stage1IsValid = stage1CreditDifference === 0 && stage1DebitDifference === 0;

      const stage1Result: ValidationResult = {
        transactionType: "deposits_stage1",
        isValid: stage1IsValid,
        dbTotalAmount: stage1DbTotalAmount,
        ledgerTotalAmount: stage1CreditAmount,
        difference: stage1CreditDifference,
        transactionCount: stage1Deposits.length,
        ledgerEntryCount: ledgerEntries.filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return (
            stage1DepositIds.has(transactionId) &&
            ((entry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_1 && entry.side === "debit") ||
              (clientAccountCodes.has(entry.account_code as LedgerAccounts) && entry.side === "credit"))
          );
        }).length
      };

      if (!stage1IsValid) {
        stage1Result.discrepancies = await AccountingValidationService._findDepositDiscrepancies(
          stage1Deposits,
          ledgerEntries
        );
      }

      // Stage 2 Validation: Devengo collection confirmed deposits (Intermediary #1 → Intermediary #2) - Instant flow only
      const stage2Query: any = {
        "transferWithIntermediary.collection.outgoingPayment.providers.devengo.status": "confirmed",
        "transferWithIntermediary.collection.outgoingPayment.providers.devengo.settledAt": {
          $gte: new Date(fromDate)
        },
        linkedCreditTicket: { $exists: true } // Only instant flow has Stage 2 (determined by linked credit ticket)
      };

      const stage2Deposits = await DepositCashTransaction.find(stage2Query).populate("owner");

      const stage2DbTotalAmount = stage2Deposits
        .reduce((sum, deposit) => {
          const amount = Decimal.div(deposit.consideration.amount || 0, 100);
          return sum.plus(amount);
        }, new Decimal(0))
        .toNumber();

      const stage2DepositIds = new Set(stage2Deposits.map((d) => d._id.toString()));

      // Check credits on intermediary deposits #1 account
      const stage2CreditAmount = ledgerEntries
        .filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return (
            stage2DepositIds.has(transactionId) &&
            entry.side === "credit" &&
            entry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_1
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Check debits on intermediary deposits #2 account
      const stage2DebitAmount = ledgerEntries
        .filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return (
            stage2DepositIds.has(transactionId) &&
            entry.side === "debit" &&
            entry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_2
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Both debit and credit sides should match db total
      const stage2CreditDifference = new Decimal(stage2DbTotalAmount).minus(stage2CreditAmount).toNumber();
      const stage2DebitDifference = new Decimal(stage2DbTotalAmount).minus(stage2DebitAmount).toNumber();
      const stage2IsValid = stage2CreditDifference === 0 && stage2DebitDifference === 0;

      const stage2Result: ValidationResult = {
        transactionType: "deposits_stage2",
        isValid: stage2IsValid,
        dbTotalAmount: stage2DbTotalAmount,
        ledgerTotalAmount: stage2CreditAmount,
        difference: stage2CreditDifference,
        transactionCount: stage2Deposits.length,
        ledgerEntryCount: ledgerEntries.filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return (
            stage2DepositIds.has(transactionId) &&
            ((entry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_2 && entry.side === "debit") ||
              (entry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_1 && entry.side === "credit"))
          );
        }).length
      };

      if (!stage2IsValid) {
        stage2Result.discrepancies = await AccountingValidationService._findDepositDiscrepancies(
          stage2Deposits,
          ledgerEntries
        );
      }

      // Stage 3 Validation: WealthKernel settled deposits (Intermediary → Omnibus)
      const stage3Query: any = {
        "providers.wealthkernel.status": "Settled",
        "providers.wealthkernel.settledAt": { $gte: new Date(fromDate) }
      };

      const stage3Deposits = await DepositCashTransaction.find(stage3Query).populate("owner");

      const stage3DbTotalAmount = stage3Deposits
        .reduce((sum, deposit) => {
          const amount = Decimal.div(deposit.consideration.amount || 0, 100);
          return sum.plus(amount);
        }, new Decimal(0))
        .toNumber();

      const stage3DepositIds = new Set(stage3Deposits.map((d) => d._id.toString()));

      // Determine which intermediary account to check based on flow type
      const stage3CreditAmount = ledgerEntries
        .filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          if (!stage3DepositIds.has(transactionId) || entry.side !== "credit") return false;

          // Credit should be from either INTERMEDIARY_DEPOSITS_1 (standard flow) or INTERMEDIARY_DEPOSITS_2 (instant flow)
          return (
            entry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_1 ||
            entry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_2
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Check debits on omnibus account
      const stage3DebitAmount = ledgerEntries
        .filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return (
            stage3DepositIds.has(transactionId) &&
            entry.side === "debit" &&
            entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Both debit and credit sides should match db total
      const stage3CreditDifference = new Decimal(stage3DbTotalAmount).minus(stage3CreditAmount).toNumber();
      const stage3DebitDifference = new Decimal(stage3DbTotalAmount).minus(stage3DebitAmount).toNumber();
      const stage3IsValid = stage3CreditDifference === 0 && stage3DebitDifference === 0;

      const stage3Result: ValidationResult = {
        transactionType: "deposits_stage3",
        isValid: stage3IsValid,
        dbTotalAmount: stage3DbTotalAmount,
        ledgerTotalAmount: stage3CreditAmount,
        difference: stage3CreditDifference,
        transactionCount: stage3Deposits.length,
        ledgerEntryCount: ledgerEntries.filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return (
            stage3DepositIds.has(transactionId) &&
            ((entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS && entry.side === "debit") ||
              ((entry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_1 ||
                entry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_2) &&
                entry.side === "credit"))
          );
        }).length
      };

      if (!stage3IsValid) {
        stage3Result.discrepancies = await AccountingValidationService._findDepositDiscrepancies(
          stage3Deposits,
          ledgerEntries
        );
      }

      logger.info("Deposits validation completed", {
        module: "AccountingValidationService",
        method: "validateDepositsDbWithLedger",
        data: { stage1Result, stage2Result, stage3Result }
      });

      return [stage1Result, stage2Result, stage3Result];
    } catch (error) {
      captureException(error);
      logger.error("Error during deposits validation", {
        module: "AccountingValidationService",
        method: "validateDepositsDbWithLedger",
        data: { error }
      });
    }
  }

  /**
   * Validates that withdrawal transaction amounts match their accounting ledger entries
   * Validates both Stage 1 (WealthKernel settled) and Stage 2 (Devengo outgoing confirmed)
   *
   * Checks for Stage 1 (Omnibus → Intermediary)
   * => Total db amount should match total credits on omnibus account
   * => Total db amount should match total debits on intermediary withdrawals account
   *
   * Checks for Stage 2 (Intermediary → Client)
   * => Total db amount should match total credits on intermediary withdrawals account
   * => Total db amount should match total debits on client accounts
   */
  public static async validateWithdrawalsDbWithLedger(fromDate: string): Promise<ValidationResult[]> {
    logger.info("Starting withdrawals validation", {
      module: "AccountingValidationService",
      method: "validateWithdrawalsDbWithLedger",
      data: { fromDate }
    });

    try {
      // Get all ledger entries for bank transactions with date filter
      // TODO: update how we fetch that
      const ledgerEntries = await AccountingLedgerStorageService.queryLedgerEntriesByEventType(
        AccountingEventType.BANK_TRANSACTION_WITHDRAWAL,
        fromDate
      );

      // Stage 1 Validation: WealthKernel settled withdrawals (Omnibus → Intermediary)
      const stage1Query: any = {
        "providers.wealthkernel.status": "Settled",
        "providers.wealthkernel.settledAt": { $gte: new Date(fromDate) }
      };

      const stage1Withdrawals = await WithdrawalCashTransaction.find(stage1Query).populate("owner");

      const stage1DbTotalAmount = stage1Withdrawals
        .reduce((sum, withdrawal) => {
          const amount = Decimal.div(withdrawal.consideration.amount || 0, 100);
          return sum.plus(amount);
        }, new Decimal(0))
        .toNumber();

      const stage1WithdrawalIds = new Set(stage1Withdrawals.map((w) => w._id.toString()));

      // Check debits on intermediary withdrawals account
      const stage1DebitAmount = ledgerEntries
        .filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return (
            stage1WithdrawalIds.has(transactionId) &&
            entry.side === "debit" &&
            entry.account_code === LedgerAccounts.INTERMEDIARY_WITHDRAWALS
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Check credits on omnibus account
      const stage1CreditAmount = ledgerEntries
        .filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return (
            stage1WithdrawalIds.has(transactionId) &&
            entry.side === "credit" &&
            entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Both debit and credit sides should match db total
      const stage1DebitDifference = new Decimal(stage1DbTotalAmount).minus(stage1DebitAmount).toNumber();
      const stage1CreditDifference = new Decimal(stage1DbTotalAmount).minus(stage1CreditAmount).toNumber();
      const stage1IsValid = stage1DebitDifference === 0 && stage1CreditDifference === 0;

      const stage1Result: ValidationResult = {
        transactionType: "withdrawals_stage1",
        isValid: stage1IsValid,
        dbTotalAmount: stage1DbTotalAmount,
        ledgerTotalAmount: stage1CreditAmount,
        difference: stage1CreditDifference,
        transactionCount: stage1Withdrawals.length,
        ledgerEntryCount: ledgerEntries.filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return stage1WithdrawalIds.has(transactionId);
        }).length
      };

      if (!stage1IsValid) {
        stage1Result.discrepancies = await AccountingValidationService._findWithdrawalDiscrepancies(
          stage1Withdrawals,
          ledgerEntries
        );
      }

      // Stage 2 Validation: Devengo outgoing confirmed withdrawals (Intermediary → Client)
      const stage2Query: any = {
        "transferWithIntermediary.collection.outgoingPayment.providers.devengo.status": "confirmed",
        "transferWithIntermediary.collection.outgoingPayment.providers.devengo.settledAt": {
          $gte: new Date(fromDate)
        }
      };

      const stage2Withdrawals = await WithdrawalCashTransaction.find(stage2Query).populate("owner");

      const stage2DbTotalAmount = stage2Withdrawals
        .reduce((sum, withdrawal) => {
          const amount = Decimal.div(withdrawal.consideration.amount || 0, 100);
          return sum.plus(amount);
        }, new Decimal(0))
        .toNumber();

      const stage2WithdrawalIds = new Set(stage2Withdrawals.map((w) => w._id.toString()));
      const clientAccountCodes = new Set([
        LedgerAccounts.CLIENT_DOMESTIC,
        LedgerAccounts.CLIENT_EU_EEA,
        LedgerAccounts.CLIENT_INTERNATIONAL
      ]);

      // Check debits on client accounts
      const stage2DebitAmount = ledgerEntries
        .filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return (
            stage2WithdrawalIds.has(transactionId) &&
            entry.side === "debit" &&
            clientAccountCodes.has(entry.account_code as LedgerAccounts)
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Check credits on intermediary withdrawals account
      const stage2CreditAmount = ledgerEntries
        .filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return (
            stage2WithdrawalIds.has(transactionId) &&
            entry.side === "credit" &&
            entry.account_code === LedgerAccounts.INTERMEDIARY_WITHDRAWALS
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Both debit and credit sides should match db total
      const stage2DebitDifference = new Decimal(stage2DbTotalAmount).minus(stage2DebitAmount).toNumber();
      const stage2CreditDifference = new Decimal(stage2DbTotalAmount).minus(stage2CreditAmount).toNumber();
      const stage2IsValid = stage2DebitDifference === 0 && stage2CreditDifference === 0;

      const stage2Result: ValidationResult = {
        transactionType: "withdrawals_stage2",
        isValid: stage2IsValid,
        dbTotalAmount: stage2DbTotalAmount,
        ledgerTotalAmount: stage2DebitAmount,
        difference: stage2DebitDifference,
        transactionCount: stage2Withdrawals.length,
        ledgerEntryCount: ledgerEntries.filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return stage2WithdrawalIds.has(transactionId);
        }).length
      };

      if (!stage2IsValid) {
        stage2Result.discrepancies = await AccountingValidationService._findWithdrawalDiscrepancies(
          stage2Withdrawals,
          ledgerEntries
        );
      }

      logger.info("Withdrawals validation completed", {
        module: "AccountingValidationService",
        method: "validateWithdrawalsDbWithLedger",
        data: { stage1Result, stage2Result }
      });

      return [stage1Result, stage2Result];
    } catch (error) {
      captureException(error);
      logger.error("Error during withdrawals validation", {
        module: "AccountingValidationService",
        method: "validateWithdrawalsDbWithLedger",
        data: { error }
      });
    }
  }

  /**
   * Validates that dividend transaction amounts match their accounting ledger entries
   * Validates three types of dividend flows according to PRD_ACCOUNTING.md specifications
   *
   * Checks for Asset Dividends (DividendTransaction)
   * => Validates that total dividend amounts debited from omnibus account
   * => match total dividend amounts credited to client accounts
   * => Uses ASSET_DIVIDEND event type for ledger entry matching
   *
   * Checks for MMF Dividend Receipt (SavingsDividendTransaction - Receipt flow)
   * => Validates gross dividend flow from omnibus to client accounts
   * => Debits: CLIENTS_ACCOUNTS_OMNIBUS for gross dividend amount
   * => Credits: Client accounts (CLIENT_DOMESTIC/CLIENT_EU_EEA/CLIENT_INTERNATIONAL) for gross dividend amount
   * => Uses ASSET_DIVIDEND event type for ledger entry matching
   *
   * Checks for MMF Dividend Commission (SavingsDividendTransaction - Commission flow)
   * => Validates commission charges from client accounts to fee income account
   * => Debits: Client accounts for commission amount
   * => Credits: MMF_DIVIDEND_FEES_WH account for commission amount
   * => Uses MMF_DIVIDEND_COMMISSION event type for ledger entry matching
   *
   * @param fromDate - ISO date string to filter transactions from this date onwards
   * @returns Promise<ValidationResult[]> - Array with 3 validation results (asset, mmf_receipt, mmf_commission)
   */
  public static async validateDividendsDbWithLedger(fromDate: string): Promise<ValidationResult[]> {
    logger.info("Starting dividends validation", {
      module: "AccountingValidationService",
      method: "validateDividendsDbWithLedger",
      data: { fromDate }
    });

    try {
      // Get all ledger entries for dividend transactions with date filter
      const assetDividendLedgerEntries = await AccountingLedgerStorageService.queryLedgerEntriesByEventType(
        AccountingEventType.ASSET_DIVIDEND,
        fromDate
      );

      const mmfCommissionLedgerEntries = await AccountingLedgerStorageService.queryLedgerEntriesByEventType(
        AccountingEventType.MMF_DIVIDEND_COMMISSION,
        fromDate
      );

      // Asset Dividends Validation (DividendTransaction)
      const assetDividendQuery: any = {
        $or: [{ "providers.wealthkernel.status": "Settled" }, { "providers.wealthkernel.status": "Matched" }],
        settledAt: { $gte: new Date(fromDate) }
      };

      const assetDividends = await DividendTransaction.find(assetDividendQuery).populate("owner");

      const assetDbTotalAmount = assetDividends
        .reduce((sum, dividend) => {
          const amount = new Decimal(dividend.consideration.amount || 0).div(100);
          return sum.plus(amount);
        }, new Decimal(0))
        .toNumber();

      const assetDividendIds = new Set(assetDividends.map((d) => d._id.toString()));
      const clientAccountCodes = new Set([
        LedgerAccounts.CLIENT_DOMESTIC,
        LedgerAccounts.CLIENT_EU_EEA,
        LedgerAccounts.CLIENT_INTERNATIONAL
      ]);

      // Check credits to client accounts for asset dividends
      const assetCreditAmount = assetDividendLedgerEntries
        .filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return (
            assetDividendIds.has(transactionId) &&
            entry.side === "credit" &&
            clientAccountCodes.has(entry.account_code as LedgerAccounts)
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Check debits from omnibus account for asset dividends
      const assetDebitAmount = assetDividendLedgerEntries
        .filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return (
            assetDividendIds.has(transactionId) &&
            entry.side === "debit" &&
            entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Both debit and credit sides should match db total
      const assetCreditDifference = new Decimal(assetDbTotalAmount).minus(assetCreditAmount).toNumber();
      const assetDebitDifference = new Decimal(assetDbTotalAmount).minus(assetDebitAmount).toNumber();
      const assetIsValid = assetCreditDifference === 0 && assetDebitDifference === 0;

      const assetDividendsResult: ValidationResult = {
        transactionType: "dividends_asset",
        isValid: assetIsValid,
        dbTotalAmount: assetDbTotalAmount,
        ledgerTotalAmount: assetCreditAmount,
        difference: assetCreditDifference,
        transactionCount: assetDividends.length,
        ledgerEntryCount: assetDividendLedgerEntries.filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return assetDividendIds.has(transactionId);
        }).length
      };

      if (!assetIsValid) {
        assetDividendsResult.discrepancies = await AccountingValidationService._findAssetDividendDiscrepancies(
          assetDividends,
          assetDividendLedgerEntries
        );
      }

      // MMF Dividend Receipt Validation (SavingsDividendTransaction - Receipt flow)
      const mmfDividendQuery: any = {
        createdAt: { $gte: new Date(fromDate) }
      };

      const mmfDividends = await SavingsDividendTransaction.find(mmfDividendQuery).populate("owner");

      const mmfReceiptDbTotalAmount = mmfDividends
        .reduce((sum, dividend) => {
          const amount = new Decimal(dividend.originalDividendAmount || 0).div(100);
          return sum.plus(amount);
        }, new Decimal(0))
        .toNumber();

      const mmfDividendIds = new Set(mmfDividends.map((d) => d._id.toString()));

      // Check credits to client accounts for MMF dividend receipts
      const mmfReceiptCreditAmount = assetDividendLedgerEntries
        .filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return (
            mmfDividendIds.has(transactionId) &&
            entry.side === "credit" &&
            clientAccountCodes.has(entry.account_code as LedgerAccounts)
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Check debits from omnibus account for MMF dividend receipts
      const mmfReceiptDebitAmount = assetDividendLedgerEntries
        .filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return (
            mmfDividendIds.has(transactionId) &&
            entry.side === "debit" &&
            entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Both debit and credit sides should match db total
      const mmfReceiptCreditDifference = new Decimal(mmfReceiptDbTotalAmount)
        .minus(mmfReceiptCreditAmount)
        .toNumber();
      const mmfReceiptDebitDifference = new Decimal(mmfReceiptDbTotalAmount)
        .minus(mmfReceiptDebitAmount)
        .toNumber();
      const mmfReceiptIsValid = mmfReceiptCreditDifference === 0 && mmfReceiptDebitDifference === 0;

      const mmfReceiptResult: ValidationResult = {
        transactionType: "dividends_mmf_receipt",
        isValid: mmfReceiptIsValid,
        dbTotalAmount: mmfReceiptDbTotalAmount,
        ledgerTotalAmount: mmfReceiptCreditAmount,
        difference: mmfReceiptCreditDifference,
        transactionCount: mmfDividends.length,
        ledgerEntryCount: assetDividendLedgerEntries.filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return mmfDividendIds.has(transactionId);
        }).length
      };

      if (!mmfReceiptIsValid) {
        mmfReceiptResult.discrepancies = await AccountingValidationService._findMMFDividendReceiptDiscrepancies(
          mmfDividends,
          assetDividendLedgerEntries
        );
      }

      // MMF Dividend Commission Validation (SavingsDividendTransaction - Commission flow)
      const mmfCommissionDbTotalAmount = mmfDividends
        .reduce((sum, dividend) => {
          const commissionAmount = new Decimal(dividend.fees.commission.amount || 0);
          return sum.plus(commissionAmount);
        }, new Decimal(0))
        .toNumber();

      // Check debits from client accounts for MMF dividend commissions
      const mmfCommissionDebitAmount = mmfCommissionLedgerEntries
        .filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return (
            mmfDividendIds.has(transactionId) &&
            entry.side === "debit" &&
            clientAccountCodes.has(entry.account_code as LedgerAccounts)
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Check credits to MMF dividend fee income account for commissions
      const mmfCommissionCreditAmount = mmfCommissionLedgerEntries
        .filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return (
            mmfDividendIds.has(transactionId) &&
            entry.side === "credit" &&
            entry.account_code === LedgerAccounts.MMF_DIVIDEND_FEES_WH
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Both debit and credit sides should match db total
      const mmfCommissionDebitDifference = new Decimal(mmfCommissionDbTotalAmount)
        .minus(mmfCommissionDebitAmount)
        .toNumber();
      const mmfCommissionCreditDifference = new Decimal(mmfCommissionDbTotalAmount)
        .minus(mmfCommissionCreditAmount)
        .toNumber();
      const mmfCommissionIsValid = mmfCommissionDebitDifference === 0 && mmfCommissionCreditDifference === 0;

      const mmfCommissionResult: ValidationResult = {
        transactionType: "dividends_mmf_commission",
        isValid: mmfCommissionIsValid,
        dbTotalAmount: mmfCommissionDbTotalAmount,
        ledgerTotalAmount: mmfCommissionDebitAmount,
        difference: mmfCommissionDebitDifference,
        transactionCount: mmfDividends.filter((d) => (d.fees.commission.amount || 0) > 0).length,
        ledgerEntryCount: mmfCommissionLedgerEntries.filter((entry) => {
          const transactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return mmfDividendIds.has(transactionId);
        }).length
      };

      if (!mmfCommissionIsValid) {
        mmfCommissionResult.discrepancies =
          await AccountingValidationService._findMMFDividendCommissionDiscrepancies(
            mmfDividends,
            mmfCommissionLedgerEntries
          );
      }

      logger.info("Dividends validation completed", {
        module: "AccountingValidationService",
        method: "validateDividendsDbWithLedger",
        data: { assetDividendsResult, mmfReceiptResult, mmfCommissionResult }
      });

      return [assetDividendsResult, mmfReceiptResult, mmfCommissionResult];
    } catch (error) {
      captureException(error);
      logger.error("Error during dividends validation", {
        module: "AccountingValidationService",
        method: "validateDividendsDbWithLedger",
        data: { error }
      });
      // Return empty results on error to maintain consistent interface
      return [];
    }
  }

  /**
   * Validates that order transaction amounts and commission fees match their accounting ledger entries
   * Validates four types of trading flows according to PRD_ACCOUNTING.md specifications
   *
   * Checks for Asset Buy Orders (linked to AssetTransaction)
   * Checks for Asset Sell Orders (linked to AssetTransaction)
   * Checks for MMF Buy Orders (linked to SavingsTopupTransaction)
   * Checks for MMF Sell Orders (linked to SavingsWithdrawalTransaction)
   *
   * Excludes: Orders linked to custody charge transactions (sell orders from custody fees)
   * Includes: Orders from rebalance transactions, regular asset transactions
   */
  public static async validateOrdersDbWithLedger(fromDate: string): Promise<ValidationResult[]> {
    logger.info("Starting orders validation", {
      module: "AccountingValidationService",
      method: "validateOrdersDbWithLedger",
      data: { fromDate }
    });

    try {
      // Get all ledger entries for asset trades with date filter
      const assetBuyLedgerEntries = await AccountingLedgerStorageService.queryLedgerEntriesByEventType(
        AccountingEventType.ASSET_BUY,
        fromDate
      );
      const assetSellLedgerEntries = await AccountingLedgerStorageService.queryLedgerEntriesByEventType(
        AccountingEventType.ASSET_SELL,
        fromDate
      );

      const ledgerEntries = [...assetBuyLedgerEntries, ...assetSellLedgerEntries];

      const clientAccountCodes = new Set([
        LedgerAccounts.CLIENT_DOMESTIC,
        LedgerAccounts.CLIENT_EU_EEA,
        LedgerAccounts.CLIENT_INTERNATIONAL
      ]);

      // Query all matched orders from the specified date, excluding custody charges
      const ordersQuery = {
        $or: [{ status: "Matched" }, { status: "Settled" }],
        filledAt: { $gte: new Date(fromDate) }
      };

      const orders = await Order.find(ordersQuery).populate({
        path: "transaction",
        select: "category chargeType"
      });

      // Filter out orders linked to custody charge transactions
      const validOrders = orders.filter((order) => {
        const transaction = order.transaction as any;
        return transaction.category !== "ChargeTransaction";
      });

      // Separate orders by transaction type and side
      const assetBuyOrders = validOrders.filter((order) => {
        const transaction = order.transaction as any;
        return (
          order.side === "Buy" &&
          (transaction.category === "AssetTransaction" || transaction.category === "RebalanceTransaction")
        );
      });

      const assetSellOrders = validOrders.filter((order) => {
        const transaction = order.transaction as any;
        return (
          order.side === "Sell" &&
          (transaction.category === "AssetTransaction" || transaction.category === "RebalanceTransaction")
        );
      });

      const mmfBuyOrders = validOrders.filter((order) => {
        const transaction = order.transaction as any;
        return order.side === "Buy" && transaction.category === "SavingsTopupTransaction";
      });

      const mmfSellOrders = validOrders.filter((order) => {
        const transaction = order.transaction as any;
        return order.side === "Sell" && transaction.category === "SavingsWithdrawalTransaction";
      });

      // Validate each order type
      const assetBuyResult = await AccountingValidationService._validateOrderType(
        assetBuyOrders,
        ledgerEntries,
        clientAccountCodes,
        "asset_buy",
        "Buy"
      );

      const assetSellResult = await AccountingValidationService._validateOrderType(
        assetSellOrders,
        ledgerEntries,
        clientAccountCodes,
        "asset_sell",
        "Sell"
      );

      const mmfBuyResult = await AccountingValidationService._validateOrderType(
        mmfBuyOrders,
        ledgerEntries,
        clientAccountCodes,
        "mmf_buy",
        "Buy"
      );

      const mmfSellResult = await AccountingValidationService._validateOrderType(
        mmfSellOrders,
        ledgerEntries,
        clientAccountCodes,
        "mmf_sell",
        "Sell"
      );

      logger.info("Orders validation completed", {
        module: "AccountingValidationService",
        method: "validateOrdersDbWithLedger",
        data: { assetBuyResult, assetSellResult, mmfBuyResult, mmfSellResult }
      });

      return [assetBuyResult, assetSellResult, mmfBuyResult, mmfSellResult];
    } catch (error) {
      captureException(error);
      logger.error("Error during orders validation", {
        module: "AccountingValidationService",
        method: "validateOrdersDbWithLedger",
        data: { error: error.message, stack: error.stack }
      });
      return [];
    }
  }

  /**
   * Ledger ↔ WealthKernel cash reconciliation validation method
   * Compares WealthKernel cash balance snapshots with ledger entries for a given period
   * This method operates independently of the existing DB ↔ Ledger validation suite
   *
   * @param fromDate ISO date string (YYYY-MM-DD) for the start of the reconciliation period
   * @returns CashReconciliationResult with per-account and aggregate deltas
   */
  public static async validateCashLedgerReconciliation(fromDate: string): Promise<CashReconciliationResult> {
    logger.info("Starting cash ledger reconciliation", {
      module: "AccountingValidationService",
      method: "validateCashLedgerReconciliation",
      data: { fromDate }
    });

    try {
      const perAccount: Record<LedgerAccounts, CashReconDelta> = {} as Record<LedgerAccounts, CashReconDelta>;
      let aggregateStartBalance = 0;
      let aggregateEndBalance = 0;
      let aggregateLedgerNetChange = 0;

      // Get the list of accounts to validate (only those with WK mappings)
      const accountsToValidate = Object.keys(CASH_ACCOUNTS_WK_MAPPING).filter(
        (accountCode) =>
          CASH_ACCOUNTS_WK_MAPPING[accountCode as LedgerAccounts] !== undefined ||
          accountCode === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS
      ) as LedgerAccounts[];

      logger.info(`Validating cash reconciliation for ${accountsToValidate.length} accounts`, {
        module: "AccountingValidationService",
        method: "validateCashLedgerReconciliation",
        data: { accounts: accountsToValidate }
      });

      // Loop through each account
      for (const accountCode of accountsToValidate) {
        // 1. Fetch the starting balance from cash balances table
        const startingSnapshot = await AccountingLedgerStorageService.getCashBalanceOnOrAfterDate(
          accountCode,
          fromDate
        );
        const startBalance = startingSnapshot?.balance || 0;

        // 2. Fetch the ending balance (latest snapshot)
        const endingSnapshot = await AccountingLedgerStorageService.getLatestCashBalance(accountCode);
        const endBalance = endingSnapshot?.balance || 0;

        // 3. Calculate WK delta
        const deltaWK = new Decimal(endBalance).minus(startBalance).toNumber();

        // 4. Query ledger entries for this account between fromDate and now
        const ledgerEntries = await AccountingLedgerStorageService.queryLedgerEntries({
          account_code: accountCode,
          article_date_from: fromDate
        });

        // 5. Calculate ledger net change (debits - credits)
        const deltaLedger = ledgerEntries
          .reduce((net, entry) => {
            if (entry.side === "debit") {
              return net.plus(entry.amount);
            } else {
              return net.minus(entry.amount);
            }
          }, new Decimal(0))
          .toNumber();

        // 6. Calculate difference (0 tolerance)
        const difference = new Decimal(deltaWK).minus(deltaLedger).toNumber();

        // Store per-account result
        perAccount[accountCode] = {
          deltaWK,
          deltaLedger,
          difference
        };

        // Add to aggregates
        aggregateStartBalance = new Decimal(aggregateStartBalance).plus(startBalance).toNumber();
        aggregateEndBalance = new Decimal(aggregateEndBalance).plus(endBalance).toNumber();
        aggregateLedgerNetChange = new Decimal(aggregateLedgerNetChange).plus(deltaLedger).toNumber();

        logger.info("Account reconciliation completed", {
          module: "AccountingValidationService",
          method: "validateCashLedgerReconciliation",
          data: {
            accountCode,
            startBalance,
            endBalance,
            deltaWK,
            deltaLedger,
            difference,
            ledgerEntryCount: ledgerEntries.length
          }
        });
      }

      // Calculate aggregate results
      const aggregateDeltaWK = new Decimal(aggregateEndBalance).minus(aggregateStartBalance).toNumber();
      const aggregateDifference = new Decimal(aggregateDeltaWK).minus(aggregateLedgerNetChange).toNumber();

      const aggregate: CashReconDelta = {
        deltaWK: aggregateDeltaWK,
        deltaLedger: aggregateLedgerNetChange,
        difference: aggregateDifference
      };

      const result: CashReconciliationResult = {
        fromDate,
        perAccount,
        aggregate
      };

      // Log structured result
      logger.info("Cash ledger reconciliation completed", {
        module: "AccountingValidationService",
        method: "validateCashLedgerReconciliation",
        data: result
      });

      return result;
    } catch (error) {
      captureException(error);
      logger.error("Error during cash ledger reconciliation", {
        module: "AccountingValidationService",
        method: "validateCashLedgerReconciliation",
        data: { error: error.message, stack: error.stack }
      });
    }
  }

  /**
   * Helper method to validate a specific order type (buy/sell) - NO TOLERANCE
   */
  private static async _validateOrderType(
    orders: any[],
    ledgerEntries: LedgerQueryResult[],
    clientAccountCodes: Set<LedgerAccounts>,
    transactionType: "asset_buy" | "asset_sell" | "mmf_buy" | "mmf_sell",
    side: "Buy" | "Sell"
  ): Promise<ValidationResult> {
    // Calculate total amounts from database
    const dbTotalAmount = orders
      .reduce((sum, order) => {
        const amount = new Decimal(order.consideration.amount || 0).div(100);
        return sum.plus(amount);
      }, new Decimal(0))
      .toNumber();

    // Get unique transaction IDs from the orders
    const transactionIds = new Set(
      orders.map((order) => {
        const transaction = order.transaction as any;
        return transaction._id.toString();
      })
    );

    // Filter ledger entries for these transactions (not orders!)
    const orderLedgerEntries = ledgerEntries.filter((entry) => {
      const transactionId = AccountingValidationService._extractTransactionIdFromDescription(entry.description);
      return transactionIds.has(transactionId);
    });

    // Calculate total client-side ledger amounts
    let ledgerClientAmount = 0;

    if (side === "Buy") {
      // For buy orders: sum all client debits (net settlement + commission fees)
      ledgerClientAmount = orderLedgerEntries
        .filter((entry) => {
          const isClientAccount = clientAccountCodes.has(entry.account_code as LedgerAccounts);
          return entry.side === "debit" && isClientAccount;
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();
    } else {
      // For sell orders: sum client credits + client debits (net proceeds + commission)
      const clientCredits = orderLedgerEntries
        .filter((entry) => {
          const isClientAccount = clientAccountCodes.has(entry.account_code as LedgerAccounts);
          return entry.side === "credit" && isClientAccount;
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      const clientDebits = orderLedgerEntries
        .filter((entry) => {
          const isClientAccount = clientAccountCodes.has(entry.account_code as LedgerAccounts);
          return entry.side === "debit" && isClientAccount;
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // For sell orders, total client impact = net proceeds + commission charges
      ledgerClientAmount = clientCredits + clientDebits;
    }

    // EXACT validation - NO tolerance
    const difference = new Decimal(dbTotalAmount).minus(ledgerClientAmount).toNumber();
    const isValid = difference === 0;

    const result: ValidationResult = {
      transactionType,
      isValid,
      dbTotalAmount,
      ledgerTotalAmount: ledgerClientAmount,
      difference,
      transactionCount: orders.length,
      ledgerEntryCount: orderLedgerEntries.length
    };

    if (!isValid) {
      result.discrepancies = await AccountingValidationService._findOrderDiscrepancies(
        orders,
        orderLedgerEntries,
        side
      );
    }

    return result;
  }

  /**
   * Finds individual discrepancies between orders and their ledger entries
   */
  private static async _findOrderDiscrepancies(
    orders: any[],
    ledgerEntries: LedgerQueryResult[],
    side: "Buy" | "Sell"
  ): Promise<ValidationDiscrepancy[]> {
    const discrepancies: ValidationDiscrepancy[] = [];
    const clientAccountCodes = new Set([
      LedgerAccounts.CLIENT_DOMESTIC,
      LedgerAccounts.CLIENT_EU_EEA,
      LedgerAccounts.CLIENT_INTERNATIONAL
    ]);

    for (const order of orders) {
      const orderId = order._id.toString();
      const transaction = order.transaction as any;
      const transactionId = transaction._id.toString();

      // Use order.consideration.amount directly as the total amount (including fees)
      const dbTotalAmount = new Decimal(order.consideration.amount || 0).div(100).toNumber();

      // Find all ledger entries for this transaction (not order)
      const orderEntries = ledgerEntries.filter((entry) => {
        const entryTransactionId = AccountingValidationService._extractTransactionIdFromDescription(
          entry.description
        );
        return entryTransactionId === transactionId;
      });

      // Calculate total client-side ledger amount
      let ledgerClientAmount = 0;

      if (side === "Buy") {
        ledgerClientAmount = orderEntries
          .filter((entry) => {
            const isClientAccount = clientAccountCodes.has(entry.account_code as LedgerAccounts);
            return entry.side === "debit" && isClientAccount;
          })
          .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
          .toNumber();
      } else {
        // For sell orders: sum client credits + client debits
        const clientCredits = orderEntries
          .filter((entry) => {
            const isClientAccount = clientAccountCodes.has(entry.account_code as LedgerAccounts);
            return entry.side === "credit" && isClientAccount;
          })
          .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
          .toNumber();

        const clientDebits = orderEntries
          .filter((entry) => {
            const isClientAccount = clientAccountCodes.has(entry.account_code as LedgerAccounts);
            return entry.side === "debit" && isClientAccount;
          })
          .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
          .toNumber();

        ledgerClientAmount = clientCredits + clientDebits;
      }

      const difference = new Decimal(dbTotalAmount).minus(ledgerClientAmount).toNumber();

      if (Math.abs(difference) > 0) {
        discrepancies.push({
          transactionId: orderId,
          dbAmount: dbTotalAmount,
          ledgerAmount: ledgerClientAmount,
          difference
        });
      }
    }

    return discrepancies;
  }

  /**
   * Extracts transaction ID from accounting ledger description
   * Format: "user_id|transaction_id|event_type" or "user_id|transaction_id|isin|event_type"
   */
  private static _extractTransactionIdFromDescription(description: string): string {
    const parts = description.split("|");
    return parts.length >= 2 ? parts[1] : "";
  }

  /**
   * Finds individual discrepancies between deposits and their ledger entries
   */
  private static async _findDepositDiscrepancies(
    deposits: any[],
    ledgerEntries: LedgerQueryResult[]
  ): Promise<ValidationDiscrepancy[]> {
    const discrepancies: ValidationDiscrepancy[] = [];
    const clientAccountCodes = new Set([
      LedgerAccounts.CLIENT_DOMESTIC,
      LedgerAccounts.CLIENT_EU_EEA,
      LedgerAccounts.CLIENT_INTERNATIONAL
    ]);

    for (const deposit of deposits) {
      const transactionId = deposit._id.toString();
      const dbAmount = Decimal.div(deposit.consideration.amount || 0, 100).toNumber();

      // Find credit entries to client accounts for this transaction
      const creditEntries = ledgerEntries.filter((entry) => {
        const entryTransactionId = AccountingValidationService._extractTransactionIdFromDescription(
          entry.description
        );
        return (
          entryTransactionId === transactionId &&
          entry.side === "credit" &&
          clientAccountCodes.has(entry.account_code as LedgerAccounts)
        );
      });

      // Sum credit amounts to client accounts
      const ledgerAmount = creditEntries.reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0)).toNumber();

      const difference = new Decimal(dbAmount).minus(ledgerAmount).toNumber();

      if (Math.abs(difference) > 0) {
        discrepancies.push({
          transactionId,
          dbAmount,
          ledgerAmount,
          difference
        });
      }
    }

    return discrepancies;
  }

  /**
   * Finds individual discrepancies between withdrawals and their ledger entries
   */
  private static async _findWithdrawalDiscrepancies(
    withdrawals: any[],
    ledgerEntries: LedgerQueryResult[]
  ): Promise<ValidationDiscrepancy[]> {
    const discrepancies: ValidationDiscrepancy[] = [];
    const clientAccountCodes = new Set([
      LedgerAccounts.CLIENT_DOMESTIC,
      LedgerAccounts.CLIENT_EU_EEA,
      LedgerAccounts.CLIENT_INTERNATIONAL
    ]);

    for (const withdrawal of withdrawals) {
      const transactionId = withdrawal._id.toString();
      const dbAmount = Decimal.div(withdrawal.consideration.amount || 0, 100).toNumber();

      // Check if withdrawal has reached Stage 2 (Devengo outgoing confirmed)
      const hasReachedStage2 =
        withdrawal.transferWithIntermediary?.collection?.outgoingPayment?.providers?.devengo?.status ===
        "confirmed";

      let debitEntries;
      if (hasReachedStage2) {
        // Stage 2: Find debit entries from client accounts (Intermediary → Client)
        debitEntries = ledgerEntries.filter((entry) => {
          const entryTransactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return (
            entryTransactionId === transactionId &&
            entry.side === "debit" &&
            clientAccountCodes.has(entry.account_code as LedgerAccounts)
          );
        });
      } else {
        // Stage 1: Find debit entries from intermediary withdrawals account (Omnibus → Intermediary)
        debitEntries = ledgerEntries.filter((entry) => {
          const entryTransactionId = AccountingValidationService._extractTransactionIdFromDescription(
            entry.description
          );
          return (
            entryTransactionId === transactionId &&
            entry.side === "debit" &&
            entry.account_code === LedgerAccounts.INTERMEDIARY_WITHDRAWALS
          );
        });
      }

      // Sum debit amounts
      const ledgerAmount = debitEntries.reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0)).toNumber();

      const difference = new Decimal(dbAmount).minus(ledgerAmount).toNumber();

      if (Math.abs(difference) > 0) {
        discrepancies.push({
          transactionId,
          dbAmount,
          ledgerAmount,
          difference
        });
      }
    }

    return discrepancies;
  }

  /**
   * Finds individual discrepancies between asset dividends and their ledger entries
   */
  private static async _findAssetDividendDiscrepancies(
    dividends: any[],
    ledgerEntries: LedgerQueryResult[]
  ): Promise<ValidationDiscrepancy[]> {
    const discrepancies: ValidationDiscrepancy[] = [];
    const clientAccountCodes = new Set([
      LedgerAccounts.CLIENT_DOMESTIC,
      LedgerAccounts.CLIENT_EU_EEA,
      LedgerAccounts.CLIENT_INTERNATIONAL
    ]);

    for (const dividend of dividends) {
      const transactionId = dividend._id.toString();
      const dbAmount = new Decimal(dividend.consideration.amount || 0).div(100).toNumber();

      // Find credit entries to client accounts for this transaction
      const creditEntries = ledgerEntries.filter((entry) => {
        const entryTransactionId = AccountingValidationService._extractTransactionIdFromDescription(
          entry.description
        );
        return (
          entryTransactionId === transactionId &&
          entry.side === "credit" &&
          clientAccountCodes.has(entry.account_code as LedgerAccounts)
        );
      });

      // Sum credit amounts to client accounts
      const ledgerAmount = creditEntries.reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0)).toNumber();

      const difference = new Decimal(dbAmount).minus(ledgerAmount).toNumber();

      if (Math.abs(difference) > 0) {
        discrepancies.push({
          transactionId,
          dbAmount,
          ledgerAmount,
          difference
        });
      }
    }

    return discrepancies;
  }

  /**
   * Finds individual discrepancies between MMF dividend receipts and their ledger entries
   */
  private static async _findMMFDividendReceiptDiscrepancies(
    dividends: any[],
    ledgerEntries: LedgerQueryResult[]
  ): Promise<ValidationDiscrepancy[]> {
    const discrepancies: ValidationDiscrepancy[] = [];
    const clientAccountCodes = new Set([
      LedgerAccounts.CLIENT_DOMESTIC,
      LedgerAccounts.CLIENT_EU_EEA,
      LedgerAccounts.CLIENT_INTERNATIONAL
    ]);

    for (const dividend of dividends) {
      const transactionId = dividend._id.toString();
      const dbAmount = new Decimal(dividend.originalDividendAmount || 0).div(100).toNumber();

      // Find credit entries to client accounts for this transaction
      const creditEntries = ledgerEntries.filter((entry) => {
        const entryTransactionId = AccountingValidationService._extractTransactionIdFromDescription(
          entry.description
        );
        return (
          entryTransactionId === transactionId &&
          entry.side === "credit" &&
          clientAccountCodes.has(entry.account_code as LedgerAccounts)
        );
      });

      // Sum credit amounts to client accounts
      const ledgerAmount = creditEntries.reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0)).toNumber();

      const difference = new Decimal(dbAmount).minus(ledgerAmount).toNumber();

      if (Math.abs(difference) > 0) {
        discrepancies.push({
          transactionId,
          dbAmount,
          ledgerAmount,
          difference
        });
      }
    }

    return discrepancies;
  }

  /**
   * Finds individual discrepancies between MMF dividend commissions and their ledger entries
   */
  private static async _findMMFDividendCommissionDiscrepancies(
    dividends: any[],
    ledgerEntries: LedgerQueryResult[]
  ): Promise<ValidationDiscrepancy[]> {
    const discrepancies: ValidationDiscrepancy[] = [];
    const clientAccountCodes = new Set([
      LedgerAccounts.CLIENT_DOMESTIC,
      LedgerAccounts.CLIENT_EU_EEA,
      LedgerAccounts.CLIENT_INTERNATIONAL
    ]);

    for (const dividend of dividends) {
      const transactionId = dividend._id.toString();
      const dbAmount = new Decimal(dividend.fees.commission.amount || 0).toNumber();

      // Skip dividends with no commission
      if (dbAmount <= 0) continue;

      // Find debit entries from client accounts for this transaction
      const debitEntries = ledgerEntries.filter((entry) => {
        const entryTransactionId = AccountingValidationService._extractTransactionIdFromDescription(
          entry.description
        );
        return (
          entryTransactionId === transactionId &&
          entry.side === "debit" &&
          clientAccountCodes.has(entry.account_code as LedgerAccounts)
        );
      });

      // Sum debit amounts from client accounts
      const ledgerAmount = debitEntries.reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0)).toNumber();

      const difference = new Decimal(dbAmount).minus(ledgerAmount).toNumber();

      if (Math.abs(difference) > 0) {
        discrepancies.push({
          transactionId,
          dbAmount,
          ledgerAmount,
          difference
        });
      }
    }

    return discrepancies;
  }
}
