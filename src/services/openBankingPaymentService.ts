import {
  BeneficiaryEnum,
  FailureStatusType as TruelayerFailureStatusType,
  PaymentStatusTypeV3 as TruelayerPaymentStatusTypeV3,
  TruelayerPaymentsClient,
  TruelayerPayProvidersType
} from "./../external-services/truelayerService";
import { banksConfig, countriesConfig } from "@wealthyhood/shared-configs";
import ConfigUtil from "../utils/configUtil";
import { PaymentStatusType, SaltedgeService } from "../external-services/saltedgeService";
import { customAlphabet } from "nanoid";
import Decimal from "decimal.js";
import {
  DepositActionEnum,
  SALTEDGE_REDIRECT_URI_CONFIG,
  TRUELAYER_REDIRECT_URI_CONFIG
} from "../configs/depositsConfig";
import { PlatformCategoryEnum } from "../configs/platformConfig";
import { UNKNOWN_LOGO_URL } from "../utils/banksUtil";

const alphabet = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
export const nanoid = customAlphabet(alphabet, 16);

/**
 * Types
 */
type OpenBankingDepositPaymentStatus = TruelayerPaymentStatusTypeV3 | PaymentStatusType;
type OpenBankingDepositFailureStatus = TruelayerFailureStatusType;
type OpenBankingPayProviders = TruelayerPayProvidersType;

export type OpenBankingPaymentProvider = {
  id: string;
  name: string;
  logo: string;
};

type OpenBankingDepositType = {
  status: OpenBankingDepositPaymentStatus;
  failureReason?: OpenBankingDepositFailureStatus | string;
  executedAt?: Date;
};

type OpenBankingDepositCreationResponseType = {
  providerData: TruelayerDepositCreationDataType | SaltedgeDepositCreationDataType;
  paymentUri: string;
  bankReference?: string;
};

export type TruelayerDepositCreationDataType = {
  truelayer: {
    id: string;
    version?: "v3";
    status: TruelayerPaymentStatusTypeV3;
  };
};

type SaltedgeDepositCreationDataType = {
  saltedge: {
    customId: string;
  };
};

type CreateOpenBankingDepositType = {
  amount: number;
  beneficiary: BeneficiaryEnum;
  remitter: {
    bankId?: banksConfig.BankType;
    name: string;
    number: string;
    sortCode: string;
    providerId: OpenBankingPayProviders;
  };
  user: {
    email: string;
    name: string;
    providers: {
      saltedge?: {
        id: string;
      };
    };
  };
  depositAction: DepositActionEnum;
  clientInfo: {
    platform?: PlatformCategoryEnum;
    version?: string;
  };
};

/**
 * Deposit Payment Service Interface
 */
export interface OpenBankingPaymentServiceInterface {
  getAvailableBanks(residencyCountry: countriesConfig.CountryCodesType): OpenBankingPaymentProvider[];
  createOpenBankingDeposit(data: CreateOpenBankingDepositType): Promise<OpenBankingDepositCreationResponseType>;
  getOpenBankingDeposit(openBankingDepositId: string): Promise<OpenBankingDepositType>;
}

export class SaltedgeBasedOpenBankingPaymentService implements OpenBankingPaymentServiceInterface {
  private static _instance: SaltedgeBasedOpenBankingPaymentService;

  public static get Instance(): SaltedgeBasedOpenBankingPaymentService {
    return this._instance || (this._instance = new this());
  }

  public getAvailableBanks(residencyCountry: countriesConfig.CountryCodesType): OpenBankingPaymentProvider[] {
    return Object.entries(banksConfig.BANKS_CONFIG)
      .filter(([bankKey]) =>
        banksConfig.AVAILABLE_PROVIDERS[ConfigUtil.getRegion(residencyCountry)][
          banksConfig.BankProviderScopeEnum.PAY
        ].includes(bankKey as banksConfig.BankType)
      )
      .map(([id, config]) => {
        return {
          id,
          providerId: config.saltedgeInstitutionId,
          name: config.name,
          logo: config.logo ?? UNKNOWN_LOGO_URL
        };
      });
  }

  public async createOpenBankingDeposit(
    data: CreateOpenBankingDepositType
  ): Promise<OpenBankingDepositCreationResponseType> {
    if (!data?.user?.providers?.saltedge?.id) {
      throw new Error("We cannot create payment for user as they are not submitted to Saltedge!");
    }

    const customId = nanoid();

    const response = await SaltedgeService.Instance.createPaymentSession(
      {
        customerId: data.user.providers.saltedge.id,
        returnTo: `${SALTEDGE_REDIRECT_URI_CONFIG[data.clientInfo.platform][data.depositAction]}?customId=${customId}&bankId=${data.remitter.bankId}`,
        amount: Decimal.div(data.amount, 100).toNumber().toString(),
        fullName: data.user.name,
        reference: customId
      },
      data.remitter.bankId,
      customId
    );

    return {
      paymentUri: response.data.connect_url,
      bankReference: customId,
      providerData: {
        saltedge: {
          customId
        }
      }
    };
  }

  public async getOpenBankingDeposit(openBankingDepositId: string): Promise<OpenBankingDepositType> {
    const payment = await SaltedgeService.Instance.getPayment(openBankingDepositId);

    return {
      status: payment.data.status
    };
  }
}

export class TruelayerBasedOpenBankingPaymentService implements OpenBankingPaymentServiceInterface {
  private _truelayerPaymentsClient: TruelayerPaymentsClient;
  private static _instance: TruelayerBasedOpenBankingPaymentService;

  public static get Instance(): TruelayerBasedOpenBankingPaymentService {
    return this._instance || (this._instance = new this());
  }

  constructor() {
    this._truelayerPaymentsClient = new TruelayerPaymentsClient();
  }

  public getAvailableBanks(residencyCountry: countriesConfig.CountryCodesType): OpenBankingPaymentProvider[] {
    return Object.entries(banksConfig.BANKS_CONFIG)
      .filter(([bankKey]) =>
        banksConfig.AVAILABLE_PROVIDERS[ConfigUtil.getRegion(residencyCountry)][
          banksConfig.BankProviderScopeEnum.PAY
        ].includes(bankKey as banksConfig.BankType)
      )
      .map(([id, config]) => {
        return {
          id,
          providerId: config.truelayerInstitutionId,
          name: config.name,
          logo: config.logo ?? UNKNOWN_LOGO_URL
        };
      });
  }

  public async createOpenBankingDeposit(
    data: CreateOpenBankingDepositType
  ): Promise<OpenBankingDepositCreationResponseType> {
    const bankReference = nanoid();

    const response = await this._truelayerPaymentsClient.createPayment({
      ...data,
      bankReference,
      redirectUri: TRUELAYER_REDIRECT_URI_CONFIG[data.clientInfo.platform][data.depositAction]
    });

    return {
      bankReference,
      paymentUri: response.paymentUri,
      providerData: {
        truelayer: {
          id: response.paymentId,
          status: response.status,
          version: "v3"
        }
      }
    };
  }

  public async getOpenBankingDeposit(openBankingDepositId: string): Promise<OpenBankingDepositType> {
    return this._truelayerPaymentsClient.getPayment(openBankingDepositId);
  }
}
