import {
  DefaultSumsubBasedKycService,
  ExtendedVerificationSumsubBasedKycService,
  KycServiceInterface
} from "./sumsubBasedKycService";
import {
  GoCardlessBasedRepeatingDepositPaymentService,
  RepeatingDepositPaymentServiceInterface,
  WealthkernelBasedRepeatingDepositPaymentService
} from "./repeatingDepositPaymentService";
import {
  CardBasedSubscriptionPaymentService,
  SubscriptionPaymentServiceInterface
} from "./subscriptionPaymentService";
import {
  OpenBankingPaymentServiceInterface,
  SaltedgeBasedOpenBankingPaymentService,
  TruelayerBasedOpenBankingPaymentService
} from "./openBankingPaymentService";
import {
  AnonymousWealthkernelBasedBrokerageService,
  BrokerageServiceInterface,
  DefaultWealthkernelBasedBrokerageService
} from "./brokerageService";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { PartialRecord } from "utils";
import { ProviderEnum } from "../configs/providersConfig";
import {
  GoCardlessBasedOpenBankingDataService,
  OpenBankingDataServiceInterface,
  TruelayerBasedOpenBankingDataService
} from "./openBankingDataService";
import {
  BankTransferPaymentServiceInterface,
  BaseBankTransferPaymentService,
  DevengoBasedSingleDepositPaymentService
} from "./bankTransferPaymentService";
import { WealthkernelAccountRegionEnum } from "../external-services/wealthkernelService";

enum ServiceTypeEnum {
  BROKERAGE = "brokerageService",
  OPEN_BANKING_DATA = "openBankingDataService",
  OPEN_BANKING_PAYMENTS = "openBankingPaymentService",
  BANK_TRANSFER_PAYMENTS = "bankTransferPaymentService",
  REPEATING_DEPOSIT_PAYMENTS = "repeatingDepositPaymentService",
  SUBSCRIPTION_PAYMENTS = "subscriptionPaymentService",
  KYC = "kycService"
}

export enum ProviderScopeEnum {
  BROKERAGE = "brokerage",
  BANK_ACCOUNTS = "bankAccounts",
  BANK_TRANSFER_PAYMENTS = "bankTransferDepositPayment",
  SINGLE_DEPOSIT_PAYMENTS = "singleDepositPayment",
  SINGLE_DEPOSIT_CHARGES = "singleDepositCharge",
  DIRECT_DEBIT_DEPOSIT_PAYMENTS = "repeatingDepositPayment",
  DIRECT_DEBIT_MANDATES = "directDebitMandate",
  CARD_SUBSCRIPTION_PAYMENTS = "cardSubscriptionPayment",
  KYC = "kyc",
  ADDRESSES = "addresses",
  W_8BEN = "w8ben"
}

type ActiveProviderConfigType = Record<
  entitiesConfig.CompanyEntityEnum,
  PartialRecord<ProviderScopeEnum, ProviderEnum[]>
>;
type ServiceConfigType = Record<
  entitiesConfig.CompanyEntityEnum,
  Record<
    ServiceTypeEnum,
    | BrokerageServiceInterface
    | SubscriptionPaymentServiceInterface
    | RepeatingDepositPaymentServiceInterface
    | OpenBankingPaymentServiceInterface
    | OpenBankingDataServiceInterface
    | BankTransferPaymentServiceInterface
    | KycServiceInterface
  >
>;

export default class ProviderService {
  private static _serviceConfig: ServiceConfigType = {
    [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK]: {
      [ServiceTypeEnum.BROKERAGE]: DefaultWealthkernelBasedBrokerageService.getInstance({
        region: WealthkernelAccountRegionEnum.UK
      }),
      [ServiceTypeEnum.OPEN_BANKING_DATA]: TruelayerBasedOpenBankingDataService.Instance,
      [ServiceTypeEnum.OPEN_BANKING_PAYMENTS]: TruelayerBasedOpenBankingPaymentService.Instance,
      [ServiceTypeEnum.BANK_TRANSFER_PAYMENTS]: BaseBankTransferPaymentService.Instance,
      [ServiceTypeEnum.SUBSCRIPTION_PAYMENTS]: CardBasedSubscriptionPaymentService.Instance,
      [ServiceTypeEnum.REPEATING_DEPOSIT_PAYMENTS]: WealthkernelBasedRepeatingDepositPaymentService.Instance,
      [ServiceTypeEnum.KYC]: DefaultSumsubBasedKycService.Instance
    },
    [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE]: {
      [ServiceTypeEnum.BROKERAGE]: AnonymousWealthkernelBasedBrokerageService.getInstance({
        region: WealthkernelAccountRegionEnum.EU
      }),
      [ServiceTypeEnum.OPEN_BANKING_DATA]: GoCardlessBasedOpenBankingDataService.Instance,
      [ServiceTypeEnum.OPEN_BANKING_PAYMENTS]: SaltedgeBasedOpenBankingPaymentService.Instance,
      [ServiceTypeEnum.BANK_TRANSFER_PAYMENTS]: DevengoBasedSingleDepositPaymentService.Instance,
      [ServiceTypeEnum.SUBSCRIPTION_PAYMENTS]: CardBasedSubscriptionPaymentService.Instance,
      [ServiceTypeEnum.REPEATING_DEPOSIT_PAYMENTS]: GoCardlessBasedRepeatingDepositPaymentService.Instance,
      [ServiceTypeEnum.KYC]: ExtendedVerificationSumsubBasedKycService.Instance
    }
  };

  private static _defaultActiveProviderConfig: ActiveProviderConfigType = {
    [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK]: {
      [ProviderScopeEnum.BROKERAGE]: [ProviderEnum.WEALTHKERNEL],
      [ProviderScopeEnum.ADDRESSES]: [ProviderEnum.WEALTHKERNEL],
      [ProviderScopeEnum.W_8BEN]: [ProviderEnum.WEALTHKERNEL],
      [ProviderScopeEnum.BANK_ACCOUNTS]: [ProviderEnum.WEALTHKERNEL],
      [ProviderScopeEnum.SINGLE_DEPOSIT_PAYMENTS]: [ProviderEnum.TRUELAYER, ProviderEnum.WEALTHKERNEL],
      [ProviderScopeEnum.SINGLE_DEPOSIT_CHARGES]: [ProviderEnum.TRUELAYER],
      [ProviderScopeEnum.DIRECT_DEBIT_MANDATES]: [ProviderEnum.WEALTHKERNEL],
      [ProviderScopeEnum.DIRECT_DEBIT_DEPOSIT_PAYMENTS]: [ProviderEnum.WEALTHKERNEL],
      [ProviderScopeEnum.CARD_SUBSCRIPTION_PAYMENTS]: [ProviderEnum.STRIPE],
      [ProviderScopeEnum.KYC]: [ProviderEnum.SUMSUB]
    },
    [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE]: {
      [ProviderScopeEnum.BROKERAGE]: [ProviderEnum.WEALTHKERNEL],
      [ProviderScopeEnum.BANK_ACCOUNTS]: [ProviderEnum.GOCARDLESS, ProviderEnum.WEALTHYHOOD],
      [ProviderScopeEnum.SINGLE_DEPOSIT_PAYMENTS]: [ProviderEnum.SALTEDGE, ProviderEnum.WEALTHKERNEL],
      [ProviderScopeEnum.BANK_TRANSFER_PAYMENTS]: [ProviderEnum.DEVENGO, ProviderEnum.WEALTHKERNEL],
      [ProviderScopeEnum.DIRECT_DEBIT_MANDATES]: [ProviderEnum.GOCARDLESS],
      [ProviderScopeEnum.DIRECT_DEBIT_DEPOSIT_PAYMENTS]: [ProviderEnum.GOCARDLESS],
      [ProviderScopeEnum.CARD_SUBSCRIPTION_PAYMENTS]: [ProviderEnum.STRIPE],
      [ProviderScopeEnum.KYC]: [ProviderEnum.SUMSUB]
    }
  };

  public static getBrokerageService(companyEntity: entitiesConfig.CompanyEntityEnum): BrokerageServiceInterface {
    return ProviderService._serviceConfig[companyEntity][ServiceTypeEnum.BROKERAGE] as BrokerageServiceInterface;
  }

  public static getOpenBankingPaymentService(
    // Default is set to WEALTHYHOOD_UK as we have not implemented EU deposits yet.
    companyEntity: entitiesConfig.CompanyEntityEnum = entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
  ): OpenBankingPaymentServiceInterface {
    return ProviderService._serviceConfig[companyEntity][
      ServiceTypeEnum.OPEN_BANKING_PAYMENTS
    ] as OpenBankingPaymentServiceInterface;
  }

  public static getBankTransferPaymentService(
    companyEntity: entitiesConfig.CompanyEntityEnum = entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
  ): BankTransferPaymentServiceInterface {
    return ProviderService._serviceConfig[companyEntity][
      ServiceTypeEnum.BANK_TRANSFER_PAYMENTS
    ] as BankTransferPaymentServiceInterface;
  }

  public static getOpenBankingDataService(
    companyEntity: entitiesConfig.CompanyEntityEnum = entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
  ): OpenBankingDataServiceInterface {
    return ProviderService._serviceConfig[companyEntity][
      ServiceTypeEnum.OPEN_BANKING_DATA
    ] as OpenBankingDataServiceInterface;
  }

  public static getSubscriptionPaymentService(
    companyEntity: entitiesConfig.CompanyEntityEnum
  ): SubscriptionPaymentServiceInterface {
    return ProviderService._serviceConfig[companyEntity][
      ServiceTypeEnum.SUBSCRIPTION_PAYMENTS
    ] as SubscriptionPaymentServiceInterface;
  }

  public static getRepeatingDepositPaymentService(
    // Default is set to WEALTHYHOOD_UK as we have not implemented EU repeating deposits yet.
    companyEntity: entitiesConfig.CompanyEntityEnum = entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
  ): RepeatingDepositPaymentServiceInterface {
    return ProviderService._serviceConfig[companyEntity][
      ServiceTypeEnum.REPEATING_DEPOSIT_PAYMENTS
    ] as RepeatingDepositPaymentServiceInterface;
  }

  public static getKycService(companyEntity: entitiesConfig.CompanyEntityEnum): KycServiceInterface {
    return ProviderService._serviceConfig[companyEntity][ServiceTypeEnum.KYC] as KycServiceInterface;
  }

  public static getProviders(
    companyEntity: entitiesConfig.CompanyEntityEnum,
    services: ProviderScopeEnum[]
  ): ProviderEnum[] {
    return [
      ...new Set(
        services.flatMap((service) => ProviderService._defaultActiveProviderConfig[companyEntity][service])
      )
    ].filter((provider) => !!provider);
  }
}
