import { QueryOptions } from "mongoose";
import validator from "validator";
import { InternalServerError } from "../models/ApiErrors";
import logger from "../external-services/loggerService";
import { GiftsFilter } from "filters";
import DbUtil from "../utils/dbUtil";
import PaginationUtil from "../utils/paginationUtil";
import { PaginatedGiftsResponse } from "apiResponse";
import { Gift, GiftDocument, GiftDTOInterface } from "../models/Gift";
import UserService from "./userService";
import MailerService from "../external-services/mailerService";
import PortfolioService from "./portfolioService";
import MailchimpService, { AudienceIdEnum } from "../external-services/mailchimpService";
import { captureException } from "@sentry/node";
import { UserDocument } from "../models/User";
import { BonusStatusType } from "../external-services/wealthkernelService";
import Decimal from "decimal.js";
import eventEmitter from "../loaders/eventEmitter";
import events from "../event-handlers/events";
import { TrackGiftPropertiesType } from "../external-services/segmentAnalyticsService";
import { TransactionService } from "./transactionService";
import { AssetTransactionDocument } from "../models/Transaction";
import ProviderService, { ProviderScopeEnum } from "./providerService";
import { currenciesConfig } from "@wealthyhood/shared-configs";
import { ProviderEnum } from "../configs/providersConfig";
import NotificationService from "./notificationService";

const GIFT_TESTING_EMAILS = ["<EMAIL>"];

export default class GiftService {
  /**
   * PUBLIC METHODS
   */

  /**
   * Given gift data, checks whether target user and gifter are eligible and if they are, creates a gift document and
   * sends an e-mail to the two users involved.
   * @param requestedGiftData
   */
  public static async createGift(requestedGiftData: GiftDTOInterface): Promise<GiftDocument> {
    const gifter = await UserService.getUser(requestedGiftData.gifter.toString());

    // We remove the gift sending ability for the gifter regardless or whether the gift is valid or not.
    await UserService.removeGiftSendingAbility(gifter.id);

    // Temporarily allow some users to re-send gifts.
    if (GIFT_TESTING_EMAILS.includes(gifter.email)) {
      await MailchimpService.updateMember(
        gifter.email,
        {
          merge_fields: { SENDGIFTCM: "Eligible to send" }
        },
        AudienceIdEnum.WEALTHYHOOD,
        { silent: true }
      );
    } else {
      await MailchimpService.updateMember(
        gifter.email,
        {
          merge_fields: { SENDGIFTCM: "Sent" }
        },
        AudienceIdEnum.WEALTHYHOOD,
        { silent: true }
      );
    }

    if (!gifter.canSendGiftUntil || gifter.canSendGiftUntil < new Date(Date.now())) {
      logger.warn("Attempted to create gift but gifter is not eligible", {
        module: "GiftService",
        method: "createGift",
        data: {
          targetUser: requestedGiftData.targetUserEmail,
          gifter: requestedGiftData.gifter.toString()
        }
      });
      return;
    }

    const targetUserExistingGifts = (await GiftService.getGifts({
      targetUserEmail: requestedGiftData.targetUserEmail
    })) as { data: GiftDocument[] };

    if (targetUserExistingGifts.data?.length > 0) {
      logger.warn(
        `Attempted to create gift but target user ${requestedGiftData.targetUserEmail} already has one`,
        {
          module: "GiftService",
          method: "createGift",
          data: {
            targetUser: requestedGiftData.targetUserEmail,
            gifter: requestedGiftData.gifter.toString()
          }
        }
      );
      return;
    }

    const targetUser = await UserService.getUserByEmail(requestedGiftData.targetUserEmail);

    let giftData = requestedGiftData;
    if (targetUser) {
      const assetTransactions = (await TransactionService.getAssetTransactions(
        {
          owner: targetUser._id,
          statuses: ["PendingDeposit", "PendingGift", "Pending", "Settled"]
        },
        null,
        { pendingDeposit: true }
      )) as AssetTransactionDocument[];

      if (assetTransactions.length > 0) {
        logger.warn(`Attempted to create gift but target user ${targetUser.id} has invested`, {
          module: "GiftService",
          method: "createGift",
          data: {
            targetUser: requestedGiftData.targetUserEmail,
            conversionStatus: targetUser.portfolioConversionStatus,
            gifter: requestedGiftData.gifter.toString()
          }
        });
        return;
      }

      // We only add active providers in the deposit block if the target user already exists and has a residency country
      if (targetUser.residencyCountry) {
        giftData = {
          ...requestedGiftData,
          deposit: {
            activeProviders: ProviderService.getProviders(targetUser.companyEntity, [ProviderScopeEnum.BROKERAGE])
          }
        };
      }
    }

    // We've reached this stage which means target user and gifter are eligible for gift creation.
    const gift = await new Gift(giftData).save();

    await NotificationService.createEmailNotification(
      gifter.id,
      {
        notificationId: "gifterGiftCreation",
        properties: new Map(
          Object.entries({
            target_user_email: requestedGiftData.targetUserEmail
          })
        )
      },
      { sendImmediately: true }
    );

    if (!targetUser) {
      MailerService.sendNotExistingTargetUserGiftCreation(
        requestedGiftData.targetUserEmail,
        gifter,
        requestedGiftData.message
      );
    } else if (!targetUser.hasPassedKyc) {
      await NotificationService.createEmailNotification(
        targetUser.id,
        {
          notificationId: "unverifiedTargetUserGiftCreation",
          properties: new Map(
            Object.entries({
              gifter_full_name: `${gifter.firstName} ${gifter.lastName}`,
              message: requestedGiftData.message
            })
          )
        },
        { sendImmediately: true }
      );
    } else {
      await NotificationService.createEmailNotification(
        targetUser.id,
        {
          notificationId: "verifiedTargetUserGiftCreation",
          properties: new Map(
            Object.entries({
              gifter_full_name: `${gifter.firstName} ${gifter.lastName}`,
              message: requestedGiftData.message
            })
          )
        },
        { sendImmediately: true }
      );
    }

    logger.info(`Created gift for user ${requestedGiftData.targetUserEmail}`, {
      module: "GiftService",
      method: "createGift",
      data: {
        targetUser: requestedGiftData.targetUserEmail,
        gifter: requestedGiftData.gifter,
        amount: requestedGiftData.consideration.amount
      }
    });

    eventEmitter.emit(events.gift.giftCreation.eventId, gifter, targetUser, {
      targetUserEmail: requestedGiftData.targetUserEmail,
      amount: new Decimal(requestedGiftData.consideration.amount).div(100).toNumber(),
      currency: requestedGiftData.consideration.currency
    } as TrackGiftPropertiesType);

    return gift;
  }

  public static async getGift(giftId: string, populate = { linkedAssetTransaction: false, gifter: false }) {
    return Gift.findById(giftId).populate(DbUtil.getPopulationString(populate));
  }

  public static async getGifts(
    filter: GiftsFilter = {},
    pageConfig?: { page: number; pageSize: number },
    populate = { linkedAssetTransaction: true, gifter: true },
    sort = "-createdAt"
  ) {
    //  We ensure that if the 'used' filter is applied, the 'linkedAssetTransaction' is always populated.
    //  This is crucial because the 'used' status of a gift depends on the presence of a linked asset transaction.
    //  If 'linkedAssetTransaction' is not populated it will be undefined since it's a virtual. Then, the 'used' value will always be false,
    if ("used" in filter) {
      populate = { ...populate, linkedAssetTransaction: true };
    }

    return pageConfig
      ? GiftService._getGiftsPaginated(filter, pageConfig, populate, sort)
      : { data: await GiftService._getGifts(filter, populate, sort) };
  }

  public static async updateGift(
    giftId: string,
    giftData: {
      hasViewedAppModal?: boolean;
      consideration?: {
        amount: number;
        currency: currenciesConfig.MainCurrencyType;
      };
      deposit?: { activeProviders: ProviderEnum[] };
    }
  ): Promise<void> {
    if (!giftId || !validator.isMongoId(giftId)) {
      throw new InternalServerError(`Gift ID ${giftId} is not valid`);
    }

    const sanitisedData = Object.fromEntries(
      Object.entries(giftData).filter(([, value]) => value !== undefined && value !== null)
    );

    if (!Object.keys(sanitisedData).length) {
      throw new InternalServerError("Data cannot be empty");
    }

    await Gift.findByIdAndUpdate(giftId, sanitisedData);
  }

  /**
   * Goes through a Mailchimp segment and for each user, gives them the capability to send gifts for a given period.
   */
  public static async createAllGiftingCapabilities() {
    const usersWithGiftingCapability = await MailchimpService.getPendingGiftingCapabilityMembers();

    for (let i = 0; i < usersWithGiftingCapability.length; i++) {
      const mailchimpUserEmail = usersWithGiftingCapability[i];

      try {
        const user = await UserService.getUserByEmail(mailchimpUserEmail);
        if (!user) {
          logger.info(
            `Attempted to create gift capability setting but no user doc exists for ${mailchimpUserEmail}`,
            {
              module: "GiftService",
              method: "createAllGiftingCapabilities",
              userEmail: mailchimpUserEmail
            }
          );
          continue;
        }

        await GiftService._processUserGiftingCapability(user);
      } catch (err) {
        captureException(err);
        logger.error(`Gift capability setting failed for ${mailchimpUserEmail}`, {
          module: "GiftService",
          method: "createAllGiftingCapabilities",
          userEmail: mailchimpUserEmail,
          data: { error: err }
        });
      }
    }
  }

  /**
   * @description Creates bonus payment requests to Wealthkernel for the gifts that have been created & are used (they
   * are linked to an asset transaction in PendingGift status) but don't have payments requested yet.
   */
  public static async createGiftDeposits(): Promise<void> {
    const giftsMissingDeposit: GiftDocument[] = await Gift.find({
      "deposit.activeProviders": ProviderEnum.WEALTHKERNEL,
      $or: [
        { "deposit.providers.wealthkernel.id": { $exists: false } },
        { "deposit.providers.wealthkernel.id": { $eq: undefined } }
      ]
    }).populate("linkedAssetTransaction");

    for (let i = 0; i < giftsMissingDeposit.length; i++) {
      const gift = giftsMissingDeposit[i];

      // We only create gift deposits for gifts that have been used for an asset transaction
      if ((gift?.linkedAssetTransaction as AssetTransactionDocument)?.status === "PendingGift") {
        await GiftService._createGiftDeposit(gift);
      }
    }
  }

  /**
   * @description Syncs the pending bonus transactions that were created as part of the gifting process.
   */
  public static async syncPendingGiftDeposits(): Promise<void> {
    // For gifts with a wk deposit id, sync wk status
    // that were createdAt at least 15 minutes ago, to act as a fallback to webhook mechanism
    const minimumCreationTime = new Date(Date.now() - 15 * 60 * 1000);

    const giftsPendingDeposits: GiftDocument[] = await Gift.find({
      "deposit.providers.wealthkernel.id": { $exists: true, $ne: null },
      "deposit.providers.wealthkernel.status": { $ne: "Settled" },
      createdAt: { $lte: minimumCreationTime }
    });

    for (let i = 0; i < giftsPendingDeposits.length; i++) {
      const gift = giftsPendingDeposits[i];
      try {
        const user = await UserService.getUserByEmail(gift.targetUserEmail);

        const wkBonus = await ProviderService.getBrokerageService(user.companyEntity).retrieveBonus(
          gift.deposit.providers?.wealthkernel.id
        );
        await GiftService.updateGiftDepositStatus(gift, wkBonus.status);
      } catch (err) {
        captureException(err);
        logger.error(`Gift deposit syncing failed for ${gift.id}`, {
          module: "GiftService",
          method: "syncPendingGiftDeposits",
          data: { giftId: gift.id, bonus: gift.deposit?.providers?.wealthkernel?.id, error: err }
        });
      }
    }
  }

  public static async getGiftByDepositId(depositId: string): Promise<GiftDocument> {
    return await Gift.findOne({
      "deposit.providers.wealthkernel.id": depositId
    });
  }

  public static async updateGiftDepositStatus(
    gift: GiftDocument,
    newDepositStatus: BonusStatusType
  ): Promise<void> {
    if (newDepositStatus === "Settled") {
      await Gift.findOneAndUpdate({ _id: gift.id }, { "deposit.providers.wealthkernel.status": "Settled" });
    } else if (newDepositStatus === "Rejected") {
      logger.error(`Gift bonus payment was rejected for ${gift.id}`, {
        module: "GiftService",
        method: "updateGiftDepositStatus"
      });
    }
  }

  /**
   * PRIVATE METHODS
   */

  private static async _createGiftDeposit(gift: GiftDocument): Promise<void> {
    logger.info(`Creating gift bonus for gift ${gift._id.toString()}`, {
      module: "GiftService",
      method: "_createGiftDeposit",
      data: {
        giftId: gift._id.toString()
      }
    });

    const user = await UserService.getUserByEmail(gift.targetUserEmail);
    const generalInvestmentPortfolio = await PortfolioService.getGeneralInvestmentPortfolio(user);
    const bonusPayment = await ProviderService.getBrokerageService(user.companyEntity).createBonus({
      destinationPortfolio: generalInvestmentPortfolio.providers.wealthkernel.id,
      consideration: {
        currency: gift.consideration.currency,
        amount: Decimal.div(gift.consideration.amount, 100).toNumber()
      },
      clientReference: gift._id.toString()
    });

    logger.info(`Created gift bonus for gift ${gift._id.toString()} with WK id ${bonusPayment.id}`, {
      module: "GiftService",
      method: "_createGiftDeposit",
      data: {
        giftId: gift._id.toString()
      }
    });

    await Gift.findByIdAndUpdate(gift._id, {
      "deposit.providers.wealthkernel": {
        status: "Created",
        id: bonusPayment.id,
        submittedAt: new Date(Date.now())
      }
    });
  }

  private static async _processUserGiftingCapability(user: UserDocument) {
    await UserService.enableGiftingCapability(user);

    await MailchimpService.updateMember(
      user.email,
      {
        merge_fields: { SENDGIFTCM: "Ready to send email" }
      },
      AudienceIdEnum.WEALTHYHOOD,
      { silent: true }
    );
  }

  private static _createGiftsDbFilter(filter: GiftsFilter) {
    const dbFilter = {
      targetUserEmail: filter.targetUserEmail,
      hasViewedAppModal: filter.hasViewedAppModal,
      status: filter.status,
      updatedAt: null as any
    };

    if (filter.updatedDate) {
      dbFilter["updatedAt"] = {
        $gte: filter.updatedDate.startDate,
        $lt: filter.updatedDate.endDate
      };
    }

    return dbFilter
      ? Object.fromEntries(
          Object.entries(dbFilter).filter(
            ([key, value]) => key != "status" && value !== undefined && value !== null
          )
        )
      : {};
  }

  private static async _getGifts(
    filter?: GiftsFilter,
    populate = { linkedAssetTransaction: true, gifter: true },
    sort?: string
  ): Promise<GiftDocument[]> {
    const dbFilter = this._createGiftsDbFilter(filter);

    const options: QueryOptions = {};
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }

    let gifts = await Gift.find(dbFilter, null, options).populate(DbUtil.getPopulationString(populate));

    const { status, used } = filter;
    gifts = status ? gifts.filter((gift) => gift.status === status) : gifts;
    gifts = used !== undefined && used !== null ? gifts.filter((gift) => gift.used === used) : gifts;

    return gifts;
  }

  private static async _getGiftsPaginated(
    filter: GiftsFilter = {},
    pageConfig: { page: number; pageSize: number },
    populate = { linkedAssetTransaction: true, gifter: true },
    sort?: string
  ): Promise<PaginatedGiftsResponse> {
    const dbFilter = this._createGiftsDbFilter(filter);
    const count = await Gift.countDocuments(dbFilter);
    const pageConfigToUse = PaginationUtil.getPaginationParametersFor(count, pageConfig);

    const options: QueryOptions = {};
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }

    let gifts = await Gift.find(dbFilter, null, options)
      .skip((pageConfigToUse.page - 1) * pageConfigToUse.pageSize)
      .limit(pageConfigToUse.pageSize)
      .populate(DbUtil.getPopulationString(populate));

    const { status, used } = filter;
    gifts = status ? gifts.filter((gift) => gift.status === status) : gifts;
    gifts = used !== undefined && used !== null ? gifts.filter((gift) => gift.used === used) : gifts;

    return { pagination: pageConfigToUse, gifts: gifts };
  }
}
