import { currenciesConfig, plansConfig } from "@wealthyhood/shared-configs";
import { StripeService } from "../external-services/stripeService";
import { WealthyhoodToStripePrices } from "../configs/stripeConfig";
import { getEnvironment } from "../utils/environmentUtil";

/**
 * Types
 */
export type CreateDirectDebitPaymentType = {
  amount: number;
  mandateId: string;
  idempotencyKey: string;
  metadata?: any;
};

type CardSubscriptionType = {
  id: string;
  current_period_end: number;
};

/**
 * Subscription Payment Service
 */
export interface SubscriptionPaymentServiceInterface {
  cancelCardSubscriptionOnPeriodEnd(subscriptionId: string): Promise<CardSubscriptionType>;

  cancelCardSubscriptionNow(subscriptionId: string): Promise<CardSubscriptionType>;

  abortCancelCardSubscription(subscriptionId: string): Promise<CardSubscriptionType>;

  updateCardSubscription(
    subscriptionId: string,
    userCurrency: currenciesConfig.MainCurrencyType,
    priceId: plansConfig.PriceType
  ): Promise<CardSubscriptionType>;

  downgradeCardSubscription(
    subscriptionId: string,
    userCurrency: currenciesConfig.MainCurrencyType,
    priceId: plansConfig.PriceType
  ): Promise<CardSubscriptionType>;

  updateCardSubscriptionPaymentMethod(
    subscriptionId: string,
    paymentMethodId: string
  ): Promise<CardSubscriptionType>;
}

export class CardBasedSubscriptionPaymentService implements SubscriptionPaymentServiceInterface {
  private static _instance: CardBasedSubscriptionPaymentService;

  public static get Instance(): CardBasedSubscriptionPaymentService {
    return this._instance || (this._instance = new this());
  }

  public async abortCancelCardSubscription(subscriptionId: string): Promise<CardSubscriptionType> {
    return StripeService.Instance.updateSubscription(subscriptionId, { cancel_at_period_end: false });
  }

  public async cancelCardSubscriptionNow(subscriptionId: string): Promise<CardSubscriptionType> {
    return StripeService.Instance.cancelSubscription(subscriptionId);
  }

  public async cancelCardSubscriptionOnPeriodEnd(subscriptionId: string): Promise<CardSubscriptionType> {
    return StripeService.Instance.updateSubscription(subscriptionId, { cancel_at_period_end: true });
  }

  public async downgradeCardSubscription(
    subscriptionId: string,
    userCurrency: currenciesConfig.MainCurrencyType,
    priceId: plansConfig.PriceType
  ): Promise<CardSubscriptionType> {
    const stripeSubscription = await StripeService.Instance.retrieveSubscription(subscriptionId);
    const stripePrice = WealthyhoodToStripePrices[userCurrency][priceId][getEnvironment()];

    return StripeService.Instance.updateSubscription(subscriptionId, {
      items: [
        {
          id: stripeSubscription.items.data[0].id,
          price: stripePrice
        }
      ]
    });
  }

  public async updateCardSubscription(
    subscriptionId: string,
    userCurrency: currenciesConfig.MainCurrencyType,
    priceId: plansConfig.PriceType
  ): Promise<CardSubscriptionType> {
    const stripeSubscription = await StripeService.Instance.retrieveSubscription(subscriptionId);
    const stripePrice = WealthyhoodToStripePrices[userCurrency][priceId][getEnvironment()];

    return StripeService.Instance.updateSubscription(subscriptionId, {
      cancel_at_period_end: false,
      proration_behavior: "always_invoice",
      items: [
        {
          id: stripeSubscription.items.data[0].id,
          price: stripePrice
        }
      ]
    });
  }

  public async updateCardSubscriptionPaymentMethod(
    subscriptionId: string,
    paymentMethodId: string
  ): Promise<CardSubscriptionType> {
    return StripeService.Instance.updateSubscription(subscriptionId, { default_payment_method: paymentMethodId });
  }
}
