import {
  AssetIsinChangeCorporateEvent,
  AssetIsinChangeCorporateEventDTOInterface,
  StockSplitCorporateEvent,
  StockSplitCorporateEventDocument,
  StockSplitCorporateEventDTOInterface
} from "../models/CorporateEvent";
import eventEmitter from "../loaders/eventEmitter";
import events from "../event-handlers/events";
import { publicInvestmentUniverseConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import DateUtil from "../utils/dateUtil";

const { ASSET_CONFIG } = investmentUniverseConfig;
const { PUBLIC_ASSET_CONFIG } = publicInvestmentUniverseConfig;

export default class CorporateEventService {
  /**
   * Creates a corporate event document for the given stock split, if it does not already exist.
   *
   * We record stock splits even for assets within our public-only investment universe.
   *
   * @param stockSplitData
   */
  public static async createStockSplit(stockSplitData: StockSplitCorporateEventDTOInterface): Promise<void> {
    const stockSplitAlreadyExists = await StockSplitCorporateEvent.exists({
      asset: stockSplitData.asset,
      $expr: {
        $and: [
          { $eq: [{ $year: "$date" }, { $year: stockSplitData.date }] },
          { $eq: [{ $month: "$date" }, { $month: stockSplitData.date }] },
          { $eq: [{ $dayOfMonth: "$date" }, { $dayOfMonth: stockSplitData.date }] }
        ]
      }
    });

    if (!stockSplitAlreadyExists) {
      await new StockSplitCorporateEvent(stockSplitData).save();

      eventEmitter.emit(events.corporateEvents.stockSplitCreation.eventId, {
        assetName: PUBLIC_ASSET_CONFIG[stockSplitData.asset].formalTicker,
        date: stockSplitData.date
      });
    }
  }

  public static async getAllStockSplits(
    assetCommonId: investmentUniverseConfig.AssetType
  ): Promise<StockSplitCorporateEventDocument[]> {
    return StockSplitCorporateEvent.find({ asset: assetCommonId });
  }

  public static async getLastNDaysStockSplits(days: number): Promise<StockSplitCorporateEventDocument[]> {
    const now = new Date(Date.now());
    const startDate = DateUtil.getStartOfDay(DateUtil.getDateOfDaysAgo(new Date(Date.now()), days));

    return StockSplitCorporateEvent.find({
      date: {
        $gte: startDate,
        $lte: now
      }
    });
  }

  public static async getMostRecentStockSplit(
    assetCommonId: publicInvestmentUniverseConfig.PublicAssetType
  ): Promise<StockSplitCorporateEventDocument> {
    return StockSplitCorporateEvent.findOne({ asset: assetCommonId }, null, { sort: { date: -1 } });
  }

  public static async createAssetIsinChange(
    assetIsinChangeData: AssetIsinChangeCorporateEventDTOInterface
  ): Promise<void> {
    const assetIsinChangeAlreadyExists = await AssetIsinChangeCorporateEvent.exists({
      asset: assetIsinChangeData.asset,
      oldISIN: assetIsinChangeData.oldISIN,
      newISIN: assetIsinChangeData.newISIN
    });

    if (!assetIsinChangeAlreadyExists) {
      await new AssetIsinChangeCorporateEvent(assetIsinChangeData).save();

      eventEmitter.emit(events.corporateEvents.assetIsinChange.eventId, {
        assetName: ASSET_CONFIG[assetIsinChangeData.asset as investmentUniverseConfig.AssetType].simpleName,
        date: assetIsinChangeData.date,
        oldISIN: assetIsinChangeData.oldISIN,
        newISIN: assetIsinChangeData.newISIN
      });
    }
  }
}
