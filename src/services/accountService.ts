import mongoose from "mongoose";
import { captureException } from "@sentry/node";
import { Account, AccountDocument, AccountDTOInterface, AccountPopulationFieldsEnum } from "../models/Account";
import { UserDocument, UserPopulationFieldsEnum } from "../models/User";
import logger from "../external-services/loggerService";
import { BadRequestError } from "../models/ApiErrors";
import DbUtil from "../utils/dbUtil";
import SubscriptionService from "./subscriptionService";
import UserService from "./userService";
import ProviderService, { ProviderScopeEnum } from "./providerService";
import { AccountStatusType, AccountType, PortfolioWrapperTypeEnum } from "./brokerageService";
import eventEmitter from "../loaders/eventEmitter";
import events from "../event-handlers/events";
import { ProviderEnum } from "../configs/providersConfig";
import { MixpanelAccountStatusEnum } from "../external-services/segmentAnalyticsService";

export default class AccountService {
  public static async syncAllPendingWkAccounts(): Promise<void> {
    const accounts = await Account.find({
      "providers.wealthkernel.id": { $exists: true },
      "providers.wealthkernel.status": { $eq: "Pending" }
    }).populate("owner");

    const allowedAccounts = accounts.filter((account) => UserService.isUserAdult(account.owner as UserDocument));

    for (let i = 0; i < allowedAccounts.length; i++) {
      const account = allowedAccounts[i];
      const { userAfterAccountUpdates } = await AccountService._syncWkEntrySafely(account);
      if (userAfterAccountUpdates) {
        await UserService.flagPotentiallyFailedToVerifyUser(userAfterAccountUpdates);
      }
    }
  }

  public static async syncWkAccountStatusByWkAccountId(wkAccountId: string, newAccountStatus: AccountStatusType) {
    const account = await Account.findOne({
      "providers.wealthkernel.id": wkAccountId,
      "providers.wealthkernel.status": { $in: ["Pending", "Suspended", "Active", "Closing"] }
    }).populate("owner");

    if (!account) {
      // Can happen if we received status Closed for example
      logger.warn(
        `WK account syncing skipped, because no doc matching the sync criteria was found for id ${wkAccountId} `,
        {
          module: "AccountService",
          method: "syncWkAccountStatusByWkAccountId",
          data: {
            wkAccountId,
            newAccountStatus
          }
        }
      );
      return;
    }

    const owner = account.owner as UserDocument;

    if (account && UserService.isUserAdult(owner)) {
      const { userAfterAccountUpdates } = await AccountService._updateAccountStatus(owner, account, {
        id: wkAccountId,
        status: newAccountStatus
      });
      if (userAfterAccountUpdates) {
        await UserService.flagPotentiallyFailedToVerifyUser(userAfterAccountUpdates);
      }
    }
  }

  public static async syncAllSuspendedWkAccounts(): Promise<void> {
    const accounts = await Account.find({
      "providers.wealthkernel.status": { $eq: "Suspended" }
    }).populate("owner");

    const allowedAccounts = accounts.filter((account) => UserService.isUserAdult(account.owner as UserDocument));

    for (let i = 0; i < allowedAccounts.length; i++) {
      const account = allowedAccounts[i];
      await AccountService._syncWkEntrySafely(account);
    }
  }

  /**
   * Returns all accounts for the given owner e.g. GIA account, ISA account etc.
   * @param owner
   */
  public static async getAccounts(owner: string): Promise<AccountDocument[]> {
    return Account.find({ owner: owner });
  }

  /**
   * @description Creates wealthkernel account for given user if there is not one already.
   * If account already exists in Wealthkernel but our document does not have a
   * reference to it, then we sync our model to include one.
   * @returns the WK account of the user alongside a boolean value representing
   * whether a new account was created in WK or not.
   */
  public static async createOrSyncBrokerageAccount(
    user: UserDocument,
    account: AccountDocument
  ): Promise<{ userAfterAccountUpdates: UserDocument; newStatus: AccountStatusType }> {
    if (!user.providers?.wealthkernel?.id) {
      logger.error(
        `Attempted to submit account to Wealthkernel for user ${user.email}, but user has no Wealthkernel party`,
        {
          module: "AccountService",
          method: "createOrSyncBrokerageAccount",
          data: { account: account.id, user: user.id }
        }
      );
      throw new Error("Account owner has no WK Party ID!");
    }

    // Fast exit if user has account with status 'Active' already
    if (account?.providers?.wealthkernel?.status == "Active") {
      return { userAfterAccountUpdates: user, newStatus: "Active" };
    }

    // Update wealthkernel account details if an account exists already
    const existingAccounts: AccountType[] = await ProviderService.getBrokerageService(
      user.companyEntity
    ).retrieveAccounts(user.providers?.wealthkernel?.id, account.id);

    if (existingAccounts.length > 0) {
      await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.ACCOUNTS);
      if (user.accounts.length !== 1) {
        logger.error(`User has ${user.accounts.length} accounts!`, {
          module: "AccountService",
          method: "createOrSyncBrokerageAccount",
          userEmail: user.email
        });
        throw new BadRequestError("User has invalid number of accounts!");
      }
      logger.info(`Wealthkernel account already exists for user ${user.email} - syncing`, {
        module: "AccountService",
        method: "createOrSyncBrokerageAccount"
      });
      return await AccountService._updateAccountStatus(user, account, existingAccounts[0]);
    }

    // No account exists for user - submit account to wealthkernel
    logger.info(`Wealthkernel account does not exist for user ${user.email} - creating one`, {
      module: "AccountService",
      method: "createOrSyncAccountWealthkernel"
    });

    const newWealthkernelAccount = await ProviderService.getBrokerageService(user.companyEntity).createAccount(
      user,
      account
    );

    await Account.findOneAndUpdate(
      { _id: account._id },
      {
        providers: {
          wealthkernel: { id: newWealthkernelAccount.id, status: "Pending" }
        }
      },
      { runValidators: true }
    );

    return { userAfterAccountUpdates: user, newStatus: "Pending" };
  }

  public static async createGeneralInvestmentAccount(user: UserDocument): Promise<AccountDocument> {
    logger.info(`Creating or updating account for owner ${user.id}`, {
      module: "AccountService",
      method: "createGeneralInvestmentAccount"
    });

    const accountData: AccountDTOInterface = {
      wrapperType: PortfolioWrapperTypeEnum.GIA,
      name: "General Investment Account",
      owner: user._id
    };
    if (user.residencyCountry) {
      accountData.activeProviders = ProviderService.getProviders(user.companyEntity, [
        ProviderScopeEnum.BROKERAGE
      ]);
    }

    return AccountService._createAccount(accountData);
  }

  public static async updateActiveProviders(
    account: AccountDocument,
    activeProviders: ProviderEnum[]
  ): Promise<void> {
    await account.updateOne({
      activeProviders
    });
  }

  private static async _syncWkEntrySafely(
    account: AccountDocument
  ): Promise<{ userAfterAccountUpdates: UserDocument; newStatus: AccountStatusType }> {
    try {
      logger.info(`Attempting to sync WK entry for account ${account.id}`, {
        module: "AccountService",
        method: "_syncWkEntrySafely",
        data: { account: account.id }
      });

      const accountId = account?.providers?.wealthkernel?.id;
      if (!accountId) {
        logger.error("Attempted to sync account to Wealthkernel but has no WK id", {
          module: "AccountService",
          method: "_syncWkEntrySafely",
          data: { account: account.id }
        });
        throw new Error("Account has no WK ID!");
      }

      await DbUtil.populateIfNotAlreadyPopulated(account, AccountPopulationFieldsEnum.OWNER);
      const user = account.owner as UserDocument;

      const wkAccount = await ProviderService.getBrokerageService(user.companyEntity).retrieveAccount(accountId);

      return await AccountService._updateAccountStatus(user, account, wkAccount);
    } catch (err) {
      captureException(err);
      logger.error(`Syncing a WK entry failed for account ${account.id}`, {
        module: "AccountService",
        method: "_syncWkEntrySafely",
        data: { account: account.id, error: err }
      });
      return { userAfterAccountUpdates: null, newStatus: null };
    }
  }

  private static async _updateAccountStatus(
    user: UserDocument,
    account: AccountDocument,
    wkAccount: {
      id: string;
      status: AccountStatusType;
    }
  ): Promise<{ userAfterAccountUpdates: UserDocument; newStatus: AccountStatusType }> {
    const accountId = wkAccount.id;
    const accountStatus = wkAccount.status;

    await Account.findOneAndUpdate(
      { _id: account._id },
      {
        providers: { wealthkernel: { id: accountId, status: accountStatus } }
      },
      { runValidators: true }
    );

    if (user.isPotentiallyDuplicateAccount) {
      // If the user has been flagged as a potential duplicate account, we don't update the status of the account, but
      // we set the KYC status of the user as failed.
      if (!user.hasFailedKyc) {
        const updatedUser = await UserService.setFailedKycStatus(user);
        return { userAfterAccountUpdates: updatedUser, newStatus: accountStatus };
      }
    } else if (wkAccount.status === "Suspended") {
      const updatedUser = await UserService.setFailedKycStatus(user);

      if (account.providers.wealthkernel.status !== "Suspended") {
        // The update method can run multiple times on suspended accounts - we don't want to emit an event every time
        eventEmitter.emit(events.user.accountSuspended.eventId, user);
      }

      eventEmitter.emit(events.user.whAccountStatusUpdate.eventId, updatedUser, {
        accountStatus: MixpanelAccountStatusEnum.WkSuspendedClosed
      });

      return { userAfterAccountUpdates: updatedUser, newStatus: wkAccount.status };
    } else if (wkAccount.status === "Closed") {
      // On account closure, if the user has a fee-based subscription, we need to deactivate it, as we will not be
      // able to charge cash/holdings while their WK account/portfolio are not active - the subscription will be
      // activated once the account becomes active.
      const subscription = await SubscriptionService.getSubscription(user.id);
      if (subscription?.category === "FeeBasedSubscription") {
        await SubscriptionService.deactivateSubscription(user.id);
      }

      if (account.providers.wealthkernel.status !== "Closed") {
        // We don't want to emit an event every time the method runs on the same account and the account has status closed
        eventEmitter.emit(events.user.wkAccountClosed.eventId, user);
      }

      eventEmitter.emit(events.user.whAccountStatusUpdate.eventId, user, {
        accountStatus: MixpanelAccountStatusEnum.WkSuspendedClosed
      });

      return { userAfterAccountUpdates: user, newStatus: wkAccount.status };
    } else if (wkAccount.status === "Active" && !user.hasPassedKyc) {
      const updatedUser = await UserService.setPassedKycStatusIfEligible(user.id);

      return { userAfterAccountUpdates: updatedUser, newStatus: "Active" };
    }

    return { userAfterAccountUpdates: user, newStatus: accountStatus };
  }

  private static async _createAccount(accountData: AccountDTOInterface): Promise<AccountDocument> {
    return Account.findOneAndUpdate(
      { owner: new mongoose.Types.ObjectId(accountData.owner.toString()) },
      accountData,
      {
        runValidators: true,
        setDefaultsOnInsert: true,
        upsert: true,
        new: true
      }
    );
  }
}
