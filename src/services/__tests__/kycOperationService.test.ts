import { UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildKycOperation, buildUser } from "../../tests/utils/generateModels";
import { faker } from "@faker-js/faker";
import { KycOperation, KycOperationDocument } from "../../models/KycOperation";
import KycOperationService, { KycInitiationCredentials } from "../kycOperationService";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import { ProviderEnum } from "../../configs/providersConfig";
import { SumsubService } from "../../external-services/sumsubService";
import DateUtil from "../../utils/dateUtil";
import { buildApplicant } from "../../tests/utils/generateSumsub";

describe("KycOperationService", () => {
  beforeAll(async () => await connectDb("KycOperationService"));
  afterAll(async () => await closeDb());

  describe("initiateKycOperation", () => {
    describe("when a user is initiating a KYC operation for the first time", () => {
      let user: UserDocument;
      let result: KycInitiationCredentials;

      const SUBMISSION_DATE = new Date("2023-08-07T10:49:00Z");
      const SDK_TOKEN = faker.string.uuid();

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => SUBMISSION_DATE.valueOf());

        jest.spyOn(SumsubService.Instance, "generateAccessToken").mockResolvedValue({ token: SDK_TOKEN });

        user = await buildUser({ residencyCountry: "GR" });

        result = await KycOperationService.initiateKycOperation(user);
      });
      afterAll(async () => await clearDb());

      it("should return an SDK token", () => {
        expect(result).toEqual({
          sdkToken: SDK_TOKEN
        });
      });

      it("should emit Sumsub KYC start event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.sumsubKycStarted.eventId,
          expect.objectContaining({ email: user.email })
        );
      });

      it("should create a new KYC operation document", async () => {
        const kycOperationsCount = await KycOperation.countDocuments({ owner: user.id });
        expect(kycOperationsCount).toEqual(1);

        const kycOperation = (await KycOperation.findOne({ owner: user.id })) as KycOperationDocument;

        expect(kycOperation.toObject()).toMatchObject({
          owner: user._id,
          activeProviders: [ProviderEnum.SUMSUB],
          providers: {
            sumsub: {
              submittedAt: SUBMISSION_DATE
            }
          }
        });
      });

      it("should create a new workflow in Sumsub for a new account", async () => {
        expect(SumsubService.Instance.generateAccessToken).toHaveBeenCalledWith(user.id);
      });
    });

    describe("when a user has an incomplete KYC operation", () => {
      let user: UserDocument;
      let result: KycInitiationCredentials;

      const SUBMISSION_DATE = new Date("2023-08-07T10:49:00Z");
      const SDK_TOKEN = faker.string.uuid();

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => SUBMISSION_DATE.valueOf());

        jest.spyOn(SumsubService.Instance, "generateAccessToken").mockResolvedValue({ token: SDK_TOKEN });

        user = await buildUser({ residencyCountry: "GR" });
        await buildKycOperation({
          owner: user.id,
          providers: {
            sumsub: {
              submittedAt: DateUtil.getDateOfDaysAgo(SUBMISSION_DATE, 15)
            }
          }
        });

        result = await KycOperationService.initiateKycOperation(user);
      });
      afterAll(async () => await clearDb());

      it("should return an SDK token", () => {
        expect(result).toEqual({
          sdkToken: SDK_TOKEN
        });
      });

      it("should NOT emit Sumsub KYC start event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.user.sumsubKycStarted.eventId,
          expect.objectContaining({ email: user.email })
        );
      });

      it("should NOT create a new KycOperation document", async () => {
        const kycOperationsCount = await KycOperation.countDocuments({ owner: user.id });
        expect(kycOperationsCount).toEqual(1);
      });

      it("should update the existing KYC operation document submittedAt", async () => {
        const kycOperation = await KycOperation.findOne({ owner: user.id });
        expect(kycOperation.providers?.sumsub?.submittedAt).toEqual(SUBMISSION_DATE);
      });

      it("should create a new workflow in Sumsub for a new account", async () => {
        expect(SumsubService.Instance.generateAccessToken).toHaveBeenCalledWith(user.id);
      });
    });
  });

  describe("syncAllSumsubKycOperations", () => {
    describe("when there is no Sumsub KYC operation to be synced", () => {
      const NOW = new Date("2023-08-07T10:49:00Z");

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => NOW.valueOf());

        await Promise.all([
          buildKycOperation({
            activeProviders: [ProviderEnum.JUMIO]
          }),
          buildKycOperation({
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "pending",
                submittedAt: NOW
              }
            }
          }),
          buildKycOperation({
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                submittedAt: DateUtil.getDateOfMinutesAgo(80)
              }
            }
          })
        ]);

        jest.spyOn(SumsubService.Instance, "retrieveApplicant");

        await KycOperationService.syncAllSumsubKycOperations();
      });
      afterAll(async () => await clearDb());

      it("should not call the Sumsub API", () => {
        expect(SumsubService.Instance.retrieveApplicant).not.toHaveBeenCalled();
      });
    });

    describe("when there is a KYC operation to be synced", () => {
      const NOW = new Date("2023-08-07T10:49:00Z");
      const REVIEW_ID = faker.string.uuid();

      let kycOperation: KycOperationDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => NOW.valueOf());

        kycOperation = await buildKycOperation({
          activeProviders: [ProviderEnum.SUMSUB],
          providers: {
            sumsub: {
              id: faker.string.uuid(),
              status: "pending",
              submittedAt: DateUtil.getDateOfMinutesAgo(80)
            }
          }
        });

        jest.spyOn(SumsubService.Instance, "retrieveApplicant").mockResolvedValue(
          buildApplicant({
            review: {
              reviewId: REVIEW_ID,
              reviewStatus: "completed",
              reviewResult: {
                reviewAnswer: "GREEN"
              }
            }
          })
        );

        await KycOperationService.syncAllSumsubKycOperations();
      });
      afterAll(async () => await clearDb());

      it("should update the KYC operation", async () => {
        const updatedKycOperation = await KycOperation.findById(kycOperation.id);
        expect(updatedKycOperation.toObject()).toEqual(
          expect.objectContaining({
            status: "Passed",
            isJourneyCompleted: true,
            isProcessed: true,
            isManualAmlWorkflowSubmitted: false,
            providers: {
              sumsub: expect.objectContaining({
                id: REVIEW_ID,
                status: "completed",
                decision: "GREEN"
              })
            }
          })
        );
      });
    });
  });
});
