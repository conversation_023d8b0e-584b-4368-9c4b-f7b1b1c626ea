import { KycStatusEnum, User, UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildAssetTransaction,
  buildDepositCashTransaction,
  buildGift,
  buildHoldingDTO,
  buildNotificationSettings,
  buildPortfolio,
  buildUser
} from "../../tests/utils/generateModels";
import { faker } from "@faker-js/faker";
import MailerService from "../../external-services/mailerService";
import GiftService from "../giftService";
import { WealthkernelService, CurrencyEnum } from "../../external-services/wealthkernelService";
import { Gift, GiftDocument } from "../../models/Gift";
import MailchimpService, { AudienceIdEnum } from "../../external-services/mailchimpService";
import Decimal from "decimal.js";
import { buildWealthkernelBonusResponse } from "../../tests/utils/generateWealthkernel";
import DateUtil from "../../utils/dateUtil";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import { TrackGiftPropertiesType } from "../../external-services/segmentAnalyticsService";
import { ProviderEnum } from "../../configs/providersConfig";

describe("GiftService", () => {
  beforeAll(async () => await connectDb("GiftService"));
  afterAll(async () => await closeDb());

  describe("createGift", () => {
    describe("when gift is created for target user that has a settled investment", () => {
      let targetUser: UserDocument;
      let gifter: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        gifter = await buildUser({
          portfolioConversionStatus: "completed",
          canSendGiftUntil: DateUtil.getDateAfterNdays(TODAY, 1)
        });
        targetUser = await buildUser({ portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: targetUser.id,
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });
        await buildAssetTransaction({ owner: targetUser.id, portfolio: portfolio.id, status: "Settled" });

        await GiftService.createGift({
          gifter: gifter._id,
          targetUserEmail: targetUser.email,
          consideration: {
            currency: CurrencyEnum.GBP,
            amount: 1000
          },
          message: faker.lorem.words()
        });
      });
      afterAll(async () => await clearDb());

      it("should not create a gift document", async () => {
        const gifts = await Gift.find({});
        expect(gifts.length).toBe(0);
      });

      it("should remove the canSendGiftUntil field from the gifter", async () => {
        const updatedGifter = (await User.findById(gifter.id)) as UserDocument;
        expect(updatedGifter.canSendGiftUntil).toBe(undefined);
      });
    });

    describe("when gift is created for target user that has an investment pending an executed deposit", () => {
      let targetUser: UserDocument;
      let gifter: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        gifter = await buildUser({
          portfolioConversionStatus: "completed",
          canSendGiftUntil: DateUtil.getDateAfterNdays(TODAY, 1)
        });
        targetUser = await buildUser({ portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: targetUser.id,
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)]
        });
        const deposit = await buildDepositCashTransaction({
          owner: portfolio.owner,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            }
          }
        });
        await buildAssetTransaction({
          owner: targetUser.id,
          portfolio: portfolio.id,
          status: "PendingDeposit",
          pendingDeposit: deposit.id
        });

        await GiftService.createGift({
          gifter: gifter._id,
          targetUserEmail: targetUser.email,
          consideration: {
            currency: CurrencyEnum.GBP,
            amount: 1000
          },
          message: faker.lorem.words()
        });
      });
      afterAll(async () => await clearDb());

      it("should not create a gift document", async () => {
        const gifts = await Gift.find({});
        expect(gifts.length).toBe(0);
      });

      it("should remove the canSendGiftUntil field from the gifter", async () => {
        const updatedGifter = (await User.findById(gifter.id)) as UserDocument;
        expect(updatedGifter.canSendGiftUntil).toBe(undefined);
      });
    });

    describe("when gift is created for target user that has a pending gift", () => {
      let targetUser: UserDocument;
      let gifter: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        gifter = await buildUser({
          portfolioConversionStatus: "completed",
          canSendGiftUntil: DateUtil.getDateAfterNdays(TODAY, 1)
        });
        targetUser = await buildUser({ portfolioConversionStatus: "completed" });
        await buildPortfolio({
          owner: targetUser.id,
          holdings: []
        });

        await buildGift({
          targetUserEmail: targetUser.email,
          gifter: gifter.id
        });

        await GiftService.createGift({
          gifter: gifter._id,
          targetUserEmail: targetUser.email,
          consideration: {
            currency: CurrencyEnum.GBP,
            amount: 1000
          },
          message: faker.lorem.words()
        });
      });
      afterAll(async () => await clearDb());

      it("should not create a gift document", async () => {
        const gifts = await Gift.find({});
        expect(gifts.length).toBe(1);
      });

      it("should remove the canSendGiftUntil field from the gifter", async () => {
        const updatedGifter = (await User.findById(gifter.id)) as UserDocument;
        expect(updatedGifter.canSendGiftUntil).toBe(undefined);
      });
    });

    describe("when gift is created for target user that has a used gift", () => {
      let targetUser: UserDocument;
      let gifter: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        gifter = await buildUser({
          portfolioConversionStatus: "completed",
          canSendGiftUntil: DateUtil.getDateAfterNdays(TODAY, 1)
        });
        targetUser = await buildUser({ portfolioConversionStatus: "completed" });
        await buildPortfolio({
          owner: targetUser.id,
          holdings: []
        });

        const gift = await buildGift({
          targetUserEmail: targetUser.email,
          gifter: gifter.id
        });
        await buildAssetTransaction({
          status: "PendingGift",
          pendingGift: gift.id
        });

        await GiftService.createGift({
          gifter: gifter._id,
          targetUserEmail: targetUser.email,
          consideration: {
            currency: CurrencyEnum.GBP,
            amount: 1000
          },
          message: faker.lorem.words()
        });
      });
      afterAll(async () => await clearDb());

      it("should not create a gift document", async () => {
        const gifts = await Gift.find({});
        expect(gifts.length).toBe(1);
      });

      it("should remove the canSendGiftUntil field from the gifter", async () => {
        const updatedGifter = (await User.findById(gifter.id)) as UserDocument;
        expect(updatedGifter.canSendGiftUntil).toBe(undefined);
      });
    });

    describe("when gift is created by gifter that has canSendGiftUntil in the past", () => {
      let targetUser: UserDocument;
      let gifter: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        gifter = await buildUser({
          portfolioConversionStatus: "completed",
          canSendGiftUntil: DateUtil.getDateOfDaysAgo(TODAY, 1)
        });
        targetUser = await buildUser({ portfolioConversionStatus: "completed" });
        await buildPortfolio({
          owner: targetUser.id,
          holdings: []
        });

        await GiftService.createGift({
          gifter: gifter._id,
          targetUserEmail: targetUser.email,
          consideration: {
            currency: CurrencyEnum.GBP,
            amount: 1000
          },
          message: faker.lorem.words()
        });
      });
      afterAll(async () => await clearDb());

      it("should not create a gift document", async () => {
        const gifts = await Gift.find({});
        expect(gifts.length).toBe(0);
      });

      it("should remove the canSendGiftUntil field from the gifter", async () => {
        const updatedGifter = (await User.findById(gifter.id)) as UserDocument;
        expect(updatedGifter.canSendGiftUntil).toBe(undefined);
      });
    });

    describe("when gift is created by gifter that does not have a canSendGiftUntil field", () => {
      let targetUser: UserDocument;
      let gifter: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        gifter = await buildUser({ portfolioConversionStatus: "completed" });
        targetUser = await buildUser({ portfolioConversionStatus: "completed" });
        await buildPortfolio({
          owner: targetUser.id,
          holdings: []
        });

        await GiftService.createGift({
          gifter: gifter._id,
          targetUserEmail: targetUser.email,
          consideration: {
            currency: CurrencyEnum.GBP,
            amount: 1000
          },
          message: faker.lorem.words()
        });
      });
      afterAll(async () => await clearDb());

      it("should not create a gift document", async () => {
        const gifts = await Gift.find({});
        expect(gifts.length).toBe(0);
      });
    });

    describe("when gift is created for existing verified target user that does not have investments", () => {
      let targetUser: UserDocument;
      let gifter: UserDocument;
      let gift: GiftDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();

        [gifter, targetUser] = await Promise.all([
          buildUser({
            portfolioConversionStatus: "completed",
            canSendGiftUntil: DateUtil.getDateAfterNdays(TODAY, 1)
          }),
          buildUser({
            portfolioConversionStatus: "completed",
            kycStatus: KycStatusEnum.PASSED,
            residencyCountry: "GB"
          })
        ]);

        await Promise.all([
          buildNotificationSettings({ owner: gifter.id }),
          buildNotificationSettings({ owner: targetUser.id })
        ]);

        await buildPortfolio({
          owner: targetUser.id,
          holdings: []
        });

        gift = await GiftService.createGift({
          gifter: gifter._id,
          targetUserEmail: targetUser.email,
          consideration: {
            currency: CurrencyEnum.GBP,
            amount: 1000
          },
          message: faker.lorem.words()
        });
      });
      afterAll(async () => await clearDb());

      it("should create a gift document", async () => {
        const gifts = await Gift.find({});
        expect(gifts.length).toBe(1);
        expect(gifts[0]).toEqual(
          expect.objectContaining({
            gifter: gifter._id,
            targetUserEmail: targetUser.email,
            message: gift.message,
            consideration: gift.consideration,
            hasViewedAppModal: false,
            used: false,
            status: "Pending",
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            }
          })
        );
      });

      it("should send an e-mail to both users", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(2);

        expect(MailerService.sendEmail).toHaveBeenNthCalledWith(
          1,
          expect.objectContaining({ email: gifter.email }),
          "gifterGiftCreation",
          { target_user_email: targetUser.email }
        );
        expect(MailerService.sendEmail).toHaveBeenCalledWith(
          expect.objectContaining({ email: targetUser.email }),
          "verifiedTargetUserGiftCreation",
          { gifter_full_name: `${gifter.firstName} ${gifter.lastName}`, message: gift.message }
        );
      });

      it("should remove the canSendGiftUntil field from the gifter", async () => {
        const updatedGifter = (await User.findById(gifter.id)) as UserDocument;
        expect(updatedGifter.canSendGiftUntil).toBe(undefined);
      });

      it("should update the Mailchimp status of the user", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalledTimes(1);
        expect(MailchimpService.updateMember).toHaveBeenCalledWith(
          gifter.email,
          {
            merge_fields: {
              SENDGIFTCM: "Sent"
            }
          },
          AudienceIdEnum.WEALTHYHOOD,
          { silent: true }
        );
      });

      it("should emit a 'giftCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.gift.giftCreation.eventId,
          expect.objectContaining({ id: gifter.id }),
          expect.objectContaining({ id: targetUser.id }),
          {
            targetUserEmail: targetUser.email,
            amount: 10,
            currency: "GBP"
          } as TrackGiftPropertiesType
        );
      });
    });

    describe("when gift is created for existing verified target user that has not selected a residency country", () => {
      let targetUser: UserDocument;
      let gifter: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();

        gifter = await buildUser({
          portfolioConversionStatus: "completed",
          canSendGiftUntil: DateUtil.getDateAfterNdays(TODAY, 1)
        });
        targetUser = await buildUser({
          portfolioConversionStatus: "completed",
          kycStatus: KycStatusEnum.PASSED,
          residencyCountry: undefined
        });

        await Promise.all([
          buildNotificationSettings({ owner: gifter.id }),
          buildNotificationSettings({ owner: targetUser.id })
        ]);

        await buildPortfolio({
          owner: targetUser.id,
          holdings: []
        });

        await GiftService.createGift({
          gifter: gifter._id,
          targetUserEmail: targetUser.email,
          consideration: {
            currency: CurrencyEnum.GBP,
            amount: 1000
          },
          message: faker.lorem.words()
        });
      });
      afterAll(async () => await clearDb());

      it("should create a gift document without active providers", async () => {
        const gifts = await Gift.find({});
        expect(gifts.length).toBe(1);
        expect(gifts[0]).toEqual(
          expect.not.objectContaining({
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL]
            }
          })
        );
      });
    });

    describe("when gift is created for existing verified target user that has an investment pending a cancelled deposit", () => {
      let targetUser: UserDocument;
      let gifter: UserDocument;
      let gift: GiftDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();

        gifter = await buildUser({
          portfolioConversionStatus: "completed",
          canSendGiftUntil: DateUtil.getDateAfterNdays(TODAY, 1)
        });
        targetUser = await buildUser({ portfolioConversionStatus: "completed", kycStatus: KycStatusEnum.PASSED });

        await Promise.all([
          buildNotificationSettings({ owner: gifter.id }),
          buildNotificationSettings({ owner: targetUser.id })
        ]);

        const portfolio = await buildPortfolio({
          owner: targetUser.id,
          holdings: []
        });
        const deposit = await buildDepositCashTransaction({
          owner: portfolio.owner,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              version: "v3",
              status: "authorization_required"
            }
          }
        });
        await buildAssetTransaction({
          owner: targetUser.id,
          portfolio: portfolio.id,
          status: "PendingDeposit",
          pendingDeposit: deposit.id
        });

        gift = await GiftService.createGift({
          gifter: gifter._id,
          targetUserEmail: targetUser.email,
          consideration: {
            currency: CurrencyEnum.GBP,
            amount: 1000
          },
          message: faker.lorem.words()
        });
      });
      afterAll(async () => await clearDb());

      it("should create a gift document", async () => {
        const gifts = await Gift.find({});
        expect(gifts.length).toBe(1);
        expect(gifts[0]).toEqual(
          expect.objectContaining({
            gifter: gifter._id,
            targetUserEmail: targetUser.email,
            message: gift.message,
            consideration: gift.consideration,
            hasViewedAppModal: false,
            used: false,
            status: "Pending"
          })
        );
      });

      it("should send an e-mail to both users", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(2);
      });

      it("should remove the canSendGiftUntil field from the gifter", async () => {
        const updatedGifter = (await User.findById(gifter.id)) as UserDocument;
        expect(updatedGifter.canSendGiftUntil).toBe(undefined);
      });

      it("should update the Mailchimp status of the user", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalledTimes(1);
        expect(MailchimpService.updateMember).toHaveBeenCalledWith(
          gifter.email,
          {
            merge_fields: {
              SENDGIFTCM: "Sent"
            }
          },
          AudienceIdEnum.WEALTHYHOOD,
          { silent: true }
        );
      });

      it("should emit a 'giftCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.gift.giftCreation.eventId,
          expect.objectContaining({ id: gifter.id }),
          expect.objectContaining({ id: targetUser.id }),
          {
            targetUserEmail: targetUser.email,
            amount: 10,
            currency: "GBP"
          } as TrackGiftPropertiesType
        );
      });
    });

    describe("when gift is created for existing unverified target user", () => {
      let targetUser: UserDocument;
      let gifter: UserDocument;
      let gift: GiftDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        gifter = await buildUser({
          portfolioConversionStatus: "completed",
          canSendGiftUntil: DateUtil.getDateAfterNdays(TODAY, 1)
        });
        targetUser = await buildUser({
          portfolioConversionStatus: "notStarted",
          kycStatus: KycStatusEnum.PENDING
        });

        await Promise.all([
          buildNotificationSettings({ owner: gifter.id }),
          buildNotificationSettings({ owner: targetUser.id })
        ]);

        await buildPortfolio({
          owner: targetUser.id,
          holdings: []
        });

        gift = await GiftService.createGift({
          gifter: gifter._id,
          targetUserEmail: targetUser.email,
          consideration: {
            currency: CurrencyEnum.GBP,
            amount: 1000
          },
          message: faker.lorem.words()
        });
      });
      afterAll(async () => await clearDb());

      it("should create a gift document", async () => {
        const gifts = await Gift.find({});
        expect(gifts.length).toBe(1);
        expect(gifts[0]).toEqual(
          expect.objectContaining({
            gifter: gifter._id,
            targetUserEmail: targetUser.email,
            message: gift.message,
            consideration: gift.consideration,
            hasViewedAppModal: false,
            used: false,
            status: "Pending"
          })
        );
      });

      it("should send an e-mail to both users", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(2);

        expect(MailerService.sendEmail).toHaveBeenNthCalledWith(
          1,
          expect.objectContaining({ email: gifter.email }),
          "gifterGiftCreation",
          { target_user_email: targetUser.email }
        );
        expect(MailerService.sendEmail).toHaveBeenNthCalledWith(
          2,
          expect.objectContaining({ email: targetUser.email }),
          "unverifiedTargetUserGiftCreation",
          {
            gifter_full_name: `${gifter.firstName} ${gifter.lastName}`,
            message: gift.message
          }
        );
      });

      it("should remove the canSendGiftUntil field from the gifter", async () => {
        const updatedGifter = (await User.findById(gifter.id)) as UserDocument;
        expect(updatedGifter.canSendGiftUntil).toBe(undefined);
      });

      it("should update the Mailchimp status of the user", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalledTimes(1);
        expect(MailchimpService.updateMember).toHaveBeenCalledWith(
          gifter.email,
          {
            merge_fields: {
              SENDGIFTCM: "Sent"
            }
          },
          AudienceIdEnum.WEALTHYHOOD,
          { silent: true }
        );
      });

      it("should emit a 'giftCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.gift.giftCreation.eventId,
          expect.objectContaining({ id: gifter.id }),
          expect.objectContaining({ id: targetUser.id }),
          {
            targetUserEmail: targetUser.email,
            amount: 10,
            currency: "GBP"
          } as TrackGiftPropertiesType
        );
      });
    });

    describe("when gift is created for non-existing target user", () => {
      let gifter: UserDocument;
      let gift: GiftDocument;
      const targetUserEmail = faker.internet.email();

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        gifter = await buildUser({
          portfolioConversionStatus: "completed",
          canSendGiftUntil: DateUtil.getDateAfterNdays(TODAY, 1)
        });

        await buildNotificationSettings({ owner: gifter.id });

        gift = await GiftService.createGift({
          gifter: gifter._id,
          targetUserEmail,
          consideration: {
            currency: CurrencyEnum.GBP,
            amount: 1000
          },
          message: faker.lorem.words()
        });
      });
      afterAll(async () => await clearDb());

      it("should create a gift document", async () => {
        const gifts = await Gift.find({});
        expect(gifts.length).toBe(1);
        expect(gifts[0]).toEqual(
          expect.objectContaining({
            gifter: gifter._id,
            targetUserEmail: gift.targetUserEmail,
            message: gift.message,
            consideration: gift.consideration,
            hasViewedAppModal: false,
            used: false,
            status: "Pending"
          })
        );
      });

      it("should send an e-mail to both users", async () => {
        expect(MailerService.sendEmail).toHaveBeenCalledTimes(1);
        expect(MailerService.sendEmail).toHaveBeenCalledWith(
          expect.objectContaining({ email: gifter.email }),
          "gifterGiftCreation",
          { target_user_email: targetUserEmail }
        );

        expect(MailerService.sendNotExistingTargetUserGiftCreation).toHaveBeenCalledTimes(1);
        expect(MailerService.sendNotExistingTargetUserGiftCreation).toHaveBeenCalledWith(
          gift.targetUserEmail,
          expect.objectContaining({ email: gifter.email }),
          gift.message
        );
      });

      it("should remove the canSendGiftUntil field from the gifter", async () => {
        const updatedGifter = (await User.findById(gifter.id)) as UserDocument;
        expect(updatedGifter.canSendGiftUntil).toBe(undefined);
      });

      it("should update the Mailchimp status of the user", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalledTimes(1);
        expect(MailchimpService.updateMember).toHaveBeenCalledWith(
          gifter.email,
          {
            merge_fields: {
              SENDGIFTCM: "Sent"
            }
          },
          AudienceIdEnum.WEALTHYHOOD,
          { silent: true }
        );
      });

      it("should emit a 'giftCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.gift.giftCreation.eventId,
          expect.objectContaining({ id: gifter.id }),
          null,
          {
            targetUserEmail,
            amount: 10,
            currency: "GBP"
          } as TrackGiftPropertiesType
        );
      });
    });
  });

  describe("createAllGiftingCapabilities", () => {
    describe("when Mailchimp does not have any users in segment", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(MailchimpService, "getPendingGiftingCapabilityMembers").mockResolvedValue([]);

        user = await buildUser();

        await GiftService.createAllGiftingCapabilities();
      });
      afterAll(async () => await clearDb());

      it("should not edit any users", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.canSendGiftUntil).toBe(undefined);
      });
    });

    describe("when Mailchimp segment has a valid user", () => {
      let user: UserDocument;

      const TODAY = new Date("2022-08-31T11:00:00Z");

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser();
        Date.now = jest.fn(() => TODAY.valueOf());

        jest.spyOn(MailchimpService, "getPendingGiftingCapabilityMembers").mockResolvedValue([user.email]);
        jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();

        await GiftService.createAllGiftingCapabilities();
      });
      afterAll(async () => await clearDb());

      it("should update the user's canSendGiftUntil field", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.canSendGiftUntil).toEqual(new Date(+TODAY + 7 * 24 * 60 * 60 * 1000)); // 7 days from today
      });

      it("should update the Mailchimp status of the user", async () => {
        expect(MailchimpService.updateMember).toHaveBeenCalledTimes(1);
        expect(MailchimpService.updateMember).toHaveBeenCalledWith(
          user.email,
          {
            merge_fields: {
              SENDGIFTCM: "Ready to send email"
            }
          },
          AudienceIdEnum.WEALTHYHOOD,
          { silent: true }
        );
      });
    });
  });

  describe("createGiftDeposits", () => {
    describe("when gift already has WK bonus ID", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createBonus");

        const gift = await buildGift({
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Created", submittedAt: new Date() } }
          }
        });
        await buildAssetTransaction({ status: "PendingGift", pendingGift: gift.id });
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel API", async () => {
        await GiftService.createGiftDeposits();

        expect(WealthkernelService.UKInstance.createBonus).not.toHaveBeenCalled();
      });
    });

    describe("when gift does not have wk listed as an active provider", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createBonus");

        const gift = await buildGift({
          deposit: {
            activeProviders: undefined
          }
        });
        await buildAssetTransaction({ status: "PendingGift", pendingGift: gift.id });
      });
      afterAll(async () => await clearDb());

      it("should not be submitted to Wealthkernel", async () => {
        await GiftService.createGiftDeposits();
        expect(WealthkernelService.UKInstance.createBonus).not.toHaveBeenCalled();
      });
    });

    describe("when gift is not linked to an asset transaction", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createBonus");

        await buildGift();
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel API", async () => {
        await GiftService.createGiftDeposits();

        expect(WealthkernelService.UKInstance.createBonus).not.toHaveBeenCalled();
      });
    });

    describe("when gift is linked to an asset transaction and is missing WK deposit ID", () => {
      let gift: GiftDocument;

      const WK_BONUS_ID = faker.string.uuid();
      const WK_GIA_TARGET_PORTFOLIO_ID = faker.string.uuid();
      const TODAY = new Date("2022-08-31T11:00:00Z");

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createBonus").mockResolvedValue({
          id: WK_BONUS_ID
        });

        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: {
            wealthkernel: {
              id: WK_GIA_TARGET_PORTFOLIO_ID,
              status: "Active"
            }
          }
        });

        gift = await buildGift({
          targetUserEmail: user.email
        });
        await buildAssetTransaction({ portfolio: portfolio.id, status: "PendingGift", pendingGift: gift.id });
      });
      afterAll(async () => await clearDb());

      it("should create a bonus payment request for the gift", async () => {
        await GiftService.createGiftDeposits();

        const updatedGift = (await Gift.findById(gift.id)) as GiftDocument;
        expect(updatedGift.toObject().deposit).toEqual(
          expect.objectContaining({
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: WK_BONUS_ID,
                status: "Created",
                submittedAt: TODAY
              }
            }
          })
        );

        expect(WealthkernelService.UKInstance.createBonus).toHaveBeenCalledWith(
          expect.objectContaining({
            clientReference: gift.id,
            consideration: {
              currency: "GBP",
              amount: Decimal.div(gift.consideration.amount, 100).toNumber()
            },
            destinationPortfolio: WK_GIA_TARGET_PORTFOLIO_ID
          })
        );
      });
    });
  });

  describe("syncPendingGiftDeposits", () => {
    describe("when gift has no bonus ID", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "retrieveBonus");

        const DATE = new Date("2022-08-20T11:00:00Z");
        Date.now = jest.fn(() => DATE.valueOf());
        const minimumCreationTime = new Date(Date.now() - 15 * 60 * 1000);

        await buildGift({
          createdAt: minimumCreationTime
        });
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel API", async () => {
        await GiftService.syncPendingGiftDeposits();

        expect(WealthkernelService.UKInstance.retrieveBonus).not.toHaveBeenCalled();
      });
    });

    describe("when gift has a settled bonus", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "retrieveBonus");

        const DATE = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => DATE.valueOf());
        const minimumCreationTime = new Date(Date.now() - 15 * 60 * 1000);

        await buildGift({
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          },
          createdAt: minimumCreationTime
        });
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel API", async () => {
        await GiftService.syncPendingGiftDeposits();

        expect(WealthkernelService.UKInstance.retrieveBonus).not.toHaveBeenCalled();
      });
    });

    describe("when gift has a bonus ID with a non-settled status", () => {
      let gift: GiftDocument;

      const WK_BONUS_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "retrieveBonus").mockResolvedValue(
          buildWealthkernelBonusResponse({
            id: WK_BONUS_ID,
            status: "Settled"
          })
        );

        const DATE = new Date("2022-08-30T11:00:00Z");
        Date.now = jest.fn(() => DATE.valueOf());
        const minimumCreationTime = new Date(Date.now() - 15 * 60 * 1000);

        const user = await buildUser();
        gift = await buildGift({
          targetUserEmail: user.email,
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: WK_BONUS_ID, status: "Created" } }
          },
          createdAt: minimumCreationTime
        });
      });
      afterAll(async () => await clearDb());

      it("should update the status of the gift bonus", async () => {
        await GiftService.syncPendingGiftDeposits();

        const updatedGift = (await Gift.findById(gift.id)) as GiftDocument;
        expect(updatedGift.toObject().deposit).toEqual(
          expect.objectContaining({
            providers: {
              wealthkernel: { id: WK_BONUS_ID, status: "Settled" }
            }
          })
        );

        expect(WealthkernelService.UKInstance.retrieveBonus).toHaveBeenCalledWith(WK_BONUS_ID);
      });
    });

    describe("when gift was created in the last 15 minutes", () => {
      const DATE = new Date("2022-08-30T11:00:00Z");

      const WK_BONUS_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "retrieveBonus").mockResolvedValue(
          buildWealthkernelBonusResponse({
            id: WK_BONUS_ID,
            status: "Settled"
          })
        );

        Date.now = jest.fn(() => DATE.valueOf());

        const user = await buildUser();
        await buildGift({
          targetUserEmail: user.email,
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: WK_BONUS_ID, status: "Created" } }
          },
          createdAt: DATE
        });
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel API", async () => {
        await GiftService.syncPendingGiftDeposits();

        expect(WealthkernelService.UKInstance.retrieveBonus).not.toHaveBeenCalled();
      });
    });
  });
});
