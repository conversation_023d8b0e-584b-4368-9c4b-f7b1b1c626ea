import { Payout } from "../../models/Payout";
import { faker } from "@faker-js/faker";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import PayoutService from "../payoutService";
import { GoCardlessPaymentsService } from "../../external-services/goCardlessPaymentsService";
import { buildGoCardlessPayout } from "../../tests/utils/generateGoCardless";
import { ProviderEnum } from "../../configs/providersConfig";
import { buildPayout } from "../../tests/utils/generateModels";

describe("PayoutService", () => {
  beforeAll(async () => await connectDb("PayoutService"));
  afterAll(async () => await closeDb());

  describe("processGoCardlessPayoutEvent", () => {
    describe("when event has payout link that is not in our DB", () => {
      const GC_PAYOUT_ID = faker.string.uuid();
      const GC_PAYOUT_REFERENCE = faker.string.uuid();

      beforeAll(async () => {
        jest.restoreAllMocks();

        jest
          .spyOn(GoCardlessPaymentsService.Instance, "retrievePayout")
          .mockResolvedValue(
            buildGoCardlessPayout({ id: GC_PAYOUT_ID, reference: GC_PAYOUT_REFERENCE, status: "paid" })
          );

        await PayoutService.processGoCardlessPayoutEvent({
          id: "EV123",
          resource_type: "payouts",
          action: "paid",
          links: {
            payout: GC_PAYOUT_ID
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should create a payout document and sync with GoCardless", async () => {
        const payout = await Payout.findOne({ "providers.gocardless.id": GC_PAYOUT_ID });

        expect(payout).toMatchObject({
          status: "Pending",
          reference: GC_PAYOUT_REFERENCE,
          activeProviders: [ProviderEnum.GOCARDLESS],
          providers: {
            gocardless: {
              id: GC_PAYOUT_ID,
              status: "paid"
            }
          }
        });
      });
    });

    describe("when event has payout link that exists in our DB", () => {
      const GC_PAYOUT_ID = faker.string.uuid();
      const GC_PAYOUT_REFERENCE = faker.string.uuid();

      beforeAll(async () => {
        jest.restoreAllMocks();

        jest
          .spyOn(GoCardlessPaymentsService.Instance, "retrievePayout")
          .mockResolvedValue(
            buildGoCardlessPayout({ id: GC_PAYOUT_ID, reference: GC_PAYOUT_REFERENCE, status: "paid" })
          );

        await buildPayout({
          reference: GC_PAYOUT_REFERENCE,
          providers: {
            gocardless: {
              id: GC_PAYOUT_ID,
              status: "pending"
            }
          }
        });

        await PayoutService.processGoCardlessPayoutEvent({
          id: "EV123",
          resource_type: "payouts",
          action: "fx_rate_confirmed",
          links: {
            payout: GC_PAYOUT_ID
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should sync with GoCardless without creating a new payout document", async () => {
        const payouts = await Payout.find({ "providers.gocardless.id": GC_PAYOUT_ID });

        expect(payouts).toHaveLength(1);
        expect(payouts[0]).toMatchObject({
          status: "Pending",
          reference: GC_PAYOUT_REFERENCE,
          activeProviders: [ProviderEnum.GOCARDLESS, ProviderEnum.DEVENGO],
          providers: {
            gocardless: {
              id: GC_PAYOUT_ID,
              status: "paid"
            }
          }
        });
      });
    });
  });
});
