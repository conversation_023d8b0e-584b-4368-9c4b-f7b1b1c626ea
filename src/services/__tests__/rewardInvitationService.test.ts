import { Reward } from "../../models/Reward";
import { UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildRewardInvitation, buildUser } from "../../tests/utils/generateModels";
import { faker } from "@faker-js/faker";
import MailerService from "../../external-services/mailerService";
import RewardInvitationService from "../rewardInvitationService";
import { RewardInvitation } from "../../models/RewardInvitation";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";

describe("RewardInvitationService", () => {
  beforeAll(async () => await connectDb("RewardInvitationService"));
  afterAll(async () => await closeDb());

  describe("createRewardInvitation", () => {
    describe("when existing user is invited", () => {
      let targetUser: UserDocument;
      let referrer: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        referrer = await buildUser();
        targetUser = await buildUser();

        await RewardInvitationService.createRewardInvitation(targetUser.email, referrer._id);
      });
      afterAll(async () => await clearDb());

      it("should not create a reward document", async () => {
        const rewards = await Reward.find({});
        expect(rewards.length).toBe(0);
      });
    });

    describe("when user who is already invited by someone is invited again", () => {
      let referrer: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        referrer = await buildUser();

        const targetUserEmail = faker.internet.email();
        await buildRewardInvitation({
          referrer: referrer.id,
          targetUserEmail
        });

        await RewardInvitationService.createRewardInvitation(targetUserEmail, referrer._id);
      });
      afterAll(async () => await clearDb());

      it("should not create a reward invitation document", async () => {
        const rewards = await RewardInvitation.find({});
        expect(rewards.length).toBe(1);
      });
    });

    describe("when non-existent user is invited", () => {
      let targetUserEmail: string;
      let referrer: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        referrer = await buildUser();
        targetUserEmail = faker.internet.email();

        await RewardInvitationService.createRewardInvitation(targetUserEmail, referrer._id);
      });
      afterAll(async () => await clearDb());

      it("should create a reward invitation document", async () => {
        const rewardInvitations = await RewardInvitation.find({});
        expect(rewardInvitations.length).toBe(1);
        expect(rewardInvitations).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              referrer: referrer._id,
              targetUserEmail
            })
          ])
        );
      });

      it("should send an e-mail to the target user", async () => {
        expect(MailerService.sendUserInvitation).toHaveBeenCalledTimes(1);
        expect(MailerService.sendUserInvitation).toHaveBeenCalledWith(
          targetUserEmail,
          expect.objectContaining({ email: referrer.email })
        );
      });

      it("should emit a friend invitation event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.friendInvitation.eventId,
          expect.objectContaining({ id: referrer.id }),
          { invitedEmail: targetUserEmail }
        );
      });
    });
  });
});
