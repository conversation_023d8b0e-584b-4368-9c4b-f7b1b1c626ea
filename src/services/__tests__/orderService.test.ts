import mongoose from "mongoose";
import Decimal from "decimal.js";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildAssetTransaction,
  buildCashbackTransaction,
  buildDepositCashTransaction,
  buildHoldingDTO,
  buildInvestmentProduct,
  buildOrder,
  buildPortfolio,
  buildSavingsTopup,
  buildSavingsWithdrawal,
  buildSubscription,
  buildUser
} from "../../tests/utils/generateModels";
import OrderService, { OrderActivityItemType } from "../orderService";
import { entitiesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { InvestmentProduct, InvestmentProductDocument } from "../../models/InvestmentProduct";
import { ProviderEnum } from "../../configs/providersConfig";
import { Order, OrderDocument, OrderDTOInterface, OrderSubmissionIntentEnum } from "../../models/Order";
import { WealthkernelService, CurrencyEnum } from "../../external-services/wealthkernelService";
import { faker } from "@faker-js/faker";
import { Portfolio, PortfolioDocument } from "../../models/Portfolio";
import { User, UserDocument } from "../../models/User";
import {
  AssetTransaction,
  AssetTransactionDocument,
  CashbackTransactionDocument,
  SavingsTopupTransactionDocument,
  SavingsWithdrawalTransactionDocument,
  Transaction
} from "../../models/Transaction";
import { BadRequestError } from "../../models/ApiErrors";
import eventEmitter from "../../loaders/eventEmitter";
import logger from "../../external-services/loggerService";
import events from "../../event-handlers/events";
import InvestmentProductService from "../investmentProductService";
import { buildWealthkernelOrderResponse } from "../../tests/utils/generateWealthkernel";
import DateUtil from "../../utils/dateUtil";
import { TenorEnum } from "../../configs/durationConfig";

const { ASSET_CONFIG } = investmentUniverseConfig;

const ZERO_GBP_FEES = {
  fx: { currency: CurrencyEnum.GBP, amount: 0 },
  commission: { currency: CurrencyEnum.GBP, amount: 0 },
  executionSpread: { currency: CurrencyEnum.GBP, amount: 0 },
  realtimeExecution: { currency: CurrencyEnum.GBP, amount: 0 }
};

describe("OrderService", () => {
  const CONSIDERATION_AMOUNT = 5;
  const QUANTITY = 10;

  beforeEach(() => jest.clearAllMocks());
  beforeAll(async () => await connectDb("OrderService"));
  afterEach(async () => await clearDb());
  afterAll(async () => await closeDb());

  describe("createDbOrder", () => {
    it("should create an order", async () => {
      const assetTransaction = await buildAssetTransaction();
      const orderData: OrderDTOInterface = {
        consideration: {
          currency: "GBP",
          amount: CONSIDERATION_AMOUNT * 100
        },
        quantity: QUANTITY,
        isin: "IE00BF11F565",
        settlementCurrency: "GBP",
        side: "Buy",
        transaction: new mongoose.Types.ObjectId(assetTransaction.id),
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
      };

      await OrderService.createDbOrder(orderData);

      const orders = await Order.find({ isin: "IE00BF11F565" });
      expect(orders.length).toEqual(1);
      expect(orders[0]).toMatchObject(
        expect.objectContaining({
          consideration: expect.objectContaining({
            currency: "GBP",
            amount: CONSIDERATION_AMOUNT * 100
          }),
          quantity: QUANTITY,
          isin: "IE00BF11F565",
          settlementCurrency: "GBP",
          side: "Buy",
          transaction: new mongoose.Types.ObjectId(assetTransaction.id),
          submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
        })
      );
    });
  });

  describe("getRebalanceBuyOrdersToCreate", () => {
    it("should create an order of amount 1 GBP", async () => {
      const allocation = [
        {
          asset: await buildInvestmentProduct(true, { assetId: "equities_us", price: 10 }),
          assetCommonId: "equities_us" as investmentUniverseConfig.AssetType,
          percentage: 50
        },
        {
          asset: await buildInvestmentProduct(true, { assetId: "equities_uk", price: 10 }),
          assetCommonId: "equities_uk" as investmentUniverseConfig.AssetType,
          percentage: 50
        }
      ];

      const assetsToBuy = [
        {
          amount: 10,
          assetCommonId: "equities_us" as investmentUniverseConfig.AssetType,
          asset: await InvestmentProduct.findOne({ commonId: "equities_us" }).populate("currentTicker")
        }
      ];
      const orders = await OrderService.getRebalanceBuyOrdersToCreate(
        "GBP",
        assetsToBuy,
        allocation,
        1,
        entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
      );

      expect(orders.length).toBe(1);
      expect(orders?.[0]?.consideration?.amount).toBe(100); // £1
    });
  });

  describe("submitRealtimeOrdersSafely", () => {
    describe("when there are only ETF orders with AGGREGATE submission intent", () => {
      beforeEach(async () => {
        const TODAY = new Date("2022-08-31T15:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(WealthkernelService.UKInstance, "createOrder");

        await buildInvestmentProduct(true, {
          assetId: "equities_us",
          price: 10
        });

        const user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });

        const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });

        const order = await buildOrder({
          quantity: 1,
          side: "Sell",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_us"].isin,
          consideration: undefined,
          submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
        });

        await OrderService.submitRealtimeOrdersSafely([order], portfolio);
      });

      it("should not submit any orders", async () => {
        expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
      });
    });

    describe("when there are only ETF orders with REAL_TIME submission intent", () => {
      const WK_PORTFOLIO_ID = faker.string.uuid();

      let order: OrderDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-31T15:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(WealthkernelService.UKInstance, "createOrder");

        await buildInvestmentProduct(true, {
          assetId: "equities_us",
          price: 10
        });

        const user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } }
        });

        const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });

        order = await buildOrder({
          quantity: 1,
          side: "Sell",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_us"].isin,
          consideration: undefined,
          submissionIntent: OrderSubmissionIntentEnum.REAL_TIME
        });

        await OrderService.submitRealtimeOrdersSafely([order], portfolio);
      });

      it("should submit the valid order realtime", async () => {
        expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledTimes(1);
        expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledWith(
          {
            isin: ASSET_CONFIG["equities_us"].isin,
            aggregate: false,
            side: "Sell",
            quantity: 1,
            portfolioId: WK_PORTFOLIO_ID,
            settlementCurrency: "GBP"
          },
          order.id
        );
      });
    });

    describe("when there is a valid stock order with REAL_TIME submission intent", () => {
      const WK_PORTFOLIO_ID = faker.string.uuid();

      let order: OrderDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-31T15:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: faker.string.uuid() });

        await buildInvestmentProduct(true, {
          assetId: "equities_apple",
          price: 10
        });

        const user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } }
        });

        const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });

        order = await buildOrder({
          quantity: 1,
          side: "Sell",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_apple"].isin,
          consideration: undefined,
          submissionIntent: OrderSubmissionIntentEnum.REAL_TIME
        });

        await OrderService.submitRealtimeOrdersSafely([order], portfolio);
      });

      it("should submit the valid order realtime", async () => {
        expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledTimes(1);
        expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledWith(
          {
            isin: ASSET_CONFIG["equities_apple"].isin,
            aggregate: false,
            side: "Sell",
            quantity: 1,
            portfolioId: WK_PORTFOLIO_ID,
            settlementCurrency: "GBP"
          },
          order.id
        );
      });
    });

    describe("when there is a valid stock order with AGGREGATE submission intent", () => {
      const WK_PORTFOLIO_ID = faker.string.uuid();

      let order: OrderDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-31T15:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: faker.string.uuid() });

        await buildInvestmentProduct(true, {
          assetId: "equities_apple",
          price: 10
        });

        const user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } }
        });

        const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });

        order = await buildOrder({
          quantity: 1,
          side: "Sell",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_apple"].isin,
          consideration: undefined,
          submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
        });

        await OrderService.submitRealtimeOrdersSafely([order], portfolio);
      });

      it("should not submit any orders", async () => {
        expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
      });
    });
  });

  describe("cancelOrder", () => {
    describe("when order is already submitted to broker", () => {
      let user: UserDocument;
      let order: OrderDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-31T08:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await buildInvestmentProduct(true, { assetId: "equities_apple" });

        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildSubscription({
          owner: user.id
        });

        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update"
        });
        order = await buildOrder({
          consideration: {
            amount: 988,
            originalAmount: 1000,
            currency: "GBP"
          },
          side: "Buy",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_apple"].isin,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Pending",
              submittedAt: new Date()
            }
          }
        });

        assetTransaction.orders = [order];
        await assetTransaction.save();
      });

      it("should throw an error", async () => {
        await expect(async () => await OrderService.cancelOrder(order)).rejects.toThrowError(
          new BadRequestError(`Order ${order.id} is not cancellable`)
        );
      });
    });

    describe("when order is already cancelled", () => {
      let user: UserDocument;
      let order: OrderDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-31T08:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await buildInvestmentProduct(true, { assetId: "equities_apple" });

        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildSubscription({
          owner: user.id
        });

        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update"
        });
        order = await buildOrder({
          consideration: {
            amount: 988,
            originalAmount: 1000,
            currency: "GBP"
          },
          side: "Buy",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_apple"].isin,
          status: "Cancelled"
        });

        assetTransaction.orders = [order];
        await assetTransaction.save();
      });

      it("should throw an error", async () => {
        await expect(async () => await OrderService.cancelOrder(order)).rejects.toThrowError(
          new BadRequestError(`Order ${order.id} is not cancellable`)
        );
      });
    });

    describe("when order belongs to a pending deposit single asset buy transaction and the payment flow is NOT completed", () => {
      let user: UserDocument;
      let order: OrderDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-31T08:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await buildInvestmentProduct(true, { assetId: "equities_apple" });

        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildSubscription({
          owner: user.id
        });

        const deposit = await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              version: "v3",
              status: "authorization_required"
            }
          }
        });

        const assetTransaction = await buildAssetTransaction({
          status: "PendingDeposit",
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          pendingDeposit: deposit.id
        });
        order = await buildOrder({
          consideration: {
            amount: 988,
            originalAmount: 1000,
            currency: "GBP"
          },
          side: "Buy",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_apple"].isin
        });

        assetTransaction.orders = [order];
        await assetTransaction.save();

        await OrderService.cancelOrder(order);
      });

      it("should set the order status to Cancelled", async () => {
        const updatedOrder = await Order.findById(order.id);
        expect(updatedOrder!.status).toBe("Cancelled");
      });

      it("should NOT return the cash to the user", async () => {
        const updatedPortfolio = await Portfolio.findOne({ owner: user.id });
        expect(updatedPortfolio!.cash!.GBP!.available).toBe(0);
      });

      it("should set the transaction status to Cancelled", async () => {
        const updatedTransaction = await Transaction.findById(order.transaction);
        expect(updatedTransaction!.status).toBe("Cancelled");
      });

      it("should emit an order cancelled event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.order.orderCancellation.eventId,
          expect.objectContaining({
            email: user.email
          }),
          { asset: "equities_apple", amount: 10, currency: "GBP", side: "Buy" }
        );
      });
    });

    describe("when order belongs to a pending single asset buy transaction", () => {
      let user: UserDocument;
      let order: OrderDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-31T08:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await buildInvestmentProduct(true, { assetId: "equities_apple" });

        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildSubscription({
          owner: user.id
        });

        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update"
        });
        order = await buildOrder({
          consideration: {
            amount: 988,
            originalAmount: 1000,
            currency: "GBP"
          },
          side: "Buy",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_apple"].isin
        });

        assetTransaction.orders = [order];
        await assetTransaction.save();

        await OrderService.cancelOrder(order);
      });

      it("should set the order status to Cancelled", async () => {
        const updatedOrder = await Order.findById(order.id);
        expect(updatedOrder!.status).toBe("Cancelled");
      });

      it("should return the cash to the user", async () => {
        const updatedPortfolio = await Portfolio.findOne({ owner: user.id });
        expect(updatedPortfolio!.cash!.GBP!.available).toBe(10);
        expect(updatedPortfolio!.cash!.GBP!.settled).toBe(10);
      });

      it("should set the transaction status to Cancelled", async () => {
        const updatedTransaction = await Transaction.findById(order.transaction);
        expect(updatedTransaction!.status).toBe("Cancelled");
      });

      it("should emit an order cancelled event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.order.orderCancellation.eventId,
          expect.objectContaining({
            email: user.email
          }),
          { asset: "equities_apple", amount: 10, currency: "GBP", side: "Buy" }
        );
      });
    });

    describe("when order belongs to a pending single asset buy transaction, the user has portfolio conversion status in progress and has no other pending orders", () => {
      let user: UserDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-31T08:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await buildInvestmentProduct(true, { assetId: "equities_apple" });

        user = await buildUser({ portfolioConversionStatus: "inProgress" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildSubscription({
          owner: user.id
        });

        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update"
        });
        const order = await buildOrder({
          consideration: {
            amount: 988,
            originalAmount: 1000,
            currency: "GBP"
          },
          side: "Buy",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_apple"].isin
        });

        assetTransaction.orders = [order];
        await assetTransaction.save();

        await OrderService.cancelOrder(order);
      });

      it("should set the portfolio conversion status of the user to notStarted", async () => {
        const updatedUser = await User.findById(user.id);
        expect(updatedUser!.portfolioConversionStatus).toBe("notStarted");
      });
    });

    describe("when order belongs to a pending single asset buy transaction, the user has portfolio conversion status in progress and has other pending orders", () => {
      let user: UserDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-31T08:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await buildInvestmentProduct(true, { assetId: "equities_apple" });

        user = await buildUser({ portfolioConversionStatus: "inProgress" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildSubscription({
          owner: user.id
        });

        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update"
        });
        const order = await buildOrder({
          consideration: {
            amount: 988,
            originalAmount: 1000,
            currency: "GBP"
          },
          side: "Buy",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_apple"].isin
        });

        assetTransaction.orders = [order];
        await assetTransaction.save();

        const anotherAssetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update"
        });
        const anotherOrder = await buildOrder({
          consideration: {
            amount: 988,
            originalAmount: 1000,
            currency: "GBP"
          },
          side: "Buy",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_apple"].isin
        });

        anotherAssetTransaction.orders = [anotherOrder];
        await anotherAssetTransaction.save();

        await OrderService.cancelOrder(order);
      });

      it("should NOT set the portfolio conversion status of the user to notStarted", async () => {
        const updatedUser = await User.findById(user.id);
        expect(updatedUser!.portfolioConversionStatus).toBe("inProgress");
      });
    });

    describe("when order belongs to a pending single asset buy transaction that has a cashback linked to it", () => {
      let user: UserDocument;
      let order: OrderDocument;
      let cashback: CashbackTransactionDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-31T08:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await buildInvestmentProduct(true, { assetId: "equities_apple" });

        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildSubscription({
          owner: user.id
        });

        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update"
        });
        order = await buildOrder({
          consideration: {
            amount: 5000,
            originalAmount: 5000,
            currency: "GBP"
          },
          side: "Buy",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_apple"].isin
        });

        assetTransaction.orders = [order];
        await assetTransaction.save();

        cashback = await buildCashbackTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAssetTransaction: assetTransaction.id
        });

        await OrderService.cancelOrder(order);
      });

      it("should set the cashback status to Cancelled", async () => {
        const updatedCashback = await Transaction.findById(cashback.id);
        expect(updatedCashback!.status).toBe("Cancelled");
      });
    });

    describe("when order belongs to a pending single asset buy transaction and it is the first transaction of the user", () => {
      let user: UserDocument;
      let order: OrderDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-31T08:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await buildInvestmentProduct(true, { assetId: "equities_apple" });

        user = await buildUser({
          portfolioConversionStatus: "inProgress"
        });
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildSubscription({
          owner: user.id
        });

        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update"
        });
        order = await buildOrder({
          consideration: {
            amount: 5000,
            originalAmount: 5000,
            currency: "GBP"
          },
          side: "Buy",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_apple"].isin
        });

        assetTransaction.orders = [order];
        await assetTransaction.save();

        await OrderService.cancelOrder(order);
      });

      it("should set the user portfolio conversion status to notStarted", async () => {
        const updatedUser = await User.findById(user.id);
        expect(updatedUser!.portfolioConversionStatus).toBe("notStarted");
      });
    });

    describe("when order belongs to a pending single asset sell transaction", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let order: OrderDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-31T08:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await buildInvestmentProduct(true, { assetId: "equities_apple" });

        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          cash: { GBP: { available: 1000, reserved: 0, settled: 1000 } }
        });

        await buildSubscription({
          owner: user.id
        });

        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update"
        });
        order = await buildOrder({
          consideration: undefined,
          quantity: 1,
          side: "Sell",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_apple"].isin
        });

        assetTransaction.orders = [order];
        await assetTransaction.save();

        await OrderService.cancelOrder(order);
      });

      it("should set the order status to Cancelled", async () => {
        const updatedOrder = await Order.findById(order.id);
        expect(updatedOrder!.status).toBe("Cancelled");
      });

      it("should NOT update the cash of the user", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio.id);
        expect(updatedPortfolio!.cash!.GBP!.available).toBe(portfolio!.cash!.GBP!.available);
        expect(updatedPortfolio!.cash!.GBP!.settled).toBe(portfolio!.cash!.GBP!.settled);
      });

      it("should set the transaction status to Cancelled", async () => {
        const updatedTransaction = await Transaction.findById(order.transaction);
        expect(updatedTransaction!.status).toBe("Cancelled");
      });

      it("should emit an order cancelled event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.order.orderCancellation.eventId,
          expect.objectContaining({
            email: user.email
          }),
          { asset: "equities_apple", quantity: 1, currency: "GBP", side: "Sell" }
        );
      });
    });

    describe("when order belongs to a pending portfolio buy transaction that only has cancelled orders", () => {
      let user: UserDocument;
      let orderToCancel: OrderDocument;
      let alreadyCancelledOrder: OrderDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-31T08:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_apple" }),
          buildInvestmentProduct(true, { assetId: "equities_microsoft" })
        ]);

        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildSubscription({
          owner: user.id
        });

        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 5000
        });

        [orderToCancel, alreadyCancelledOrder] = await Promise.all([
          buildOrder({
            consideration: {
              amount: 2450,
              originalAmount: 2500,
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_apple"].isin,
            status: "Pending"
          }),
          buildOrder({
            consideration: {
              amount: 2450,
              originalAmount: 2500,
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_microsoft"].isin,
            status: "Cancelled"
          })
        ]);

        assetTransaction.orders = [orderToCancel, alreadyCancelledOrder];
        await assetTransaction.save();

        await OrderService.cancelOrder(orderToCancel);
      });

      it("should set the order status to Cancelled", async () => {
        const updatedOrder = await Order.findById(orderToCancel.id);
        expect(updatedOrder!.status).toBe("Cancelled");
      });

      it("should NOT affect the order status of the other pending order to Cancelled", async () => {
        const updatedOrder = await Order.findById(alreadyCancelledOrder.id);
        expect(updatedOrder!.status).toBe("Cancelled");
      });

      it("should return the cash to the user ONLY for the cancelled order", async () => {
        const updatedPortfolio = await Portfolio.findOne({ owner: user.id });
        expect(updatedPortfolio!.cash!.GBP!.available).toBe(25);
        expect(updatedPortfolio!.cash!.GBP!.settled).toBe(25);
      });

      it("should set the transaction status to Cancelled", async () => {
        const updatedTransaction = await AssetTransaction.findById(orderToCancel.transaction);
        expect(updatedTransaction!.status).toBe("Cancelled");
      });

      it("should update the display amount for the transaction to the original investment amount", async () => {
        const investmentProducts = await InvestmentProductService.getInvestmentProductsDict("isin", true);

        const updatedTransaction = await AssetTransaction.findById(orderToCancel.transaction).populate("orders");
        expect(updatedTransaction!.getDisplayAmount("GBP", investmentProducts)).toBe(5000);
      });
    });

    describe("when order belongs to a pending portfolio buy transaction that has other pending orders", () => {
      let user: UserDocument;
      let orderToCancel: OrderDocument;
      let pendingOrder: OrderDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-31T08:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_apple" }),
          buildInvestmentProduct(true, { assetId: "equities_microsoft" })
        ]);

        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildSubscription({
          owner: user.id
        });

        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy"
        });

        [orderToCancel, pendingOrder] = await Promise.all([
          buildOrder({
            consideration: {
              amount: 2450,
              originalAmount: 2500,
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_apple"].isin,
            status: "Pending"
          }),
          buildOrder({
            consideration: {
              amount: 2450,
              originalAmount: 2500,
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_microsoft"].isin,
            status: "Pending"
          })
        ]);

        assetTransaction.orders = [orderToCancel, pendingOrder];
        await assetTransaction.save();

        await OrderService.cancelOrder(orderToCancel);
      });

      it("should set the order status to Cancelled", async () => {
        const updatedOrder = await Order.findById(orderToCancel.id);
        expect(updatedOrder!.status).toBe("Cancelled");
      });

      it("should NOT affect the order status of the other pending order to Cancelled", async () => {
        const updatedOrder = await Order.findById(pendingOrder.id);
        expect(updatedOrder!.status).toBe("Pending");
      });

      it("should return the cash to the user ONLY for the cancelled order", async () => {
        const updatedPortfolio = await Portfolio.findOne({ owner: user.id });
        expect(updatedPortfolio!.cash!.GBP!.available).toBe(25);
        expect(updatedPortfolio!.cash!.GBP!.settled).toBe(25);
      });

      it("should NOT set the transaction status to Cancelled", async () => {
        const updatedTransaction = await Transaction.findById(orderToCancel.transaction);
        expect(updatedTransaction!.status).toBe("Pending");
      });

      it("should update the display amount for the transaction to the original investment amount", async () => {
        const investmentProducts = await InvestmentProductService.getInvestmentProductsDict("isin", true);

        const updatedTransaction = await AssetTransaction.findById(orderToCancel.transaction).populate("orders");
        expect(updatedTransaction!.getDisplayAmount("GBP", investmentProducts)).toBe(2500);
      });
    });

    describe("when order belongs to a pending portfolio buy transaction that only has matched orders", () => {
      let user: UserDocument;
      let orderToCancel: OrderDocument;
      let matchedOrder: OrderDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-31T08:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_apple" }),
          buildInvestmentProduct(true, { assetId: "equities_microsoft" })
        ]);

        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildSubscription({
          owner: user.id
        });

        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy"
        });

        [orderToCancel, matchedOrder] = await Promise.all([
          buildOrder({
            consideration: {
              amount: 2450,
              originalAmount: 2500,
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_apple"].isin,
            status: "Pending"
          }),
          buildOrder({
            consideration: {
              amount: 2450,
              originalAmount: 2500,
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_microsoft"].isin,
            status: "Matched"
          })
        ]);

        assetTransaction.orders = [orderToCancel, matchedOrder];
        await assetTransaction.save();

        await OrderService.cancelOrder(orderToCancel);
      });

      it("should set the order status to Cancelled", async () => {
        const updatedOrder = await Order.findById(orderToCancel.id);
        expect(updatedOrder!.status).toBe("Cancelled");
      });

      it("should NOT affect the order status of the matched order", async () => {
        const updatedOrder = await Order.findById(matchedOrder.id);
        expect(updatedOrder!.status).toBe("Matched");
      });

      it("should return the cash to the user ONLY for the cancelled order", async () => {
        const updatedPortfolio = await Portfolio.findOne({ owner: user.id });
        expect(updatedPortfolio!.cash!.GBP!.available).toBe(25);
        expect(updatedPortfolio!.cash!.GBP!.settled).toBe(25);
      });

      it("should set the transaction status to Settled", async () => {
        const updatedTransaction = await Transaction.findById(orderToCancel.transaction);
        expect(updatedTransaction!.status).toBe("Settled");
      });

      it("should update the display amount for the transaction to the original investment amount", async () => {
        const investmentProducts = await InvestmentProductService.getInvestmentProductsDict("isin", true);

        const updatedTransaction = await AssetTransaction.findById(orderToCancel.transaction).populate("orders");
        expect(updatedTransaction!.getDisplayAmount("GBP", investmentProducts)).toBe(2500);
      });
    });

    describe("when order belongs to a pending portfolio buy transaction that has a cashback linked to it", () => {
      let user: UserDocument;
      let cashback: CashbackTransactionDocument;
      let orderToCancel: OrderDocument;
      let pendingOrder: OrderDocument;
      let anotherPendingOrder: OrderDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-31T08:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_apple" }),
          buildInvestmentProduct(true, { assetId: "equities_microsoft" })
        ]);

        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildSubscription({
          owner: user.id
        });

        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 15000,
          consideration: {
            amount: 15000,
            currency: "GBP"
          }
        });

        [orderToCancel, pendingOrder, anotherPendingOrder] = await Promise.all([
          buildOrder({
            consideration: {
              amount: 4990,
              originalAmount: 5000,
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_apple"].isin
          }),
          buildOrder({
            consideration: {
              amount: 4990,
              originalAmount: 5000,
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_microsoft"].isin
          }),
          buildOrder({
            consideration: {
              amount: 4990,
              originalAmount: 5000,
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_microsoft"].isin
          })
        ]);

        assetTransaction.orders = [orderToCancel, pendingOrder, anotherPendingOrder];
        await assetTransaction.save();

        cashback = await buildCashbackTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAssetTransaction: assetTransaction.id,
          consideration: {
            amount: 38, // £0.38
            currency: "GBP"
          }
        });

        await OrderService.cancelOrder(orderToCancel);
      });

      it("should set the order status to Cancelled", async () => {
        const updatedOrder = await Order.findById(orderToCancel.id);
        expect(updatedOrder!.status).toBe("Cancelled");
      });

      it("should NOT affect the order status of the other pending orders to Cancelled", async () => {
        const updatedOrders = await Order.find({ _id: { $ne: orderToCancel.id } });
        updatedOrders.forEach((order) => expect(order.status).toBe("Pending"));
      });

      it("should return the cash to the user ONLY for the cancelled order", async () => {
        const updatedPortfolio = await Portfolio.findOne({ owner: user.id });
        expect(updatedPortfolio!.cash!.GBP!.available).toBe(50);
        expect(updatedPortfolio!.cash!.GBP!.settled).toBe(50);
      });

      it("should NOT set the transaction status to Cancelled", async () => {
        const updatedTransaction = await Transaction.findById(orderToCancel.transaction);
        expect(updatedTransaction!.status).toBe("Pending");
      });

      it("should update the cashback consideration amount", async () => {
        const updatedCashback = await Transaction.findById(cashback.id);
        expect(updatedCashback!.toObject()).toEqual(
          expect.objectContaining({
            status: "Pending",
            consideration: {
              amount: 25, // £0.25
              currency: "GBP"
            }
          })
        );
      });
    });

    describe("when order belongs to a pending portfolio buy transaction that has a cashback linked to it and is now under our cashback minimum", () => {
      let user: UserDocument;
      let cashback: CashbackTransactionDocument;
      let orderToCancel: OrderDocument;
      let pendingOrder: OrderDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-31T08:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_apple" }),
          buildInvestmentProduct(true, { assetId: "equities_microsoft" })
        ]);

        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildSubscription({
          owner: user.id
        });

        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          consideration: {
            amount: 5000,
            currency: "GBP"
          },
          originalInvestmentAmount: 5000
        });

        [orderToCancel, pendingOrder] = await Promise.all([
          buildOrder({
            consideration: {
              amount: 2450,
              originalAmount: 2500,
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_apple"].isin
          }),
          buildOrder({
            consideration: {
              amount: 2450,
              originalAmount: 2500,
              currency: "GBP"
            },
            side: "Buy",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_microsoft"].isin
          })
        ]);

        assetTransaction.orders = [orderToCancel, pendingOrder];
        await assetTransaction.save();

        cashback = await buildCashbackTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAssetTransaction: assetTransaction.id,
          consideration: {
            amount: 13, // £0.13
            currency: "GBP"
          }
        });

        await OrderService.cancelOrder(orderToCancel);
      });

      it("should set the order status to Cancelled", async () => {
        const updatedOrder = await Order.findById(orderToCancel.id);
        expect(updatedOrder!.status).toBe("Cancelled");
      });

      it("should NOT affect the order status of the other pending order to Cancelled", async () => {
        const updatedOrder = await Order.findById(pendingOrder.id);
        expect(updatedOrder!.status).toBe("Pending");
      });

      it("should return the cash to the user ONLY for the cancelled order", async () => {
        const updatedPortfolio = await Portfolio.findOne({ owner: user.id });
        expect(updatedPortfolio!.cash!.GBP!.available).toBe(25);
        expect(updatedPortfolio!.cash!.GBP!.settled).toBe(25);
      });

      it("should NOT set the transaction status to Cancelled", async () => {
        const updatedTransaction = await Transaction.findById(orderToCancel.transaction);
        expect(updatedTransaction!.status).toBe("Pending");
      });

      it("should cancel the cashback", async () => {
        const updatedCashback = await Transaction.findById(cashback.id);
        expect(updatedCashback!.status).toBe("Cancelled");
      });
    });
  });

  describe("createSavingsOrder", () => {
    describe("when the transaction is a topup and has no orders", () => {
      const ORDER_AMOUNT = 1000;
      let savingsTopup: SavingsTopupTransactionDocument;

      beforeEach(async () => {
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user });

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: ORDER_AMOUNT
            }
          },
          {},
          false
        );

        await OrderService.createSavingsOrder(savingsTopup);
      });

      it("should create only one savings order", async () => {
        const orders = await Order.find({ transaction: savingsTopup.id });

        expect(orders.length).toEqual(1);
        expect(orders[0]).toMatchObject({
          transaction: savingsTopup._id,
          commonId: savingsTopup.savingsProduct,
          side: "Buy",
          consideration: expect.objectContaining({
            currency: "GBP",
            amount: ORDER_AMOUNT,
            originalAmount: ORDER_AMOUNT,
            amountSubmitted: ORDER_AMOUNT
          }),
          fees: expect.objectContaining(ZERO_GBP_FEES),
          isSubmittedToBroker: false,
          submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
        });
      });
    });

    describe("when the transaction is a topup and has no orders and pending buy amount is less than minimum", () => {
      const ORDER_AMOUNT = 1;
      let savingsTopup: SavingsTopupTransactionDocument;

      beforeEach(async () => {
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user });

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: ORDER_AMOUNT
            }
          },
          {},
          false
        );
      });

      it("should not create an order document", async () => {
        await OrderService.createSavingsOrder(savingsTopup);

        const orders = await Order.find({ transaction: savingsTopup.id });
        expect(orders).toHaveLength(0);
      });
    });

    describe("when the transaction is a topup and has some InternallyFilled", () => {
      const TRANSACTION_AMOUNT = 1000;
      const RESTICTED_AMOUNT = 500;
      let savingsTopup: SavingsTopupTransactionDocument;

      beforeEach(async () => {
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user });

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: TRANSACTION_AMOUNT
            }
          },
          {
            consideration: {
              currency: "GBP",
              amount: RESTICTED_AMOUNT
            },
            status: "InternallyFilled"
          }
        );

        await OrderService.createSavingsOrder(savingsTopup);
      });

      it("should create only one savings order", async () => {
        expect(savingsTopup.orders).toHaveLength(1);
        const orders = await Order.find({ transaction: savingsTopup.id });

        expect(orders.length).toEqual(2);
        expect(orders).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              transaction: savingsTopup._id,
              commonId: savingsTopup.savingsProduct,
              side: "Buy",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: TRANSACTION_AMOUNT - RESTICTED_AMOUNT,
                originalAmount: TRANSACTION_AMOUNT - RESTICTED_AMOUNT,
                amountSubmitted: TRANSACTION_AMOUNT - RESTICTED_AMOUNT
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              transaction: savingsTopup._id,
              commonId: savingsTopup.savingsProduct,
              side: "Buy",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: RESTICTED_AMOUNT,
                originalAmount: RESTICTED_AMOUNT,
                amountSubmitted: RESTICTED_AMOUNT
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );
      });
    });

    describe("when the transaction is a topup and has some InternallyFilled and pending buy amount is less than minimum", () => {
      const TRANSACTION_AMOUNT = 1000;
      const RESTICTED_AMOUNT = 999;
      let savingsTopup: SavingsTopupTransactionDocument;

      beforeEach(async () => {
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user });

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: TRANSACTION_AMOUNT
            }
          },
          {
            consideration: {
              currency: "GBP",
              amount: RESTICTED_AMOUNT
            },
            status: "InternallyFilled"
          }
        );
      });

      it("should not create a new order document", async () => {
        await OrderService.createSavingsOrder(savingsTopup);

        const orders = await Order.find({ transaction: savingsTopup.id });
        // 1 InteranalFilled order already exists
        expect(orders).toHaveLength(1);
      });
    });

    describe("when the transaction is a withdrawal and has no orders", () => {
      const ORDER_AMOUNT = 1000;
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;

      beforeEach(async () => {
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user });

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: ORDER_AMOUNT
            }
          },
          {},
          false
        );

        await OrderService.createSavingsOrder(savingsWithdrawal);
      });

      it("should create only one savings order", async () => {
        const orders = await Order.find({ transaction: savingsWithdrawal.id });

        expect(orders.length).toEqual(1);
        expect(orders[0]).toMatchObject({
          transaction: savingsWithdrawal._id,
          commonId: savingsWithdrawal.savingsProduct,
          side: "Sell",
          consideration: expect.objectContaining({
            currency: "GBP",
            amount: ORDER_AMOUNT,
            originalAmount: ORDER_AMOUNT,
            amountSubmitted: ORDER_AMOUNT
          }),
          fees: expect.objectContaining(ZERO_GBP_FEES),
          isSubmittedToBroker: false,
          submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
        });
      });
    });

    describe("when the transaction is a withdrawal and has no orders and pending sell amount is less than minimum", () => {
      const ORDER_AMOUNT = 1;
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;

      beforeEach(async () => {
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user });

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: ORDER_AMOUNT
            }
          },
          {},
          false
        );
      });

      it("should not create an order document", async () => {
        await OrderService.createSavingsOrder(savingsWithdrawal);

        const orders = await Order.find({ transaction: savingsWithdrawal.id });
        expect(orders).toHaveLength(0);
      });
    });

    describe("when the transaction is a withdrawal and has some InternallyFilled", () => {
      const TRANSACTION_AMOUNT = 1000;
      const RESTRICTED_AMOUNT = 500;

      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;

      beforeEach(async () => {
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user });

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: TRANSACTION_AMOUNT
            }
          },
          {
            consideration: {
              currency: "GBP",
              amount: RESTRICTED_AMOUNT
            },
            status: "InternallyFilled"
          }
        );

        await OrderService.createSavingsOrder(savingsWithdrawal);
      });

      it("should create only one savings order", async () => {
        expect(savingsWithdrawal.orders).toHaveLength(1);
        const orders = await Order.find({ transaction: savingsWithdrawal.id });

        expect(orders.length).toEqual(2);
        expect(orders).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              transaction: savingsWithdrawal._id,
              commonId: savingsWithdrawal.savingsProduct,
              side: "Sell",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: TRANSACTION_AMOUNT - RESTRICTED_AMOUNT,
                originalAmount: TRANSACTION_AMOUNT - RESTRICTED_AMOUNT,
                amountSubmitted: TRANSACTION_AMOUNT - RESTRICTED_AMOUNT
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              transaction: savingsWithdrawal._id,
              commonId: savingsWithdrawal.savingsProduct,
              side: "Sell",
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: RESTRICTED_AMOUNT,
                originalAmount: RESTRICTED_AMOUNT,
                amountSubmitted: RESTRICTED_AMOUNT
              }),
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );
      });
    });

    describe("when the transaction is a withdrawal and has some InternallyFilled and pending sell amount is less than minimum", () => {
      const TRANSACTION_AMOUNT = 1000;
      const RESTICTED_AMOUNT = 999;
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;

      beforeEach(async () => {
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user });

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: TRANSACTION_AMOUNT
            }
          },
          {
            consideration: {
              currency: "GBP",
              amount: RESTICTED_AMOUNT
            },
            status: "InternallyFilled"
          }
        );
      });

      it("should not create a new order document", async () => {
        await OrderService.createSavingsOrder(savingsWithdrawal);

        const orders = await Order.find({ transaction: savingsWithdrawal.id });
        // 1 InteranalFilled order already exists
        expect(orders).toHaveLength(1);
      });
    });
  });

  describe("applyFeesToOrders", () => {
    describe("when a single stock order has order amount > total fees", () => {
      const ORDER_ISIN = investmentUniverseConfig.ASSET_CONFIG["equities_apple"].isin;
      const ORDER_DATA: Omit<OrderDTOInterface, "transaction"> = {
        isin: ORDER_ISIN,
        settlementCurrency: "GBP",
        side: "Buy",
        consideration: {
          currency: "GBP",
          amount: 1000,
          originalAmount: 1000
        },
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
      };
      let investmentProduct: InvestmentProductDocument;

      beforeEach(async () => {
        const user = await buildUser({ currency: "GBP" });
        [investmentProduct] = await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_apple", price: 10 }),
          buildSubscription({ owner: user.id, price: "free_monthly" })
        ]);
      });

      it("should return order including fees", () => {
        const [orderWithFees] = OrderService.applyFeesToOrders("free", [ORDER_DATA], "GBP", {
          [ORDER_ISIN]: investmentProduct
        });

        // FX fee calculation for free plan:
        // Original amount: £10 (1000 cents)
        // FX_FEE_SPREADS_WH.free = 0.0015 (0.15%)
        // FX fee = 1000 * 0.0015 = 1.5 cents, which rounds to 2 cents (0.02)
        // Consideration after FX fee = 1000 - 2 = 998 cents
        expect(orderWithFees).toMatchObject({
          ...ORDER_DATA,
          consideration: {
            amount: 998,
            currency: "GBP",
            originalAmount: 1000
          },
          fees: {
            fx: { amount: 0.02, currency: "GBP" },
            commission: { amount: 0, currency: "GBP" },
            executionSpread: { amount: 0, currency: "GBP" },
            realtimeExecution: { amount: 0, currency: "GBP" }
          }
        });
      });
    });

    describe("when a single stock order with real-time submission intent has order amount > total fees", () => {
      const ORDER_ISIN = investmentUniverseConfig.ASSET_CONFIG["equities_apple"].isin;
      const ORDER_DATA: Omit<OrderDTOInterface, "transaction"> = {
        isin: ORDER_ISIN,
        settlementCurrency: "GBP",
        side: "Buy",
        consideration: {
          currency: "GBP",
          amount: 1000,
          originalAmount: 1000
        },
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        submissionIntent: OrderSubmissionIntentEnum.REAL_TIME
      };
      let investmentProduct: InvestmentProductDocument;

      beforeEach(async () => {
        const user = await buildUser({ currency: "GBP" });
        [investmentProduct] = await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_apple", price: 10 }),
          buildSubscription({ owner: user.id, price: "free_monthly" })
        ]);
      });

      it("should return order including fees without a real-time execution fee", () => {
        const [orderWithFees] = OrderService.applyFeesToOrders("free", [ORDER_DATA], "GBP", {
          [ORDER_ISIN]: investmentProduct
        });

        // FX fee calculation for free plan:
        // Original amount: £10 (1000 cents)
        // FX_FEE_SPREADS_WH.free = 0.0015 (0.15%)
        // FX fee = 1000 * 0.0015 = 1.5 cents, which rounds to 2 cents (0.02)
        // Consideration after FX fee = 1000 - 2 = 998 cents
        expect(orderWithFees).toMatchObject({
          ...ORDER_DATA,
          consideration: {
            amount: 998,
            currency: "GBP",
            originalAmount: 1000
          },
          fees: {
            fx: { amount: 0.02, currency: "GBP" },
            commission: { amount: 0, currency: "GBP" },
            executionSpread: { amount: 0, currency: "GBP" },
            realtimeExecution: { amount: 0, currency: "GBP" }
          }
        });
      });
    });

    describe("when a single ETF order with real-time submission intent has order amount equal to real time execution fee", () => {
      const ORDER_ISIN = investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin;
      const ORDER_DATA: Omit<OrderDTOInterface, "transaction"> = {
        isin: ORDER_ISIN,
        settlementCurrency: "GBP",
        side: "Buy",
        consideration: {
          currency: "GBP",
          amount: 100,
          originalAmount: 100
        },
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        submissionIntent: OrderSubmissionIntentEnum.REAL_TIME
      };
      let investmentProduct: InvestmentProductDocument;

      beforeEach(async () => {
        const user = await buildUser({ currency: "GBP" });
        [investmentProduct] = await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_us", price: 10 }),
          buildSubscription({ owner: user.id, price: "free_monthly" })
        ]);
      });

      it("should return order including fees with a real-time execution fee", () => {
        const [orderWithFees] = OrderService.applyFeesToOrders("free", [ORDER_DATA], "GBP", {
          [ORDER_ISIN]: investmentProduct
        });

        expect(orderWithFees).toMatchObject({
          ...ORDER_DATA,
          consideration: {
            amount: 0,
            currency: "GBP",
            originalAmount: 100
          },
          fees: {
            fx: { amount: 0, currency: "GBP" },
            commission: { amount: 0, currency: "GBP" },
            executionSpread: { amount: 0, currency: "GBP" },
            realtimeExecution: { amount: 1, currency: "GBP" }
          }
        });
      });
    });

    describe("when a single ETF order with real-time submission intent has sufficient order amount for real time execution", () => {
      const ORDER_ISIN = investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin;
      const ORDER_DATA: Omit<OrderDTOInterface, "transaction"> = {
        isin: ORDER_ISIN,
        settlementCurrency: "GBP",
        side: "Buy",
        consideration: {
          currency: "GBP",
          amount: 1000,
          originalAmount: 1000
        },
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        submissionIntent: OrderSubmissionIntentEnum.REAL_TIME
      };
      let investmentProduct: InvestmentProductDocument;

      beforeEach(async () => {
        const user = await buildUser({ currency: "GBP" });
        [investmentProduct] = await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_us", price: 10 }),
          buildSubscription({ owner: user.id, price: "free_monthly" })
        ]);
      });
      it("should return order including fees with a real-time execution fee", () => {
        const [orderWithFees] = OrderService.applyFeesToOrders("free", [ORDER_DATA], "GBP", {
          [ORDER_ISIN]: investmentProduct
        });

        expect(orderWithFees).toMatchObject({
          ...ORDER_DATA,
          consideration: {
            amount: 900,
            currency: "GBP",
            originalAmount: 1000
          },
          fees: {
            fx: { amount: 0, currency: "GBP" },
            commission: { amount: 0, currency: "GBP" },
            executionSpread: { amount: 0, currency: "GBP" },
            realtimeExecution: { amount: 1, currency: "GBP" }
          }
        });
      });
    });

    describe("when a single ETF order with aggregate submission intent has sufficient order amount for real time execution", () => {
      const ORDER_ISIN = investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin;
      const ORDER_DATA: Omit<OrderDTOInterface, "transaction"> = {
        isin: ORDER_ISIN,
        settlementCurrency: "GBP",
        side: "Buy",
        consideration: {
          currency: "GBP",
          amount: 1000,
          originalAmount: 1000
        },
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
      };
      let investmentProduct: InvestmentProductDocument;

      beforeEach(async () => {
        const user = await buildUser({ currency: "GBP" });
        [investmentProduct] = await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_us", price: 10 }),
          buildSubscription({ owner: user.id, price: "free_monthly" })
        ]);
      });

      it("should return order including fees without real-time execution fee", () => {
        const [orderWithFees] = OrderService.applyFeesToOrders("free", [ORDER_DATA], "GBP", {
          [ORDER_ISIN]: investmentProduct
        });

        expect(orderWithFees).toMatchObject({
          ...ORDER_DATA,
          consideration: {
            amount: 1000,
            currency: "GBP",
            originalAmount: 1000
          },
          fees: {
            fx: { amount: 0, currency: "GBP" },
            commission: { amount: 0, currency: "GBP" },
            executionSpread: { amount: 0, currency: "GBP" },
            realtimeExecution: { amount: 0, currency: "GBP" }
          }
        });
      });
    });
  });

  describe("syncOrderByWealthkernelId", () => {
    describe("order status transitions", () => {
      let order: OrderDocument;
      let transaction: AssetTransactionDocument;

      beforeEach(async () => {
        jest.spyOn(logger, "warn").mockImplementation(() => null);

        const user = await buildUser();
        await buildSubscription({
          owner: user.id
        });
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } },
          holdings: [await buildHoldingDTO(true, "equities_us", 10, { price: 50 })]
        });
        transaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });

        // Mock WealthkernelService response
        const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
          id: "123",
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          status: "Matched",
          side: "Buy"
        });
        jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);
      });

      afterEach(() => {
        jest.resetAllMocks();
      });

      it("should allow transition from Pending to Matched", async () => {
        order = await buildOrder({
          isin: ASSET_CONFIG["equities_us"].isin,
          status: "Pending",
          providers: { wealthkernel: { status: "Pending", id: "123", submittedAt: new Date() } },
          transaction: transaction.id
        });

        await OrderService.syncOrderByWealthkernelId(order.id, "Matched");

        const updatedOrder = await Order.findById(order.id);
        expect(updatedOrder!.status).toBe("Matched");
        expect(updatedOrder!.providers!.wealthkernel!.status).toBe("Matched");
      });

      it("should not allow transition from Pending to Settled", async () => {
        order = await buildOrder({
          status: "Pending",
          providers: { wealthkernel: { status: "Pending", id: "123", submittedAt: new Date() } },
          transaction: transaction.id
        });

        await expect(OrderService.syncOrderByWealthkernelId(order.id, "Settled")).rejects.toThrow(
          `Cannot update order ${order.id} as the transition from Pending to Settled is not valid`
        );

        const updatedOrder = await Order.findById(order.id);
        expect(updatedOrder!.status).toBe("Pending");
        expect(updatedOrder!.providers!.wealthkernel!.status).toBe("Pending");
        expect(logger.warn).toHaveBeenCalledWith(
          "Cannot update WK order as the transition from Pending to Settled is not valid",
          expect.any(Object)
        );
      });

      it("should allow transition from Matched to Settled", async () => {
        order = await buildOrder({
          status: "Matched",
          providers: { wealthkernel: { status: "Matched", id: "123", submittedAt: new Date() } },
          transaction: transaction.id
        });

        await OrderService.syncOrderByWealthkernelId(order.id, "Settled");

        const updatedOrder = await Order.findById(order.id);
        expect(updatedOrder!.status).toBe("Settled");
        expect(updatedOrder!.providers!.wealthkernel!.status).toBe("Matched");
      });

      it("should not allow transition from Settled to Matched", async () => {
        order = await buildOrder({
          status: "Settled",
          providers: { wealthkernel: { status: "Matched", id: "123", submittedAt: new Date() } },
          transaction: transaction.id
        });

        await expect(OrderService.syncOrderByWealthkernelId(order.id, "Matched")).rejects.toThrow(
          `Cannot update order ${order.id} as the transition from Settled to Matched is not valid`
        );

        const updatedOrder = await Order.findById(order.id);
        expect(updatedOrder!.status).toBe("Settled");
        expect(updatedOrder!.providers!.wealthkernel!.status).toBe("Matched");
        expect(logger.warn).toHaveBeenCalledWith(
          "Cannot update WK order as the transition from Settled to Matched is not valid",
          expect.any(Object)
        );
      });
    });

    describe("when orderId matches buy order and status is settled", () => {
      const DATE = new Date("2022-08-31T11:00:00Z");
      const WK_BUY_ORDER_ID = faker.string.uuid();
      const WK_SETTLEMENT_DATE = "2022-09-02T14:30:00Z";

      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let order: OrderDocument;
      let transaction: AssetTransactionDocument;

      beforeEach(async () => {
        Date.now = jest.fn(() => DATE.valueOf());

        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } },
          cash: { GBP: { available: 10, reserved: 0, settled: 0 } }
        });
        await buildSubscription({ owner: user.id });
        await buildInvestmentProduct(true, { assetId: "equities_us" });

        transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id
        });

        order = await buildOrder({
          providers: { wealthkernel: { id: WK_BUY_ORDER_ID, status: "Matched", submittedAt: DATE } },
          status: "Matched",
          side: "Buy",
          transaction: transaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            amount: 20000,
            originalAmount: 20000,
            amountSubmitted: 19700,
            currency: "GBP"
          }
        });

        transaction.orders = [order];
        await transaction.save();

        const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
          id: WK_BUY_ORDER_ID,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          status: "Matched",
          side: "Buy",
          marketSettledAt: new Date(WK_SETTLEMENT_DATE)
        });
        jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

        await OrderService.syncOrderByWealthkernelId(order.id, "Settled");
      });

      it("should update order status to Settled", async () => {
        const updatedOrder = await Order.findById(order.id);
        expect(updatedOrder!.status).toBe("Settled");
      });

      it("should keep providers.wealthkernel.status as Matched", async () => {
        const updatedOrder = await Order.findById(order.id);
        expect(updatedOrder!.providers!.wealthkernel!.status).toBe("Matched");
      });

      it("should NOT update cash", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio.id);
        expect(updatedPortfolio!.cash).toMatchObject(
          expect.objectContaining({
            GBP: expect.objectContaining({ available: 10, reserved: 0, settled: 0 })
          })
        );
      });
    });

    describe("when orderId matches sell order and status is settled", () => {
      const DATE = new Date("2022-08-31T11:00:00Z");
      const WK_SELL_ORDER_ID = faker.string.uuid();

      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let order: OrderDocument;
      let transaction: AssetTransactionDocument;

      beforeEach(async () => {
        Date.now = jest.fn(() => DATE.valueOf());

        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } },
          cash: { GBP: { available: 200, reserved: 0, settled: 0 } }
        });
        await buildSubscription({ owner: user.id });
        await buildInvestmentProduct(true, { assetId: "equities_us" });

        transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id
        });

        order = await buildOrder({
          providers: { wealthkernel: { id: WK_SELL_ORDER_ID, status: "Matched", submittedAt: DATE } },
          status: "Matched",
          side: "Sell",
          transaction: transaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            amount: 20000,
            originalAmount: 20000,
            currency: "GBP"
          }
        });

        transaction.orders = [order];
        await transaction.save();

        const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
          id: WK_SELL_ORDER_ID,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          status: "Matched",
          side: "Sell"
        });
        jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

        await OrderService.syncOrderByWealthkernelId(order.id, "Settled");
      });

      it("should update order status to Settled", async () => {
        const updatedOrder = await Order.findById(order.id);
        expect(updatedOrder!.status).toBe("Settled");
      });

      it("should keep providers.wealthkernel.status as Matched", async () => {
        const updatedOrder = await Order.findById(order.id);
        expect(updatedOrder!.providers!.wealthkernel!.status).toBe("Matched");
      });

      it("should update cash availability from available to settled", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio.id);
        expect(updatedPortfolio!.cash).toMatchObject(
          expect.objectContaining({
            GBP: expect.objectContaining({ available: 200, reserved: 0, settled: 200 })
          })
        );
      });
    });

    describe("when an order is rejected", () => {
      const DATE = new Date("2022-08-31T11:00:00Z");
      const WK_SELL_ORDER_ID = faker.string.uuid();

      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let order: OrderDocument;
      let transaction: AssetTransactionDocument;

      beforeEach(async () => {
        Date.now = jest.fn(() => DATE.valueOf());

        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } },
          cash: { GBP: { available: 200, reserved: 0, settled: 0 } }
        });
        await buildSubscription({ owner: user.id });
        await buildInvestmentProduct(true, { assetId: "equities_us" });

        transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id
        });

        order = await buildOrder({
          providers: { wealthkernel: { id: WK_SELL_ORDER_ID, status: "Pending", submittedAt: DATE } },
          status: "Pending",
          side: "Sell",
          transaction: transaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          quantity: 1
        });

        transaction.orders = [order];
        await transaction.save();

        const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
          id: WK_SELL_ORDER_ID,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          status: "Rejected",
          side: "Sell",
          reason: "Could not process transaction"
        });
        jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

        await OrderService.syncOrderByWealthkernelId(order.id, "Rejected");
      });

      it("should keep order status as Pending", async () => {
        const updatedOrder = await Order.findById(order.id);
        expect(updatedOrder!.status).toBe("Pending");
      });

      it("should change providers.wealthkernel.status to Rejected", async () => {
        const updatedOrder = await Order.findById(order.id);
        expect(updatedOrder!.providers!.wealthkernel!.status).toBe("Rejected");
      });

      it("should emit an order rejected event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.order.orderRejection.eventId,
          expect.objectContaining({
            email: user.email
          }),
          {
            asset: "equities_us",
            amount: undefined,
            quantity: 1,
            currency: "GBP",
            side: "Sell",
            rejectionReason: "Could not process transaction"
          }
        );
      });
    });

    describe("when order is for asset with order currency different from traded currency (paid_mid plan)", () => {
      const DATE = new Date("2022-08-31T11:00:00Z");
      const WK_ORDER_ID = "wk-order-123";
      const EXCHANGE_RATE = 1.25;
      const BASE_EXCHANGE_RATE = 1.2;

      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let order: OrderDocument;
      let transaction: AssetTransactionDocument;

      beforeEach(async () => {
        Date.now = jest.fn(() => DATE.valueOf());

        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
        });
        await buildSubscription({ owner: user.id, price: "paid_mid_monthly" });
        await buildInvestmentProduct(true, { assetId: "equities_apple" }); // Apple stock (foreign currency)

        transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id
        });

        order = await buildOrder({
          providers: { wealthkernel: { id: WK_ORDER_ID, status: "Pending", submittedAt: DATE } },
          status: "Pending",
          side: "Buy",
          transaction: transaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_apple"].isin,
          consideration: {
            amount: 10000, // £100
            originalAmount: 10000,
            currency: "GBP"
          }
        });

        transaction.orders = [order];
        await transaction.save();

        // Mock WK order response with exchange rate
        const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
          id: WK_ORDER_ID,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_apple"].isin,
          status: "Matched",
          side: "Buy",
          fills: [
            {
              status: "Matched",
              exchangeRate: EXCHANGE_RATE,
              baseExchangeRate: BASE_EXCHANGE_RATE,
              price: { currency: CurrencyEnum.USD, amount: 12500 },
              consideration: { currency: CurrencyEnum.GBP, amount: 10000 },
              quantity: 1
            }
          ]
        });
        jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

        await OrderService.syncOrderByWealthkernelId(order.id, "Matched");
      });

      it("should store broker FX rate from WealthKernel", async () => {
        const updatedOrder = await Order.findById(order.id);
        expect(updatedOrder!.providers!.wealthkernel!.brokerFxRate).toBe(EXCHANGE_RATE);
      });

      it("should store base exchange rate from WealthKernel", async () => {
        const updatedOrder = await Order.findById(order.id);
        expect(updatedOrder!.providers!.wealthkernel!.baseExchangeRate).toBe(BASE_EXCHANGE_RATE);
      });

      it("should calculate and store broker FX fee", async () => {
        const updatedOrder = await Order.findById(order.id);
        // For paid_mid plan (0.25% spread) on £100 = £0.25 (25 cents)
        expect(updatedOrder!.providers!.wealthkernel!.accountingBrokerFxFee).toBe(25);
      });

      it("should store exchange rate with spread for paid_mid plan", async () => {
        const updatedOrder = await Order.findById(order.id);

        // For paid_mid plan (0.325% spread) on BUY order: 1.2 * (1 - 0.00325) = 1.1961
        const expectedExchangeRateWithSpread = new Decimal(BASE_EXCHANGE_RATE)
          .mul(new Decimal(1).minus(0.00325))
          .toNumber();
        expect(updatedOrder!.exchangeRate).toBe(expectedExchangeRateWithSpread);
      });
    });

    describe("when order is for asset with order currency different from traded currency (free plan)", () => {
      const DATE = new Date("2022-08-31T11:00:00Z");
      const WK_ORDER_ID = "wk-order-free-123";
      const EXCHANGE_RATE = 1.25;
      const BASE_EXCHANGE_RATE = 1.2;

      let user: UserDocument;
      let order: OrderDocument;

      beforeEach(async () => {
        Date.now = jest.fn(() => DATE.valueOf());

        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
        });
        await buildSubscription({ owner: user.id, price: "free_monthly" });
        await buildInvestmentProduct(true, { assetId: "equities_apple" });

        const transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id
        });

        order = await buildOrder({
          providers: { wealthkernel: { id: WK_ORDER_ID, status: "Pending", submittedAt: DATE } },
          status: "Pending",
          side: "Buy",
          transaction: transaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_apple"].isin,
          consideration: {
            amount: 10000, // £100
            originalAmount: 10000,
            currency: "GBP"
          }
        });

        transaction.orders = [order];
        await transaction.save();

        const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
          id: WK_ORDER_ID,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_apple"].isin,
          status: "Matched",
          side: "Buy",
          fills: [
            {
              status: "Matched",
              exchangeRate: EXCHANGE_RATE,
              baseExchangeRate: BASE_EXCHANGE_RATE,
              price: { currency: CurrencyEnum.USD, amount: 12500 },
              consideration: { currency: CurrencyEnum.GBP, amount: 10000 },
              quantity: 1
            }
          ]
        });
        jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

        await OrderService.syncOrderByWealthkernelId(order.id, "Matched");
      });

      it("should store exchange rate with spread for free plan", async () => {
        const updatedOrder = await Order.findById(order.id);

        // For free plan (0.55% spread) on BUY order: 1.2 * (1 - 0.0055) = 1.1934
        const expectedExchangeRateWithSpread = new Decimal(BASE_EXCHANGE_RATE)
          .mul(new Decimal(1).minus(0.0055))
          .toNumber();
        expect(updatedOrder!.exchangeRate).toBe(expectedExchangeRateWithSpread);
      });
    });

    describe("when order is for asset with order currency different from traded currency (paid_low plan)", () => {
      const DATE = new Date("2022-08-31T11:00:00Z");
      const WK_ORDER_ID = "wk-order-paid-low-123";
      const EXCHANGE_RATE = 1.25;
      const BASE_EXCHANGE_RATE = 1.2;

      let user: UserDocument;
      let order: OrderDocument;

      beforeEach(async () => {
        Date.now = jest.fn(() => DATE.valueOf());

        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
        });
        await buildSubscription({ owner: user.id, price: "paid_low_monthly" });
        await buildInvestmentProduct(true, { assetId: "equities_apple" });

        const transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id
        });

        order = await buildOrder({
          providers: { wealthkernel: { id: WK_ORDER_ID, status: "Pending", submittedAt: DATE } },
          status: "Pending",
          side: "Buy",
          transaction: transaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_apple"].isin,
          consideration: {
            amount: 10000, // £100
            originalAmount: 10000,
            currency: "GBP"
          }
        });

        transaction.orders = [order];
        await transaction.save();

        const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
          id: WK_ORDER_ID,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_apple"].isin,
          status: "Matched",
          side: "Buy",
          fills: [
            {
              status: "Matched",
              exchangeRate: EXCHANGE_RATE,
              baseExchangeRate: BASE_EXCHANGE_RATE,
              price: { currency: CurrencyEnum.USD, amount: 12500 },
              consideration: { currency: CurrencyEnum.GBP, amount: 10000 },
              quantity: 1
            }
          ]
        });
        jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

        await OrderService.syncOrderByWealthkernelId(order.id, "Matched");
      });

      it("should store exchange rate with spread for paid_low plan", async () => {
        const updatedOrder = await Order.findById(order.id);

        // For paid_low plan (0.4% spread) on BUY order: 1.2 * (1 - 0.004) = 1.1952
        const expectedExchangeRateWithSpread = new Decimal(BASE_EXCHANGE_RATE)
          .mul(new Decimal(1).minus(0.004))
          .toNumber();
        expect(updatedOrder!.exchangeRate).toBe(expectedExchangeRateWithSpread);
      });
    });

    describe("when order is for asset with order currency same as traded currency", () => {
      const DATE = new Date("2022-08-31T11:00:00Z");
      const WK_ORDER_ID = "wk-order-456";

      let user: UserDocument;
      let order: OrderDocument;

      beforeEach(async () => {
        Date.now = jest.fn(() => DATE.valueOf());

        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } }
        });
        await buildSubscription({ owner: user.id, price: "paid_mid_monthly" });
        await buildInvestmentProduct(true, { assetId: "equities_us" });

        const transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id
        });

        order = await buildOrder({
          providers: { wealthkernel: { id: WK_ORDER_ID, status: "Pending", submittedAt: DATE } },
          status: "Pending",
          side: "Buy",
          transaction: transaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            amount: 10000, // £100
            originalAmount: 10000,
            currency: "GBP"
          }
        });

        // Mock WK order response
        const WK_ORDER_RESPONSE = buildWealthkernelOrderResponse({
          id: WK_ORDER_ID,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
          status: "Matched",
          side: "Buy",
          fills: [
            {
              status: "Matched",
              exchangeRate: 1,
              baseExchangeRate: 1,
              price: { currency: CurrencyEnum.GBP, amount: 10000 },
              consideration: { currency: CurrencyEnum.GBP, amount: 10000 },
              quantity: 1
            }
          ]
        });
        jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockResolvedValue(WK_ORDER_RESPONSE);

        await OrderService.syncOrderByWealthkernelId(order.id, "Matched");
      });

      it("should store broker exchange rate and have value of 1", async () => {
        const updatedOrder = await Order.findById(order.id);
        expect(updatedOrder!.providers!.wealthkernel!.brokerFxRate).toBe(1);
      });

      it("should store base exchange rate and have value of 1", async () => {
        const updatedOrder = await Order.findById(order.id);
        expect(updatedOrder!.providers!.wealthkernel!.baseExchangeRate).toBe(1);
      });

      it("should store broker FX fee and have value of 0", async () => {
        const updatedOrder = await Order.findById(order.id);
        expect(updatedOrder!.providers!.wealthkernel!.accountingBrokerFxFee).toBe(0);
      });
    });
  });

  describe("getAssetOrderActivityByTenor", () => {
    describe("when there have been orders for the asset in various time in the past", () => {
      let activity: Record<TenorEnum, OrderActivityItemType[]> = {
        [TenorEnum.ALL_TIME]: [],
        [TenorEnum.ONE_YEAR]: [],
        [TenorEnum.SIX_MONTHS]: [],
        [TenorEnum.THREE_MONTHS]: [],
        [TenorEnum.ONE_MONTH]: [],
        [TenorEnum.ONE_WEEK]: [],
        [TenorEnum.ONE_DAY]: [],
        [TenorEnum.TODAY]: []
      };

      beforeEach(async () => {
        const TODAY = new Date("2022-08-31T15:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(WealthkernelService.UKInstance, "createOrder");

        await buildInvestmentProduct(true, {
          assetId: "equities_us",
          price: 10
        });

        const user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });

        const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });

        for (let i = 0; i < 7; i++) {
          await buildOrder({
            quantity: 1,
            side: "Sell",
            status: "Matched",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_us"].isin,
            consideration: undefined,
            filledAt: DateUtil.getDateOfDaysAgo(TODAY, i),
            submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
          });
        }

        await buildOrder({
          quantity: 1,
          side: "Sell",
          status: "Matched",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_us"].isin,
          consideration: undefined,
          filledAt: DateUtil.getDateOfDaysAgo(TODAY, 25),
          submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
        });

        await buildOrder({
          quantity: 1,
          side: "Sell",
          status: "Matched",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_us"].isin,
          consideration: undefined,
          filledAt: DateUtil.getDateOfDaysAgo(TODAY, 300),
          submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
        });

        activity = await OrderService.getAssetOrderActivityByTenor(user.id, "equities_us");
      });

      it("should return the correct activity by tenor", async () => {
        expect(activity["1w"].length).toBe(7);
        expect(activity["6m"].length).toBe(8);
        expect(activity["1y"].length).toBe(9);
      });
    });

    describe("when there have been orders for the asset and then it was deprecated by another asset", () => {
      let activity: Record<TenorEnum, OrderActivityItemType[]> = {
        [TenorEnum.ALL_TIME]: [],
        [TenorEnum.ONE_YEAR]: [],
        [TenorEnum.SIX_MONTHS]: [],
        [TenorEnum.THREE_MONTHS]: [],
        [TenorEnum.ONE_MONTH]: [],
        [TenorEnum.ONE_WEEK]: [],
        [TenorEnum.ONE_DAY]: [],
        [TenorEnum.TODAY]: []
      };

      beforeEach(async () => {
        const TODAY = new Date("2022-08-31T15:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(WealthkernelService.UKInstance, "createOrder");

        await buildInvestmentProduct(true, {
          assetId: "equities_blackrock_deprecated_1",
          price: 10
        });

        await buildInvestmentProduct(true, {
          assetId: "equities_blackrock",
          price: 10
        });

        const user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });

        const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });

        await buildOrder({
          quantity: 1,
          side: "Sell",
          status: "Matched",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_blackrock_deprecated_1"].isin,
          consideration: undefined,
          filledAt: TODAY,
          submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
        });

        await buildOrder({
          quantity: 1,
          side: "Sell",
          status: "Matched",
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_blackrock"].isin,
          consideration: undefined,
          filledAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
        });

        activity = await OrderService.getAssetOrderActivityByTenor(user.id, "equities_blackrock");
      });

      it("should return the correct activity by tenor INCLUDING all the orders of the deprecated asset", async () => {
        expect(activity["1w"].length).toBe(2);
      });
    });
  });
});
