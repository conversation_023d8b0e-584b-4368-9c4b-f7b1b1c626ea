import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { UserDocument } from "../../models/User";
import { buildMandate, buildUser } from "../../tests/utils/generateModels";
import { Mandate, MandateDocument } from "../../models/Mandate";
import MandateService from "../mandateService";
import logger from "../../external-services/loggerService";

describe("MandateService", () => {
  beforeAll(async () => await connectDb("MandateService"));
  afterAll(async () => await closeDb());

  describe("processGoCardlessMandateEvent", () => {
    describe("when event has mandate link that is not in our DB", () => {
      beforeAll(async () => {
        jest.restoreAllMocks();

        jest.spyOn(MandateService, "processGoCardlessMandateEvent");
      });
      afterAll(async () => await clearDb());

      it("should throw an error", async () => {
        await expect(
          async () =>
            await MandateService.processGoCardlessMandateEvent({
              id: "EV123",
              resource_type: "mandates",
              action: "active",
              links: {
                mandate: "SOME-MANDATE-WE-DONT-HAVE"
              }
            })
        ).rejects.toThrow(
          new Error(
            "Could not update mandate SOME-MANDATE-WE-DONT-HAVE because we don't have a reference to it in our database"
          )
        );
      });
    });

    describe("when event has mandate action that should not be processed", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.restoreAllMocks();

        jest.spyOn(logger, "info");

        user = await buildUser();
        await buildMandate({
          owner: user.id,
          bankAccount: user.bankAccounts[0].id,
          providers: { gocardless: { id: "MD123", status: "pending_customer_approval" } }
        });

        await MandateService.processGoCardlessMandateEvent({
          id: "EV123",
          resource_type: "mandates",
          action: "expired",
          links: {
            mandate: "MD123"
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should log the fact that we are not processing that action", async () => {
        expect(logger.info).toHaveBeenCalledWith("Not processing mandate events of action expired", {
          module: "MandateService",
          method: "processGoCardlessMandateEvent",
          data: {
            mandate: "MD123",
            action: "expired"
          }
        });
      });
    });

    describe("when event has mandate action that should be processed", () => {
      let user: UserDocument;
      let mandate: MandateDocument;

      beforeAll(async () => {
        jest.restoreAllMocks();

        user = await buildUser();
        mandate = await buildMandate({
          owner: user.id,
          bankAccount: user.bankAccounts[0].id,
          providers: { gocardless: { id: "MD123", status: "pending_submission" } }
        });

        await MandateService.processGoCardlessMandateEvent({
          id: "EV123",
          resource_type: "mandates",
          action: "active",
          links: {
            mandate: "MD123"
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should update the status of that mandate to active", async () => {
        const updatedMandate = await Mandate.findById(mandate.id);
        expect(updatedMandate?.providers?.gocardless).toEqual({
          id: "MD123",
          status: "active"
        });
      });
    });
  });
});
