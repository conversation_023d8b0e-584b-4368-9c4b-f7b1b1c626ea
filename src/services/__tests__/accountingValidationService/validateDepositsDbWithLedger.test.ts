import "jest";
import Decimal from "decimal.js";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { clearSqliteDb, createSqliteDb, connectDb, clearDb, closeDb } from "../../../tests/utils/db";
import {
  buildDepositCashTransaction,
  buildUser,
  buildPortfolio,
  buildCreditTicket
} from "../../../tests/utils/generateModels";
import { AccountingValidationService } from "../../accountingValidationService";
import AccountingLedgerStorageService from "../../../external-services/accountingLedgerStorageService";
import { DepositMethodEnum } from "../../../models/Transaction";
import { LedgerAccounts, AccountingEventType } from "../../../types/accounting";
import { UserDocument } from "../../../models/User";
import { PortfolioDocument } from "../../../models/Portfolio";

describe("AccountingValidationService.validateDepositsDbWithLedger", () => {
  const TODAY = "2025-06-12";
  const YESTERDAY = "2025-06-11";

  beforeAll(async () => {
    Date.now = jest.fn(() => +new Date(TODAY));
    await connectDb("validateDepositsDbWithLedger");
    await createSqliteDb();
  });

  afterAll(async () => await closeDb());

  afterEach(async () => {
    await clearSqliteDb();
    await clearDb();
  });

  it("should pass validation for all deposit stages with complete ledger entries", async () => {
    const DEPOSIT_AMOUNT_CENTS = 1_000_00; // €1,000.00 in cents
    const DEPOSIT_AMOUNT_EUROS = Decimal.div(DEPOSIT_AMOUNT_CENTS, 100).toNumber();

    // Create domestic user (GR) => client ledger 30-00-00-0000
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create credit ticket for instant money flow (needed for stage 2)
    const creditTicket = await buildCreditTicket({
      status: "Credited"
    });

    // Stage 1 Deposit: Devengo acquisition confirmed (External → Intermediary #1)
    const stage1Deposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-1",
                  status: "confirmed",
                  accountId: "acc-1",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
        }
      },
      user,
      portfolio
    );

    // Stage 2 Deposit: Devengo collection confirmed + instant flow (Intermediary #1 → Intermediary #2)
    const stage2Deposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        linkedCreditTicket: creditTicket.id, // This makes inInstantMoneyFlow = true
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-2",
                  status: "confirmed",
                  accountId: "acc-2",
                  settledAt: new Date(YESTERDAY) // Stage 1 happened yesterday
                }
              }
            }
          },
          collection: {
            outgoingPayment: {
              providers: {
                devengo: {
                  id: "devengo-out-2",
                  status: "confirmed",
                  settledAt: new Date(TODAY) // Stage 2 happens today
                }
              }
            }
          }
        }
      },
      user,
      portfolio
    );

    // Stage 3 Deposit: WealthKernel settled (Intermediary → Omnibus)
    const stage3Deposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-3",
                  status: "confirmed",
                  accountId: "acc-3",
                  settledAt: new Date(YESTERDAY) // Stage 1 happened yesterday
                }
              }
            }
          },
          collection: {
            outgoingPayment: {
              providers: {
                devengo: {
                  id: "devengo-out-3",
                  status: "confirmed",
                  settledAt: new Date(YESTERDAY) // Stage 2 happened yesterday
                }
              }
            }
          }
        },
        providers: {
          wealthkernel: {
            id: "wk-dep-3",
            status: "Settled",
            settledAt: new Date(TODAY) // Stage 3 happens today
          }
        }
      },
      user,
      portfolio
    );

    // Create ledger entries for each stage
    const stage1Description = `${user.id}|${stage1Deposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;
    const stage2Description = `${user.id}|${stage2Deposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;
    const stage3Description = `${user.id}|${stage3Deposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      // Stage 1: External → Intermediary #1 (Devengo acquisition confirmed)
      {
        aa: 1,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage1Description
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage1Description
      },
      // Stage 2: Intermediary #1 → Intermediary #2 (Devengo collection confirmed, instant flow)
      {
        aa: 2,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_2,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage2Description
      },
      {
        aa: 2,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage2Description
      },
      // Stage 3: Intermediary → Omnibus (WealthKernel settled)
      {
        aa: 3,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage3Description
      },
      {
        aa: 3,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_2,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage3Description
      }
    ]);

    // Validate deposits
    const results = await AccountingValidationService.validateDepositsDbWithLedger(TODAY);

    expect(results).toHaveLength(3);
    const [stage1Result, stage2Result, stage3Result] = results;

    // Stage 1 validation (External → Intermediary #1)
    expect(stage1Result).toEqual({
      isValid: true,
      ledgerEntryCount: 2,
      transactionType: "deposits_stage1",
      dbTotalAmount: DEPOSIT_AMOUNT_EUROS,
      ledgerTotalAmount: DEPOSIT_AMOUNT_EUROS,
      difference: 0,
      transactionCount: 1
    });
    expect(stage1Result.discrepancies).toBeUndefined();

    // Stage 2 validation (Intermediary #1 → Intermediary #2)
    expect(stage2Result).toEqual({
      isValid: true,
      ledgerEntryCount: 2,
      transactionType: "deposits_stage2",
      dbTotalAmount: DEPOSIT_AMOUNT_EUROS,
      ledgerTotalAmount: DEPOSIT_AMOUNT_EUROS,
      difference: 0,
      transactionCount: 1
    });
    expect(stage2Result.discrepancies).toBeUndefined();

    // Stage 3 validation (Intermediary → Omnibus)
    expect(stage3Result).toEqual({
      isValid: true,
      ledgerEntryCount: 2,
      transactionType: "deposits_stage3",
      dbTotalAmount: DEPOSIT_AMOUNT_EUROS,
      ledgerTotalAmount: DEPOSIT_AMOUNT_EUROS,
      difference: 0,
      transactionCount: 1
    });
    expect(stage3Result.discrepancies).toBeUndefined();
  });

  it("should fail validation when deposit amounts don't match ledger entries", async () => {
    const DEPOSIT_AMOUNT_CENTS = 1_000_00; // €1,000.00 in cents
    const DEPOSIT_AMOUNT_EUROS = Decimal.div(DEPOSIT_AMOUNT_CENTS, 100).toNumber();
    const LEDGER_AMOUNT_EUROS = 999.5; // Different amount in ledger

    // Create domestic user
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create Stage 1 deposit with Devengo acquisition confirmed
    const deposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-1",
                  status: "confirmed",
                  accountId: "acc-1",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
        }
      },
      user,
      portfolio
    );

    // Create ledger entry with different amount
    const description = `${user.id}|${deposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      {
        aa: 1,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: LEDGER_AMOUNT_EUROS,
        article_date: TODAY,
        description
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: LEDGER_AMOUNT_EUROS,
        article_date: TODAY,
        description
      }
    ]);

    // Validate deposits
    const results = await AccountingValidationService.validateDepositsDbWithLedger(TODAY);

    expect(results).toHaveLength(3);
    const [stage1Result, stage2Result, stage3Result] = results;

    // Stage 1 should fail due to amount mismatch
    expect(stage1Result.isValid).toBe(false);
    expect(stage1Result.transactionType).toBe("deposits_stage1");
    expect(stage1Result.dbTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS);
    expect(stage1Result.ledgerTotalAmount).toBe(LEDGER_AMOUNT_EUROS);
    expect(stage1Result.difference).toBe(DEPOSIT_AMOUNT_EUROS - LEDGER_AMOUNT_EUROS);
    expect(stage1Result.transactionCount).toBe(1);
    expect(stage1Result.discrepancies).toEqual([
      {
        transactionId: deposit.id,
        dbAmount: DEPOSIT_AMOUNT_EUROS,
        ledgerAmount: LEDGER_AMOUNT_EUROS,
        difference: DEPOSIT_AMOUNT_EUROS - LEDGER_AMOUNT_EUROS
      }
    ]);

    // Stage 2 and 3 should be valid (no transactions)
    expect(stage2Result.isValid).toBe(true);
    expect(stage2Result.transactionCount).toBe(0);
    expect(stage3Result.isValid).toBe(true);
    expect(stage3Result.transactionCount).toBe(0);
  });

  it("should handle multiple deposits with different client segments", async () => {
    const AMOUNT_1_CENTS = 500_00; // €500.00
    const AMOUNT_2_CENTS = 750_00; // €750.00
    const AMOUNT_1_EUROS = Decimal.div(AMOUNT_1_CENTS, 100).toNumber();
    const AMOUNT_2_EUROS = Decimal.div(AMOUNT_2_CENTS, 100).toNumber();

    // Create domestic user (GR)
    const userGR: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolioGR: PortfolioDocument = await buildPortfolio({ owner: userGR.id });

    // Create EU user (DE)
    const userEU: UserDocument = await buildUser({
      residencyCountry: "DE",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolioEU: PortfolioDocument = await buildPortfolio({ owner: userEU.id });

    // Create Stage 1 deposits with Devengo acquisition confirmed
    const depositGR = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: AMOUNT_1_CENTS },
        portfolio: portfolioGR.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-gr",
                  status: "confirmed",
                  accountId: "acc-gr",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
        }
      },
      userGR,
      portfolioGR
    );

    const depositEU = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: AMOUNT_2_CENTS },
        portfolio: portfolioEU.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-eu",
                  status: "confirmed",
                  accountId: "acc-eu",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
        }
      },
      userEU,
      portfolioEU
    );

    // Create corresponding ledger entries
    const descriptionGR = `${userGR.id}|${depositGR.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;
    const descriptionEU = `${userEU.id}|${depositEU.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      // GR deposit entries
      {
        aa: 1,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: AMOUNT_1_EUROS,
        article_date: TODAY,
        description: descriptionGR
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: AMOUNT_1_EUROS,
        article_date: TODAY,
        description: descriptionGR
      },
      // EU deposit entries
      {
        aa: 2,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: AMOUNT_2_EUROS,
        article_date: TODAY,
        description: descriptionEU
      },
      {
        aa: 2,
        account_code: LedgerAccounts.CLIENT_EU_EEA,
        side: "credit",
        amount: AMOUNT_2_EUROS,
        article_date: TODAY,
        description: descriptionEU
      }
    ]);

    // Validate deposits
    const results = await AccountingValidationService.validateDepositsDbWithLedger(TODAY);

    expect(results).toHaveLength(3);
    const [stage1Result, stage2Result, stage3Result] = results;

    // Stage 1 should validate both deposits
    expect(stage1Result.isValid).toBe(true);
    expect(stage1Result.transactionType).toBe("deposits_stage1");
    expect(stage1Result.dbTotalAmount).toBe(AMOUNT_1_EUROS + AMOUNT_2_EUROS);
    expect(stage1Result.ledgerTotalAmount).toBe(AMOUNT_1_EUROS + AMOUNT_2_EUROS);
    expect(stage1Result.difference).toBe(0);
    expect(stage1Result.transactionCount).toBe(2);

    // Stage 2 and 3 should be valid (no transactions)
    expect(stage2Result.isValid).toBe(true);
    expect(stage2Result.transactionCount).toBe(0);
    expect(stage3Result.isValid).toBe(true);
    expect(stage3Result.transactionCount).toBe(0);
  });

  it("should ignore non-settled deposits", async () => {
    const DEPOSIT_AMOUNT_CENTS = 1_000_00; // €1,000.00 in cents

    // Create user
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create deposits with various non-settled statuses (all should be ignored)
    await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Pending"
      },
      user,
      portfolio
    );

    await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "PendingDeposit"
      },
      user,
      portfolio
    );

    await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Cancelled"
      },
      user,
      portfolio
    );

    // Validate deposits
    const results = await AccountingValidationService.validateDepositsDbWithLedger(TODAY);

    expect(results).toHaveLength(3);
    const [stage1Result, stage2Result, stage3Result] = results;

    // All stages should be valid with no transactions (non-settled deposits are ignored)
    expect(stage1Result.isValid).toBe(true);
    expect(stage1Result.transactionCount).toBe(0);
    expect(stage1Result.dbTotalAmount).toBe(0);
    expect(stage1Result.ledgerTotalAmount).toBe(0);

    expect(stage2Result.isValid).toBe(true);
    expect(stage2Result.transactionCount).toBe(0);

    expect(stage3Result.isValid).toBe(true);
    expect(stage3Result.transactionCount).toBe(0);
  });

  it("should handle validation with no transactions", async () => {
    const results = await AccountingValidationService.validateDepositsDbWithLedger(TODAY);

    expect(results).toHaveLength(3);
    const [stage1Result, stage2Result, stage3Result] = results;

    // All stages should be valid with no transactions
    expect(stage1Result.isValid).toBe(true);
    expect(stage1Result.transactionCount).toBe(0);
    expect(stage1Result.dbTotalAmount).toBe(0);
    expect(stage1Result.ledgerTotalAmount).toBe(0);
    expect(stage1Result.difference).toBe(0);

    expect(stage2Result.isValid).toBe(true);
    expect(stage2Result.transactionCount).toBe(0);

    expect(stage3Result.isValid).toBe(true);
    expect(stage3Result.transactionCount).toBe(0);
  });

  it("should handle validation with no ledger entries", async () => {
    const DEPOSIT_AMOUNT_CENTS = 1_000_00;
    const DEPOSIT_AMOUNT_EUROS = Decimal.div(DEPOSIT_AMOUNT_CENTS, 100).toNumber();

    // Create user and deposit without ledger entries
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-1",
                  status: "confirmed",
                  accountId: "acc-1",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
        }
      },
      user,
      portfolio
    );

    const results = await AccountingValidationService.validateDepositsDbWithLedger(TODAY);

    expect(results).toHaveLength(3);
    const [stage1Result, stage2Result, stage3Result] = results;

    // Stage 1 should fail due to no ledger entries
    expect(stage1Result.isValid).toBe(false);
    expect(stage1Result.transactionCount).toBe(1);
    expect(stage1Result.dbTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS);
    expect(stage1Result.ledgerTotalAmount).toBe(0);
    expect(stage1Result.difference).toBe(DEPOSIT_AMOUNT_EUROS);

    // Stage 2 and 3 should be valid (no transactions)
    expect(stage2Result.isValid).toBe(true);
    expect(stage2Result.transactionCount).toBe(0);

    expect(stage3Result.isValid).toBe(true);
    expect(stage3Result.transactionCount).toBe(0);
  });

  it("should pass validation for deposits without instant money flow (stage 2 skipped)", async () => {
    const DEPOSIT_AMOUNT_CENTS = 1_000_00; // €1,000.00 in cents
    const DEPOSIT_AMOUNT_EUROS = Decimal.div(DEPOSIT_AMOUNT_CENTS, 100).toNumber();

    // Create domestic user (GR) => client ledger 30-00-00-0000
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Single Deposit: No instant money flow (stage 2 skipped)
    // Has both Devengo acquisition (Stage 1) and WealthKernel settlement (Stage 3)
    // No linkedCreditTicket means no instant money flow, so Stage 2 is skipped
    const deposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-1",
                  status: "confirmed",
                  accountId: "acc-1",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
          // No collection section - stage 2 is skipped
        },
        providers: {
          wealthkernel: {
            id: "wk-dep-3",
            status: "Settled",
            settledAt: new Date(TODAY) // Stage 3 happens today
          }
        }
      },
      user,
      portfolio
    );

    // Create ledger entries for stage 1 and stage 3 using the same transaction (no stage 2)
    const stage1Description = `${user.id}|${deposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;
    const stage3Description = `${user.id}|${deposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      // Stage 1: External → Intermediary #1 (Devengo acquisition confirmed)
      {
        aa: 1,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage1Description
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage1Description
      },
      // Stage 3: Intermediary → Omnibus (WealthKernel settled)
      // Direct from INTERMEDIARY_DEPOSITS_1 to CLIENTS_ACCOUNTS_OMNIBUS (no stage 2)
      {
        aa: 3,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage3Description
      },
      {
        aa: 3,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage3Description
      }
    ]);

    // Validate deposits
    const results = await AccountingValidationService.validateDepositsDbWithLedger(TODAY);

    expect(results).toHaveLength(3);
    const [stage1Result, stage2Result, stage3Result] = results;

    // Stage 1 validation (External → Intermediary #1)
    expect(stage1Result).toEqual({
      isValid: true,
      ledgerEntryCount: 2,
      transactionType: "deposits_stage1",
      dbTotalAmount: DEPOSIT_AMOUNT_EUROS,
      ledgerTotalAmount: DEPOSIT_AMOUNT_EUROS,
      difference: 0,
      transactionCount: 1
    });
    expect(stage1Result.discrepancies).toBeUndefined();

    // Stage 2 validation should be valid but empty (no instant money flow transactions)
    expect(stage2Result).toEqual({
      isValid: true,
      ledgerEntryCount: 0,
      transactionType: "deposits_stage2",
      dbTotalAmount: 0,
      ledgerTotalAmount: 0,
      difference: 0,
      transactionCount: 0
    });
    expect(stage2Result.discrepancies).toBeUndefined();

    // Stage 3 validation (Intermediary → Omnibus)
    expect(stage3Result).toEqual({
      isValid: true,
      ledgerEntryCount: 2,
      transactionType: "deposits_stage3",
      dbTotalAmount: DEPOSIT_AMOUNT_EUROS,
      ledgerTotalAmount: DEPOSIT_AMOUNT_EUROS,
      difference: 0,
      transactionCount: 1
    });
    expect(stage3Result.discrepancies).toBeUndefined();
  });

  it("should handle mixed scenarios with and without instant money flow", async () => {
    const DEPOSIT_AMOUNT_CENTS = 500_00; // €500.00 in cents
    const DEPOSIT_AMOUNT_EUROS = Decimal.div(DEPOSIT_AMOUNT_CENTS, 100).toNumber();

    // Create domestic user (GR)
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create credit ticket for one of the deposits (instant money flow)
    const creditTicket = await buildCreditTicket({
      status: "Credited"
    });

    // Instant flow deposits - separate deposits for each stage (as they happen on different days)

    // Stage 1: Devengo acquisition confirmed (External → Intermediary #1)
    const stage1InstantDeposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-instant-1",
                  status: "confirmed",
                  accountId: "acc-instant-1",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
        }
      },
      user,
      portfolio
    );

    // Stage 2: Devengo collection confirmed + instant flow (Intermediary #1 → Intermediary #2)
    const stage2InstantDeposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        linkedCreditTicket: creditTicket.id, // This makes inInstantMoneyFlow = true
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-instant-1",
                  status: "confirmed",
                  accountId: "acc-instant-1",
                  settledAt: new Date(YESTERDAY) // Stage 1 happened yesterday
                }
              }
            }
          },
          collection: {
            outgoingPayment: {
              providers: {
                devengo: {
                  id: "devengo-out-instant-2",
                  status: "confirmed",
                  settledAt: new Date(TODAY) // Stage 2 happens today
                }
              }
            }
          }
        }
      },
      user,
      portfolio
    );

    // Stage 3: WealthKernel settled (Intermediary → Omnibus)
    const stage3InstantDeposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-instant-1",
                  status: "confirmed",
                  accountId: "acc-instant-1",
                  settledAt: new Date(YESTERDAY) // Stage 1 happened yesterday
                }
              }
            }
          },
          collection: {
            outgoingPayment: {
              providers: {
                devengo: {
                  id: "devengo-out-instant-2",
                  status: "confirmed",
                  settledAt: new Date(YESTERDAY) // Stage 2 happened yesterday
                }
              }
            }
          }
        },
        providers: {
          wealthkernel: {
            id: "wk-dep-instant-3",
            status: "Settled",
            settledAt: new Date(TODAY) // Stage 3 happens today
          }
        }
      },
      user,
      portfolio
    );

    // No instant flow deposits - separate deposits for stage 1 and 3 (stage 2 skipped)

    // Stage 1: Devengo acquisition confirmed (External → Intermediary #1)
    const stage1NoInstantDeposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-no-instant-1",
                  status: "confirmed",
                  accountId: "acc-no-instant-1",
                  settledAt: new Date(TODAY)
                }
              }
            }
          }
          // No collection section - stage 2 is skipped
        }
      },
      user,
      portfolio
    );

    // Stage 3: WealthKernel settled (Intermediary → Omnibus) - direct from stage 1
    const stage3NoInstantDeposit = await buildDepositCashTransaction(
      {
        depositMethod: DepositMethodEnum.BANK_TRANSFER,
        consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
        portfolio: portfolio.id,
        status: "Settled",
        transferWithIntermediary: {
          acquisition: {
            incomingPayment: {
              providers: {
                devengo: {
                  id: "devengo-in-no-instant-1",
                  status: "confirmed",
                  accountId: "acc-no-instant-1",
                  settledAt: new Date(YESTERDAY) // Stage 1 happened yesterday
                }
              }
            }
          }
          // No collection section - stage 2 is skipped
        },
        providers: {
          wealthkernel: {
            id: "wk-dep-no-instant-3",
            status: "Settled",
            settledAt: new Date(TODAY) // Stage 3 happens today
          }
        }
      },
      user,
      portfolio
    );

    // Create ledger entries for all deposits
    const stage1InstantDesc = `${user.id}|${stage1InstantDeposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;
    const stage2InstantDesc = `${user.id}|${stage2InstantDeposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;
    const stage3InstantDesc = `${user.id}|${stage3InstantDeposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;
    const stage1NoInstantDesc = `${user.id}|${stage1NoInstantDeposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;
    const stage3NoInstantDesc = `${user.id}|${stage3NoInstantDeposit.id}|${AccountingEventType.BANK_TRANSACTION_DEPOSIT}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      // Instant flow deposits - Stage 1
      {
        aa: 1,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage1InstantDesc
      },
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage1InstantDesc
      },
      // Instant flow deposits - Stage 2
      {
        aa: 2,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_2,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage2InstantDesc
      },
      {
        aa: 2,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage2InstantDesc
      },
      // Instant flow deposits - Stage 3
      {
        aa: 3,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage3InstantDesc
      },
      {
        aa: 3,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_2,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage3InstantDesc
      },
      // No instant flow deposits - Stage 1
      {
        aa: 4,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage1NoInstantDesc
      },
      {
        aa: 4,
        account_code: LedgerAccounts.CLIENT_DOMESTIC,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage1NoInstantDesc
      },
      // No instant flow deposits - Stage 3 (direct from INTERMEDIARY_DEPOSITS_1)
      {
        aa: 5,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "debit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage3NoInstantDesc
      },
      {
        aa: 5,
        account_code: LedgerAccounts.INTERMEDIARY_DEPOSITS_1,
        side: "credit",
        amount: DEPOSIT_AMOUNT_EUROS,
        article_date: TODAY,
        description: stage3NoInstantDesc
      }
    ]);

    // Validate deposits
    const results = await AccountingValidationService.validateDepositsDbWithLedger(TODAY);

    expect(results).toHaveLength(3);
    const [stage1Result, stage2Result, stage3Result] = results;

    // Stage 1 should validate both instant and non-instant deposits
    expect(stage1Result.isValid).toBe(true);
    expect(stage1Result.transactionType).toBe("deposits_stage1");
    expect(stage1Result.dbTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS * 2); // Two stage 1 deposits
    expect(stage1Result.ledgerTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS * 2);
    expect(stage1Result.difference).toBe(0);
    expect(stage1Result.transactionCount).toBe(2);

    // Stage 2 should only validate the instant flow deposit
    expect(stage2Result.isValid).toBe(true);
    expect(stage2Result.transactionType).toBe("deposits_stage2");
    expect(stage2Result.dbTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS); // Only one stage 2 deposit
    expect(stage2Result.ledgerTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS);
    expect(stage2Result.difference).toBe(0);
    expect(stage2Result.transactionCount).toBe(1);

    // Stage 3 should validate both instant and non-instant deposits
    expect(stage3Result.isValid).toBe(true);
    expect(stage3Result.transactionType).toBe("deposits_stage3");
    expect(stage3Result.dbTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS * 2); // Two stage 3 deposits
    expect(stage3Result.ledgerTotalAmount).toBe(DEPOSIT_AMOUNT_EUROS * 2);
    expect(stage3Result.difference).toBe(0);
    expect(stage3Result.transactionCount).toBe(2);
  });
});
