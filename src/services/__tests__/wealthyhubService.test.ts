import { RedisClientService } from "../../loaders/redis";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildContentEntry } from "../../tests/utils/generateModels";
import { faker } from "@faker-js/faker";
import WealthyhubService, { AnalystInsightType } from "../wealthyhubService";
import {
  ContentEntryCategoryEnum,
  ContentEntryContentTypeEnum,
  ContentEntryDocument
} from "../../models/ContentEntry";
import { FinimizeContentTypeEnum } from "../../external-services/finimizeService";
import { ProviderEnum } from "../../configs/providersConfig";
import ContentfulRetrievalService from "../../external-services/contentfulRetrievalService";
import { buildContentfulContentEntryResponse } from "../../tests/utils/generateContentful";
import { Entry, EntrySkeletonType } from "contentful";
import { marked } from "marked";

describe("WealthyhubService", () => {
  beforeAll(async () => await connectDb("WealthyhubService"));
  afterAll(async () => await closeDb());

  describe("getLatestAnalystInsight", () => {
    describe("when no content entry exists", () => {
      let analystInsight: AnalystInsightType;

      beforeAll(async () => {
        analystInsight = await WealthyhubService.getLatestAnalystInsight(ContentEntryContentTypeEnum.ANALYSIS);
      });
      afterAll(async () => await clearDb());

      it("should return undefined", () => {
        expect(analystInsight).toBeUndefined();
      });
    });

    describe("when an content entry exists but its not submitted to conteful yet", () => {
      let analystInsight: AnalystInsightType;

      beforeAll(async () => {
        await buildContentEntry({
          contentType: ContentEntryContentTypeEnum.ANALYSIS,
          category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
          activeProviders: [ProviderEnum.FINIMIZE, ProviderEnum.CONTENTFUL],
          providers: {
            finimize: {
              id: faker.string.uuid(),
              contentType: FinimizeContentTypeEnum.INSIGHT,
              publishedAt: new Date()
            },
            contentful: undefined
          }
        });

        analystInsight = await WealthyhubService.getLatestAnalystInsight(ContentEntryContentTypeEnum.ANALYSIS);
      });
      afterAll(async () => await clearDb());

      it("should return undefined", () => {
        expect(analystInsight).toBeUndefined();
      });
    });

    describe("when an content entry exists and its not cached", () => {
      let analystInsight: AnalystInsightType;
      let contentfulResponse: Entry<EntrySkeletonType>;
      let contentEntry: ContentEntryDocument;

      beforeAll(async () => {
        contentEntry = await buildContentEntry();

        contentfulResponse = buildContentfulContentEntryResponse({
          id: contentEntry.providers.contentful?.id,
          analystInsightType: contentEntry.contentType
        });

        jest
          .spyOn(ContentfulRetrievalService.LearnHubInstance, "getEntry")
          .mockImplementation(async (id: string): Promise<any> => {
            if (id === contentEntry.providers.contentful?.id) {
              return Promise.resolve(contentfulResponse);
            }
          });

        analystInsight = await WealthyhubService.getLatestAnalystInsight(ContentEntryContentTypeEnum.ANALYSIS);
      });
      afterAll(async () => await clearDb());

      it("should return the latest analyst insight", () => {
        const contentfulEntry = contentfulResponse.fields;

        const DISCLAIMER_ANALYST_INSIGHTS =
          "\n - \n\n Capital at risk. Our analyst insights are for educational purposes only. Wealthyhood does not render investment, financial, legal, tax, or accounting advice.";

        expect(analystInsight).toMatchObject({
          key: contentfulEntry.slug,
          createdAt: new Date(contentfulEntry?.publishedAt),
          contentType: "analystInsights",
          id: contentEntry.id,
          title: contentfulEntry.title,
          contentHTML: expect.stringContaining(
            `${marked.parse(contentfulEntry.content + DISCLAIMER_ANALYST_INSIGHTS)}`
          ),
          previewImageURL: contentfulEntry.headerImage,
          fullImageURL: contentfulEntry.headerImage,
          bannerImageURL: contentfulEntry.bannerImage,
          readingTime: contentfulEntry.readingTime,
          analystInsightType: contentEntry.contentType
        });
      });

      it("should call contentful with the correct id", () => {
        expect(ContentfulRetrievalService.LearnHubInstance.getEntry).toHaveBeenCalledWith(
          contentEntry.providers.contentful?.id
        );
      });
    });

    describe("when an content entry exists and its cached", () => {
      let analystInsight: AnalystInsightType;
      let contentfulResponse: Entry<EntrySkeletonType>;
      let contentEntry: ContentEntryDocument;

      beforeAll(async () => {
        contentEntry = await buildContentEntry();

        contentfulResponse = buildContentfulContentEntryResponse({
          id: contentEntry.providers.contentful?.id,
          analystInsightType: contentEntry.contentType
        });

        jest.spyOn(RedisClientService.Instance, "get");
        jest
          .spyOn(ContentfulRetrievalService.LearnHubInstance, "getEntry")
          .mockImplementation(async (id: string): Promise<any> => {
            if (id === contentEntry.providers.contentful?.id) {
              return Promise.resolve(contentfulResponse);
            }
          });

        expect(await RedisClientService.Instance.get(`analystInsight:${contentEntry.id}`)).toBeUndefined();
        await WealthyhubService.getLatestAnalystInsight(ContentEntryContentTypeEnum.ANALYSIS);
        expect(await RedisClientService.Instance.get(`analystInsight:${contentEntry.id}`)).toBeDefined();

        analystInsight = await WealthyhubService.getLatestAnalystInsight(ContentEntryContentTypeEnum.ANALYSIS);
      });
      afterAll(async () => await clearDb());

      it("should return the cached analyst insight", async () => {
        expect(RedisClientService.Instance.get).toHaveBeenCalledWith(`analystInsight:${contentEntry.id}`);

        const contentfulEntry = contentfulResponse.fields;
        const DISCLAIMER_ANALYST_INSIGHTS =
          "\n - \n\n Capital at risk. Our analyst insights are for educational purposes only. Wealthyhood does not render investment, financial, legal, tax, or accounting advice.";

        const cachedAnalystInsight = await RedisClientService.Instance.get(`analystInsight:${contentEntry.id}`);

        expect(cachedAnalystInsight).toMatchObject({
          key: contentfulEntry.slug,
          contentType: "analystInsights",
          title: contentfulEntry.title,
          contentHTML: expect.stringContaining(
            `${marked.parse(contentfulEntry.content + DISCLAIMER_ANALYST_INSIGHTS)}`
          ),
          previewImageURL: contentfulEntry.headerImage,
          fullImageURL: contentfulEntry.headerImage,
          bannerImageURL: contentfulEntry.bannerImage,
          readingTime: contentfulEntry.readingTime,
          analystInsightType: contentEntry.contentType
        });
        expect(analystInsight).toMatchObject({
          key: contentfulEntry.slug,
          contentType: "analystInsights",
          title: contentfulEntry.title,
          contentHTML: expect.stringContaining(
            `${marked.parse(contentfulEntry.content + DISCLAIMER_ANALYST_INSIGHTS)}`
          ),
          previewImageURL: contentfulEntry.headerImage,
          fullImageURL: contentfulEntry.headerImage,
          bannerImageURL: contentfulEntry.bannerImage,
          readingTime: contentfulEntry.readingTime,
          analystInsightType: contentEntry.contentType
        });
      });
    });
  });
});
