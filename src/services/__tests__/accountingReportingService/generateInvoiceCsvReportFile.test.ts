import "jest";
import fs from "fs";
import path from "path";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { clearSqliteDb, createSqliteDb, connectDb, clearDb, closeDb } from "../../../tests/utils/db";
import { AccountingReportingService } from "../../accountingReportingService";
import {
  buildUser,
  buildOrder,
  buildAssetTransaction,
  buildInvoiceReferenceNumber
} from "../../../tests/utils/generateModels";
import DateUtil from "../../../utils/dateUtil";

const { ASSET_CONFIG } = investmentUniverseConfig;

describe("AccountingReportingService.generateInvoiceCsvReportFile", () => {
  const REPORT_DATE_STR = "2025-06-17";
  const REPORT_DATE = new Date(REPORT_DATE_STR);
  const OUTPUT_DIR = path.join(__dirname, "test_output_accounting_reporting");

  beforeAll(async () => {
    Date.now = jest.fn(() => REPORT_DATE.getTime());
    await connectDb("generateInvoiceCsvReportFile");
    await createSqliteDb();
    if (fs.existsSync(OUTPUT_DIR)) {
      fs.rmSync(OUTPUT_DIR, { recursive: true, force: true });
    }
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  });

  afterAll(async () => {
    await closeDb();
    if (fs.existsSync(OUTPUT_DIR)) {
      fs.rmSync(OUTPUT_DIR, { recursive: true, force: true });
    }
  });

  afterEach(async () => {
    await clearSqliteDb();
    await clearDb();
  });

  it("should generate a CSV properly with all fields including FX fees", async () => {
    const FX_RATE = 1.2;

    // Get Apple asset config for testing
    const assetConfig = ASSET_CONFIG["equities_apple"];

    // Create test user
    const testUser = await buildUser({
      firstName: "John",
      lastName: "Doe"
    });

    // Create test asset transaction
    const testAssetTransaction = await buildAssetTransaction({
      owner: testUser._id,
      portfolioTransactionCategory: "buy",
      consideration: { amount: 10000, currency: "EUR" },
      createdAt: REPORT_DATE
    });

    const testOrder = await buildOrder({
      transaction: testAssetTransaction._id,
      isin: assetConfig.isin,
      side: "Buy",
      quantity: 10,
      settlementCurrency: "EUR",
      consideration: { amount: 10000, currency: "EUR" },
      unitPrice: { amount: 1000, currency: "EUR" },
      exchangeRate: FX_RATE,
      fees: {
        fx: { amount: 0.25, currency: "EUR" }, // Wealthyhood FX fee
        realtimeExecution: { amount: 1, currency: "EUR" }
      },
      providers: {
        wealthkernel: {
          id: "WK123456",
          status: "Matched",
          submittedAt: REPORT_DATE,
          accountingBrokerFxFee: 1.5 // 1.5 EUR - Broker FX fee in EUR/GBP/USD
        }
      },
      filledAt: REPORT_DATE,
      updatedAt: REPORT_DATE
    });

    // Create test invoice reference number
    const testInvoiceRef = await buildInvoiceReferenceNumber({
      linkedDocumentId: testOrder._id,
      sourceDocumentType: "Order",
      createdAt: REPORT_DATE
    });

    // Generate the CSV report
    const generatedReportPath = await AccountingReportingService.generateInvoiceCsvReportFile({
      fromDate: DateUtil.getStartOfDay(REPORT_DATE),
      toDate: DateUtil.getEndOfDay(REPORT_DATE),
      outputDir: OUTPUT_DIR,
      uploadToCloud: false
    });

    expect(generatedReportPath).not.toBe("");
    expect(fs.existsSync(generatedReportPath)).toBe(true);

    const generatedCsv = fs.readFileSync(generatedReportPath, "utf-8");
    const lines = generatedCsv.split("\n").filter((line) => line.trim() !== "");

    // Check headers
    expect(lines[0]).toBe(
      "Reference ID,Order ID,Updated at (date),Executed at (date),User ID,User Name,Email,ISIN,Symbol,Exchange,Order Type,Settlement Currency,Side,Quantity,Amount,Unit Price,FX Rate,FX Fee Amount,Commission Fee Amount"
    );

    // Verify we have exactly one data row
    expect(lines.length).toBe(2); // Header + 1 data row

    // Parse the CSV data row
    const dataColumns = lines[1].split(",");

    // Verify each field
    expect(dataColumns[0]).toBe(testInvoiceRef.invoiceId.toString()); // Reference ID
    expect(dataColumns[1]).toBe(testOrder.userFriendlyId); // Order ID
    expect(dataColumns[2]).toBe(testInvoiceRef.createdAt?.toISOString() || ""); // Updated at
    expect(dataColumns[3]).toBe(testOrder.filledAt?.toISOString() || ""); // Executed at
    expect(dataColumns[4]).toBe(testUser.id); // User ID
    expect(dataColumns[5]).toBe("John Doe"); // User Name
    expect(dataColumns[6]).toBe(testUser.email); // Email
    expect(dataColumns[7]).toBe(assetConfig.isin); // ISIN
    expect(dataColumns[8]).toBe(assetConfig.formalTicker); // Symbol (from ASSET_CONFIG)
    expect(dataColumns[9]).toBe(assetConfig.formalExchange); // Exchange (from ASSET_CONFIG)
    expect(dataColumns[10]).toBe("Market"); // Order Type
    expect(dataColumns[11]).toBe("EUR"); // Settlement Currency
    expect(dataColumns[12]).toBe("Buy"); // Side
    expect(dataColumns[13]).toBe("10"); // Quantity
    expect(dataColumns[14]).toBe("100.00"); // Amount (consideration/100)
    expect(dataColumns[15]).toBe("1000"); // Unit Price
    expect(dataColumns[16]).toBe(FX_RATE.toString()); // FX Rate
    expect(dataColumns[17]).toBe("1.75"); // FX Fee Amount (broker FX fee 1.5 + Wealthyhood FX fee 0.25)
    expect(dataColumns[18]).toBe("1"); // Commission Fee Amount (realtimeExecution fee)
  });

  it("should create CSV with right header data for empty data", async () => {
    // Use a date with no invoice records
    const futureDate = new Date("2030-01-01");

    const generatedReportPath = await AccountingReportingService.generateInvoiceCsvReportFile({
      fromDate: DateUtil.getStartOfDay(futureDate),
      toDate: DateUtil.getEndOfDay(futureDate),
      outputDir: OUTPUT_DIR,
      uploadToCloud: false
    });

    expect(generatedReportPath).not.toBe("");
    expect(fs.existsSync(generatedReportPath)).toBe(true);

    const generatedCsv = fs.readFileSync(generatedReportPath, "utf-8");
    const lines = generatedCsv.split("\n").filter((line) => line.trim() !== "");

    // Should have exactly 1 line (header only)
    expect(lines.length).toBe(1);

    // Verify the header is correct
    expect(lines[0]).toBe(
      "Reference ID,Order ID,Updated at (date),Executed at (date),User ID,User Name,Email,ISIN,Symbol,Exchange,Order Type,Settlement Currency,Side,Quantity,Amount,Unit Price,FX Rate,FX Fee Amount,Commission Fee Amount"
    );
  });
});
