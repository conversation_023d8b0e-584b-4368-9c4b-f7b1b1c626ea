import "jest";
import fs from "fs";
import path from "path";
import { XMLParser } from "fast-xml-parser";
import { clearSqliteDb, createSqliteDb, connectDb, clearDb, closeDb } from "../../../tests/utils/db";
import { AccountingReportingService } from "../../accountingReportingService";
import AccountingLedgerStorageService, {
  AccountingLedgerEntry
} from "../../../external-services/accountingLedgerStorageService";
import DateUtil from "../../../utils/dateUtil";

// Helper to convert any value to a sorted string for comparison
// Handles objects, arrays, and primitives.
function canonicalStringify(obj: any): string {
  if (typeof obj !== "object" || obj === null) {
    return String(obj);
  }

  if (Array.isArray(obj)) {
    return `[${obj.map(canonicalStringify).sort().join(",")}]`;
  }

  const keys = Object.keys(obj).sort();
  const pairs = keys.map((key) => `${key}:${canonicalStringify(obj[key])}`);
  return `{${pairs.join(",")}}`;
}

function areXmlEquivalent(generatedXmlString: string, expectedXmlString: string): boolean {
  const parserOptions = {
    ignoreAttributes: false,
    attributeNamePrefix: "@_",
    parseTagValue: true, // Parses numbers and booleans
    isArray: (name: string): boolean => {
      // Ensure ARTICLE, DETAIL, and ACCOUNT are always arrays
      return ["ARTICLE", "DETAIL", "ACCOUNT"].includes(name);
    },
    tagValueProcessor: (
      tagName: string,
      tagValue: any,
      jPath: string,
      hasAttributes: boolean,
      isLeafNode: boolean
    ) => {
      // Ensure empty tags that are not self-closing are represented consistently, e.g. "" instead of undefined
      if (tagValue === undefined && !hasAttributes && isLeafNode) {
        return "";
      }
      return tagValue;
    },
    attributesGroupName: false as const,
    alwaysCreateTextNode: false, // if false, text node will be created only if text is not empty
    trimValues: true // trim whitespace from tag values and attributes
  };

  const parser = new XMLParser(parserOptions);
  let genObj: any;
  let expObj: any;

  try {
    genObj = parser.parse(generatedXmlString);
    expObj = parser.parse(expectedXmlString);
  } catch (e) {
    console.error("XML parsing error:", e);
    return false;
  }

  // Compare root DATA attributes
  const genDataAttrs = genObj.DATA?.["@_"];
  const expDataAttrs = expObj.DATA?.["@_"];

  if (canonicalStringify(genDataAttrs) !== canonicalStringify(expDataAttrs)) {
    console.error("Root DATA attributes differ:");
    console.error("Generated DATA attrs:", genDataAttrs);
    console.error("Expected DATA attrs:", expDataAttrs);
    return false;
  }

  // Compare ARTICLES
  // Ensure ARTICLES exists and ARTICLE is an array (or make it one if not)
  const genArticles = [].concat(genObj.DATA?.ARTICLES?.ARTICLE || []);
  const expArticles = [].concat(expObj.DATA?.ARTICLES?.ARTICLE || []);

  if (genArticles.length !== expArticles.length) {
    console.error(`Article counts differ: Gen ${genArticles.length}, Exp ${expArticles.length}`);
    return false;
  }

  const genArticleStrings = genArticles.map(canonicalStringify);
  const expArticleStrings = expArticles.map(canonicalStringify);

  for (let i = 0; i < genArticleStrings.length; i++) {
    if (genArticleStrings[i] !== expArticleStrings[i]) {
      console.error(`Article content mismatch at index ${i} after sorting:`);
      console.error(`Generated: ${genArticleStrings[i]}`);
      console.error(`Expected:  ${expArticleStrings[i]}`);
      return false;
    }
  }

  // Compare ACCOUNTS
  const genAccounts = [].concat(genObj.DATA?.ACCOUNTS?.ACCOUNT || []);
  const expAccounts = [].concat(expObj.DATA?.ACCOUNTS?.ACCOUNT || []);

  if (genAccounts.length !== expAccounts.length) {
    console.error(`Account counts differ: Gen ${genAccounts.length}, Exp ${expAccounts.length}`);
    return false;
  }

  const genAccountStrings = genAccounts.map(canonicalStringify).sort();
  const expAccountStrings = expAccounts.map(canonicalStringify).sort();

  for (let i = 0; i < genAccountStrings.length; i++) {
    if (genAccountStrings[i] !== expAccountStrings[i]) {
      console.error(`Account content mismatch at index ${i} after sorting:`);
      console.error(`Generated: ${genAccountStrings[i]}`);
      console.error(`Expected:  ${expAccountStrings[i]}`);
      return false;
    }
  }

  return true;
}

describe("AccountingReportingService.generateXMLAccountingReportFile", () => {
  const REPORT_DATE_STR = "2025-06-17";
  const REPORT_DATE = new Date(REPORT_DATE_STR);
  const OUTPUT_DIR = path.join(__dirname, "test_output_accounting_reporting");

  beforeAll(async () => {
    Date.now = jest.fn(() => REPORT_DATE.getTime());
    await connectDb("generateXMLAccountingReportFile");
    await createSqliteDb();
    if (fs.existsSync(OUTPUT_DIR)) {
      fs.rmSync(OUTPUT_DIR, { recursive: true, force: true });
    }
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  });

  afterAll(async () => {
    await closeDb();
    if (fs.existsSync(OUTPUT_DIR)) {
      fs.rmSync(OUTPUT_DIR, { recursive: true, force: true });
    }
  });

  afterEach(async () => {
    await clearSqliteDb();
    await clearDb();
  });

  it("should generate an XML report matching the example data", async () => {
    const jsonDataPath = path.resolve(__dirname, "../data/end-to-end-ledger-entries.json");
    const jsonContent = fs.readFileSync(jsonDataPath, "utf-8");
    const reportData: Array<Omit<AccountingLedgerEntry, "id">> = JSON.parse(jsonContent);

    // Amounts in JSON are already in decimal currency units (e.g., 11000.00 for 11000 EUR)
    // The service expects amounts in decimal currency units for storage.
    // The XML generation part of the service then formats them to "11000,00"
    const ledgerEntriesToInsert: AccountingLedgerEntry[] = reportData.map((entry) => ({
      ...entry,
      article_date: REPORT_DATE_STR, // Force all records to 17-06-2025
      reference_number: entry.reference_number === "" ? undefined : entry.reference_number
    }));

    await AccountingLedgerStorageService.addValidatedLedgerEntries(ledgerEntriesToInsert);

    const generatedReportPath = await AccountingReportingService.generateXMLAccountingReportFile({
      fromDate: DateUtil.getStartOfDay(REPORT_DATE),
      toDate: DateUtil.getEndOfDay(REPORT_DATE),
      outputDir: OUTPUT_DIR,
      uploadToCloud: false
    });

    expect(generatedReportPath).not.toBe("");
    expect(fs.existsSync(generatedReportPath)).toBe(true);

    const generatedXml = fs.readFileSync(generatedReportPath, "utf-8");
    const expectedXmlPath = path.resolve(__dirname, "../data/expected-accounting-output.xml");
    const expectedXml = fs.readFileSync(expectedXmlPath, "utf-8");

    expect(areXmlEquivalent(generatedXml, expectedXml)).toBe(true);
  });

  it("should return an empty string if no entries are found for the date", async () => {
    const dateWithNoEntries = new Date("2024-01-01");
    const reportPath = await AccountingReportingService.generateXMLAccountingReportFile({
      fromDate: DateUtil.getStartOfDay(dateWithNoEntries),
      toDate: DateUtil.getEndOfDay(dateWithNoEntries),
      outputDir: OUTPUT_DIR,
      uploadToCloud: false
    });
    expect(reportPath).toBe("");
  });
});
