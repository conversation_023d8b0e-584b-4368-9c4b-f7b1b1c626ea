import { QueryOptions } from "mongoose";
import logger from "../external-services/loggerService";
import { MandatesFilter } from "filters";
import DbUtil from "../utils/dbUtil";
import { Mandate, MandateDocument, MandateDTOInterface, MandateInterface } from "../models/Mandate";
import { BankAccountDocument } from "../models/BankAccount";
import { UserDocument } from "../models/User";
import { captureException } from "@sentry/node";
import AutomationService from "./automationService";
import ProviderService, { ProviderScopeEnum } from "./providerService";
import UserService from "./userService";
import { AddressDocument } from "../models/Address";
import { EventType } from "../external-services/goCardlessPaymentsService";
import { entitiesConfig } from "@wealthyhood/shared-configs";

export default class MandateService {
  /**
   * PUBLIC METHODS
   */
  public static async createMandate(
    requestedMandateData: Omit<MandateDTOInterface, "activeProviders">
  ): Promise<MandateInterface> {
    const existingMandate: MandateDocument = await Mandate.findOne({
      category: requestedMandateData.category,
      bankAccount: requestedMandateData.bankAccount.toString()
    });
    if (existingMandate && existingMandate.status !== "Inactive") {
      logger.info(
        `Not creating mandate for user ${requestedMandateData.owner}, bank account ${requestedMandateData.bankAccount} as one exists already`,
        {
          module: "MandateService",
          method: "createMandate",
          data: {
            user: requestedMandateData.owner,
            bankAccount: requestedMandateData.bankAccount,
            mandate: existingMandate
          }
        }
      );

      return existingMandate;
    }

    logger.info(
      `Creating mandate for user ${requestedMandateData.owner}, bank account ${requestedMandateData.bankAccount}`,
      {
        module: "MandateService",
        method: "createMandate",
        data: {
          user: requestedMandateData.owner,
          bankAccount: requestedMandateData.bankAccount
        }
      }
    );

    const user = await UserService.getUser(requestedMandateData.owner.toString());

    const mandateData = {
      ...requestedMandateData,
      activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.DIRECT_DEBIT_MANDATES])
    };

    return new Mandate(mandateData).save();
  }

  public static async cancelMandate(mandate: MandateDocument): Promise<void> {
    if (mandate.status === "Inactive") {
      return;
    }

    if (!mandate.populated("owner")) {
      await mandate.populate("owner");
    }

    if (mandate.category === "Top-Up") {
      if (mandate.providers.wealthkernel.id) {
        await ProviderService.getRepeatingDepositPaymentService(
          (mandate.owner as UserDocument).companyEntity
        ).cancelDirectDebitMandate(mandate.providers?.wealthkernel?.id);
      }
    }
  }

  public static async getMandates(
    filter: MandatesFilter = {},
    populate = { bankAccount: true },
    sort = "-createdAt"
  ): Promise<MandateDocument[]> {
    const dbFilter = this._createMandatesDbFilter(filter);

    const options: QueryOptions = {};
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }

    const mandates: MandateDocument[] = await Mandate.find(dbFilter, null, options).populate(
      DbUtil.getPopulationString(populate)
    );

    // extra filtering that cannot be done in mongoose
    if (filter.includeInactive === false) {
      return mandates.filter(({ status }) => status !== "Inactive");
    } else return mandates;
  }

  public static async getMandate(id: string): Promise<MandateDocument> {
    return Mandate.findById(id);
  }

  /**
   * Deletes all mandates with a bank account ID that does not match a bank account document.
   */
  public static async deleteOrphanedMandates(): Promise<void> {
    const orphanedMandates = await Mandate.aggregate([
      {
        $lookup: {
          from: "bankaccounts",
          localField: "bankAccount",
          foreignField: "_id",
          as: "bankAccount"
        }
      },
      {
        $unwind: {
          path: "$bankAccount",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $match: {
          bankAccount: {
            $eq: null
          }
        }
      }
    ]);

    await Promise.all(orphanedMandates.map((order) => Mandate.findByIdAndDelete(order._id)));
  }

  public static async syncWkEntry(mandate: MandateDocument): Promise<void> {
    logger.info(`Attempting to sync WK entry for mandate ${mandate.id}`, {
      module: "MandateService",
      method: "syncWkEntry",
      data: { mandate: mandate.id }
    });

    const mandateId = mandate.providers?.wealthkernel?.id;
    if (!mandateId) {
      logger.error("Attempted to sync mandate to Wealthkernel but has no WK id", {
        module: "MandateService",
        method: "syncWkEntry",
        data: { mandate: mandate.id }
      });
      return;
    }

    try {
      const user = await UserService.getUser(mandate.owner.toString());

      const { status } = await ProviderService.getRepeatingDepositPaymentService(
        user.companyEntity
      ).getDirectDebitMandate(mandateId);

      if (mandate.status === "Active" && ["Cancelled", "Failed"].includes(status)) {
        logger.warn(`Mandate ${mandate.id} moved from active to ${status}!`, {
          module: "MandateService",
          method: "syncWkEntry",
          data: { mandate: mandate.id }
        });

        // Since the mandate is now no longer active, we should deactivate the linked automations so that it
        // does not try to create any new recurring top-ups.
        const automations = await AutomationService.getAutomations({
          activeOnly: true,
          mandate: mandate.id,
          categories: ["TopUpAutomation", "SavingsTopUpAutomation"]
        });

        if (!automations || automations?.data?.length === 0) {
          logger.info(`User ${mandate.owner} has no active top up automations for mandate with id ${mandate.id}`, {
            module: "MandateService",
            method: "syncWkEntry"
          });
        } else {
          await Promise.all(automations.data?.map((automation) => AutomationService.cancelAutomation(automation)));
        }
      }

      await Mandate.findByIdAndUpdate(mandate.id, {
        "providers.wealthkernel.status": status
      });
    } catch (err) {
      captureException(err);
      logger.error(`Syncing a WK entry failed for mandate ${mandate.id}`, {
        module: "MandateService",
        method: "syncWkEntry",
        data: { mandate: mandate.id, error: err }
      });
    }
  }

  public static async createWkEntry(mandate: MandateDocument): Promise<void> {
    try {
      logger.info(`Attempting to create Wealthkernel entry for mandate ${mandate.id}`, {
        module: "MandateService",
        method: "createWkEntry",
        data: { mandate: mandate.id }
      });

      const owner = mandate.owner as UserDocument;
      const bankAccount = mandate.bankAccount as BankAccountDocument;

      const { providerData } = await ProviderService.getRepeatingDepositPaymentService(
        entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
      ).createDirectDebitMandate({
        user: {
          providers: owner.providers
        },
        bankAccount: {
          providers: bankAccount.providers
        },
        metadata: {
          wealthyhoodId: mandate.id
        }
      });

      await Mandate.findByIdAndUpdate(mandate.id, {
        providers: providerData
      });

      logger.info(`Added Wealthkernel entry for mandate ${mandate.id}`, {
        module: "MandateService",
        method: "createWkEntry",
        data: {
          mandate: mandate.id
        }
      });
    } catch (err) {
      captureException(err);
      logger.error(`Creating a Wealthkernel entry failed for mandate ${mandate.id}`, {
        module: "MandateService",
        method: "createWkEntry",
        data: { mandate: mandate.id, error: JSON.stringify(err) }
      });
    }
  }

  public static async createGoCardlessEntry(mandate: MandateDocument): Promise<void> {
    try {
      logger.info(`Attempting to create GoCardless entry for mandate ${mandate.id}`, {
        module: "MandateService",
        method: "createGoCardlessEntry",
        data: { mandate: mandate.id }
      });

      const owner = mandate.owner as UserDocument;
      const bankAccount = mandate.bankAccount as BankAccountDocument;
      const address = owner.addresses[0] as AddressDocument;

      const { providerData } = await ProviderService.getRepeatingDepositPaymentService(
        entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      ).createDirectDebitMandate({
        user: {
          providers: owner.providers
        },
        address: {
          line1: address.line1,
          city: address.city,
          postalCode: address.postalCode,
          countryCode: address.countryCode
        },
        bankAccount: {
          providers: bankAccount.providers
        },
        metadata: {
          wealthyhoodId: mandate.id
        }
      });

      await Mandate.findByIdAndUpdate(mandate.id, {
        providers: providerData
      });

      logger.info(`Added GoCardless entry for mandate ${mandate.id}`, {
        module: "MandateService",
        method: "createGoCardlessEntry",
        data: {
          mandate: mandate.id
        }
      });
    } catch (err) {
      captureException(err);
      logger.error(`Creating a GoCardless entry failed for mandate ${mandate.id}`, {
        module: "MandateService",
        method: "createGoCardlessEntry",
        data: { mandate: mandate.id, error: JSON.stringify(err) }
      });
    }
  }

  public static async processGoCardlessMandateEvent(event: EventType): Promise<void> {
    const mandate = await MandateService._getMandateByGoCardlessID(event.links["mandate"]);

    if (!mandate) {
      throw new Error(
        `Could not update mandate ${event.links["mandate"]} because we don't have a reference to it in our database`
      );
    }

    if (["active", "cancelled", "failed"].includes(event.action)) {
      await Mandate.findByIdAndUpdate(mandate.id, {
        "providers.gocardless.status": event.action
      });
    } else {
      logger.info(`Not processing mandate events of action ${event.action}`, {
        module: "MandateService",
        method: "processGoCardlessMandateEvent",
        data: {
          mandate: event.links.mandate,
          action: event.action
        }
      });
    }
  }

  /**
   * PRIVATE METHODS
   */
  private static async _getMandateByGoCardlessID(id: string): Promise<MandateDocument> {
    return Mandate.findOne({ "providers.gocardless.id": id });
  }

  private static _createMandatesDbFilter(filter: MandatesFilter) {
    const dbFilter = {
      owner: filter.owner,
      category: filter.category
    };

    return dbFilter
      ? Object.fromEntries(
          Object.entries(dbFilter).filter(
            ([key, value]) => key != "status" && value !== undefined && value !== null
          )
        )
      : {};
  }
}
