import { createClient, Client, Row } from "@libsql/client";
import "../loaders/environment";
import { envIsProd } from "../utils/environmentUtil";

export type TursoInsertResult = {
  success: boolean;
  lastInsertRowid?: number;
  error?: string;
};

export default class TursoService {
  private _client: Client;
  private static _instance: TursoService;

  constructor() {
    if (envIsProd()) {
      if (!process.env.TURSO_DATABASE_URL) {
        throw new Error("TURSO_DATABASE_URL environment variable is required");
      }

      if (!process.env.TURSO_AUTH_TOKEN) {
        throw new Error("TURSO_AUTH_TOKEN environment variable is required");
      }

      this._client = createClient({
        url: process.env.TURSO_DATABASE_URL,
        authToken: process.env.TURSO_AUTH_TOKEN
      });
    } else {
      this._client = createClient({
        url: "file::memory:?cache=shared", // 100 % in-memory
        authToken: undefined // not required for file://
      });
    }
  }

  public static get Instance(): TursoService {
    return TursoService._instance || (TursoService._instance = new TursoService());
  }

  /**
   * Generic method to add a row to any table
   */
  public async addRow(tableName: string, data: Record<string, any>): Promise<TursoInsertResult> {
    try {
      const columns = Object.keys(data);
      const placeholders = columns.map(() => "?").join(", ");
      const values = Object.values(data);

      const result = await this._client.execute({
        sql: `INSERT INTO ${tableName} (${columns.join(", ")}) VALUES (${placeholders})`,
        args: values
      });

      return {
        success: true,
        lastInsertRowid: Number(result.lastInsertRowid)
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred"
      };
    }
  }

  /**
   * Generic method to add multiple rows in a batch transaction
   */
  public async addRows(tableName: string, dataArray: Record<string, any>[]): Promise<TursoInsertResult> {
    try {
      const statements = dataArray.map((data) => {
        const columns = Object.keys(data);
        const placeholders = columns.map(() => "?").join(", ");
        const values = Object.values(data);

        return {
          sql: `INSERT INTO ${tableName} (${columns.join(", ")}) VALUES (${placeholders})`,
          args: values
        };
      });

      await this._client.batch(statements, "write");

      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred"
      };
    }
  }

  /**
   * Execute a custom SQL statement
   */
  public async execute(sql: string, args: any[] = []): Promise<any> {
    try {
      return await this._client.execute({
        sql,
        args
      });
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Unknown error occurred");
    }
  }

  /**
   * Query rows from a table
   */
  public async queryRows(sql: string, args: any[] = []): Promise<Row[]> {
    try {
      const result = await this._client.execute({
        sql,
        args
      });
      return result.rows;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Unknown error occurred");
    }
  }

  /**
   * Execute multiple statements in a batch transaction
   */
  public async batch(
    statements: Array<{ sql: string; args?: any[] }>,
    mode: "write" | "read" = "write"
  ): Promise<any[]> {
    try {
      return await this._client.batch(statements, mode);
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Unknown error occurred");
    }
  }
}
