import iconv from "iconv-lite";

/**
 * Utility class for handling encoding operations
 */
export default class Encoder {
  /**
   * @description Encodes a string to ISO-8859-7 buffer
   */
  public static encodeToISO8859_7(input: string): Buffer {
    return iconv.encode(input, "iso-8859-7");
  }

  /**
   * @description Decodes an ISO-8859-7 buffer to string
   */
  public static decodeFromISO8859_7(input: Buffer): string {
    return iconv.decode(input, "iso-8859-7");
  }
}
