# Pothen ESXES File Formatter and Validator

This tool provides functionality to parse input files from Pothen ESXES, generate response files in the required format, and validate the generated files against the official specifications.

## Directory Structure

The tool uses the following directory structure:

- `/input`: Place input files from Pothen ESXES here
- `/output`: Generated response files are saved here

These directories are automatically created when the tool runs.

## Available Commands

The tool provides several commands to handle different tasks:

### 1. Parse Input File

This command parses an input file from Pothen ESXES and displays the extracted data, without generating a response file.

```bash
npx ts-node cli.ts parse <input-file-name>
```

Example:
```bash
npx ts-node cli.ts parse 996462465-wealtheu-pothenafm-2024-07-v001
```

This will:
- Look for the file in the `input` directory
- Parse the provided file
- Display the extracted header and entries
- Perform basic validation of entry counts

> **Note**: The tool first looks for files in the `/input` directory. If not found there, it will try the path as provided.

### 2. Generate Demo Response Files

This command generates demo response files with test data, without requiring an input file.

```bash
npx ts-node cli.ts generate-demo-response
```

This will:
- Create a sample demo response file with predefined test data
- Save the file in ISO-8859-7 encoding in the `output` directory
- Create a readable UTF-8 version with `.readable` extension

### 3. Validate an Existing Response File

This command validates an existing response file against the official specifications.

```bash
npx ts-node cli.ts validate <file-path>
```

Example:
```bash
npx ts-node cli.ts validate output/996462465-pothendata-2024-07-v001
```

This will:
- Check the file structure and formatting
- Validate record sizes, headers, and entry content
- Display detailed validation results, including any errors found

### 4. Show Help

This command displays usage information for all available commands.

```bash
npx ts-node cli.ts help
```

## Error Handling

The tool will report error messages when:
- Required file paths are missing
- Files cannot be read or written
- Unsupported commands are provided
- Files do not match the expected format

## Output Files

The tool generates two types of files in the `output` directory:

1. **Encoded File**: ISO-8859-7 encoded file in the format `<afm>-pothendata-<year>-<month>-v<version>`
2. **Readable File**: UTF-8 version of the same file with `.readable` extension

The encoded file is ready for encryption (adding `.gpg` extension) and submission, while the readable file is for verification purposes.

## Validation Checks

The validator performs several checks:
- Header size verification
- Record termination validation
- Individual record size validation 
- Header content validation
- Entry summary validation
- Entry count verification

## File Format

The generated files follow the required structure for Pothen ESXES:
- Header Record: Contains AFM, company name, and total entries count
- Entry Summary Records: Contains AFM, reference date, request type, and product count
- Investment Product Records: Contains details about investment products
- Loan Product Records: Contains details about loan products

Each record is properly formatted and terminated with a unit separator. 