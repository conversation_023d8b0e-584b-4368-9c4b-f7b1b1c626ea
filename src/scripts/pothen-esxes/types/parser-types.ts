export interface PothenEsxesHeader {
  catalogType: string; // CHAR(1) - 0: Annual, 1: Initial
  catalogReference: string; // CHAR(6) - YYYYMM
  fileIssueDate: string; // CHAR(8) - YYYYMMDD
  totalEntries: number; // INT(7)
}

export interface PothenEsxesEntry {
  afm: string; // CHAR(9) - Tax ID
  firstName: string; // CHAR(3) - First 3 capitals of first name
  lastName: string; // CHAR(3) - First 3 capitals of last name
  birthDate: string; // DATE(8) - YYYYMMDD
  referenceDate: string; // DATE(8) - YYYYMMDD
  requestType: string; // CHAR(1) - 2: Without loans, 1: With loans
}

export const HEADER_RECORD_SIZE = 22; // bytes
export const ENTRY_RECORD_SIZE = 32; // bytes
