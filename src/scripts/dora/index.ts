import * as path from "path";
import * as fs from "fs";
import { convertAndPrepareForValidation } from "./deliverable-csv-to-json";
import { validateDataHardcoded } from "./dpm-business-validator";
import { validateTechnical } from "./dpm-technical-validator";
import { validateTechnical as validateAdditionalTechnical } from "./technical-validator";
import { cleanZipFile } from "./clean-zip";

// --- Configuration ---
const INPUT_CSV_DIR = "deliverable"; // Directory containing CSV files
const REPORTS_DIR = "reports"; // Directory for all output reports
const BUSINESS_VALIDATION_RULES_FILE = "dpm-business-validation-rules.json"; // Relative to script location or workspace root
const TECHNICAL_VALIDATION_RULES_FILE = "dpm-technical-validation-rules.json"; // DPM Technical validation rules
const ADDITIONAL_TECH_RULES_FILE = "technical-rules.json"; // Additional technical validation rules
const OUTPUT_CSV_JSON_FILE = "dora_csv_converted.json"; // Optional: CSV to JSON output
const OUTPUT_VALIDATION_REPORT = "validation_report.json"; // Optional: Report file
const OUTPUT_DPM_TECHNICAL_REPORT = "technical_dpm_validation_report.json"; // Optional: Technical validation report
const OUTPUT_TECHNICAL_REPORT = "technical_validation_report.json"; // Optional: Additional technical validation report
const DEFAULT_ZIP_FILENAME = "deliverable.zip"; // Default zip filename to use if not specified

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2); // Remove node and script name
  let zipFilename = DEFAULT_ZIP_FILENAME;

  for (let i = 0; i < args.length; i++) {
    if (args[i] === "--deliverable" || args[i] === "-d") {
      if (i + 1 < args.length) {
        zipFilename = args[i + 1];
        i++; // Skip the next argument as we've already used it
      } else {
        console.error("Error: --deliverable flag requires a filename argument");
        process.exit(1);
      }
    }
  }

  return { zipFilename };
}

// Get command line arguments
const { zipFilename } = parseArgs();

// Determine absolute paths based on the script's directory
const SCRIPT_DIR = __dirname; // Or adjust if running from a different context
const inputCsvDirPath = path.resolve(SCRIPT_DIR, INPUT_CSV_DIR);
export const REPORTS_DIR_PATH = path.resolve(SCRIPT_DIR, REPORTS_DIR);
const businessRulesFilePath = path.resolve(SCRIPT_DIR, "rules", BUSINESS_VALIDATION_RULES_FILE);
const technicalRulesFilePath = path.resolve(SCRIPT_DIR, "rules", TECHNICAL_VALIDATION_RULES_FILE);
const outputCsvJsonPath = path.resolve(REPORTS_DIR_PATH, OUTPUT_CSV_JSON_FILE);
const outputReportPath = path.resolve(REPORTS_DIR_PATH, OUTPUT_VALIDATION_REPORT);
const outputTechnicalReportPath = path.resolve(REPORTS_DIR_PATH, OUTPUT_DPM_TECHNICAL_REPORT);
// Absolute path to the zip file
const zipFilePath = path.isAbsolute(zipFilename) ? zipFilename : path.resolve(process.cwd(), zipFilename);

// --- Main Execution Logic ---
async function runDoraProcessing() {
  console.log("Starting DORA Processing and Validation...");
  console.log(`Using zip file: ${zipFilePath}`);

  // Ensure reports directory exists
  if (!fs.existsSync(REPORTS_DIR_PATH)) {
    console.log(`Creating reports directory at: ${REPORTS_DIR_PATH}`);
    fs.mkdirSync(REPORTS_DIR_PATH, { recursive: true });
  }

  // Clean the zip file to remove .DS_Store files
  try {
    console.log("\n=== Step 1: Cleaning zip file ===");
    // Clean the zip file in place (overwrite the original)
    cleanZipFile(zipFilePath);
    console.log(`Zip file cleaned: ${zipFilePath}`);
  } catch (error: any) {
    console.error(`Error cleaning zip file: ${error.message}`);
    process.exit(1);
  }

  // Check if validation rules file exists
  if (!fs.existsSync(businessRulesFilePath)) {
    console.error(`Error: Validation rules file not found at ${businessRulesFilePath}`);
    process.exit(1);
  }
  if (!fs.existsSync(technicalRulesFilePath)) {
    console.error(`Error: Technical rules file not found at ${technicalRulesFilePath}`);
    process.exit(1);
  }

  try {
    let validationInputData: Record<string, Record<string, any>[]> = {};

    // Determine which data source to use based on what's available
    const useCsv = fs.existsSync(inputCsvDirPath);

    if (!useCsv) {
      console.error("Error: Neither XLSB file nor CSV directory found. At least one must exist.");
      process.exit(1);
    }

    // Process CSV files if they exist
    if (useCsv) {
      console.log("\n=== Processing CSV files ===");
      // Convert CSV files to JSON and prepare for validation
      const { validationData } = convertAndPrepareForValidation(inputCsvDirPath, {
        outputFilePath: outputCsvJsonPath
      });

      validationInputData = validationData;
    }

    // Validate the data using the hardcoded validator
    console.log("\nPrepared data for validation:", Object.keys(validationInputData));
    const validationResults = validateDataHardcoded(validationInputData, businessRulesFilePath);

    // Save the validation report
    try {
      const reportJson = JSON.stringify(validationResults, null, 2);
      fs.writeFileSync(outputReportPath, reportJson, "utf-8");
      console.log(`\nValidation report saved to: ${outputReportPath}`);
    } catch (e: any) {
      console.error(`\nError writing validation report file to ${outputReportPath}: ${e.message}`);
    }

    // Step 3: Run technical validations
    console.log("\n=== Running technical validations ===");
    // Define the type for technical results with an interface import
    type TechnicalResult = {
      ruleCode: number;
      status: "passed" | "failed" | "skipped" | "error";
      category: string;
      description: string;
      details?: string;
    };
    let technicalResults: TechnicalResult[] = [];
    let additionalTechnicalResults: TechnicalResult[] = [];
    const additionalTechRulesFilePath = path.resolve(SCRIPT_DIR, "rules", ADDITIONAL_TECH_RULES_FILE);
    const outputAdditionalTechnicalReportPath = path.resolve(REPORTS_DIR_PATH, OUTPUT_TECHNICAL_REPORT);

    // Only if CSV data is available
    if (useCsv) {
      // Extract the CSV data to pass to technical validation
      const { parsedData } = convertAndPrepareForValidation(inputCsvDirPath);

      // Run DPM technical validation
      console.log("\n=== Running DPM technical validation ===");
      technicalResults = validateTechnical(validationInputData, technicalRulesFilePath, parsedData);

      // Save the DPM technical validation report
      try {
        const technicalReportJson = JSON.stringify(technicalResults, null, 2);
        fs.writeFileSync(outputTechnicalReportPath, technicalReportJson, "utf-8");
        console.log(`\nDPM technical validation report saved to: ${outputTechnicalReportPath}`);
      } catch (e: any) {
        console.error(
          `\nError writing DPM technical validation report file to ${outputTechnicalReportPath}: ${e.message}`
        );
      }

      // Run additional technical validation
      if (fs.existsSync(additionalTechRulesFilePath)) {
        console.log("\n=== Running additional technical validation ===");
        additionalTechnicalResults = validateAdditionalTechnical(
          validationInputData,
          additionalTechRulesFilePath,
          parsedData,
          zipFilePath
        );

        // Save the additional technical validation report
        try {
          const additionalTechnicalReportJson = JSON.stringify(additionalTechnicalResults, null, 2);
          fs.writeFileSync(outputAdditionalTechnicalReportPath, additionalTechnicalReportJson, "utf-8");
          console.log(`\nAdditional technical validation report saved to: ${outputAdditionalTechnicalReportPath}`);
        } catch (e: any) {
          console.error(
            `\nError writing additional technical validation report file to ${outputAdditionalTechnicalReportPath}: ${e.message}`
          );
        }
      } else {
        console.log(
          `\nSkipping additional technical validation - rules file not found at ${additionalTechRulesFilePath}`
        );
      }
    } else {
      console.log("Skipping technical validations - no CSV data available");
    }

    // Step 4: Report final status based on all validations
    const hasValidationIssues = validationResults.some((r) => r.status === "failed" || r.status === "error");
    const hasTechnicalIssues = technicalResults.some((r) => r.status === "failed" || r.status === "error");
    const hasAdditionalTechnicalIssues = additionalTechnicalResults.some(
      (r) => r.status === "failed" || r.status === "error"
    );

    if (hasValidationIssues || hasTechnicalIssues || hasAdditionalTechnicalIssues) {
      console.error("\nProcessing completed with validation issues.");
      process.exitCode = 1; // Indicate failure
    } else {
      console.log("\nProcessing completed successfully with no validation failures or errors.");
      process.exitCode = 0; // Indicate success
    }
  } catch (error: any) {
    console.error("\nAn error occurred during the process:", error.message);
    if (error.stack) {
      console.error(error.stack);
    }
    process.exit(1); // Indicate failure
  }
}

// Run the main function
runDoraProcessing();
