import * as fs from "fs";
import * as path from "path";

// Define array of validation rule codes that should be skipped
const SKIPPED_RULES = ["e23675_e"];

// Define the expected structure of the input data
// Example: { "tB_01_01": [ { "c0010": "...", "c0020": "..." }, ... ], "tB_01_02": [...] }
type InputData = Record<string, Record<string, any>[]>;

/**
 * Interface representing a validation rule
 */
interface ValidationRule {
  code: string;
  type: string;
  source: string;
  frameworks: string;
  modules: string;
  implementedInXBRL: string;
  tables: string;
  expression: string;
  preconditionExpression: string;
  startReleaseCode: number | string;
  endReleaseCode: string;
  severity: string;
  status: string;
  description: string;
}

// Define the structure for validation results (reused from validator.ts)
interface ValidationResult {
  ruleCode: string;
  status: "passed" | "failed" | "error" | "skipped";
  severity: string;
  tableName?: string;
  rowIndex?: number; // Index within the table data array
  dataContext?: Record<string, any>; // The specific row data that was evaluated
  message?: string; // Error message or reason for failure/skip
}

// Reused Helper Functions (similar to validator.ts)

/**
 * Reads validation rules from a JSON file.
 * @param filePath - Path to the business validation rules file.
 * @returns An array of ValidationRule objects.
 */
function loadValidationRules(filePath: string): ValidationRule[] {
  try {
    const absolutePath = path.resolve(filePath);
    if (!fs.existsSync(absolutePath)) {
      throw new Error(`Validation rules file not found: ${absolutePath}`);
    }
    const fileContent = fs.readFileSync(absolutePath, "utf-8");
    const rules: ValidationRule[] = JSON.parse(fileContent);

    // Filter for active rules only, exclude rules with implementedInXBRL = 'No',
    // and exclude rules that are in the SKIPPED_RULES array
    return rules.filter((rule) => {
      const isActive = rule.status && rule.status.trim().toLowerCase() === "active";
      const isNotExcluded = rule.implementedInXBRL !== "No";
      const isNotSkipped = !SKIPPED_RULES.includes(rule.code);
      return isActive && isNotExcluded && isNotSkipped;
    });
  } catch (error: any) {
    console.error(`Error loading validation rules from ${filePath}:`, error.message);
    throw error;
  }
}

/**
 * Helper to check for null, undefined, or empty string.
 */
function isNullOrEmpty(value: any): boolean {
  return value === null || value === undefined || value === "";
}

/**
 * Helper to safely convert to number, returning NaN otherwise.
 */
function safeToNumber(value: any): number {
  if (isNullOrEmpty(value)) return NaN;
  return Number(value);
}

/**
 * Helper to safely convert to string, returning empty string otherwise.
 */
function safeToString(value: any): string {
  if (isNullOrEmpty(value)) return "";
  return String(value);
}

/**
 * Applies the hardcoded validation logic for a specific rule to a row.
 * @param rule - The validation rule object.
 * @param row - The specific data row object.
 * @param _allData - The complete input data (potentially needed for cross-table rules).
 * @returns boolean - True if the validation passes, false otherwise.
 * @throws Error if evaluation logic encounters an unexpected issue for that specific rule code.
 */
function applyHardcodedRule(
  rule: ValidationRule,
  row: Record<string, any>,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _allData: InputData
): boolean {
  const { code } = rule;

  // --- Hardcoded Logic per Rule Code ---
  // This switch statement implements the specific logic for each active rule's expression.

  // TODO: Implement simple comparisons between columns (e.g., {c0080} > {c0070})
  // TODO: Implement simple comparisons with literal numbers/strings (e.g., {col} = 'ABC', {col} != 123)
  // TODO: Implement 'in' checks (e.g., {col} in {('A', 'B')}, {col} in {([dom:X])})
  // TODO: Implement handling for more complex nested if/then/endif or and/or logic if needed by new rules.

  try {
    switch (code) {
      // == Existence Checks (not isnull) ==
      case "e23674_e": // B_05.01: not ( isnull ({(c0020, c0050, c0060, c0070, c0080, c0110)}) )
        return !["c0020", "c0050", "c0060", "c0070", "c0080", "c0110"].some((col) => isNullOrEmpty(row[col]));
      case "e23675_e": // B_01.03: not ( isnull ({c*}) )
        return !Object.keys(row).some((key) => isNullOrEmpty(row[key]));
      case "e23676_e": // B_01.02: not ( isnull ({c0020-0090}) ) - Assuming specific columns
        // TODO: Clarify exact meaning of c0020-0090 range. Hardcoding known columns for now.
        return !["c0020", "c0030", "c0040", "c0050", "c0060", "c0070", "c0080", "c0090"].some((col) =>
          isNullOrEmpty(row[col])
        );
      case "e23677_e": // B_01.01: not ( isnull ({c*}) )
        return !Object.keys(row).some((key) => isNullOrEmpty(row[key]));
      case "e23680_e": // B_02.02: not ( isnull ({c0040-0080}) ) - Assuming specific columns
        // TODO: Clarify exact meaning of c0040-0080 range. Hardcoding known columns for now.
        return !["c0040", "c0050", "c0060", "c0070", "c0080"].some((col) => isNullOrEmpty(row[col]));
      case "e23681_e": // B_07.01: not ( isnull ({(c0050, c0070, c0080, c0090, c0100, c0110)}) )
        // Rule is marked as inactive, but implementing anyway
        return !["c0050", "c0070", "c0080", "c0090", "c0100", "c0110"].some((col) => isNullOrEmpty(row[col]));
      case "e23682_e": // B_06.01: not ( isnull ({(c0020, c0030, c0050, c0070, c0080, c0090, c0100)}) )
        return !["c0020", "c0030", "c0050", "c0070", "c0080", "c0090", "c0100"].some((col) =>
          isNullOrEmpty(row[col])
        );
      case "e23683_e": // B_04.01: not ( isnull ({c0030}) )
        return !isNullOrEmpty(row["c0030"]);
      case "e23792_e": // B_02.01: not ( isnull ({(c0020, c0040, c0050)}) )
        return !["c0020", "c0040", "c0050"].some((col) => isNullOrEmpty(row[col]));

      // == Conditional Null Checks (if then not isnull) ==
      case "v22912_m": // B_02.01: if ( {c0020} = [eba_CO:x3] ) then ( not ( isnull ({c0030}) ) ) endif
      case "v8805_m": // B_02.01: Same logic as v22912_m
        if (safeToString(row["c0020"]) === "eba_CO:x3") {
          return !isNullOrEmpty(row["c0030"]);
        }
        return true; // Condition not met, rule passes
      case "v8803_m": // B_01.02: if (not (isnull ({c0110}))) then (not (isnull ({c0100}))) endif
        if (!isNullOrEmpty(row["c0110"])) {
          return !isNullOrEmpty(row["c0100"]);
        }
        return true;
      case "v8804_m": {
        // B_01.02: if({c0040} != [eba_CT:x318] and {c0040} != [eba_CT:x317]) then (not (isnull ({c0110}))) endif
        const c0040 = safeToString(row["c0040"]);
        if (c0040 !== "eba_CT:x318" && c0040 !== "eba_CT:x317") {
          return !isNullOrEmpty(row["c0110"]);
        }
        return true;
      }
      case "v8816_m": {
        // B_02.02: {c0080} > {c0070} (Rule is inactive)
        const c0070 = safeToNumber(row["c0070"]);
        const c0080 = safeToNumber(row["c0080"]);
        if (isNaN(c0070) || isNaN(c0080)) return true; // If any is not a number, pass
        return c0080 > c0070;
      }
      case "v8817_m": {
        // B_05.01: if ({c0070} = [eba_CT:x212]) then ({c0020} in {[eba_qCO:qx2000], [eba_qCO:qx2002]}) endif (inactive)
        const c0070 = safeToString(row["c0070"]);
        if (c0070 === "eba_CT:x212") {
          const c0020 = safeToString(row["c0020"]);
          return c0020 === "eba_qCO:qx2000" || c0020 === "eba_qCO:qx2002";
        }
        return true;
      }
      case "v8818_m": {
        // B_05.01: if ({c0070} = [eba_CT:x212]) then ({c0040} in {[eba_qCO:qx2000], [eba_qCO:qx2002]}) endif (inactive)
        const c0070 = safeToString(row["c0070"]);
        if (c0070 === "eba_CT:x212") {
          const c0040 = safeToString(row["c0040"]);
          return c0040 === "eba_qCO:qx2000" || c0040 === "eba_qCO:qx2002";
        }
        return true;
      }
      case "v8821_m": {
        // B_05.01: if({c0020} = [eba_qCO:qx2000]) then ((match({c0030}, "^[A-Z0-9]{18}[0-9]{2}$"))) endif (inactive)
        const c0020 = safeToString(row["c0020"]);
        if (c0020 === "eba_qCO:qx2000") {
          const c0030 = safeToString(row["c0030"]);
          return /^[A-Z0-9]{18}[0-9]{2}$/.test(c0030);
        }
        return true;
      }
      case "v8823_m": {
        // B_05.01: if({c0030} != "null") then ({c0040} != "null") endif (inactive)
        const c0030 = safeToString(row["c0030"]);
        if (c0030 !== "null" && !isNullOrEmpty(c0030)) {
          const c0040 = safeToString(row["c0040"]);
          return c0040 !== "null" && !isNullOrEmpty(c0040);
        }
        return true;
      }
      case "v8824_m": {
        // B_05.01: if({c0100} != "null") then ({c0090} != "null") endif (inactive)
        const c0100 = safeToString(row["c0100"]);
        if (c0100 !== "null" && !isNullOrEmpty(c0100)) {
          const c0090 = safeToString(row["c0090"]);
          return c0090 !== "null" && !isNullOrEmpty(c0090);
        }
        return true;
      }
      case "v8825_m": {
        // B_07.01: if {c0050} = [eba_ZZ:x959] or {c0050} = [eba_ZZ:x960] then (not (isnull ({c0060}))) endif
        const c0050 = safeToString(row["c0050"]);
        if (c0050 === "eba_ZZ:x959" || c0050 === "eba_ZZ:x960") {
          return !isNullOrEmpty(row["c0060"]);
        }
        return true;
      }

      // == Sign Checks (>= 0) ==
      case "v22913_s": {
        // B_01.02: {c0110} >= 0
        const val = safeToNumber(row["c0110"]);
        return !isNaN(val) && val >= 0;
      }
      case "v23716_s": {
        // B_02.01: {c0050} >= 0
        const val = safeToNumber(row["c0050"]);
        return !isNaN(val) && val >= 0;
      }

      // == Format Checks (match) ==
      // Note: These LEI checks might fail if the column is null/empty. Validation should pass in that case? Assuming yes.
      case "v8826_m": {
        // B_01.02: match({tB_01.02, c0060}, "^[A-Z0-9]{18}[0-9]{2}$")
        const val = safeToString(row["c0060"]);
        return isNullOrEmpty(val) || /^[A-Z0-9]{18}[0-9]{2}$/.test(val);
      }
      case "v8890_m": {
        // B_01.01: match({tB_01.01, c0020}[get ERI], "^[A-Z0-9]{18}[0-9]{2}$")
        // Ignoring annotation `[get ERI]`, as it appears to be a reference to an entity identifier
        const val = safeToString(row["c0020"]);
        return isNullOrEmpty(val) || /^[A-Z0-9]{18}[0-9]{2}$/.test(val);
      }
      case "v8891_m": {
        // B_01.02: match({tB_01.02, c0020}[get ESI], "^[A-Z0-9]{18}[0-9]{2}$")
        // Ignoring annotation `[get ESI]`, as it appears to be a reference to an entity identifier
        const val = safeToString(row["c0020"]);
        return isNullOrEmpty(val) || /^[A-Z0-9]{18}[0-9]{2}$/.test(val);
      }
      case "v8892_m": {
        // B_01.03: match({tB_01.03, c0030}[get LHH], "^[A-Z0-9]{18}[0-9]{2}$")
        // Ignoring annotation `[get LHH]`, as it appears to be a reference to an entity identifier
        const val = safeToString(row["c0030"]);
        return isNullOrEmpty(val) || /^[A-Z0-9]{18}[0-9]{2}$/.test(val);
      }
      case "v8893_m": {
        // B_02.02: match({tB_02.02, c0040}[get LES], "^[A-Z0-9]{18}[0-9]{2}$") (inactive)
        const val = safeToString(row["c0040"]);
        return isNullOrEmpty(val) || /^[A-Z0-9]{18}[0-9]{2}$/.test(val);
      }
      case "v8894_m": {
        // B_03.01: match({tB_03.01, c0030}[get LEA], "^[A-Z0-9]{18}[0-9]{2}$") (inactive)
        const val = safeToString(row["c0030"]);
        return isNullOrEmpty(val) || /^[A-Z0-9]{18}[0-9]{2}$/.test(val);
      }
      case "v8895_m": {
        // B_03.03: match({tB_03.03, c0031}[get LEB], "^[A-Z0-9]{18}[0-9]{2}$") (inactive)
        const val = safeToString(row["c0031"]);
        return isNullOrEmpty(val) || /^[A-Z0-9]{18}[0-9]{2}$/.test(val);
      }
      case "v8896_m": {
        // B_04.01: match({tB_04.01, c0030}[get LES], "^[A-Z0-9]{18}[0-9]{2}$") (inactive)
        const val = safeToString(row["c0030"]);
        return isNullOrEmpty(val) || /^[A-Z0-9]{18}[0-9]{2}$/.test(val);
      }
      case "v8897_m": {
        // B_06.01: match({tB_06.01, c0020}[get LES], "^[A-Z0-9]{18}[0-9]{2}$") (inactive)
        const val = safeToString(row["c0020"]);
        return isNullOrEmpty(val) || /^[A-Z0-9]{18}[0-9]{2}$/.test(val);
      }

      // == Complex Inter-field Null Checks (if ANY of set A !isnull then X !isnull) ==
      // Example: v8850_m - if ANY of c0030..c0120 are not null, then c0020 must not be null
      case "v8850_m": {
        // B_05.01
        const checkCols = [
          "c0030",
          "c0040",
          "c0050",
          "c0060",
          "c0070",
          "c0080",
          "c0090",
          "c0100",
          "c0110",
          "c0120"
        ];
        const anyCheckColNotNull = checkCols.some((col) => !isNullOrEmpty(row[col]));
        if (anyCheckColNotNull) {
          return !isNullOrEmpty(row["c0020"]);
        }
        return true;
      }
      case "v8851_m": {
        // B_05.01 -> if any !isnull then c0050 !isnull
        const checkCols = [
          "c0030",
          "c0040",
          "c0020",
          "c0060",
          "c0070",
          "c0080",
          "c0090",
          "c0100",
          "c0110",
          "c0120"
        ];
        const anyCheckColNotNull = checkCols.some((col) => !isNullOrEmpty(row[col]));
        if (anyCheckColNotNull) {
          return !isNullOrEmpty(row["c0050"]);
        }
        return true;
      }
      case "v8852_m": {
        // B_05.01 -> if any !isnull then c0060 !isnull
        const checkCols = [
          "c0030",
          "c0040",
          "c0050",
          "c0020",
          "c0070",
          "c0080",
          "c0090",
          "c0100",
          "c0110",
          "c0120"
        ];
        const anyCheckColNotNull = checkCols.some((col) => !isNullOrEmpty(row[col]));
        if (anyCheckColNotNull) {
          return !isNullOrEmpty(row["c0060"]);
        }
        return true;
      }
      case "v8853_m": {
        // B_05.01 -> if any !isnull then c0070 !isnull
        const checkCols = [
          "c0030",
          "c0040",
          "c0050",
          "c0060",
          "c0020",
          "c0080",
          "c0090",
          "c0100",
          "c0110",
          "c0120"
        ];
        const anyCheckColNotNull = checkCols.some((col) => !isNullOrEmpty(row[col]));
        if (anyCheckColNotNull) {
          return !isNullOrEmpty(row["c0070"]);
        }
        return true;
      }
      case "v8854_m": {
        // B_05.01 -> if any !isnull then c0080 !isnull
        const checkCols = [
          "c0030",
          "c0040",
          "c0050",
          "c0060",
          "c0070",
          "c0020",
          "c0090",
          "c0100",
          "c0110",
          "c0120"
        ];
        const anyCheckColNotNull = checkCols.some((col) => !isNullOrEmpty(row[col]));
        if (anyCheckColNotNull) {
          return !isNullOrEmpty(row["c0080"]);
        }
        return true;
      }
      case "v8855_m": {
        // B_05.01 -> if any !isnull then c0110 !isnull
        const checkCols = [
          "c0030",
          "c0040",
          "c0050",
          "c0060",
          "c0070",
          "c0080",
          "c0090",
          "c0100",
          "c0020",
          "c0120"
        ];
        const anyCheckColNotNull = checkCols.some((col) => !isNullOrEmpty(row[col]));
        if (anyCheckColNotNull) {
          return !isNullOrEmpty(row["c0110"]);
        }
        return true;
      }
      case "v8856_m": // B_01.03: if ( not ( isnull ({c0030}) ) ) then ( ( not ( isnull ({c0040}) ) )) endif
        if (!isNullOrEmpty(row["c0030"])) {
          return !isNullOrEmpty(row["c0040"]);
        }
        return true;
      case "v8857_m": // B_01.03: if ( not ( isnull ({c0040}) ) ) then ( ( not ( isnull ({c0030}) ) )) endif
        if (!isNullOrEmpty(row["c0040"])) {
          return !isNullOrEmpty(row["c0030"]);
        }
        return true;

      // B_01.02 similar pattern
      case "v8858_m": {
        // If any !isnull then c0020 !isnull
        const checkCols = ["c0030", "c0040", "c0050", "c0060", "c0070", "c0080", "c0090", "c0100", "c0110"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0020"]);
        return true;
      }
      case "v8859_m": {
        // If any !isnull then c0030 !isnull
        const checkCols = ["c0020", "c0040", "c0050", "c0060", "c0070", "c0080", "c0090", "c0100", "c0110"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0030"]);
        return true;
      }
      case "v8860_m": {
        // If any !isnull then c0040 !isnull
        const checkCols = ["c0030", "c0020", "c0050", "c0060", "c0070", "c0080", "c0090", "c0100", "c0110"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0040"]);
        return true;
      }
      case "v8861_m": {
        // If any !isnull then c0050 !isnull
        const checkCols = ["c0030", "c0040", "c0020", "c0060", "c0070", "c0080", "c0090", "c0100", "c0110"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0050"]);
        return true;
      }
      case "v8862_m": {
        // If any !isnull then c0060 !isnull
        const checkCols = ["c0030", "c0040", "c0020", "c0050", "c0070", "c0080", "c0090", "c0100", "c0110"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0060"]);
        return true;
      }
      case "v8863_m": {
        // If any !isnull then c0070 !isnull
        const checkCols = ["c0030", "c0040", "c0050", "c0060", "c0020", "c0080", "c0090", "c0100", "c0110"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0070"]);
        return true;
      }
      case "v8864_m": {
        // If any !isnull then c0080 !isnull
        const checkCols = ["c0030", "c0040", "c0050", "c0060", "c0070", "c0020", "c0090", "c0100", "c0110"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0080"]);
        return true;
      }
      case "v8865_m": {
        // If any !isnull then c0090 !isnull
        const checkCols = ["c0030", "c0040", "c0050", "c0060", "c0070", "c0080", "c0020", "c0100", "c0110"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0090"]);
        return true;
      }

      // B_02.01 similar pattern
      case "v8866_m": {
        // If any !isnull then c0050 !isnull
        const checkCols = ["c0020", "c0030", "c0040"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0050"]);
        return true;
      }
      case "v8867_m": {
        // If any !isnull then c0040 !isnull
        const checkCols = ["c0020", "c0030", "c0050"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0040"]);
        return true;
      }
      case "v8868_m": {
        // If any !isnull then c0020 !isnull
        const checkCols = ["c0050", "c0030", "c0040"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0020"]);
        return true;
      }

      // B_02.02 similar pattern
      case "v8869_m": {
        // If any !isnull then c0040 !isnull
        const checkCols = ["c0070", "c0080", "c0090", "c0100", "c0110", "c0120", "c0140", "c0170", "c0180"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0040"]);
        return true;
      }
      case "v8870_m": {
        // If any !isnull then c0070 !isnull
        const checkCols = ["c0040", "c0080", "c0090", "c0100", "c0110", "c0120", "c0140", "c0170", "c0180"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0070"]);
        return true;
      }
      case "v8871_m": {
        // If any !isnull then c0080 !isnull
        const checkCols = ["c0070", "c0040", "c0090", "c0100", "c0110", "c0120", "c0140", "c0170", "c0180"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0080"]);
        return true;
      }

      // B_01.01 similar pattern
      case "v8872_m": {
        // If any !isnull then c0020 !isnull
        const checkCols = ["c0030", "c0040", "c0050", "c0060"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0020"]);
        return true;
      }
      case "v8873_m": {
        // If any !isnull then c0030 !isnull
        const checkCols = ["c0020", "c0040", "c0050", "c0060"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0030"]);
        return true;
      }
      case "v8874_m": {
        // If any !isnull then c0040 !isnull
        const checkCols = ["c0030", "c0020", "c0050", "c0060"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0040"]);
        return true;
      }
      case "v8875_m": {
        // If any !isnull then c0050 !isnull
        const checkCols = ["c0030", "c0040", "c0020", "c0060"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0050"]);
        return true;
      }
      case "v8876_m": {
        // If any !isnull then c0060 !isnull
        const checkCols = ["c0030", "c0040", "c0050", "c0020"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0060"]);
        return true;
      }

      // B_06.01 similar pattern
      case "v8877_m": {
        // If any !isnull then c0020 !isnull
        const checkCols = ["c0030", "c0050", "c0060", "c0070", "c0080", "c0090", "c0100"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0020"]);
        return true;
      }
      case "v8878_m": {
        // If any !isnull then c0030 !isnull
        const checkCols = ["c0020", "c0050", "c0060", "c0070", "c0080", "c0090", "c0100"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0030"]);
        return true;
      }
      case "v8879_m": {
        // If any !isnull then c0050 !isnull
        const checkCols = ["c0030", "c0020", "c0060", "c0070", "c0080", "c0090", "c0100"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0050"]);
        return true;
      }
      case "v8880_m": {
        // If any !isnull then c0070 !isnull
        const checkCols = ["c0030", "c0050", "c0060", "c0020", "c0080", "c0090", "c0100"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0070"]);
        return true;
      }
      case "v8881_m": {
        // If any !isnull then c0080 !isnull
        const checkCols = ["c0030", "c0050", "c0060", "c0070", "c0020", "c0090", "c0100"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0080"]);
        return true;
      }
      case "v8882_m": {
        // If any !isnull then c0090 !isnull
        const checkCols = ["c0030", "c0050", "c0060", "c0070", "c0080", "c0020", "c0100"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0090"]);
        return true;
      }
      case "v8883_m": {
        // If any !isnull then c0100 !isnull
        // Note: Duplicated c0020 check in original expression? Assuming typo, using distinct columns.
        const checkCols = ["c0030", "c0050", "c0060", "c0070", "c0080", "c0090", "c0020"]; // Corrected distinct list
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0100"]);
        return true;
      }

      // B_07.01 similar pattern
      case "v8884_m": {
        // If any !isnull then c0050 !isnull
        const checkCols = ["c0030", "c0060", "c0070", "c0080", "c0090", "c0100", "c0110", "c0120"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0050"]);
        return true;
      }
      case "v8885_m": {
        // If any !isnull then c0070 !isnull
        const checkCols = ["c0030", "c0060", "c0050", "c0080", "c0090", "c0100", "c0110", "c0120"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0070"]);
        return true;
      }
      case "v8886_m": {
        // If any !isnull then c0080 !isnull
        const checkCols = ["c0030", "c0060", "c0070", "c0050", "c0090", "c0100", "c0110", "c0120"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0080"]);
        return true;
      }
      case "v8888_m": {
        // If any !isnull then c0100 !isnull
        const checkCols = ["c0030", "c0060", "c0070", "c0080", "c0090", "c0050", "c0110", "c0120"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0100"]);
        return true;
      }
      case "v88889_m": {
        // If any !isnull then c0110 !isnull - Note: code might be typo, using 'v88889_m' as written
        const checkCols = ["c0030", "c0060", "c0070", "c0080", "c0090", "c0100", "c0050", "c0120"];
        if (checkCols.some((col) => !isNullOrEmpty(row[col]))) return !isNullOrEmpty(row["c0110"]);
        return true;
      }

      // --- Add other active rule codes here ---
      // ...

      default:
        // If an active rule code is not handled here, log a warning.
        console.warn(`Rule ${code}: Hardcoded evaluation logic not implemented. Skipping row.`);
        // Returning true means it passes by default if not implemented,
        // which might hide issues. Returning false would flag it.
        // Let's return false to indicate it couldn't be verified.
        return false;
    }
  } catch (error: any) {
    console.error(`Error applying hardcoded logic for rule ${code}:`, error.message, row);
    throw new Error(`Evaluation failed for rule ${code}: ${error.message}`); // Propagate as evaluation error
  }
}

/**
 * Evaluates validation rules against the input data using hardcoded logic.
 * @param rule - The validation rule object.
 * @param data - The complete input data object.
 * @returns An array of ValidationResult objects.
 */
function evaluateRuleHardcoded(rule: ValidationRule, data: InputData): ValidationResult[] {
  const results: ValidationResult[] = [];
  const baseResult = {
    ruleCode: rule.code,
    severity: rule.severity
  };

  const tableName = rule.tables.trim(); // Assuming single table
  const tableKey = `t${tableName}`;

  const tableData = data[tableKey];

  // Basic Precondition Check (Only handles simple {v_Table} format for now)
  let preconditionMet = true;
  if (rule.preconditionExpression && rule.preconditionExpression.trim() !== "") {
    const precondMatch = rule.preconditionExpression.trim().match(/^\{v_(.*?)\}$/i);
    if (precondMatch) {
      const precondTableKey = `t${precondMatch[1]}`;
      if (!data[precondTableKey]) {
        preconditionMet = false;
      }
    } else {
      console.warn(
        `Unsupported precondition format: "${rule.preconditionExpression}" in rule ${rule.code}. Assuming met.`
      );
    }
  }

  if (!preconditionMet) {
    // console.log(`Rule ${rule.code} skipped: Precondition not met.`);
    results.push({
      ...baseResult,
      tableName: tableName,
      status: "skipped",
      message: `Rule skipped: Precondition "${rule.preconditionExpression}" not met.`
    });
    return results;
  }

  if (!tableData || !Array.isArray(tableData)) {
    // Handle missing or invalid table data based on precondition outcome
    results.push({
      ...baseResult,
      tableName: tableName,
      status: "skipped",
      message: `Table "${tableKey}" not found or invalid in input data.`
    });
    return results;
  }

  if (tableData.length === 0) {
    // Handle empty table - depends on rule intent, usually pass/skip
    // Simple check: if rule is 'existence' (not isnull), it should fail on empty.
    if (rule.source === "Existence" && rule.expression.includes("not ( isnull")) {
      results.push({
        ...baseResult,
        tableName: tableName,
        status: "failed",
        message: "Existence check failed: Table is empty."
      });
    } else {
      results.push({
        ...baseResult,
        tableName: tableName,
        status: "passed",
        message: "Rule passed: Table is empty."
      });
    }
    return results;
  }

  // Apply the rule to each row
  let rulePassedOverall = true;
  tableData.forEach((row, index) => {
    try {
      const expressionPasses = applyHardcodedRule(rule, row, data);
      if (!expressionPasses) {
        rulePassedOverall = false;
        results.push({
          ...baseResult,
          tableName: tableName,
          rowIndex: index,
          dataContext: row, // Include the failing row data
          status: "failed",
          message: `Validation failed for rule ${rule.code}. Expression: ${rule.expression}`
          // Could add more specific failure reason if applyHardcodedRule provided it
        });
      }
    } catch (error: any) {
      rulePassedOverall = false;
      console.error(`Error evaluating rule ${rule.code} on row ${index} of table ${tableName}:`, error);
      results.push({
        ...baseResult,
        tableName: tableName,
        rowIndex: index,
        dataContext: row,
        status: "error",
        message: `Error during evaluation: ${error.message}`
      });
    }
  });

  // If no failures or errors were added for any row, record an overall pass for the rule.
  if (rulePassedOverall && results.length === 0) {
    results.push({
      ...baseResult,
      tableName: tableName,
      status: "passed",
      message: "Rule passed for all rows."
    });
  }

  return results;
}

/**
 * Validates data against business validation rules.
 * @param inputData - The data to validate.
 * @param rulesFilePath - Path to the validation rules file.
 * @returns An array of validation results.
 */
export function validateDataHardcoded(inputData: InputData, rulesFilePath: string): ValidationResult[] {
  console.log(`Starting validation with HARDCODED logic using rules from: ${rulesFilePath}`);
  const rules = loadValidationRules(rulesFilePath); // Loads only active rules
  console.log(`Loaded ${rules.length} active validation rules.`);

  const allResults: ValidationResult[] = [];

  rules.forEach((rule) => {
    console.log(`\nProcessing rule: ${rule.code} (Table: ${rule.tables})`);
    // Skip rules without hardcoded logic defined in applyHardcodedRule
    if (isRuleImplemented(rule.code)) {
      const ruleResults = evaluateRuleHardcoded(rule, inputData);
      allResults.push(...ruleResults);
    } else {
      console.warn(`Rule ${rule.code}: Logic not implemented in script. Skipping.`);
      allResults.push({
        ruleCode: rule.code,
        severity: rule.severity,
        tableName: rule.tables.trim(),
        status: "skipped",
        message: "Rule logic not implemented in hardcoded-validator.ts"
      });
    }
  });

  console.log(`\nValidation finished. Total results generated: ${allResults.length}`);

  // Filter and display non-passed results
  const issues = allResults.filter((r) => r.status !== "passed");
  console.log(`Found ${issues.length} issues (failed, error, or skipped).`);
  issues.forEach((r) =>
    console.log(
      `- Rule ${r.ruleCode} (${r.status}): ${r.message} ${
        r.tableName ? `[Table: ${r.tableName}${r.rowIndex !== undefined ? `, Row: ${r.rowIndex}` : ""}]` : ""
      }`
    )
  );

  // Add success message with emoji if no issues found
  if (issues.length === 0) {
    console.log("\n🎉 Business Validation completed successfully! All rules passed! 🎊");
  }

  return allResults;
}

// Helper to check if a rule's logic is present in the switch statement
// This is brittle but avoids running evaluateRuleHardcoded for unimplemented rules.
function isRuleImplemented(ruleCode: string): boolean {
  const implementedCodes = [
    // Existence Checks
    "e23674_e", // B_05.01
    "e23675_e", // B_01.03
    "e23676_e", // B_01.02
    "e23677_e", // B_01.01
    "e23680_e", // B_02.02
    "e23681_e", // B_07.01 (inactive)
    "e23682_e", // B_06.01
    "e23683_e", // B_04.01
    "e23792_e", // B_02.01

    // Conditional Null Checks
    "v22912_m", // B_02.01
    "v8805_m", // B_02.01 (Same as v22912_m)
    "v8803_m", // B_01.02
    "v8804_m", // B_01.02
    "v8825_m", // B_07.01
    "v8816_m", // B_02.02 (inactive)
    "v8817_m", // B_05.01 (inactive)
    "v8818_m", // B_05.01 (inactive)
    "v8821_m", // B_05.01 (inactive)
    "v8823_m", // B_05.01 (inactive)
    "v8824_m", // B_05.01 (inactive)

    // Sign Checks
    "v22913_s", // B_01.02
    "v23716_s", // B_02.01

    // Format (Match) Checks
    "v8826_m", // B_01.02
    "v8890_m", // B_01.01
    "v8891_m", // B_01.02
    "v8892_m", // B_01.03
    "v8893_m", // B_02.02 (inactive)
    "v8894_m", // B_03.01 (inactive)
    "v8895_m", // B_03.03 (inactive)
    "v8896_m", // B_04.01 (inactive)
    "v8897_m", // B_06.01 (inactive)

    // Complex Inter-field Null Checks
    // B_05.01
    "v8850_m",
    "v8851_m",
    "v8852_m",
    "v8853_m",
    "v8854_m",
    "v8855_m",

    // B_01.03
    "v8856_m",
    "v8857_m",

    // B_01.02
    "v8858_m",
    "v8859_m",
    "v8860_m",
    "v8861_m",
    "v8862_m",
    "v8863_m",
    "v8864_m",
    "v8865_m",

    // B_02.01
    "v8866_m",
    "v8867_m",
    "v8868_m",

    // B_02.02
    "v8869_m",
    "v8870_m",
    "v8871_m",

    // B_01.01
    "v8872_m",
    "v8873_m",
    "v8874_m",
    "v8875_m",
    "v8876_m",

    // B_06.01
    "v8877_m",
    "v8878_m",
    "v8879_m",
    "v8880_m",
    "v8881_m",
    "v8882_m",
    "v8883_m",

    // B_07.01
    "v8884_m",
    "v8885_m",
    "v8886_m",
    "v8887_m", // inactive
    "v8888_m",
    "v88889_m"
  ];
  return implementedCodes.includes(ruleCode);
}

// --- Command Line Interface REMOVED ---

/* // Helper function to load data from a JSON file REMOVED
function loadInputData(filePath: string): InputData {
   // ... (implementation removed) ...
}
*/

/* // Simple CLI runner REMOVED
if (require.main === module) {
  // ... (CLI logic removed) ...
}
*/
