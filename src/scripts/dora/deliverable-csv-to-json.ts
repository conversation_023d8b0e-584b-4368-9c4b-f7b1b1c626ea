import * as fs from "fs";
import * as path from "path";
import { parse } from "csv-parse/sync";

/**
 * Interfaces for structuring the output JSON
 */
interface CsvData {
  tableName: string; // e.g. "b_01.01"
  data: Record<string, any>[];
  metadata?: {
    fileName: string;
    rowCount: number;
    columnCount: number;
    headers: string[];
  };
}

interface CsvReportData {
  parameters: Record<string, string>;
  tables: Record<string, CsvData>;
  reportInfo: {
    fileName: string;
    tables: string[];
  };
}

/**
 * Read a CSV file and parse it into a JavaScript array
 * @param filePath Path to the CSV file
 * @returns Array of objects representing the CSV data
 */
function parseCsvFile(filePath: string): Record<string, any>[] {
  try {
    // Read file content and remove BOM if present
    let fileContent = fs.readFileSync(filePath, "utf8");

    // Remove BOM character if present
    if (fileContent.charCodeAt(0) === 0xfeff) {
      fileContent = fileContent.slice(1);
    }

    const records = parse(fileContent, {
      columns: true,
      skip_empty_lines: true,
      trim: true
    });

    // Clean the data - remove BOM from column names if present
    return records.map((record: Record<string, any>) => {
      const cleanRecord: Record<string, any> = {};
      for (const [key, value] of Object.entries(record)) {
        // Remove BOM from column name if present
        const cleanKey = key.replace(/^\uFEFF/, "").trim();
        cleanRecord[cleanKey] = value;
      }
      return cleanRecord;
    });
  } catch (error: any) {
    console.error(`Error parsing CSV file ${filePath}: ${error.message}`);
    return [];
  }
}

/**
 * Parse parameters CSV file
 * @param filePath Path to the parameters.csv file
 * @returns Record of parameter name to value
 */
function parseParametersFile(filePath: string): Record<string, string> {
  try {
    // Read file content and remove BOM if present
    let fileContent = fs.readFileSync(filePath, "utf8");

    // Remove BOM character if present
    if (fileContent.charCodeAt(0) === 0xfeff) {
      fileContent = fileContent.slice(1);
    }

    const records = parse(fileContent, {
      columns: true,
      skip_empty_lines: true,
      trim: true
    });

    const params: Record<string, string> = {};
    for (const record of records) {
      // Handle potential BOM in column names
      const nameKey = Object.keys(record).find((key) => key === "name" || key.replace(/^\uFEFF/, "") === "name");

      const valueKey = Object.keys(record).find(
        (key) => key === "value" || key.replace(/^\uFEFF/, "") === "value"
      );

      if (nameKey && valueKey && record[nameKey] && record[valueKey]) {
        params[record[nameKey]] = record[valueKey];
      }
    }
    return params;
  } catch (error: any) {
    console.error(`Error parsing parameters file ${filePath}: ${error.message}`);
    return {};
  }
}

/**
 * Get all CSV report files from the deliverable directory
 * @param directoryPath Path to the reports directory
 * @returns Array of file paths for report CSV files
 */
function getReportFiles(directoryPath: string): string[] {
  try {
    const files = fs.readdirSync(directoryPath);
    // Filter for report files (matches pattern b_XX.XX.csv)
    return files
      .filter((file) => /^b_\d{2}\.\d{2}\.csv$/i.test(file))
      .map((file) => path.join(directoryPath, file));
  } catch (error: any) {
    console.error(`Error reading directory ${directoryPath}: ${error.message}`);
    return [];
  }
}

/**
 * Convert all CSV report files into a structured JSON object
 * @param directoryPath Path to the reports directory
 * @param options Configuration options
 * @returns Structured JSON representation of the report data
 */
export function convertCsvToJson(
  directoryPath: string,
  options: {
    includeMetadata?: boolean;
    outputFilePath?: string;
  } = {}
): CsvReportData {
  // Default options
  const { includeMetadata = true, outputFilePath } = options;

  // Find the reports directory
  const reportsDir = path.join(directoryPath, "reports");

  // Verify the reports directory exists
  if (!fs.existsSync(reportsDir)) {
    throw new Error(`Reports directory not found at ${reportsDir}`);
  }

  // Get all CSV report files
  const reportFiles = getReportFiles(reportsDir);
  if (reportFiles.length === 0) {
    throw new Error(`No report files found in ${reportsDir}`);
  }

  // Parse parameters file if it exists
  const parametersFilePath = path.join(reportsDir, "parameters.csv");
  const parameters = fs.existsSync(parametersFilePath) ? parseParametersFile(parametersFilePath) : {};

  // Initialize result structure
  const result: CsvReportData = {
    parameters,
    tables: {},
    reportInfo: {
      fileName: path.basename(directoryPath),
      tables: []
    }
  };

  // Process each report file
  for (const filePath of reportFiles) {
    const fileName = path.basename(filePath);
    const tableName = path.basename(filePath, ".csv"); // e.g. "b_01.01"
    const tableNameKey = tableName.toLowerCase(); // Use lowercase for consistency

    // Read column headers from file and handle BOM
    let fileContent = fs.readFileSync(filePath, "utf8");

    // Remove BOM character if present
    if (fileContent.charCodeAt(0) === 0xfeff) {
      fileContent = fileContent.slice(1);
    }

    const headerLine = fileContent.split("\n")[0].trim();
    const headers = headerLine
      .split(",")
      .map((h) => h.trim())
      .map((h) => h.replace(/^\uFEFF/, ""));

    // Parse the CSV file
    const csvData = parseCsvFile(filePath);

    // Add to result
    result.tables[tableNameKey] = {
      tableName,
      data: csvData
    };

    // Track the table name
    result.reportInfo.tables.push(tableName);

    // Add metadata if requested
    if (includeMetadata) {
      result.tables[tableNameKey].metadata = {
        fileName,
        rowCount: csvData.length,
        columnCount: headers.length,
        headers
      };
    }
  }

  // Write to file if output path is provided
  if (outputFilePath) {
    fs.writeFileSync(outputFilePath, JSON.stringify(result, null, 2));
    console.log(`CSV data converted to JSON and saved to ${outputFilePath}`);
  }

  return result;
}

/**
 * Transform CSV report data into the format required for validation
 * @param csvData Parsed CSV report data
 * @returns Data transformed into the format expected by the validator
 */
export function transformDataForValidation(csvData: CsvReportData): Record<string, Record<string, any>[]> {
  const validationData: Record<string, Record<string, any>[]> = {};

  for (const [tableKey, table] of Object.entries(csvData.tables)) {
    // The validator expects tables in the format "tB_01.01" (not "b_01.01")
    // Transform "b_01.01" to "tB_01.01"
    const validationTableKey = `t${tableKey.toUpperCase()}`;

    // Transform the data - keep column keys in the format they already are (c0010, c0020, etc.)
    validationData[validationTableKey] = table.data;
  }

  return validationData;
}

/**
 * Convert CSV report files and prepare for validation
 * @param directoryPath Path to the deliverable directory
 * @param options Configuration options
 * @returns Object containing the parsed data and validation-ready data
 */
export function convertAndPrepareForValidation(
  directoryPath: string,
  options: {
    outputFilePath?: string;
  } = {}
): {
  parsedData: CsvReportData;
  validationData: Record<string, Record<string, any>[]>;
} {
  console.log(`Converting CSV files from ${path.basename(directoryPath)} to JSON...`);

  // Parse all CSV files
  const parsedData = convertCsvToJson(directoryPath, {
    includeMetadata: true,
    outputFilePath: options.outputFilePath
  });

  console.log("CSV conversion complete!");
  console.log(`Processed ${parsedData.reportInfo.tables.length} tables`);

  // Transform for validation
  console.log("Preparing data for validation...");
  const validationData = transformDataForValidation(parsedData);

  return { parsedData, validationData };
}
