[{"code": "e23674_e", "type": "Validation", "source": "Existence", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_05.01", "expression": "with {tB_05.01,  default:null, interval:false}: not ( isnull ({(c0020, c0050, c0060, c0070, c0080, c0110)}) )", "preconditionExpression": "{v_B_05.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "e23675_e", "type": "Validation", "source": "Existence", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.03", "expression": "with {tB_01.03, default:null, interval:false}: not ( isnull ({c*}) )", "preconditionExpression": "{v_B_01.03} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "e23676_e", "type": "Validation", "source": "Existence", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.02", "expression": "with {tB_01.02,  default:null, interval:false}: not ( isnull ({c0020-0090}) )", "preconditionExpression": "{v_B_01.02} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "e23677_e", "type": "Validation", "source": "Existence", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.01", "expression": "with {tB_01.01, default:null, interval:false}: not ( isnull ({c*}) )", "preconditionExpression": "{v_B_01.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "e23680_e", "type": "Validation", "source": "Existence", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_02.02", "expression": "with {tB_02.02,  default:null, interval:false}: not ( isnull ({c0040-0080}) )", "preconditionExpression": "{v_B_02.02} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "e23681_e", "type": "Validation", "source": "Existence", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_07.01", "expression": "with {tB_07.01,  default:null, interval:false}: not ( isnull ({(c0050, c0070, c0080, c0090, c0100, c0110)}) )", "preconditionExpression": "{v_B_07.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "inactive ", "description": "To be deactivated in March 2025"}, {"code": "e23682_e", "type": "Validation", "source": "Existence", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_06.01", "expression": "with {tB_06.01,  default:null, interval:false}: not ( isnull ({(c0020, c0030, c0050, c0070, c0080, c0090, c0100)}) )", "preconditionExpression": "{v_B_06.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "e23683_e", "type": "Validation", "source": "Existence", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_04.01", "expression": "with {tB_04.01, default:null, interval:false}: not ( isnull ({c0030}) )", "preconditionExpression": "{v_B_04.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "e23792_e", "type": "Validation", "source": "Existence", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_02.01", "expression": "with {tB_02.01,  default:null, interval:false}: not ( isnull ({(c0020, c0040, c0050)}) )", "preconditionExpression": "{v_B_02.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v22912_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_02.01", "expression": "with {tB_02.01, default: null, interval: false}: if ( {c0020} = [eba_CO:x3] ) then ( not ( isnull ({c0030}) ) ) endif", "preconditionExpression": "{v_B_02.01} ", "startReleaseCode": 3.4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": "if {c0020} = [eba_CO:x3] then {c0030} != empty"}, {"code": "v22913_s", "type": "Validation", "source": "Sign", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.02", "expression": "with {tB_01.02, default: null, interval: false}: {c0110} >= 0 ", "preconditionExpression": "{v_B_01.02} ", "startReleaseCode": 3.4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": "{B 01.02} >= 0"}, {"code": "v23716_s", "type": "Validation", "source": "Sign", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_02.01", "expression": "with {tB_02.01, default: null, interval: false}: {c0050} >= 0", "preconditionExpression": "{v_B_02.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8803_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.02", "expression": "with {tB_01.02, default: 0, interval: false}: if (not (isnull ({c0110}))) then (not (isnull ({c0100}))) endif", "preconditionExpression": "{v_B_01.02} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8804_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.02", "expression": "with{tB_01.02, default:0, interval:false}: if({c0040} != [eba_CT:x318] and {c0040} != [eba_CT:x317]) then (not (isnull ({c0110}))) endif", "preconditionExpression": "{v_B_01.02} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8805_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_02.01", "expression": "with {tB_02.01, default: 0, interval: false}:if({c0020} = [eba_CO:x3]) then (not (isnull ({c0030}))) endif", "preconditionExpression": "{v_B_02.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8816_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_02.02", "expression": "with {tB_02.02, default: 0, interval: false}: {c0080} > {c0070}", "preconditionExpression": "{v_B_02.02} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "inactive ", "description": "To be deactivated in March 2025"}, {"code": "v8817_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_05.01", "expression": "with{tB_05.01, default:0, interval: false}: if ({c0070} = [eba_CT:x212]) then ({c0020} in {[eba_qCO:qx2000], [eba_qCO:qx2002]}) endif", "preconditionExpression": "{v_B_05.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "inactive ", "description": "To be deactivated in March 2025"}, {"code": "v8818_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_05.01", "expression": "with{tB_05.01, default:0, interval: false}: if ({c0070} = [eba_CT:x212]) then ({c0040} in {[eba_qCO:qx2000], [eba_qCO:qx2002]}) endif", "preconditionExpression": "{v_B_05.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "inactive ", "description": "To be deactivated in March 2025"}, {"code": "v8821_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_05.01", "expression": "with{tB_05.01, default:0, interval: false}: if({c0020} = [eba_qCO:qx2000]) then ( (match({c0030}, \"^[A-Z0-9]{18}[0-9]{2}$\"))) endif", "preconditionExpression": "{v_B_05.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "inactive ", "description": "To be deactivated in March 2025"}, {"code": "v8823_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "No", "tables": "B_05.01", "expression": "with {tB_05.01, default: 0, interval: false}: if({c0030} != \"null\") then ({c0040} != \"null\") endif", "preconditionExpression": "{v_B_05.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "inactive ", "description": "To be deactivated in March 2025"}, {"code": "v8824_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "No", "tables": "B_05.01", "expression": "with {tB_05.01, default: 0, interval: false}: if({c0100} != \"null\") then ({c0090} != \"null\") endif", "preconditionExpression": "{v_B_05.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "inactive ", "description": "To be deactivated in March 2025"}, {"code": "v8825_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_07.01", "expression": "with {tB_07.01, default:0, interval: false}: if {c0050} = [eba_ZZ:x959] or {c0050} = [eba_ZZ:x960] then (not (isnull ({c0060}))) endif", "preconditionExpression": "{v_B_07.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8826_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.02", "expression": "match({tB_01.02, c0060}, \"^[A-Z0-9]{18}[0-9]{2}$\")", "preconditionExpression": "{v_B_01.02} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": "Checks LEI format"}, {"code": "v8850_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_05.01", "expression": "with {tB_05.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0040}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0110}) )) or not (( isnull ({c0120}) )) then ( ( not ( isnull ({c0020}) ) )) endif", "preconditionExpression": "{v_B_05.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8851_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_05.01", "expression": "with {tB_05.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0040}) )) or not (( isnull ({c0020}) )) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0110}) )) or not (( isnull ({c0120}) )) then ( ( not ( isnull ({c0050}) ) )) endif", "preconditionExpression": "{v_B_05.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8852_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_05.01", "expression": "with {tB_05.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0040}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0020}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0110}) )) or not (( isnull ({c0120}) )) then ( ( not ( isnull ({c0060}) ) )) endif", "preconditionExpression": "{v_B_05.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8853_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_05.01", "expression": "with {tB_05.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0040}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0060}) )) or not (( isnull ({c0020}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0110}) )) or not (( isnull ({c0120}) )) then ( ( not ( isnull ({c0070}) ) )) endif", "preconditionExpression": "{v_B_05.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8854_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_05.01", "expression": "with {tB_05.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0040}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0020}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0110}) )) or not (( isnull ({c0120}) )) then ( ( not ( isnull ({c0080}) ) )) endif", "preconditionExpression": "{v_B_05.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8855_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_05.01", "expression": "with {tB_05.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0040}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0020}) )) or not (( isnull ({c0120}) )) then ( ( not ( isnull ({c0110}) ) )) endif", "preconditionExpression": "{v_B_05.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8856_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.03", "expression": "with {tB_01.03, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) then ( ( not ( isnull ({c0040}) ) )) endif", "preconditionExpression": "{v_B_01.03} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8857_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.03", "expression": "with {tB_01.03, default: null, interval: false}: if ( not ( isnull ({c0040}) ) ) then ( ( not ( isnull ({c0030}) ) )) endif", "preconditionExpression": "{v_B_01.03} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8858_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.02", "expression": "with {tB_01.02, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0040}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0110}) )) then ( ( not ( isnull ({c0020}) ) )) endif", "preconditionExpression": "{v_B_01.02} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8859_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.02", "expression": "with {tB_01.02, default: null, interval: false}: if ( not ( isnull ({c0020}) ) ) or not (( isnull ({c0040}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0110}) )) then ( ( not ( isnull ({c0030}) ) )) endif", "preconditionExpression": "{v_B_01.02} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8860_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.02", "expression": "with {tB_01.02, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0020}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0110}) )) then ( ( not ( isnull ({c0040}) ) )) endif", "preconditionExpression": "{v_B_01.02} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8861_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.02", "expression": "with {tB_01.02, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0040}) )) or not (( isnull ({c0020}) )) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0110}) )) then ( ( not ( isnull ({c0050}) ) )) endif", "preconditionExpression": "{v_B_01.02} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8862_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.02", "expression": "with {tB_01.02, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0040}) )) or not (( isnull ({c0020}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0110}) )) then ( ( not ( isnull ({c0060}) ) )) endif", "preconditionExpression": "{v_B_01.02} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8863_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.02", "expression": "with {tB_01.02, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0040}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0060}) )) or not (( isnull ({c0020}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0110}) )) then ( ( not ( isnull ({c0070}) ) )) endif", "preconditionExpression": "{v_B_01.02} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8864_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.02", "expression": "with {tB_01.02, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0040}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0020}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0110}) )) then ( ( not ( isnull ({c0080}) ) )) endif", "preconditionExpression": "{v_B_01.02} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8865_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.02", "expression": "with {tB_01.02, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0040}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0020}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0110}) )) then ( ( not ( isnull ({c0090}) ) )) endif", "preconditionExpression": "{v_B_01.02} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8866_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_02.01", "expression": "with {tB_02.01, default: null, interval: false}: if ( not ( isnull ({c0020}) ) ) or not (( isnull ({c0030}) )) or not (( isnull ({c0040}) )) then ( ( not ( isnull ({c0050}) ) )) endif", "preconditionExpression": "{v_B_02.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8867_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_02.01", "expression": "with {tB_02.01, default: null, interval: false}: if ( not ( isnull ({c0020}) ) ) or not (( isnull ({c0030}) )) or not (( isnull ({c0050}) )) then ( ( not ( isnull ({c0040}) ) )) endif", "preconditionExpression": "{v_B_02.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8868_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_02.01", "expression": "with {tB_02.01, default: null, interval: false}: if ( not ( isnull ({c0050}) ) ) or not (( isnull ({c0030}) )) or not (( isnull ({c0040}) )) then ( ( not ( isnull ({c0020}) ) )) endif", "preconditionExpression": "{v_B_02.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8869_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_02.02", "expression": "with {tB_02.02, default: null, interval: false}: if ( not ( isnull ({c0070}) ) ) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0110}) )) or not (( isnull ({c0120}) )) or not (( isnull ({c0140}) )) or not (( isnull ({c0170}) )) or not (( isnull ({c0180}) )) then ( ( not ( isnull ({c0040}) ) )) endif", "preconditionExpression": "{v_B_02.02} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8870_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_02.02", "expression": "with {tB_02.02, default: null, interval: false}: if ( not ( isnull ({c0040}) ) ) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0110}) )) or not (( isnull ({c0120}) )) or not (( isnull ({c0140}) )) or not (( isnull ({c0170}) )) or not (( isnull ({c0180}) )) then ( ( not ( isnull ({c0070}) ) )) endif", "preconditionExpression": "{v_B_02.02} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8871_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_02.02", "expression": "with {tB_02.02, default: null, interval: false}: if ( not ( isnull ({c0070}) ) ) or not (( isnull ({c0040}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0110}) )) or not (( isnull ({c0120}) )) or not (( isnull ({c0140}) )) or not (( isnull ({c0170}) )) or not (( isnull ({c0180}) )) then ( ( not ( isnull ({c0080}) ) )) endif", "preconditionExpression": "{v_B_02.02} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8872_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.01", "expression": "with {tB_01.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0040}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0060}) )) then ( ( not ( isnull ({c0020}) ) )) endif", "preconditionExpression": "{v_B_01.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8873_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.01", "expression": "with {tB_01.01, default: null, interval: false}: if ( not ( isnull ({c0020}) ) ) or not (( isnull ({c0040}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0060}) )) then ( ( not ( isnull ({c0030}) ) )) endif", "preconditionExpression": "{v_B_01.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8874_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.01", "expression": "with {tB_01.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0020}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0060}) )) then ( ( not ( isnull ({c0040}) ) )) endif", "preconditionExpression": "{v_B_01.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8875_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.01", "expression": "with {tB_01.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0040}) )) or not (( isnull ({c0020}) )) or not (( isnull ({c0060}) )) then ( ( not ( isnull ({c0050}) ) )) endif", "preconditionExpression": "{v_B_01.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8876_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_01.01", "expression": "with {tB_01.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0040}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0020}) )) then ( ( not ( isnull ({c0060}) ) )) endif", "preconditionExpression": "{v_B_01.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8877_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_06.01", "expression": "with {tB_06.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0050}) )) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) then ( ( not ( isnull ({c0020}) ) )) endif", "preconditionExpression": "{v_B_06.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8878_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_06.01", "expression": "with {tB_06.01, default: null, interval: false}: if ( not ( isnull ({c0020}) ) ) or not (( isnull ({c0050}) )) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) then ( ( not ( isnull ({c0030}) ) )) endif", "preconditionExpression": "{v_B_06.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8879_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_06.01", "expression": "with {tB_06.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0020}) )) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) then ( ( not ( isnull ({c0050}) ) )) endif", "preconditionExpression": "{v_B_06.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8880_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_06.01", "expression": "with {tB_06.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0050}) )) or not (( isnull ({c0060}) )) or not (( isnull ({c0020}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) then ( ( not ( isnull ({c0070}) ) )) endif", "preconditionExpression": "{v_B_06.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8881_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_06.01", "expression": "with {tB_06.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0050}) )) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0020}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) then ( ( not ( isnull ({c0080}) ) )) endif", "preconditionExpression": "{v_B_06.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8882_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_06.01", "expression": "with {tB_06.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0050}) )) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0020}) )) or not (( isnull ({c0100}) )) then ( ( not ( isnull ({c0090}) ) )) endif", "preconditionExpression": "{v_B_06.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8883_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_06.01", "expression": "with {tB_06.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0050}) )) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0020}) )) or not (( isnull ({c0020}) )) then ( ( not ( isnull ({c0100}) ) )) endif", "preconditionExpression": "{v_B_06.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8884_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_07.01", "expression": "with {tB_07.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0110}) )) or not (( isnull ({c0120}) )) then ( ( not ( isnull ({c0050}) ) )) endif", "preconditionExpression": "{v_B_07.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8885_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_07.01", "expression": "with {tB_07.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0060}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0110}) )) or not (( isnull ({c0120}) )) then ( ( not ( isnull ({c0070}) ) )) endif", "preconditionExpression": "{v_B_07.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8886_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_07.01", "expression": "with {tB_07.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0110}) )) or not (( isnull ({c0120}) )) then ( ( not ( isnull ({c0080}) ) )) endif", "preconditionExpression": "{v_B_07.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8887_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_07.01", "expression": "with {tB_07.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0110}) )) or not (( isnull ({c0120}) )) then ( ( not ( isnull ({c0090}) ) )) endif", "preconditionExpression": "{v_B_07.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "inactive ", "description": "To be deactivated in March 2025"}, {"code": "v88889_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_07.01", "expression": "with {tB_07.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0100}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0120}) )) then ( ( not ( isnull ({c0110}) ) )) endif", "preconditionExpression": "{v_B_07.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8888_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "", "tables": "B_07.01", "expression": "with {tB_07.01, default: null, interval: false}: if ( not ( isnull ({c0030}) ) ) or not (( isnull ({c0060}) )) or not (( isnull ({c0070}) )) or not (( isnull ({c0080}) )) or not (( isnull ({c0090}) )) or not (( isnull ({c0050}) )) or not (( isnull ({c0110}) )) or not (( isnull ({c0120}) )) then ( ( not ( isnull ({c0100}) ) )) endif", "preconditionExpression": "{v_B_07.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": ""}, {"code": "v8890_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "No", "tables": "B_01.01", "expression": "match({tB_01.01, c0020}[get ERI], \"^[A-Z0-9]{18}[0-9]{2}$\")", "preconditionExpression": "{v_B_01.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": "This VR checks the format of column 0010 (LEI code)"}, {"code": "v8891_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "No", "tables": "B_01.02", "expression": "match({tB_01.02, c0020}[get ESI], \"^[A-Z0-9]{18}[0-9]{2}$\")", "preconditionExpression": "{v_B_01.02} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": "This VR checks the format of column 0010 (LEI code)"}, {"code": "v8892_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "No", "tables": "B_01.03", "expression": "match({tB_01.03, c0030}[get LHH], \"^[A-Z0-9]{18}[0-9]{2}$\")", "preconditionExpression": "{v_B_01.03} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "active", "description": "This VR checks the format of column 0020 (LEI code)"}, {"code": "v8893_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "No", "tables": "B_02.02", "expression": "match({tB_02.02, c0040}[get LES], \"^[A-Z0-9]{18}[0-9]{2}$\")", "preconditionExpression": "{v_B_02.02} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "inactive ", "description": "To be deactivated in March 2025"}, {"code": "v8894_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "No", "tables": "B_03.01", "expression": "match({tB_03.01, c0030}[get LEA], \"^[A-Z0-9]{18}[0-9]{2}$\")", "preconditionExpression": "{v_B_03.01}", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "inactive ", "description": "To be deactivated in March 2025"}, {"code": "v8895_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "No", "tables": "B_03.03", "expression": "match({tB_03.03, c0031}[get LEB], \"^[A-Z0-9]{18}[0-9]{2}$\")", "preconditionExpression": "{v_B_03.03}", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "inactive ", "description": "To be deactivated in March 2025"}, {"code": "v8896_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "No", "tables": "B_04.01", "expression": "match({tB_04.01, c0030}[get LES], \"^[A-Z0-9]{18}[0-9]{2}$\")", "preconditionExpression": "{v_B_04.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "inactive ", "description": "To be deactivated in March 2025"}, {"code": "v8897_m", "type": "Validation", "source": "User defined", "frameworks": "DORA", "modules": "DORA", "implementedInXBRL": "No", "tables": "B_06.01", "expression": "match({tB_06.01, c0020}[get LES], \"^[A-Z0-9]{18}[0-9]{2}$\")", "preconditionExpression": "{v_B_06.01} ", "startReleaseCode": 4, "endReleaseCode": "", "severity": "warning", "status": "inactive ", "description": "To be deactivated in March 2025"}]