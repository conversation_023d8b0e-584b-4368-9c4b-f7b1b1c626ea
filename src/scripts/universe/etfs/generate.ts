import * as fs from "fs";
import { writeFile } from "fs/promises";
import { currenciesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import CurrencyUtil from "../../../utils/currencyUtil";
import {
  ARRAY_FIELDS,
  ASSET_CLASS_MAPPING,
  BOND_CATEGORY_MAPPING,
  CSV_HEADER_TO_KEY_MAPPINGS,
  ETFCSVHeaderType,
  INCOME_TYPE_MAPPING,
  REQUIRED_HEADERS,
  SECTOR_MAPPING
} from "./types";
import { slugify } from "../../../utils/stringUtil";

const { CURRENCY_SYMBOLS } = currenciesConfig;
const { ASSET_PROVIDER_CONFIG } = investmentUniverseConfig;

// eslint-disable-next-line @typescript-eslint/no-var-requires
require("dotenv").config({ path: "dev.env" });

/**
 * Maps a provider display name to its corresponding key from ASSET_PROVIDER_CONFIG.
 * Returns the key if found, otherwise returns null and logs a warning.
 *
 * @param displayName - The provider display name from the CSV
 * @returns The provider key or null if not found
 */
function mapProviderToKey(displayName: string): string | null {
  const normalizedName = displayName.trim();

  // Find the provider config entry that matches this display name
  const providerEntry = Object.entries(ASSET_PROVIDER_CONFIG).find(
    ([, config]) => config.displayName.toLowerCase() === normalizedName.toLowerCase()
  );

  if (providerEntry) {
    return providerEntry[0]; // Return the key
  }

  console.warn(`⚠️ Provider "${displayName}" not found in ASSET_PROVIDER_CONFIG`);
  return null;
}

/**
 * Validates that all required headers are present in the CSV file.
 * Throws an error if any required headers are missing.
 * Logs information about any extra headers found (these will be ignored).
 *
 * @param headers - Array of header strings from the CSV file
 * @throws Error if any required headers are missing
 */
function validateHeaders(headers: string[]): void {
  const trimmedHeaders = headers.map((h) => h.trim());
  const missingHeaders = REQUIRED_HEADERS.filter((required) => !trimmedHeaders.includes(required));

  if (missingHeaders.length > 0) {
    throw new Error(`Missing required headers in CSV: ${missingHeaders.join(", ")}`);
  }

  console.log("✓ All required headers are present");

  // Log any extra headers (informational only)
  const extraHeaders = trimmedHeaders.filter((header) => !REQUIRED_HEADERS.includes(header as ETFCSVHeaderType));
  if (extraHeaders.length > 0) {
    console.log(`ℹ Extra headers found (will be ignored): ${extraHeaders.join(", ")}`);
  }
}

/**
 * Generates a unique keyname for an ETF configuration.
 * First checks if a keyname already exists in ASSET_CONFIG based on ticker and exchange.
 * If not found, generates a new keyname by combining asset class and simple name,
 * then ensures uniqueness by adding a suffix if needed.
 *
 * @param config - The ETF configuration object containing ticker, exchange, and other details
 * @param existingKeyNames - Array of keynames that are already in use
 * @returns A unique keyname string for the ETF
 */
function generateKeyName(config: any, existingKeyNames: string[]): string {
  const { ASSET_CONFIG } = investmentUniverseConfig;

  // Check if keyName already exists in ASSET_CONFIG
  let foundKey = null;
  for (const [key, asset] of Object.entries(ASSET_CONFIG)) {
    if (asset.formalTicker === config.formalTicker && asset.formalExchange === config.formalExchange) {
      foundKey = key;
      // Check if ISIN has changed
      if (asset.isin !== config.isin) {
        console.warn(`⚠️ ISIN changed for ${config.formalTicker} (${config.formalExchange}):`);
        console.warn(`   Old ISIN: ${asset.isin}`);
        console.warn(`   New ISIN: ${config.isin}`);
      }
      break;
    }
  }

  if (foundKey) {
    return foundKey;
  }

  // Generate new keyName
  let baseName = config.assetClass.concat("_").concat(config.simpleName.replace(/UCITS ETF/gi, "").trim());
  baseName = baseName.replace(/\s+/g, " ");
  const keyName = slugify(baseName).replace(/-/g, "_");

  // Ensure uniqueness
  let suffix = 0;
  let uniqueKeyName = keyName;
  while (existingKeyNames.includes(uniqueKeyName) || Object.keys(ASSET_CONFIG).includes(uniqueKeyName)) {
    suffix += 1;
    uniqueKeyName = `${keyName}_${suffix}`;
  }

  return uniqueKeyName;
}

async function printETFsFromCSV(): Promise<void> {
  const csv = fs.readFileSync(`./src/scripts/universe/etfs/universe.csv`, { encoding: "utf8" });
  const lines = csv.split("\n").filter((line) => line.trim() !== "");
  const headers = lines[0].split(",");

  // Validate headers
  validateHeaders(headers);

  interface ETFConfig {
    keyName: string;
    formalTicker: string;
    formalExchange: string;
    assetClass: string;
    sorting?: number;
    [key: string]: any;
  }

  const euResult: ETFConfig[] = [];
  const ukResult: ETFConfig[] = [];

  for (let i = 1; i < lines.length; i++) {
    const config: any = {};
    const currentLine = lines[i].split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);

    console.log(`Processing line ${i}: ${currentLine[0]}`);

    // Find header indices
    const headerIndices: Record<string, number> = {};
    headers.forEach((header, index) => {
      const trimmedHeader = header.trim();
      if (REQUIRED_HEADERS.includes(trimmedHeader as ETFCSVHeaderType)) {
        headerIndices[trimmedHeader] = index;
      }
    });

    for (const header of REQUIRED_HEADERS) {
      const index = headerIndices[header];
      if (index === undefined) continue;

      const value = currentLine[index];
      if (!value) continue;

      const mappedKey = CSV_HEADER_TO_KEY_MAPPINGS[header];

      if (ARRAY_FIELDS.includes(header)) {
        config[mappedKey] = value
          .trim()
          .replace(/"+/g, "")
          .split(",")
          .map((element) => element.trim());
      } else if (mappedKey === "sorting") {
        config[mappedKey] = parseInt(value.trim().replace(/"+/g, ""), 10);
      } else {
        config[mappedKey] = value.trim().replace(/"+/g, "");
      }
    }

    // Skip if no General ID is present
    if (!config.sorting) {
      console.log(`Skipping row ${i}: No General ID present`);
      continue;
    }

    if (config.assetClass) {
      config.assetClass = ASSET_CLASS_MAPPING[config.assetClass];
    }

    if (config.income) {
      config.income = INCOME_TYPE_MAPPING[config.income];
    }

    // Map provider name to key
    if (config.provider) {
      const providerKey = mapProviderToKey(config.provider);
      if (providerKey) {
        config.provider = providerKey;
      }
    }

    // Handle sector and bondCategory based on asset class and main theme
    const mainThemeIndex = headerIndices["Main Theme"];
    if (mainThemeIndex !== undefined) {
      const mainTheme = currentLine[mainThemeIndex]?.trim().replace(/"+/g, "");

      // Only add sector for equities and readyMade
      if (config.assetClass === "equities" || config.assetClass === "readyMade") {
        if (mainTheme && SECTOR_MAPPING[mainTheme]) {
          config.sector = SECTOR_MAPPING[mainTheme];
        } else {
          config.sector = "general"; // default sector
        }
      }

      // Add bondCategory for bonds
      if (config.assetClass === "bonds" && mainTheme && BOND_CATEGORY_MAPPING[mainTheme]) {
        config.bondCategory = BOND_CATEGORY_MAPPING[mainTheme];
      }
    }

    config.tickerWithCurrency = `${CURRENCY_SYMBOLS[CurrencyUtil.getMainCurrency(config.tradedCurrency)]}${config.formalTicker}`;
    config.category = "etf";
    config.tags = ["fractional"];

    const allKeyNames = [...euResult.map((c) => c.keyName), ...ukResult.map((c) => c.keyName)];
    config.keyName = generateKeyName(config, allKeyNames);

    // Sort into EU or UK based on Entity field
    const entity = config.entity?.toLowerCase() || "";
    if (entity.includes("uk")) {
      // Remove entity key before pushing to result
      const configWithoutEntity = { ...config };
      delete configWithoutEntity.entity;
      ukResult.push(configWithoutEntity);
    }

    if (entity.includes("eu")) {
      // Remove entity key before pushing to result
      const configWithoutEntity = { ...config };
      delete configWithoutEntity.entity;
      euResult.push(configWithoutEntity);
    }
  }

  // Create asset configs for EU and UK
  const euOnlyAssetConfig = Object.fromEntries(
    euResult
      .filter((euConfig) => !ukResult.some((ukConfig) => ukConfig.formalTicker === euConfig.formalTicker))
      .map((config) => {
        const { keyName, ...configWithoutKeyname } = config;
        return [keyName, configWithoutKeyname];
      })
  );

  const ukOnlyAssetConfig = Object.fromEntries(
    ukResult
      .filter((ukConfig) => !euResult.some((euConfig) => euConfig.formalTicker === ukConfig.formalTicker))
      .map((config) => {
        const { keyName, ...configWithoutKeyname } = config;
        return [keyName, configWithoutKeyname];
      })
  );

  // Create global asset config (only ETFs present in both EU and UK)
  const globalAssetConfig = Object.fromEntries(
    euResult
      .filter((euConfig) => ukResult.some((ukConfig) => ukConfig.formalTicker === euConfig.formalTicker))
      .map((config) => {
        const { keyName, ...configWithoutKeyname } = config;
        return [keyName, configWithoutKeyname];
      })
  );

  // Write asset config files
  await writeFile("assetConfig.json", JSON.stringify(globalAssetConfig, null, 2), "utf8");
  await writeFile("assetConfig-EU.json", JSON.stringify(euOnlyAssetConfig, null, 2), "utf8");
  await writeFile("assetConfig-UK.json", JSON.stringify(ukOnlyAssetConfig, null, 2), "utf8");

  // Write single assets file containing all assets
  const allAssets = [...new Set([...euResult, ...ukResult])].map((config) => config.keyName);
  await writeFile("assets.json", JSON.stringify(allAssets), "utf8");

  // Create combined statistics files (keeping original functionality)
  const allResults = [...new Set([...euResult, ...ukResult])];

  // ASSETS_SERVICES_MAPPING
  await writeFile(
    "assetCommonIdToTicker.json",
    JSON.stringify(
      Object.fromEntries(
        allResults.map((config) => {
          return [config.keyName, config.formalTicker];
        })
      ),
      null,
      2
    ),
    "utf8"
  );

  // ASSETS_CONFIG
  await writeFile(
    "assetStatisticsUniverse.json",
    JSON.stringify(
      Object.fromEntries(
        allResults.map((config) => {
          return [
            config.formalTicker,
            {
              ticker: config.formalTicker,
              exchange: config.formalExchange,
              asset_class: config.assetClass
            }
          ];
        })
      ),
      null,
      2
    ),
    "utf8"
  );

  console.log(`\n✓ Successfully processed ${euResult.length} EU ETFs and ${ukResult.length} UK ETFs`);
}

(async () => {
  try {
    await printETFsFromCSV();
    console.log("\n✓ Success - All files generated");
    process.exit(0);
  } catch (err) {
    console.error("\n✗ Error:", err);
    process.exit(1);
  }
})();
