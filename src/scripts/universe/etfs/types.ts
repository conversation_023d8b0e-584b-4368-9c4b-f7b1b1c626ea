export type ETFCSVHeaderType =
  | "Entity"
  | "General ID"
  | "Asset class"
  | "Main Theme"
  | "Formal Product Name"
  | "Issuer"
  | "App Name"
  | "ISIN"
  | "Ticker"
  | "Exchange"
  | "Quoted Currency"
  | "Base Currency"
  | "Use of Income"
  | "Replication"
  | "Index"
  | "Short Description"
  | "About"
  | "Fundamental Ticker"
  | "Historical Price Ticker"
  | "Search Terms"
  | "Similar Companies"
  | "KID";

export const ARRAY_FIELDS: ETFCSVHeaderType[] = ["Similar Companies", "Search Terms"];

export const CSV_HEADER_TO_KEY_MAPPINGS: Record<ETFCSVHeaderType, string> = {
  Entity: "entity",
  "General ID": "sorting",
  "Asset class": "assetClass",
  "Main Theme": "sector",
  "Formal Product Name": "formalName",
  Issuer: "provider",
  "App Name": "simpleName",
  ISIN: "isin",
  Ticker: "formalTicker",
  Exchange: "formalExchange",
  "Quoted Currency": "tradedCurrency",
  "Base Currency": "baseCurrency",
  "Use of Income": "income",
  Replication: "replication",
  Index: "index",
  "Short Description": "shortDescription",
  About: "about",
  "Fundamental Ticker": "fundamentalsTicker",
  "Historical Price Ticker": "historicalPriceTicker",
  "Search Terms": "searchTerms",
  "Similar Companies": "similarCompanies",
  KID: "kid"
};

// Expected headers that must be present in the CSV
export const REQUIRED_HEADERS: ETFCSVHeaderType[] = [
  "Entity",
  "General ID",
  "Asset class",
  "Main Theme",
  "Formal Product Name",
  "Issuer",
  "App Name",
  "ISIN",
  "Ticker",
  "Exchange",
  "Quoted Currency",
  "Base Currency",
  "Use of Income",
  "Replication",
  "Index",
  "Short Description",
  "About",
  "Fundamental Ticker",
  "Historical Price Ticker",
  "Search Terms",
  "Similar Companies",
  "KID"
];

// Asset class mapping to camelCase
export const ASSET_CLASS_MAPPING: Record<string, string> = {
  Equities: "equities",
  Bonds: "bonds",
  Commodities: "commodities",
  "Real Estate": "realEstate",
  "Ready-Made": "readyMade",
  MMF: "mmf"
};

// Income type mapping
export const INCOME_TYPE_MAPPING: Record<string, string> = {
  ACC: "Accumulating",
  DIST: "Distributing"
};

// Sector mapping based on InvestmentSectorArrayConst
export const SECTOR_MAPPING: Record<string, string> = {
  // Direct mappings
  Broad: "general",
  Tech: "technology",
  Healthcare: "healthcare",
  Consumer: "consumer",
  Energy: "energy",
  Communication: "communication",
  Financials: "financials",
  Industrials: "industrials",
  Utilities: "utilities",
  Materials: "materials"
};

// Bond category mapping
export const BOND_CATEGORY_MAPPING: Record<string, string> = {
  Corporate: "corporate",
  Government: "government"
};
