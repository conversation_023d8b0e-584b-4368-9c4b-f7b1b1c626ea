/**
 * convert-data-to-xml.ts
 *
 * CLI helper that converts the Wealthyhood accounting JSONC export
 * (end‑to‑end‑data.jsonc) into Epsilon "Extra Λογιστική Διαχείριση"
 * XML, following the ARTICLE & DETAIL mapping documented in PRD_EPSILON.md.
 *
 * Usage:
 *   npx ts-node convert-data-to-xml.ts path/to/input.jsonc path/to/out.xml
 *
 * Notes on the mapping implemented here (2025‑06‑05):
 *   • Produces both ACCOUNTS and ARTICLES sections
 *   • ACCOUNTS: Required fields with constants per PRD
 *   • ARTICLES: Transaction details as before
 *   • Constant fields:
 *       DGRS  = "0"           (General Ledger)
 *       MTYPE = "11"          (Wealthyhood hard‑coded movement type)
 *       ISKEPYO / ISAGRYP = "0"
 *   • All KEPYO / retail / cancellation related fields are omitted.
 *   • INVOICE      → reference_number
 *   • MDATE        → article_date (current date in dd/MM/yyyy format)
 *   • DETAIL nodes inherit:
 *       LCODE  → account_code (from entries.account)
 *       CRDB   → side (0 = Debit, 1 = Credit, from entries.type)
 *       AMOUNT → amount.toFixed(2) with decimal comma (from entries.amount)
 *       INVOICE/REASON as above
 */

import { readFileSync, writeFileSync } from "fs";
import { AccountingReportingService } from "../../services/accountingReportingService";
import { AccountingLedgerEntry } from "../../external-services/accountingLedgerStorageService";

/**
 * Simple function to strip JSON comments
 */
function stripJsonComments(str: string): string {
  // Remove single line comments (// ...)
  str = str.replace(/\/\/.*$/gm, "");

  // Remove multi-line comments (/* ... */)
  str = str.replace(/\/\*[\s\S]*?\*\//g, "");

  // Remove trailing commas before closing brackets/braces
  str = str.replace(/,(\s*[}\]])/g, "$1");

  return str;
}

type JsonRule = {
  account: string;
  amount: number;
  type: "debit" | "credit";
};

type JsonEntry = {
  description: string; // Format: "user_id | transaction_id | transaction_type"
  reference_number?: string; // Optional reference number for asset buy/sell transactions
  rules: JsonRule[];
};

function generateXmlFromJsonEntries(jsonEntries: any[]): string {
  const ledgerEntries: AccountingLedgerEntry[] = [];
  let currentAa = 1;

  jsonEntries.forEach((entry: any) => {
    entry.rules.forEach((rule: any) => {
      ledgerEntries.push({
        aa: currentAa,
        account_code: rule.account,
        side: rule.type,
        amount: rule.amount,
        article_date: new Date().toISOString().split("T")[0],
        description: entry.description,
        reference_number: entry.reference_number || undefined
      });
    });
    currentAa++;
  });

  return AccountingReportingService.generateAccountingXml(ledgerEntries);
}

/**
 * Entrypoint
 */
(function main() {
  const [, , inputPath, outputPath] = process.argv;

  if (!inputPath || !outputPath) {
    console.error("Usage: ts-node convert-data-to-xml.ts <input.jsonc> <output.xml>");
    process.exit(1);
  }

  const raw = readFileSync(inputPath, "utf-8");
  const jsonData = JSON.parse(stripJsonComments(raw)) as JsonEntry[] | JsonEntry;

  const entries: JsonEntry[] = Array.isArray(jsonData) ? jsonData : [jsonData];

  const xml = generateXmlFromJsonEntries(entries);

  writeFileSync(outputPath, xml, "utf-8");
  console.log(`✅ Converted ${entries.length} entry(ies) to XML and wrote to ${outputPath}`);
})();
