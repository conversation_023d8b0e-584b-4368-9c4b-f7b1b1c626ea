#!/usr/bin/env node

import { program } from "commander";
import logger from "../../external-services/loggerService";
import { AccountingReportingService } from "../../services/accountingReportingService";
import { captureException } from "@sentry/node";

program
  .name("generate-accounting-report")
  .description("Generate accounting report in Epsilon XML format")
  .option("-d, --date <date>", "Date for the report (YYYY-MM-DD format)", "")
  .parse(process.argv);

const options = program.opts();

async function main() {
  try {
    let reportDate: Date;

    if (options.date) {
      // Parse the provided date
      const parsedDate = new Date(options.date);
      if (isNaN(parsedDate.getTime())) {
        throw new Error(`Invalid date format: ${options.date}. Please use YYYY-MM-DD format.`);
      }
      reportDate = parsedDate;
    } else {
      // Use today's date
      reportDate = new Date();
    }

    logger.info(`Generating accounting report for date: ${reportDate.toISOString()}`, {
      module: "accountingReportGenerator",
      method: "main",
      data: {
        date: reportDate.toISOString()
      }
    });

    const filePath = await AccountingReportingService.generateXMLAccountingReportFile({
      fromDate: reportDate,
      toDate: reportDate
    });

    if (filePath) {
      logger.info(`✅ Accounting report generated successfully: ${filePath}`, {
        module: "accountingReportGenerator",
        method: "main"
      });
    } else {
      logger.info("ℹ️ No accounting entries found for the specified date", {
        module: "accountingReportGenerator",
        method: "main"
      });
    }

    process.exit(0);
  } catch (error) {
    captureException(error);
    logger.error("❌ Failed to generate accounting report", {
      module: "accountingReportGenerator",
      method: "main",
      data: {
        error
      }
    });
    process.exit(1);
  }
}

main();
