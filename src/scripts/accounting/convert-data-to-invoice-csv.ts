/**
 * convert-data-to-invoice-csv.ts
 *
 * CL<PERSON> helper that converts the Wealthyhood accounting JSONC export
 * (end‑to‑end‑data.jsonc) into an invoice CSV format.
 *
 * Usage:
 *   npx ts-node convert-data-to-invoice-csv.ts path/to/input.jsonc path/to/out.csv
 *
 * The CSV output includes the following columns:
 *   - Reference ID, Order ID, Updated at (date), Executed at (date)
 *   - User ID, User Name, Email, ISIN, Symbol, Exchange
 *   - Order Type, Settlement Currency, Side, Quantity, Amount
 *   - Unit Price, FX Rate, FX Fee Amount, Commission Fee Amount
 *
 * Note: This script generates mock invoice data based on the JSONC entries
 * since the actual invoice generation requires database queries.
 */

import { readFileSync, writeFileSync } from "fs";
import mongoose from "mongoose";
import { AccountingReportingService } from "../../services/accountingReportingService";
import { InvoiceReferenceNumberDocument } from "../../models/InvoiceReferenceNumber";
import { OrderDocument } from "../../models/Order";
import { UserDocument } from "../../models/User";
import { ProviderEnum } from "../../configs/providersConfig";
import { OrderSubmissionIntentEnum } from "../../models/Order";

/**
 * Simple function to strip JSON comments
 */
function stripJsonComments(str: string): string {
  // Remove single line comments (// ...)
  str = str.replace(/\/\/.*$/gm, "");

  // Remove multi-line comments (/* ... */)
  str = str.replace(/\/\*[\s\S]*?\*\//g, "");

  // Remove trailing commas before closing brackets/braces
  str = str.replace(/,(\s*[}\]])/g, "$1");

  return str;
}

type JsonRule = {
  account: string;
  amount: number;
  type: "debit" | "credit";
};

type JsonEntry = {
  description: string; // Format: "user_id | transaction_id | transaction_type"
  reference_number?: string; // Optional reference number for asset buy/sell transactions
  rules: JsonRule[];
};

function createMockInvoiceReferenceNumbers(entries: JsonEntry[]): InvoiceReferenceNumberDocument[] {
  const mockInvoiceRefs: InvoiceReferenceNumberDocument[] = [];
  let invoiceCounter = 1;

  entries.forEach((entry) => {
    // Parse description to extract user_id, transaction_id, and transaction_type
    const descParts = entry.description.split(" | ");
    const userId = descParts[0] || "";
    const transactionId = descParts[1] || "";
    const transactionType = descParts[3] || "";

    // Only process asset buy/sell transactions
    if ((transactionType === "asset buy" || transactionType === "asset sell") && entry.reference_number) {
      // Extract ISIN from description if present
      const isin = descParts[2] || "";

      // Calculate total amount from rules
      let totalAmount = 0;
      let commissionFee = 0;

      entry.rules.forEach((rule) => {
        // Identify commission fees
        if (rule.account === "73-00-00-0000" && rule.type === "credit") {
          commissionFee = rule.amount;
        }
        // Calculate main transaction amount (asset buy/sell)
        if (rule.account === "01-99-00-0000" || rule.account === "05-99-00-0000") {
          if (
            (transactionType === "asset buy" && rule.type === "debit" && rule.account === "01-99-00-0000") ||
            (transactionType === "asset sell" && rule.type === "debit" && rule.account === "05-99-00-0000")
          ) {
            totalAmount = rule.amount;
          }
        }
      });

      // Create mock User document
      const mockUser = {
        _id: new mongoose.Types.ObjectId(),
        email: `user${userId}@example.com`,
        firstName: "User",
        lastName: userId
      } as UserDocument;

      // Create mock Order document
      const mockOrder = {
        _id: new mongoose.Types.ObjectId(),
        isin: isin,
        side: transactionType === "asset buy" ? "buy" : "sell",
        quantity: totalAmount > 50 ? 1 : 10,
        consideration: {
          amount: Math.round(totalAmount * 100), // Convert to cents
          currency: "EUR"
        },
        unitPrice: {
          amount: totalAmount > 50 ? totalAmount : totalAmount / 10,
          currency: "EUR"
        },
        exchangeRate: 1.0,
        fees: {
          fx: { amount: 0, currency: "EUR" },
          commission: { amount: commissionFee, currency: "EUR" }
        },
        settlementCurrency: "EUR",
        updatedAt: new Date(),
        filledAt: new Date(),
        submissionIntent: OrderSubmissionIntentEnum.AGGREGATE,
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        providers: {
          wealthkernel: {
            id: `WK-${transactionId}`,
            status: "MATCHED",
            submittedAt: new Date()
          }
        },
        transaction: {
          owner: mockUser._id
        }
      } as unknown as OrderDocument;

      // Create mock InvoiceReferenceNumber document
      const mockInvoiceRef = {
        _id: new mongoose.Types.ObjectId(),
        invoiceId: invoiceCounter,
        linkedDocumentId: mockOrder._id,
        sourceDocumentType: "Order",
        createdAt: new Date(),
        updatedAt: new Date(),
        linkedOrder: mockOrder
      } as InvoiceReferenceNumberDocument;

      mockInvoiceRefs.push(mockInvoiceRef);
      invoiceCounter++;
    }
  });

  return mockInvoiceRefs;
}

/**
 * Entrypoint
 */
(async function main() {
  const [, , inputPath, outputPath] = process.argv;

  if (!inputPath || !outputPath) {
    console.error("Usage: ts-node convert-data-to-invoice-csv.ts <input.jsonc> <output.csv>");
    process.exit(1);
  }

  const raw = readFileSync(inputPath, "utf-8");
  const jsonData = JSON.parse(stripJsonComments(raw)) as JsonEntry[];
  const entries: JsonEntry[] = jsonData;

  // Create mock InvoiceReferenceNumber documents from asset buy/sell transactions
  const mockInvoiceReferenceNumbers = createMockInvoiceReferenceNumbers(entries);

  // Generate CSV using the actual AccountingReportingService method
  const csv = await AccountingReportingService.generateInvoiceCsv(mockInvoiceReferenceNumbers);

  writeFileSync(outputPath, csv, "utf-8");
  console.log(
    `✅ Generated invoice CSV with ${mockInvoiceReferenceNumbers.length} entries and wrote to ${outputPath}`
  );
})();
