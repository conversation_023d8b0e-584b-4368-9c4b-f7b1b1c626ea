/**
 * convert-data-to-csv.ts
 *
 * CLI helper that converts the Wealthyhood accounting JSONC export
 * (end‑to‑end‑data.jsonc) into CSV format using the generateAccountingCsv
 * method from AccountingReportingService.
 *
 * Usage:
 *   npx ts-node convert-data-to-csv.ts path/to/input.jsonc path/to/out.csv
 *
 * The CSV output includes the following columns:
 *   - <PERSON>GR<PERSON>, MTYPE, ISKEPYO, ISAGRYP, CUSTID, INVOICE, MDATE, REASON
 *   - KEPYOAMT, ISBUILD, INBRCODE, SUMKEPYOYP, SUMKEPYONOTYP, SUMKEPYOFPA
 *   - OTHEREXPEND, CASHREGISTERID, HASRETAILID, CA<PERSON><PERSON>ED, CANCELGROUPID
 *   - ART39BVAT, UID, LCODE, CRDB, AMOUNT, DETAIL_INVOICE, DETAIL_REASON
 *   - DETAIL_ISAGRYP, KEPYOPARTY
 */

import { readFileSync, writeFileSync } from "fs";
import { AccountingReportingService } from "../../services/accountingReportingService";
import { AccountingLedgerEntry } from "../../external-services/accountingLedgerStorageService";

/**
 * Simple function to strip JSON comments
 */
function stripJsonComments(str: string): string {
  // Remove single line comments (// ...)
  str = str.replace(/\/\/.*$/gm, "");

  // Remove multi-line comments (/* ... */)
  str = str.replace(/\/\*[\s\S]*?\*\//g, "");

  // Remove trailing commas before closing brackets/braces
  str = str.replace(/,(\s*[}\]])/g, "$1");

  return str;
}

type JsonRule = {
  account: string;
  amount: number;
  type: "debit" | "credit";
};

type JsonEntry = {
  description: string; // Format: "user_id | transaction_id | transaction_type"
  reference_number?: string; // Optional reference number for asset buy/sell transactions
  rules: JsonRule[];
};

function generateCsvFromJsonEntries(jsonEntries: any[]): string {
  const ledgerEntries: AccountingLedgerEntry[] = [];
  let currentAa = 1;

  jsonEntries.forEach((entry: any) => {
    entry.rules.forEach((rule: any) => {
      ledgerEntries.push({
        aa: currentAa,
        account_code: rule.account,
        side: rule.type,
        amount: rule.amount,
        article_date: new Date().toISOString().split("T")[0],
        description: entry.description,
        reference_number: entry.reference_number || undefined
      });
    });
    currentAa++;
  });

  return AccountingReportingService.generateAccountingCsv(ledgerEntries);
}

/**
 * Entrypoint
 */
(function main() {
  const [, , inputPath, outputPath] = process.argv;

  if (!inputPath || !outputPath) {
    console.error("Usage: ts-node convert-data-to-csv.ts <input.jsonc> <output.csv>");
    process.exit(1);
  }

  const raw = readFileSync(inputPath, "utf-8");
  const jsonData = JSON.parse(stripJsonComments(raw)) as JsonEntry[] | JsonEntry;

  const entries: JsonEntry[] = Array.isArray(jsonData) ? jsonData : [jsonData];

  console.log("generating csv from entries");
  console.log("entries", entries);

  const csv = generateCsvFromJsonEntries(entries);

  writeFileSync(outputPath, csv, "utf-8");
  console.log(`✅ Converted ${entries.length} entry(ies) to CSV and wrote to ${outputPath}`);
  process.exit(0);
})();
