# Accounting Reporting Service

This service generates daily accounting reports in Epsilon XML format for external accounting system integration.

## Overview

The Accounting Reporting Service:
- Retrieves accounting ledger entries from the database for a specified date
- Groups entries by accounting article (aa field)
- Generates XML files following the Epsilon "Extra Λογιστική Διαχείριση" format
- Outputs files with the naming convention: `accounting_report_DD-MM-YYYY.xml`

## Usage

### Automatic Daily Generation

The service runs automatically every day via a cron job. The generated reports are saved to the `output/accounting/` directory.

### Manual Generation

You can manually generate reports using the CLI script:

```bash
# Generate today's report
npm run script:accounting:report

# Generate report for a specific date
npm run script:accounting:report -- --date 2025-06-08

# Using ts-node directly
npx ts-node src/scripts/accounting/accountingReportGenerator.ts --date 2025-06-08
```

### Output

Reports are saved to `output/accounting/` with filenames like:
- `accounting_report_09-06-2025.xml`

## XML Format

The generated XML follows the Epsilon format with two main sections:

### ARTICLES Section
Contains transaction details grouped by accounting article:
- MTYPE: Movement type (constant: 11)
- INVOICE: Reference number (if available)
- MDATE: Posting date in dd/MM/yyyy format
- REASON: Transaction description
- DETAILS: Individual ledger entries with account codes, debit/credit indicators, and amounts

### ACCOUNTS Section
Contains unique account definitions:
- LCODE: Account code
- LDESC: Account description
- ISTRN: Transaction flag (constant: 1)
- ACTYPE: Account type (constant: 9)

## Configuration

- **Output Directory**: Default is `output/accounting/`, can be customized via options
