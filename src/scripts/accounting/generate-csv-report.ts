import { AccountingReportingService } from "../../services/accountingReportingService";
import logger from "../../external-services/loggerService";
import DateUtil from "../../utils/dateUtil";

async function generateCsvReport() {
  try {
    // Generate CSV report for today
    const filePath = await AccountingReportingService.generateCSVAccountingReportFile({
      fromDate: DateUtil.getStartOfDay(new Date()),
      toDate: DateUtil.getEndOfDay(new Date()),
      outputDir: "output/accounting/csv"
    });

    if (filePath) {
      logger.info(`CSV report generated successfully at: ${filePath}`);
    } else {
      logger.info("No entries found for the specified date");
    }
  } catch (error) {
    logger.error("Failed to generate CSV report", error);
    process.exit(1);
  }
}

// Run the script
generateCsvReport();
