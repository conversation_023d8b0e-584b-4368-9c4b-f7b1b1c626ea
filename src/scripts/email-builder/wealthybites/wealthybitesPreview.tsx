import type { WealthybitesEmailBody } from "types/wealthybites";
import * as fs from "fs";
import * as path from "path";
import { WealthybitesEmail } from "../../../emails/templates/wealthybites/index";

const previewData: WealthybitesEmailBody = JSON.parse(
  fs.readFileSync(path.resolve(__dirname, "./wealthybitesProps.json"), "utf-8")
);

export default function WealthybitesPreview() {
  const {
    firstName,
    userLocale,
    userCurrency,
    userEmail,
    investmentSnapshot,
    topPortfolioMovers,
    marketRoundup,
    topMarketIndexes,
    topNews,
    analystInsights,
    learningGuide
  } = previewData;
  return (
    <WealthybitesEmail
      firstName={firstName}
      userLocale={userLocale}
      userCurrency={userCurrency}
      userEmail={userEmail}
      investmentSnapshot={investmentSnapshot!}
      topPortfolioMovers={topPortfolioMovers!}
      marketRoundup={marketRoundup}
      topMarketIndexes={topMarketIndexes}
      topNews={topNews}
      analystInsights={analystInsights}
      learningGuide={learningGuide}
    />
  );
}
