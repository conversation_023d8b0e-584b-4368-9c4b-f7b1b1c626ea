import mongoose, { Document } from "mongoose";
import { UserDocument } from "./User";
import { BankAccountDocument } from "./BankAccount";
import {
  WealthkernelMandateStatusArray,
  WealthkernelMandateStatusType
} from "../external-services/wealthkernelService";
import { ProviderEnum } from "../configs/providersConfig";
import {
  MandateStatusArray as GoCardlessMandateStatusArray,
  MandateStatusType as GoCardlessMandateStatusType
} from "../external-services/goCardlessPaymentsService";

export const MandateStatusArray = ["Pending", "Active", "Inactive"] as const;
export type MandateStatusType = (typeof MandateStatusArray)[number];

export const MandateCategoryArray = ["Subscription", "Top-Up"] as const;
export type MandateCategoryType = (typeof MandateCategoryArray)[number];

/**
 * INTERFACES
 */
export interface MandateDTOInterface {
  owner: mongoose.Types.ObjectId;
  bankAccount: mongoose.Types.ObjectId;
  category: MandateCategoryType;
  activeProviders: ProviderEnum[];
  providers?: {
    gocardless?: {
      status: GoCardlessMandateStatusType;
      id: string;
    };
    wealthkernel?: {
      status: WealthkernelMandateStatusType;
      id: string;
    };
  };
}

export interface MandateInterface extends Omit<MandateDTOInterface, "owner" | "bankAccount"> {
  createdAt: Date;
  updatedAt: Date;
  owner: mongoose.Types.ObjectId | UserDocument;
  bankAccount: mongoose.Types.ObjectId | BankAccountDocument;

  // VIRTUALS
  readonly status: MandateStatusType;
  readonly isActive: boolean;
}

export interface MandateDocument extends MandateInterface, Document {}

/**
 * SCHEMA
 */

const mandateSchema = new mongoose.Schema(
  {
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: "User"
    },
    bankAccount: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: "BankAccount"
    },
    category: {
      type: String,
      enum: MandateCategoryArray
    },
    activeProviders: { type: Object.values(ProviderEnum), required: true },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.WEALTHKERNEL]: {
          _id: false,
          id: { type: String },
          status: { type: String, enum: WealthkernelMandateStatusArray }
        },
        [ProviderEnum.GOCARDLESS]: {
          _id: false,
          id: { type: String },
          status: { type: String, enum: GoCardlessMandateStatusArray }
        }
      }
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

/**
 * INDEXES
 */
mandateSchema.index({ owner: 1, category: 1 });

/**
 * VIRTUALS
 */
mandateSchema.virtual("isActive").get(function (): boolean {
  return (this as MandateDocument).status === "Active";
});

mandateSchema.virtual("status").get(function (): MandateStatusType {
  if (!this.providers?.gocardless?.id && !this.providers?.wealthkernel?.id) {
    return "Pending";
  } else if (this.providers?.gocardless?.id) {
    if (
      ["suspended_by_payer", "failed", "cancelled", "expired", "consumed", "blocked"].includes(
        this.providers?.gocardless?.status
      )
    ) {
      return "Inactive";
    } else if (this.providers?.gocardless?.status === "active") {
      return "Active";
    } else return "Pending";
  } else if (this.providers?.wealthkernel?.id) {
    if (["Cancelled", "Failed"].includes(this.providers?.wealthkernel?.status)) {
      return "Inactive";
    } else if (this.providers?.wealthkernel?.status === "Active") {
      return "Active";
    } else return "Pending";
  }
});

export const Mandate = mongoose.model<MandateDocument>("Mandate", mandateSchema);
