import mongoose, { Document, Schema } from "mongoose";
import { UserDocument } from "./User";
import { MoneyAmount } from "amounts";
import { currenciesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { moneySchema } from "./common/money";
import { SentimentScoreType } from "../services/dailySummarySnapshotService";
import { envIsDev } from "../utils/environmentUtil";

const { MainCurrencies } = currenciesConfig;

/**
 * ENUMS
 */
export enum IndividualSentimentScoreComponentEnum {
  NEWS = "news",
  ANALYST = "analyst",
  PRICE_MOMENTUM = "priceMomentum"
}

export enum TotalSentimentScoreComponentEnum {
  TOTAL = "total"
}

export enum DailySummarySnapshotPopulationFieldsEnum {
  OWNER = "metadata.owner"
}

/**
 * TYPES
 */
type DailySummarySnapshotMetadataType = {
  owner: mongoose.Types.ObjectId | UserDocument;
};

type RichHoldingType = {
  assetId: investmentUniverseConfig.AssetType;
  quantity: number;
  dailyReturnPercentage: number;

  // The below two values do not exist for backfilled data.
  latestPrice?: MoneyAmount;
  holdingWeightPercentage?: number;
};

type RichHoldingsType = RichHoldingType[];

type SnapshotPortfolioComponentType = {
  value: MoneyAmount;
};

export type HoldingsSnapshotPortfolioComponentType = SnapshotPortfolioComponentType & {
  dailyUpBy?: number;
  dailyReturnPercentage?: number;
  assets: RichHoldingsType;
};

type SavingsSnapshotPortfolioComponentType = SnapshotPortfolioComponentType & {
  unrealisedInterest?: MoneyAmount;
};

type PortfolioSnapshotType = {
  cash: SnapshotPortfolioComponentType;
  savings: SavingsSnapshotPortfolioComponentType;
  total: SnapshotPortfolioComponentType;
  holdings: HoldingsSnapshotPortfolioComponentType;
};

/**
 * DTO INTERFACES
 */
export interface DailySummarySnapshotDTOInterface {
  metadata: DailySummarySnapshotMetadataType;
  date: Date;
  portfolio: PortfolioSnapshotType;
  sentimentScore: SentimentScoreType;
  isBackfilled?: boolean;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface DailySummarySnapshotInterface extends DailySummarySnapshotDTOInterface {}

/**
 * DOCUMENTS
 */
export interface DailySummarySnapshotDocument extends DailySummarySnapshotInterface, Document {}

/**
 * SCHEMAS
 */
const holdingSchema: Schema = new mongoose.Schema(
  {
    assetId: {
      type: String,
      enum: investmentUniverseConfig.AssetArrayConst
    },
    latestPrice: {
      amount: {
        type: Number
      },
      currency: {
        type: String,
        enum: MainCurrencies
      }
    },
    holdingWeightPercentage: Number,
    dailyReturnPercentage: Number,
    quantity: Number
  },
  { _id: false, toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

export const snapshotPortfolioSchema: Schema = new mongoose.Schema(
  {
    cash: {
      value: moneySchema
    },
    savings: {
      value: moneySchema,
      unrealisedInterest: moneySchema
    },
    holdings: {
      assets: [holdingSchema],
      value: moneySchema,
      dailyUpBy: Number,
      dailyReturnPercentage: Number
    },
    total: {
      value: moneySchema
    }
  },
  { _id: false }
);

const dailySummarySnapshotSchema: Schema = new mongoose.Schema(
  {
    metadata: {
      owner: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: true
      }
    },
    date: {
      type: Date,
      required: true
    },
    portfolio: snapshotPortfolioSchema,
    sentimentScore: {
      [TotalSentimentScoreComponentEnum.TOTAL]: Number,
      [IndividualSentimentScoreComponentEnum.NEWS]: Number,
      [IndividualSentimentScoreComponentEnum.ANALYST]: Number,
      [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: Number
    },
    isBackfilled: Boolean
  },
  {
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
    timeseries: !envIsDev()
      ? {
          timeField: "date",
          metaField: "metadata",
          granularity: "hours"
        }
      : undefined,
    autoCreate: true,
    timestamps: true
  }
);

dailySummarySnapshotSchema.index({ "metadata.owner": 1, date: 1 });

/**
 * MODELS
 */
export const DailySummarySnapshot = mongoose.model<DailySummarySnapshotDocument>(
  "DailySummarySnapshot",
  dailySummarySnapshotSchema
);
