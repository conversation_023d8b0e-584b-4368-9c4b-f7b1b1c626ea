import mongoose, { Document, Schema } from "mongoose";
// eslint-disable-next-line @typescript-eslint/no-require-imports
const AutoIncrement = require("mongoose-sequence")(mongoose);

export interface InvoiceReferenceNumberInterface {
  invoiceId: number; // This is the auto-incrementing field
  linkedDocumentId: mongoose.Types.ObjectId;
  sourceDocumentType: "Order" | "Reward" | string;
  createdAt?: Date;
  updatedAt?: Date;

  // Virtual fields
  readonly linkedOrder?: any;
  readonly linkedReward?: any;
}

export interface InvoiceReferenceNumberDocument extends InvoiceReferenceNumberInterface, Document {}

const invoiceReferenceNumberSchema: Schema = new mongoose.Schema(
  {
    invoiceId: { type: Number, unique: true }, // Auto-incremented by the plugin
    linkedDocumentId: {
      type: Schema.Types.ObjectId,
      required: true,
      index: true
    },
    sourceDocumentType: {
      type: String,
      required: true,
      index: true
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

// Attach the auto-increment plugin unconditionally
invoiceReferenceNumberSchema.plugin(AutoIncrement, {
  id: "invoice_reference_number_counter", // Counter document name
  inc_field: "invoiceId", // Field to increment
  start_seq: 1
});

// Virtual field for linked Order
invoiceReferenceNumberSchema.virtual("linkedOrder", {
  ref: "Order",
  localField: "linkedDocumentId",
  foreignField: "_id",
  justOne: true
});

// Virtual field for linked Reward
invoiceReferenceNumberSchema.virtual("linkedReward", {
  ref: "Reward",
  localField: "linkedDocumentId",
  foreignField: "_id",
  justOne: true
});

/**
 * INDEXES
 */
// Index for createdAt queries (used in report generation)
invoiceReferenceNumberSchema.index({ createdAt: 1 });

// Compound index for the common query pattern in invoice report generation
// This covers: sourceDocumentType + createdAt + invoiceId (for sorting)
invoiceReferenceNumberSchema.index({ sourceDocumentType: 1, createdAt: 1, invoiceId: 1 });

// Index for incremental processing queries (invoiceId > lastProcessedId)
invoiceReferenceNumberSchema.index({ sourceDocumentType: 1, invoiceId: 1 });

export const InvoiceReferenceNumber = mongoose.model<InvoiceReferenceNumberDocument>(
  "InvoiceReferenceNumber",
  invoiceReferenceNumberSchema
);
