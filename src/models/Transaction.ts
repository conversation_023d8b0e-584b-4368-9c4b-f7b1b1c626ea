// Transaction corresponds not to the Wealthkernel Transactions, but to the individual action that corresponds
// to the user's transaction. That can be a cash transaction (deposit, withdrawal) or an asset transaction
// (buy, sell orders). So the wealthkernel status & id will be the corresponding status & id for deposits & withdrawals.
// For asset transactions we don't keep any wealthkernel info, because it depends on the status of the underlying
// orders. There is only a field indicating whether the asset transaction is settled or not.
import mongoose, { Document, Schema } from "mongoose";
import {
  currenciesConfig,
  investmentUniverseConfig,
  plansConfig,
  savingsUniverseConfig
} from "@wealthyhood/shared-configs";
import { OrderDocument } from "./Order";
import { InitialHoldingsAllocationType, PortfolioDocument } from "./Portfolio";
import { UserDocument } from "./User";
import logger from "../external-services/loggerService";
import {
  FailureStatusArray,
  FailureStatusType,
  PaymentStatusArrayV1,
  PaymentStatusArrayV3,
  PaymentStatusTypeV1,
  PaymentStatusTypeV3,
  TruelayerPaymentVersionArray,
  TruelayerPaymentVersionType
} from "../external-services/truelayerService";
import {
  BonusStatusArray,
  BonusStatusType,
  ChargeStatusArray,
  DepositStatusArray,
  DepositStatusType,
  DirectDebitPaymentStatusArray,
  DirectDebitPaymentStatusType,
  InternalTransferStatusArray,
  TransactionStatusArray as WealthkernelTransactionStatusArray,
  TransactionStatusType as WealthkernelTransactionStatusType,
  WithdrawalRequestArray,
  WithdrawalRequestType,
  WithdrawalStatusArray,
  WithdrawalStatusType
} from "../external-services/wealthkernelService";
import { BankAccountDocument } from "./BankAccount";
import { InvestmentProductDocument } from "./InvestmentProduct";
import { SubscriptionDocument } from "./Subscription";
import { UserDataRequestDocument } from "./UserDataRequest";
import { RewardDocument } from "./Reward";
import { AutomationDocument } from "./Automation";
import { ProviderEnum } from "../configs/providersConfig";
import { PaymentIntentStatusArray, PaymentIntentStatusType } from "../external-services/stripeService";
import { PaymentMethodDocument } from "./PaymentMethod";
import { ForeignCurrencyRatesType } from "currencies";
import Decimal from "decimal.js";
import { InvestmentProductsDictType } from "investmentProducts";
import {
  IncomingPaymentStatusArray,
  IncomingPaymentStatusType,
  OutgoingPaymentStatusArray,
  OutgoingPaymentStatusType
} from "../external-services/devengoService";
import { DepositActionEnum } from "../configs/depositsConfig";
import {
  AggregateExecutionWindowType,
  ExecutionTypeEnum,
  ExecutionWindowsType
} from "../configs/executionWindowConfig";
import {
  PaymentStatusArray as GoCardlessPaymentStatusArray,
  PaymentStatusType as GoCardlessPaymentStatusType
} from "../external-services/goCardlessPaymentsService";
import { PaymentStatusArray, PaymentStatusType } from "../external-services/saltedgeService";
import { ESTIMATED_WORK_DAYS_TO_RECEIVE_FUNDS_AFTER_COLLECTION } from "../configs/directDebitConfig";
import DateUtil from "../utils/dateUtil";
import { StockSplitCorporateEventDocument } from "./CorporateEvent";
import { GiftDocument } from "./Gift";
import { CreditTicketDocument } from "./CreditTicket";
import DbUtil from "../utils/dbUtil";
import { AccountingService } from "../services/accountingService";

const { MainCurrencies } = currenciesConfig;
const { AssetArrayConst } = investmentUniverseConfig;
const { SavingsProductArray, SAVINGS_PRODUCT_CONFIG_GLOBAL } = savingsUniverseConfig;

/**
 * ENUMS
 */
export enum TransactionPopulationFieldsEnum {
  CASHBACK = "cashback",
  ORDERS = "orders",
  OWNER = "owner",
  PENDING_DEPOSIT = "pendingDeposit",
  PORTFOLIO = "portfolio",
  BANK_ACCOUNT = "bankAccount",
  CREDIT_TICKET = "linkedCreditTicket"
}

export enum TransferWithIntermediaryStageEnum {
  ACQUISITION = "acquisition",
  COLLECTION = "collection"
}

export enum DepositMethodEnum {
  OPEN_BANKING = "OPEN_BANKING",
  BANK_TRANSFER = "BANK_TRANSFER",
  DIRECT_DEBIT = "DIRECT_DEBIT",
  DIRECT_DEBIT_AND_BANK_TRANSFER = "DIRECT_DEBIT_AND_BANK_TRANSFER",
  BONUS = "BONUS"
}

export enum WithdrawalMethodEnum {
  DIRECT = "DIRECT",
  WITH_INTERMEDIARY = "WITH_INTERMEDIARY"
}

export enum DisplayTagEnum {
  INSTANT_INVEST = "INSTANT_INVEST",
  AUTOPILOT = "AUTOPILOT"
}

/**
 * TYPES
 */
export type TransactionConsiderationType = {
  currency: currenciesConfig.MainCurrencyType;
  amount?: number; // stored in cents
  cashAmount?: number; // for some transaction types (e.g. charge), the cash part of the consideration.amount, stored in cents
  holdingsAmount?: number; // for some transaction types (e.g. charge), the holdings part of the consideration.amount, stored in cents
};

export type FeesType = {
  fx: FeeType;
  realtimeExecution?: FeeType; // Older transactions/rewards do not have real time execution
  commission?: FeeType; // Older transactions/rewards do not have commission
  executionSpread?: FeeType; // Older transactions/rewards do not have execution spread
};

export type FeeType = {
  currency: currenciesConfig.MainCurrencyType;
  amount: number; // Stored in whole currency (not cents)
};

type ExecutionProgressType = {
  total: number;
  matched: number;
  label: string;
};

type BilateralPaymentType = {
  incomingPayment?: {
    providers: {
      devengo: {
        id?: string;
        status?: IncomingPaymentStatusType;
        accountId: string;
        settledAt?: Date;
      };
    };
  };
  outgoingPayment?: {
    providers: {
      devengo: {
        id: string;
        status: OutgoingPaymentStatusType;
        settledAt?: Date;
      };
    };
  };
};

export const TransactionStatusArray = [
  /**
   * PendingTopUp status:
   * SavingsWithdrawal Transactions that are waiting for a SavingsTopupTransaction to settle get this temporary status
   * after the SavingsTopupTransaction is settled they're moved to Pending status
   */
  "PendingTopUp",
  /**
   * PendingReinvestment status:
   * This status is used for dividends that are reinvested. The dividend transaction is settled when the reinvestment is completed.
   * Dividends move from Pending to PendingReinvestment status when the reinvestment transaction is created.
   *
   * Currently applicable to Savings dividends only.
   */
  "PendingReinvestment",
  "PendingDeposit",
  "PendingGift",
  "PendingWealthkernelCharge",
  "Pending",
  "Cancelled",
  "Rejected",
  "DepositFailed",
  "Settled"
] as const;
export type TransactionStatusType = (typeof TransactionStatusArray)[number];
export const RebalanceTransactionStatusArray = [
  "NotStarted",
  "PendingSell",
  "PendingBuy",
  "Settled",
  "Rejected",
  "Cancelled"
] as const;
export type RebalanceTransactionStatusType = (typeof RebalanceTransactionStatusArray)[number];
export const ChargePaymentStatusArray = ["Pending", "Confirmed", "PaidOut", "Failed"] as const;
export type ChargePaymentStatusType = (typeof ChargePaymentStatusArray)[number];

export const TransactionCategoryArray = [
  "DepositCashTransaction",
  "DividendTransaction",
  "AssetDividendTransaction",
  "WithdrawalCashTransaction",
  "AssetTransaction",
  "RebalanceTransaction",
  "ChargeTransaction",
  "RevertRewardTransaction",
  "CashbackTransaction",
  "WealthyhoodDividendTransaction",
  "SavingsTopupTransaction",
  "SavingsWithdrawalTransaction",
  "SavingsDividendTransaction",
  "StockSplitTransaction"
] as const;
export type TransactionCategoryType = (typeof TransactionCategoryArray)[number];

const PortfolioTransactionCategoryArray = ["buy", "sell", "update"] as const;
export type PortfolioTransactionCategoryType = (typeof PortfolioTransactionCategoryArray)[number];

// 1. Methods cash/holdings/combined are charges that get applies to the user's portfolio i.e. we deduct some cash from the
// user, sell some of their holdings or both (combined).
// 2. Direct debit charges, are charges that do not affect the user's portfolio but instead charge the user via our direct
// debit provider, directly via their bank.
// 3. Orders charges are relevant to charges that affect transactions i.e. FX, commissions, etc. Instead of charging the
// user's portfolio, we apply the charge to the buy/sell orders of the transaction.
// 4. Lifetime payment charges are related to one-time subscription fees (e.g. Black Friday lifetime plans).
// 5. Card payments are for subscription charges made with Stripe.
const ChargeMethodArray = [
  "cash",
  "holdings",
  "combined",
  "direct-debit",
  "orders",
  "lifetime-payment",
  "card"
] as const;
export type ChargeMethodType = (typeof ChargeMethodArray)[number];

const ChargeTypeArray = [
  "fx",
  "commission",
  "subscription",
  "custody",
  "executionSpread",
  "remainder",
  /**
   * Dividend charge type applies to only dividends from MMFs.
   */
  "dividendCommission",
  "realtimeExecution"
] as const;
export type ChargeTypeType = (typeof ChargeTypeArray)[number];

export type TruelayerPayloadType = {
  status: PaymentStatusTypeV3;
  failureReason?: FailureStatusType | string;
  executedAt?: Date;
};

/**
 * Include old transaction status and new charge status
 */
const WealthkernelChargeStatusArray = [
  ...WealthkernelTransactionStatusArray,
  ...ChargeStatusArray,
  ...InternalTransferStatusArray
] as const;

export type WealthkernelChargeStatusType = (typeof WealthkernelChargeStatusArray)[number];

export enum TransactionActivityFilterEnum {
  Rebalance = "Rebalance",
  Dividends = "Dividends",
  Deposit = "Deposit",
  Withdraw = "Withdraw",
  Bonus = "Bonus", // combines Wealthyhood dividends and cashback
  Asset = "Asset",
  Buy = "Buy",
  Sell = "Sell"
}

export enum TransactionInvestmentActivityFilterEnum {
  Buy = "Buy",
  Sell = "Sell",
  Rebalance = "Rebalance",
  Dividends = "Dividends" // stock dividends
}

export enum TransactionCashActivityFilterEnum {
  Deposit = "Deposit", // payments & savings withdrawals
  Withdraw = "Withdraw", // withdrawals & savings top-ups
  Investments = "Investments", // all asset transactions (excluding the ones linked to gifts)
  Dividends = "Dividends", // stock dividends
  Bonus = "Bonus" // combines Wealthyhood plan dividends and cashback
}

const POSITIVE_CASH_FLOW_CATEGORIES: TransactionCategoryType[] = [
  // deposits
  "DepositCashTransaction",
  "SavingsWithdrawalTransaction",
  // stock dividends
  "DividendTransaction",
  // bonus
  "CashbackTransaction",
  "WealthyhoodDividendTransaction"
];

const CATEGORY_TO_ACTIVITY_FILTER: Record<TransactionCategoryType, TransactionActivityFilterEnum | undefined> = {
  AssetTransaction: TransactionActivityFilterEnum.Asset,
  RebalanceTransaction: TransactionActivityFilterEnum.Rebalance,
  DepositCashTransaction: TransactionActivityFilterEnum.Deposit,
  WithdrawalCashTransaction: TransactionActivityFilterEnum.Withdraw,
  DividendTransaction: TransactionActivityFilterEnum.Dividends,
  AssetDividendTransaction: undefined,
  WealthyhoodDividendTransaction: TransactionActivityFilterEnum.Bonus,
  CashbackTransaction: TransactionActivityFilterEnum.Bonus,
  ChargeTransaction: undefined,
  RevertRewardTransaction: undefined,
  SavingsTopupTransaction: TransactionActivityFilterEnum.Withdraw,
  SavingsWithdrawalTransaction: TransactionActivityFilterEnum.Deposit,
  SavingsDividendTransaction: undefined,
  StockSplitTransaction: undefined
};

const CATEGORY_TO_CASH_ACTIVITY_FILTER: Record<
  TransactionCategoryType,
  TransactionCashActivityFilterEnum | undefined
> = {
  AssetTransaction: TransactionCashActivityFilterEnum.Investments,
  RebalanceTransaction: undefined,
  DepositCashTransaction: TransactionCashActivityFilterEnum.Deposit,
  WithdrawalCashTransaction: TransactionCashActivityFilterEnum.Withdraw,
  DividendTransaction: TransactionCashActivityFilterEnum.Dividends,
  AssetDividendTransaction: undefined,
  WealthyhoodDividendTransaction: TransactionCashActivityFilterEnum.Bonus,
  CashbackTransaction: TransactionCashActivityFilterEnum.Bonus,
  ChargeTransaction: undefined,
  RevertRewardTransaction: undefined,
  SavingsTopupTransaction: TransactionCashActivityFilterEnum.Withdraw,
  SavingsWithdrawalTransaction: TransactionCashActivityFilterEnum.Deposit,
  SavingsDividendTransaction: undefined,
  StockSplitTransaction: undefined
};

const CATEGORY_TO_INVESTMENT_ACTIVITY_FILTER: Record<
  TransactionCategoryType,
  TransactionInvestmentActivityFilterEnum | undefined
> = {
  AssetTransaction: {} as
    | TransactionInvestmentActivityFilterEnum.Buy
    | TransactionInvestmentActivityFilterEnum.Sell,
  RebalanceTransaction: TransactionInvestmentActivityFilterEnum.Rebalance,
  DepositCashTransaction: undefined,
  WithdrawalCashTransaction: undefined,
  DividendTransaction: TransactionInvestmentActivityFilterEnum.Dividends,
  AssetDividendTransaction: undefined,
  WealthyhoodDividendTransaction: undefined,
  CashbackTransaction: undefined,
  ChargeTransaction: undefined,
  RevertRewardTransaction: undefined,
  SavingsTopupTransaction: undefined,
  SavingsWithdrawalTransaction: undefined,
  SavingsDividendTransaction: undefined,
  StockSplitTransaction: undefined
};

/**
 * DOCUMENTS
 */
interface TransactionInterfaceDTO {
  consideration: {
    currency: currenciesConfig.MainCurrencyType;
    amount?: number; // stored in cents
    cashAmount?: number; // for some transaction types (e.g. charge), the cash part of the consideration.amount, stored in cents
    holdingsAmount?: number; // for some transaction types (e.g. charge), the holdings part of the consideration.amount, stored in cents
  };
  owner: mongoose.Types.ObjectId;
  portfolio: mongoose.Types.ObjectId | PortfolioDocument;
  createdAt: Date;
  settledAt?: Date;
}

interface TransactionInterface extends Omit<TransactionInterfaceDTO, "owner" | "portfolio"> {
  category: TransactionCategoryType;
  owner: UserDocument | mongoose.Types.ObjectId;
  portfolio: PortfolioDocument | mongoose.Types.ObjectId;

  // virtual for asset transactions, called to fill displayAmount field
  getDisplayAmount: (
    userCurrency: currenciesConfig.MainCurrencyType,
    investmentProductsDict: { [isin: string]: InvestmentProductDocument }
  ) => number;
  getIsCancellable: (
    user: UserDocument,
    investmentProductsDict: { [isin: string]: InvestmentProductDocument }
  ) => boolean;

  // virtual for deposits, withdrawals, dividends, charges otherwise filled by getDisplayAmount
  displayAmount?: number;
  isCancellable?: boolean;
  status: TransactionStatusType;
  hasTerminalStatus: boolean;

  // virtual for all transactions except charge
  readonly displayDate: Date;
  readonly activityFilter?: TransactionActivityFilterEnum;
  readonly investmentActivityFilter?: TransactionInvestmentActivityFilterEnum;
  readonly cashActivityFilter?: TransactionCashActivityFilterEnum;
  readonly isCashFlowPositive?: boolean;

  readonly sortingField: Date;
}

export interface DepositCashTransactionInterfaceDTO extends TransactionInterfaceDTO {
  // A deposit does not have a Truelayer entity & bank reference if it is initiated from a direct debit payment.
  bankReference?: string;
  depositMethod: DepositMethodEnum;
  activeProviders: ProviderEnum[];
  status: TransactionStatusType;
  providers: {
    truelayer?: {
      id: string;
      version?: TruelayerPaymentVersionType;
      status: PaymentStatusTypeV1 | PaymentStatusTypeV3;
      failureReason?: FailureStatusType;
      executedAt?: Date;
    };
    saltedge?: {
      id?: string; // This field is not present initially for deposits before syncing.
      status?: PaymentStatusType; // This field is not present initially for deposits before syncing.
      customId: string;
    };
    wealthkernel?: {
      id: string;
      // This is a workaround for scenarios where wealthkernel settles a deposit received from the
      // user, without having received a deposit expectation. In that case they transfer the cash
      // to the user's portfolio and they create a transaction (but no deposit).
      // We have to manually submit that transaction id, so that the deposit can settle.
      transactionId?: string;
      status: DepositStatusType;
      settledAt?: Date;
    };
  };
  transferWithIntermediary?: {
    [TransferWithIntermediaryStageEnum.ACQUISITION]?: BilateralPaymentType;
    [TransferWithIntermediaryStageEnum.COLLECTION]?: BilateralPaymentType;
  };
  createdWhilePendingMandate: boolean; // Set to true when the deposit was created while the mandate was pending.
  directDebit?: {
    activeProviders: ProviderEnum[];
    // Represents the date we **request** the provider to charge the bank account. Not present for older deposits.
    collectionRequestDate?: Date;
    providers: {
      wealthkernel?: {
        id: string;
        status: DirectDebitPaymentStatusType;
      };
      gocardless?: {
        id: string;
        status: GoCardlessPaymentStatusType;
      };
    };
  };
  bankAccount: mongoose.Types.ObjectId;
  linkedAutomation?: mongoose.Types.ObjectId;
  depositAction?: DepositActionEnum;
  linkedCreditTicket?: mongoose.Types.ObjectId;
}

interface DepositCashTransactionInterface
  extends TransactionInterface,
    Omit<DepositCashTransactionInterfaceDTO, "owner" | "bankAccount" | "linkedAutomation" | "linkedCreditTicket"> {
  bankAccount: mongoose.Types.ObjectId | BankAccountDocument;
  linkedAutomation?: mongoose.Types.ObjectId | AutomationDocument;
  linkedCreditTicket?: mongoose.Types.ObjectId | CreditTicketDocument;

  readonly displayTag: DisplayTagEnum;
  readonly isTruelayerFlowCompleted: boolean;
  readonly isSaltedgeFlowCompleted: boolean;
  readonly isPaymentAuthorised: boolean;
  readonly isDirectDebitPaymentCollected: boolean;
  readonly hasFailedDirectDebit: boolean;
  readonly inInstantMoneyFlow: boolean;
  readonly linkedAssetTransaction?: AssetTransactionDocument;
  readonly linkedSavingsTopup?: SavingsTopupTransactionDocument;
  readonly hasDevengoAcquisitionStageCompleted: boolean;
}

export interface WithdrawalCashTransactionInterfaceDTO extends TransactionInterfaceDTO {
  withdrawalMethod: WithdrawalMethodEnum;
  transferWithIntermediary?: {
    [TransferWithIntermediaryStageEnum.COLLECTION]?: {
      incomingPayment?: {
        providers: {
          devengo: {
            id: string;
            status: IncomingPaymentStatusType;
            accountId: string;
          };
        };
      };
      outgoingPayment?: {
        providers: {
          devengo: {
            id: string;
            status: OutgoingPaymentStatusType;
            settledAt?: Date;
          };
        };
      };
    };
  };
  activeProviders: ProviderEnum[];
  providers: {
    wealthkernel?: {
      id: string;
      status: WithdrawalStatusType;
      settledAt?: Date;
    };
  };
  bankReference: string;
  withdrawalRequestType: WithdrawalRequestType;
}

export interface WithdrawalCashTransactionInterface
  extends TransactionInterface,
    Omit<WithdrawalCashTransactionInterfaceDTO, "owner" | "bankAccount" | "linkedUserDataRequest"> {
  bankAccount: mongoose.Types.ObjectId | BankAccountDocument;
  linkedUserDataRequest: mongoose.Types.ObjectId | UserDataRequestDocument;
}

interface RevertRewardTransactionInterface extends TransactionInterface {
  linkedUserDataRequest: mongoose.Types.ObjectId | UserDataRequestDocument;
  reward: mongoose.Types.ObjectId | RewardDocument;

  readonly orders: OrderDocument[];
}

export interface RevertRewardTransactionInterfaceDTO extends TransactionInterfaceDTO {
  linkedUserDataRequest: mongoose.Types.ObjectId;
  reward: mongoose.Types.ObjectId;
  activeProviders: ProviderEnum[];
}

export interface AssetTransactionInterfaceDTO extends TransactionInterfaceDTO {
  portfolioTransactionCategory: PortfolioTransactionCategoryType;
  pendingDeposit?: mongoose.Types.ObjectId;
  pendingGift?: mongoose.Types.ObjectId;
  linkedUserDataRequest?: mongoose.Types.ObjectId;
  linkedAutomation?: mongoose.Types.ObjectId;
  fees?: FeesType;
  executionWindow: ExecutionWindowsType;
  originalInvestmentAmount: number;
  status: TransactionStatusType;
}

export interface RebalanceTransactionInterfaceDTO extends Omit<TransactionInterfaceDTO, "consideration"> {
  targetAllocation: InitialHoldingsAllocationType[];
  buyExecutionWindow: AggregateExecutionWindowType;
  sellExecutionWindow: AggregateExecutionWindowType;
  linkedAutomation?: mongoose.Types.ObjectId;
}

export interface StockSplitTransactionInterfaceDTO extends Omit<TransactionInterfaceDTO, "consideration"> {
  stockSplit: mongoose.Types.ObjectId;
}

interface AssetTransactionInterface
  extends TransactionInterface,
    Omit<AssetTransactionInterfaceDTO, "owner" | "portfolio" | "pendingDeposit" | "pendingGift"> {
  orders: OrderDocument[];
  pendingDeposit?: DepositCashTransactionDocument | mongoose.Types.ObjectId;
  pendingGift?: GiftDocument | mongoose.Types.ObjectId;
  foreignCurrencyRates?: ForeignCurrencyRatesType;

  readonly cashback?: CashbackTransactionDocument | mongoose.Types.ObjectId;
  readonly getDisplayQuantity: (
    getDisplayQuantity: currenciesConfig.MainCurrencyType,
    investmentProductsDict: { [isin: string]: InvestmentProductDocument }
  ) => number;
  readonly displayQuantity?: number;
  readonly hasExecutionStarted: boolean;
  readonly executionProgress: ExecutionProgressType;
}

interface RebalanceTransactionInterface extends TransactionInterface {
  targetAllocation: InitialHoldingsAllocationType[];
  rebalanceStatus: RebalanceTransactionStatusType;
  fees: FeesType;
  buyExecutionWindow: AggregateExecutionWindowType;
  sellExecutionWindow: AggregateExecutionWindowType;
  linkedAutomation?: mongoose.Types.ObjectId | AutomationDocument;

  readonly orders: OrderDocument[];
  readonly hasExecutionStarted: boolean;
  readonly hasSellExecutionStarted: boolean;
}

interface StockSplitTransactionInterface
  extends TransactionInterface,
    Omit<StockSplitTransactionInterfaceDTO, "owner" | "stockSplit"> {
  stockSplit: mongoose.Types.ObjectId | StockSplitCorporateEventDocument;
}

export interface DividendTransactionDTOInterface extends TransactionInterfaceDTO {
  asset: investmentUniverseConfig.AssetType;
  isin: string;
  activeProviders: ProviderEnum[];
  providers?: {
    wealthkernel?: {
      id: string;
      status: WealthkernelTransactionStatusType;
    };
  };
}

export interface AssetDividendTransactionDTOInterface extends Omit<TransactionInterfaceDTO, "consideration"> {
  asset: investmentUniverseConfig.AssetType;
  isin: string;
  quantity: number;

  activeProviders: ProviderEnum[];
  providers?: {
    wealthkernel?: {
      id: string;
      status: WealthkernelTransactionStatusType;
    };
  };
}

export interface AssetDividendTransactionInterface extends Omit<TransactionInterface, "consideration"> {
  readonly asset: investmentUniverseConfig.AssetType;
  readonly quantity: number;

  readonly activeProviders: ProviderEnum[];
  readonly providers?: {
    wealthkernel?: {
      id: string;
      status: WealthkernelTransactionStatusType;
    };
  };
}

export interface DividendTransactionInterface extends TransactionInterface {
  readonly asset: investmentUniverseConfig.AssetType;
  readonly activeProviders: ProviderEnum[];
  readonly providers?: {
    wealthkernel?: {
      id: string;
      status: WealthkernelTransactionStatusType;
    };
  };
}

export interface CashbackTransactionDTOInterface extends TransactionInterfaceDTO {
  price: plansConfig.PriceType;
  cashbackMonth: string; // YYYY-MM
  linkedAssetTransaction?: mongoose.Types.ObjectId;
  deposit?: {
    activeProviders: ProviderEnum[];
    providers?: {
      wealthkernel: {
        id?: string;
        status?: BonusStatusType;
        submittedAt?: Date;
      };
    };
  };
}

interface CashbackTransactionInterface
  extends TransactionInterface,
    Omit<CashbackTransactionDTOInterface, "owner" | "portfolio" | "linkedAssetTransaction"> {
  linkedAssetTransaction: mongoose.Types.ObjectId | AssetTransactionDocument;
}

interface WealthyhoodDividendTransactionInterface
  extends TransactionInterface,
    Omit<WealthyhoodDividendTransactionDTOInterface, "owner" | "portfolio"> {}

export interface ChargeTransactionDTOInterface extends TransactionInterfaceDTO {
  chargeMethod: ChargeMethodType;
  chargeType: ChargeTypeType;
  originalChargeAmount: number;
  status?: TransactionStatusType;
  activeProviders: ProviderEnum[];
  providers?: {
    wealthkernel?: {
      id: string; // The WK ID of the charge taking out the amount from the user's portfolio and moving it to ours.
      status: WealthkernelChargeStatusType;
    };
    // If chargeMethod is direct debit, the payment is done via GoCardless.
    gocardless?: {
      id: string;
      status: GoCardlessPaymentStatusType;
    };
    // If chargeMethod is card, the payment is done via Stripe.
    stripe?: {
      id: string;
      status: PaymentIntentStatusType;
    };
    // If chargeMethod is lifetime payment, the payment is done via Truelayer.
    truelayer?: {
      id: string;
      status: PaymentStatusTypeV3;
      failureReason?: FailureStatusType;
      executedAt?: Date;
    };
  };

  // Only present in subscription charges
  chargeMonth?: string; // YYYY-MM
  subscription?: mongoose.Types.ObjectId;
  bankAccount?: mongoose.Types.ObjectId;
  paymentMethod?: mongoose.Types.ObjectId; // Only applicable to charges made with Stripe
  bankReference?: string;
  price?: plansConfig.PriceType;

  // Only present in commission charges
  linkedReward?: mongoose.Types.ObjectId;
  linkedTransaction?: mongoose.Types.ObjectId;
}

export interface WealthyhoodDividendTransactionDTOInterface extends TransactionInterfaceDTO {
  price: plansConfig.PriceType;
  dividendMonth?: string; // YYYY-MM
  hasViewedAppModal?: boolean;
  deposit?: {
    activeProviders: ProviderEnum[];
    providers?: {
      wealthkernel: {
        id?: string;
        status?: BonusStatusType;
        submittedAt?: Date;
      };
    };
  };
}

export interface ChargeTransactionInterface extends TransactionInterface {
  readonly chargeMethod: ChargeMethodType;
  readonly chargeMonth: string; // YYYY-MM
  readonly chargeType: ChargeTypeType;
  readonly subscription: mongoose.Types.ObjectId | SubscriptionDocument;
  readonly bankAccount?: mongoose.Types.ObjectId | BankAccountDocument;
  readonly paymentMethod?: mongoose.Types.ObjectId | PaymentMethodDocument;
  readonly bankReference?: string;
  readonly price?: plansConfig.PriceType;
  readonly activeProviders: ProviderEnum[];
  readonly providers?: {
    wealthkernel?: {
      id: string; // The WK ID of the charge taking out the amount from the user's portfolio and moving it to ours.
      status: WealthkernelChargeStatusType;
      submittedAt?: Date;
    };
    // If chargeMethod is direct debit, the payment is done via GoCardless.
    gocardless?: {
      id: string;
      status: GoCardlessPaymentStatusType;
    };
    // If chargeMethod is card, the payment is done via Stripe.
    stripe?: {
      id: string;
      status: PaymentIntentStatusType;
    };
    // If chargeMethod is lifetime payment, the payment is done via Truelayer.
    truelayer?: {
      id: string;
      status: PaymentStatusTypeV3;
      failureReason?: FailureStatusType;
      executedAt?: Date;
    };
  };
  readonly originalChargeAmount: number;

  // Only present in commission charges
  readonly linkedReward?: mongoose.Types.ObjectId | RewardDocument;
  readonly linkedTransaction?:
    | mongoose.Types.ObjectId
    | AssetTransactionDocument
    | RebalanceTransactionDocument
    | SavingsDividendTransactionDocument;
  // Only Present in subscription charges
  readonly fallbackCharge?: mongoose.Types.ObjectId | ChargeTransactionDocument;
  readonly downgradePlan?: plansConfig.PriceType;

  // VIRTUALS
  readonly orders: OrderDocument[];
  readonly paymentStatus?: ChargePaymentStatusType; // only present when charge method is direct-debit
  readonly isCustody: boolean;
}

export interface SavingsTopupTransactionDTOInterface extends TransactionInterfaceDTO {
  savingsProduct: savingsUniverseConfig.SavingsProductType;
  pendingDeposit?: mongoose.Types.ObjectId;
  linkedAutomation?: mongoose.Types.ObjectId;
  status: TransactionStatusType;
}

export interface SavingsTopupTransactionInterface
  extends TransactionInterface,
    Omit<SavingsTopupTransactionDTOInterface, "owner" | "portfolio" | "pendingDeposit" | "linkedAutomation"> {
  pendingDeposit?: DepositCashTransactionDocument | mongoose.Types.ObjectId;
  linkedAutomation?: AutomationDocument | mongoose.Types.ObjectId;

  // VIRTUALS
  readonly orders: OrderDocument[];
  readonly displayTitle: string;
  readonly internallyFilledAmount: number; // in cents
  readonly remainingAmountToSubmit: number; // in cents
  readonly linkedSavingsDividend?: SavingsDividendTransactionDocument;
}

export interface SavingsWithdrawalTransactionDTOInterface extends TransactionInterfaceDTO {
  savingsProduct: savingsUniverseConfig.SavingsProductType;
  linkedUserDataRequest?: mongoose.Types.ObjectId;
  status: TransactionStatusType;
}

export interface SavingsWithdrawalTransactionInterface
  extends TransactionInterface,
    Omit<SavingsWithdrawalTransactionDTOInterface, "owner" | "portfolio"> {
  // VIRTUALS
  readonly orders: OrderDocument[];
  readonly displayTitle: string;
  readonly internallyFilledAmount: number; // in cents
  readonly remainingAmountToSubmit: number; // in cents
}

export interface SavingsDividendTransactionDTOInterface extends TransactionInterfaceDTO {
  fees: FeesType;
  status: TransactionStatusType;
  savingsProduct: savingsUniverseConfig.SavingsProductType;
  activeProviders: ProviderEnum[];
  originalDividendAmount: number;
  linkedSavingsTopup?: mongoose.Types.ObjectId;
  dividendMonth?: string; // YYYY-MM
  providers?: {
    wealthkernel?: {
      id: string;
      status: WealthkernelTransactionStatusType;
    };
  };
}

export interface SavingsDividendTransactionInterface
  extends TransactionInterface,
    Omit<SavingsDividendTransactionDTOInterface, "owner" | "portfolio" | "linkedSavingsTopup"> {
  linkedSavingsTopup?: mongoose.Types.ObjectId | SavingsTopupTransactionDocument;

  // VIRTUALS
  readonly displayTitle: string;
}

export interface TransactionDocument extends TransactionInterface, Document {}

export interface DepositCashTransactionDocument extends DepositCashTransactionInterface, TransactionDocument {}

export interface WithdrawalCashTransactionDocument
  extends WithdrawalCashTransactionInterface,
    TransactionDocument {}

export interface AssetTransactionDocument extends AssetTransactionInterface, TransactionDocument {}

export interface RebalanceTransactionDocument extends RebalanceTransactionInterface, TransactionDocument {}

export interface StockSplitTransactionDocument extends StockSplitTransactionInterface, TransactionDocument {}

export interface DividendTransactionDocument extends DividendTransactionInterface, TransactionDocument {}

export interface AssetDividendTransactionDocument extends AssetDividendTransactionInterface, TransactionDocument {}

export interface CashbackTransactionDocument extends CashbackTransactionInterface, TransactionDocument {}

export interface WealthyhoodDividendTransactionDocument
  extends WealthyhoodDividendTransactionInterface,
    TransactionDocument {}

export interface ChargeTransactionDocument extends ChargeTransactionInterface, TransactionDocument {}

export interface RevertRewardTransactionDocument extends RevertRewardTransactionInterface, TransactionDocument {}

export interface SavingsTopupTransactionDocument extends SavingsTopupTransactionInterface, TransactionDocument {}

export interface SavingsWithdrawalTransactionDocument
  extends SavingsWithdrawalTransactionInterface,
    TransactionDocument {}

export interface SavingsDividendTransactionDocument
  extends SavingsDividendTransactionInterface,
    TransactionDocument {}

/**
 * SCHEMAS
 */
const transactionSchema = new mongoose.Schema(
  {
    consideration: {
      currency: {
        type: String,
        enum: MainCurrencies,
        default: "GBP"
      },
      amount: { type: Number, required: false },
      cashAmount: { type: Number, required: false },
      holdingsAmount: { type: Number, required: false }
    },
    createdAt: { type: Date, required: true },
    settledAt: { type: Date, required: false },
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    },
    portfolio: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Portfolio",
      required: true
    }
  },
  { discriminatorKey: "category" }
);
const depositCashTransactionSchema = new mongoose.Schema(
  {
    bankReference: { type: String, required: false }, // We don't have a bank reference if payment by direct debit
    activeProviders: { type: Object.values(ProviderEnum), required: true },
    status: { type: String, enum: TransactionStatusArray, default: "Pending" },
    depositMethod: {
      type: String,
      enum: Object.values(DepositMethodEnum),
      required: true
    },
    depositAction: { type: String, enum: Object.values(DepositActionEnum), required: false },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.WEALTHKERNEL]: {
          _id: false,
          id: { type: String },
          transactionId: { type: String, required: false },
          status: { type: String, enum: DepositStatusArray },
          settledAt: { type: Date, required: false }
        },
        [ProviderEnum.SALTEDGE]: {
          _id: false,
          id: { type: String },
          customId: { type: String },
          status: { type: String, enum: PaymentStatusArray }
        },
        [ProviderEnum.TRUELAYER]: {
          _id: false,
          id: { type: String },
          version: {
            type: String,
            enum: TruelayerPaymentVersionArray
          },
          status: { type: String, enum: [...PaymentStatusArrayV1, ...PaymentStatusArrayV3] },
          failureReason: { type: String, enum: FailureStatusArray },
          executedAt: { type: Date }
        }
      }
    },
    // In deposits, incoming/outgoing payments are present when the deposit is a 2-stage process such as bank
    // transfers, where there is an intermediary between the user bank account and the broker. In that case, there
    // is an acquisition block, representing the money flowing in our acquisition accounts (one per user), and a
    // collection block, representing money flowing in our single collection account. Both acquisition and collection
    // blocks have an incoming payment and an outgoing payment stage.
    transferWithIntermediary: {
      type: {
        _id: false,
        [TransferWithIntermediaryStageEnum.ACQUISITION]: {
          type: {
            _id: false,
            incomingPayment: {
              providers: {
                _id: false,
                type: {
                  [ProviderEnum.DEVENGO]: {
                    _id: false,
                    id: { type: String },
                    status: { type: String, enum: IncomingPaymentStatusArray },
                    accountId: { type: String },
                    settledAt: { type: Date, required: false }
                  }
                }
              }
            },
            outgoingPayment: {
              providers: {
                _id: false,
                type: {
                  [ProviderEnum.DEVENGO]: {
                    _id: false,
                    id: { type: String },
                    status: { type: String, enum: OutgoingPaymentStatusArray },
                    settledAt: { type: Date, required: false }
                  }
                }
              }
            }
          }
        },
        [TransferWithIntermediaryStageEnum.COLLECTION]: {
          type: {
            _id: false,
            incomingPayment: {
              providers: {
                _id: false,
                type: {
                  [ProviderEnum.DEVENGO]: {
                    _id: false,
                    id: { type: String },
                    status: { type: String, enum: IncomingPaymentStatusArray },
                    accountId: { type: String },
                    settledAt: { type: Date, required: false }
                  }
                }
              }
            },
            outgoingPayment: {
              providers: {
                _id: false,
                type: {
                  [ProviderEnum.DEVENGO]: {
                    _id: false,
                    id: { type: String },
                    status: { type: String, enum: OutgoingPaymentStatusArray },
                    settledAt: { type: Date, required: false }
                  }
                }
              }
            }
          }
        }
      }
    },
    createdWhilePendingMandate: { type: Boolean, default: false },
    directDebit: {
      _id: false,
      type: {
        activeProviders: { type: Object.values(ProviderEnum), required: true },
        collectionRequestDate: { type: Date },
        providers: {
          _id: false,
          type: {
            [ProviderEnum.WEALTHKERNEL]: {
              _id: false,
              id: { type: String },
              status: { type: String, enum: DirectDebitPaymentStatusArray }
            },
            [ProviderEnum.GOCARDLESS]: {
              _id: false,
              id: { type: String },
              status: { type: String, enum: GoCardlessPaymentStatusArray }
            }
          }
        }
      },
      required: false
    },
    bankAccount: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "BankAccount",
      required: false
    },
    linkedAutomation: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Automation",
      required: false
    },
    linkedCreditTicket: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "CreditTicket",
      required: false
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);
const withdrawalCashTransactionSchema = new mongoose.Schema(
  {
    // In withdrawals, incoming/outgoing payments are present when the withdrawal is a 2-stage process such as bank
    // transfers, where there is an intermediary between the broker and the user bank account. In that case, there
    // is an incoming payment representing the payment to the intermediary and an outgoing payment representing
    // the payment to the user bank account.
    transferWithIntermediary: {
      type: {
        _id: false,
        [TransferWithIntermediaryStageEnum.COLLECTION]: {
          type: {
            _id: false,
            incomingPayment: {
              providers: {
                _id: false,
                type: {
                  [ProviderEnum.DEVENGO]: {
                    _id: false,
                    id: { type: String },
                    status: { type: String, enum: IncomingPaymentStatusArray },
                    accountId: { type: String }
                  }
                }
              }
            },
            outgoingPayment: {
              providers: {
                _id: false,
                type: {
                  [ProviderEnum.DEVENGO]: {
                    _id: false,
                    id: { type: String },
                    status: { type: String, enum: OutgoingPaymentStatusArray },
                    settledAt: { type: Date, required: false }
                  }
                }
              }
            }
          }
        }
      }
    },
    withdrawalMethod: {
      type: String,
      enum: Object.values(WithdrawalMethodEnum),
      default: WithdrawalMethodEnum.DIRECT
    },
    activeProviders: { type: Object.values(ProviderEnum), required: true },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.WEALTHKERNEL]: {
          _id: false,
          id: { type: String },
          status: { type: String, enum: WithdrawalStatusArray },
          settledAt: { type: Date, required: false }
        }
      }
    },
    bankAccount: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "BankAccount",
      required: false
    },
    bankReference: { type: String, required: true },
    linkedUserDataRequest: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "UserDataRequest",
      required: false
    },
    withdrawalRequestType: {
      type: String,
      enum: WithdrawalRequestArray,
      default: "SpecifiedAmount"
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);
const assetCategorySeparatedExecutionWindowSchema: Schema = new mongoose.Schema(
  {
    stocks: {
      type: {
        start: { type: Date, required: false },
        end: { type: Date, required: false },
        executionType: {
          type: String,
          enum: ExecutionTypeEnum,
          default: ExecutionTypeEnum.MARKET_HOURS
        }
      },
      required: false
    },
    etfs: {
      type: {
        start: { type: Date, required: false },
        end: { type: Date, required: false },
        executionType: {
          type: String,
          enum: ExecutionTypeEnum,
          default: ExecutionTypeEnum.MARKET_HOURS
        }
      },
      required: false
    }
  },
  { _id: false }
);
const singleExecutionWindowSchema: Schema = new mongoose.Schema(
  {
    start: { type: Date, required: false },
    end: { type: Date, required: false },
    executionType: {
      type: String,
      enum: ExecutionTypeEnum,
      default: ExecutionTypeEnum.MARKET_HOURS
    }
  },
  { _id: false }
);
const feesSchema: Schema = new mongoose.Schema(
  {
    fx: {
      currency: {
        type: String,
        enum: MainCurrencies,
        default: "GBP"
      },
      amount: { type: Number, default: 0 }
    },
    commission: {
      currency: {
        type: String,
        enum: MainCurrencies,
        default: "GBP"
      },
      amount: { type: Number, default: 0 }
    },
    executionSpread: {
      currency: {
        type: String,
        enum: MainCurrencies,
        default: "GBP"
      },
      amount: { type: Number, default: 0 }
    },
    realtimeExecution: {
      currency: {
        type: String,
        enum: MainCurrencies,
        default: "GBP"
      },
      amount: { type: Number, default: 0 }
    }
  },
  { _id: false }
);
const assetTransactionSchema = new mongoose.Schema(
  {
    orders: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Order",
        required: true
      }
    ],
    portfolioTransactionCategory: {
      type: String,
      enum: PortfolioTransactionCategoryArray
    },
    fees: feesSchema,
    status: { type: String, enum: TransactionStatusArray, default: "Pending" },
    originalInvestmentAmount: { type: Number, required: false },
    pendingDeposit: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "DepositCashTransaction",
      required: false
    },
    pendingGift: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Gift",
      required: false
    },
    executionWindow: {
      type: assetCategorySeparatedExecutionWindowSchema,
      required: false
    },
    linkedUserDataRequest: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "UserDataRequest",
      required: false
    },
    linkedAutomation: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Automation",
      required: false
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

transactionSchema.post("save", function (error: Error, doc: TransactionDocument, next: (err?: Error) => void) {
  if (error) {
    logger.error(`Error in transaction ${doc._id} of type ${doc.category}`, {
      module: "Transaction",
      method: "post.save"
    });
    error.message += ` - for transaction ${doc._id}`;
    next(error);
  } else {
    next();
  }
});

const allocationPercentageSchema: Schema = new mongoose.Schema(
  {
    assetCommonId: String,
    percentage: Number
  },
  { _id: false }
);

const rebalanceTransactionSchema = new mongoose.Schema(
  {
    targetAllocation: [allocationPercentageSchema],
    rebalanceStatus: { type: String, enum: RebalanceTransactionStatusArray, default: "NotStarted" },
    buyExecutionWindow: {
      type: singleExecutionWindowSchema,
      required: false
    },
    sellExecutionWindow: {
      type: singleExecutionWindowSchema,
      required: false
    },
    fees: feesSchema,
    linkedAutomation: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Automation",
      required: false
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

const stockSplitTransactionSchema = new mongoose.Schema(
  {
    status: { type: String, enum: TransactionStatusArray, default: "Pending" },
    stockSplit: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "StockSplitCorporateEvent",
      required: true
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

const dividendTransactionSchema = new mongoose.Schema(
  {
    asset: { type: String, enum: AssetArrayConst, required: true },
    isin: { type: String },
    activeProviders: { type: Object.values(ProviderEnum), required: true },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.WEALTHKERNEL]: {
          _id: false,
          id: { type: String },
          status: { type: String, enum: WealthkernelTransactionStatusArray }
        }
      }
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

const assetDividendTransactionSchema = new mongoose.Schema(
  {
    asset: { type: String, enum: AssetArrayConst, required: true },
    isin: { type: String },
    activeProviders: { type: Object.values(ProviderEnum), required: true },
    quantity: { type: Number, required: true },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.WEALTHKERNEL]: {
          _id: false,
          id: { type: String },
          status: { type: String, enum: WealthkernelTransactionStatusArray }
        }
      }
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

const cashbackTransactionSchema = new mongoose.Schema(
  {
    // Cashbacks linked to deposits with status DepositFailed will get a Rejected status
    status: { type: String, enum: TransactionStatusArray, default: "Pending" },
    cashbackMonth: { type: String, required: true },
    price: {
      type: String,
      enum: plansConfig.PriceArrayConst,
      required: true
    },
    linkedAssetTransaction: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "AssetTransaction",
      required: true
    },
    deposit: {
      activeProviders: { type: Object.values(ProviderEnum), required: true },
      providers: {
        _id: false,
        type: {
          [ProviderEnum.WEALTHKERNEL]: {
            _id: false,
            id: { type: String },
            status: { type: String, enum: BonusStatusArray },
            submittedAt: { type: Date }
          }
        }
      }
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

const wealthyhoodDividendTransactionSchema = new mongoose.Schema(
  {
    dividendMonth: { type: String, required: true },
    hasViewedAppModal: { type: Boolean, default: false },
    price: {
      type: String,
      enum: plansConfig.PriceArrayConst,
      required: true
    },
    deposit: {
      activeProviders: { type: Object.values(ProviderEnum), required: true },
      providers: {
        _id: false,
        type: {
          [ProviderEnum.WEALTHKERNEL]: {
            _id: false,
            id: { type: String },
            status: { type: String, enum: BonusStatusArray },
            submittedAt: { type: Date }
          }
        }
      }
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

const revertRewardTransactionSchema = new mongoose.Schema(
  {
    status: { type: String, enum: TransactionStatusArray, default: "Pending" },
    linkedUserDataRequest: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "UserDataRequest",
      required: false
    },
    reward: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Reward",
      required: true
    },
    activeProviders: { type: Object.values(ProviderEnum), required: true },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.WEALTHKERNEL]: {
          _id: false,
          id: { type: String },
          status: { type: String, enum: WealthkernelTransactionStatusArray }
        }
      }
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

const chargeTransactionSchema = new mongoose.Schema(
  {
    status: { type: String, enum: TransactionStatusArray, default: "Pending" },
    chargeType: { type: String, enum: ChargeTypeArray, required: true },
    chargeMethod: { type: String, enum: ChargeMethodArray, required: true },
    originalChargeAmount: { type: Number, required: true },
    chargeMonth: { type: String, required: false },
    activeProviders: { type: Object.values(ProviderEnum), required: true },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.WEALTHKERNEL]: {
          _id: false,
          id: { type: String },
          submittedAt: { type: Date },
          status: { type: String, enum: WealthkernelChargeStatusArray }
        },
        [ProviderEnum.TRUELAYER]: {
          _id: false,
          id: { type: String },
          status: { type: String, enum: [...PaymentStatusArrayV1, ...PaymentStatusArrayV3] },
          failureReason: { type: String, enum: FailureStatusArray },
          executedAt: { type: Date }
        },
        [ProviderEnum.GOCARDLESS]: {
          _id: false,
          id: { type: String },
          status: { type: String, enum: GoCardlessPaymentStatusArray }
        },
        [ProviderEnum.STRIPE]: {
          _id: false,
          id: { type: String },
          status: { type: String, enum: PaymentIntentStatusArray }
        }
      }
    },
    bankReference: { type: String, required: false }, // The bank reference for the deposits made in direct debit subscriptions
    price: {
      type: String,
      enum: plansConfig.PriceArrayConst,
      required: false
    },
    bankAccount: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "BankAccount",
      required: false // Bank account is only present for charges made with direct debit subscriptions
    },
    paymentMethod: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "PaymentMethod",
      required: false // Payment method is only present for charges made with card subscriptions
    },
    subscription: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Subscription",
      required: false // Subscription is only required in subscription charges
    },
    linkedReward: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Reward",
      required: false
    },
    linkedTransaction: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Transaction",
      required: false
    },
    fallbackCharge: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ChargeTransaction",
      required: false
    },
    downgradePlan: {
      type: String,
      enum: plansConfig.PriceArrayConst,
      required: false
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

const savingsTopupTransactionSchema = new mongoose.Schema(
  {
    status: { type: String, enum: TransactionStatusArray, default: "Pending" },
    savingsProduct: { type: String, enum: SavingsProductArray, required: true },
    pendingDeposit: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "DepositCashTransaction",
      required: false
    },
    linkedAutomation: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Automation",
      required: false
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

const savingsWithdrawalTransactionSchema = new mongoose.Schema(
  {
    status: { type: String, enum: TransactionStatusArray, default: "Pending" },
    savingsProduct: { type: String, enum: SavingsProductArray, required: true },
    linkedUserDataRequest: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "UserDataRequest",
      required: false
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

const savingsDividendTransactionSchema = new mongoose.Schema(
  {
    status: { type: String, enum: TransactionStatusArray, default: "Pending" },
    savingsProduct: { type: String, enum: SavingsProductArray, required: true },
    activeProviders: { type: Object.values(ProviderEnum), required: true },
    fees: feesSchema,
    providers: {
      _id: false,
      type: {
        [ProviderEnum.WEALTHKERNEL]: {
          _id: false,
          id: { type: String },
          status: { type: String, enum: WealthkernelTransactionStatusArray }
        }
      }
    },
    linkedSavingsTopup: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "SavingsTopupTransaction",
      required: false
    },
    originalDividendAmount: { type: Number, required: true },
    dividendMonth: { type: String, required: true }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

/**
 * VIRTUALS
 */
depositCashTransactionSchema.virtual("isTruelayerFlowCompleted").get(function (): boolean {
  const document = this as DepositCashTransactionDocument;

  if (document?.providers?.truelayer?.version === "v3") {
    return !(["authorization_required", "authorizing", "failed"] as PaymentStatusTypeV3[]).includes(
      document?.providers?.truelayer?.status as PaymentStatusTypeV3
    );
  } else {
    return !(["new", "cancelled", "rejected"] as PaymentStatusTypeV1[]).includes(
      document?.providers?.truelayer?.status as PaymentStatusTypeV1
    );
  }
});

depositCashTransactionSchema.virtual("isSaltedgeFlowCompleted").get(function (): boolean {
  const document = this as DepositCashTransactionDocument;

  if (!document?.providers?.saltedge?.id) {
    return false;
  }

  return !["processing", "rejected", "failed", "unknown", "deleted"].includes(
    document?.providers?.saltedge?.status
  );
});

depositCashTransactionSchema.virtual("hasDevengoAcquisitionStageCompleted").get(function (): boolean {
  const document = this as DepositCashTransactionDocument;

  return (
    document.transferWithIntermediary?.acquisition?.outgoingPayment?.providers?.devengo?.status === "confirmed"
  );
});

depositCashTransactionSchema.virtual("isPaymentAuthorised").get(function (): boolean {
  const document = this as DepositCashTransactionDocument;

  if (document.depositMethod === DepositMethodEnum.BANK_TRANSFER) {
    return true;
  } else if (document.depositMethod === DepositMethodEnum.OPEN_BANKING) {
    if (document.activeProviders.includes(ProviderEnum.TRUELAYER)) {
      return document.isTruelayerFlowCompleted;
    } else if (document.activeProviders.includes(ProviderEnum.SALTEDGE)) {
      return document.isSaltedgeFlowCompleted;
    }
  }
});

depositCashTransactionSchema.virtual("isDirectDebitPaymentCollected").get(function (): boolean {
  const document = this as DepositCashTransactionDocument;

  if (
    ![DepositMethodEnum.DIRECT_DEBIT, DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER].includes(
      document.depositMethod
    )
  ) {
    return false;
  } else
    return (
      ["confirmed", "paid_out"].includes(document?.directDebit?.providers?.gocardless?.status) ||
      ["Collected", "Completed"].includes(document?.directDebit?.providers?.wealthkernel?.status)
    );
});

depositCashTransactionSchema.virtual("directDebitProgressPercentage").get(function (): number {
  const document = this as DepositCashTransactionDocument;

  if (
    ![DepositMethodEnum.DIRECT_DEBIT, DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER].includes(
      document.depositMethod
    ) ||
    !document.isDirectDebitPaymentCollected ||
    !document.directDebit?.collectionRequestDate
  ) {
    return;
  }

  let estimatedCompletionDate: Date;
  if (document.directDebit?.activeProviders?.includes(ProviderEnum.WEALTHKERNEL)) {
    estimatedCompletionDate = DateUtil.getDateAfterNthUKWorkDays(
      document.directDebit.collectionRequestDate,
      ESTIMATED_WORK_DAYS_TO_RECEIVE_FUNDS_AFTER_COLLECTION[ProviderEnum.WEALTHKERNEL]
    );
  } else if (document.directDebit?.activeProviders?.includes(ProviderEnum.GOCARDLESS)) {
    estimatedCompletionDate = DateUtil.getDateAfterNthUKWorkDays(
      document.directDebit.collectionRequestDate,
      ESTIMATED_WORK_DAYS_TO_RECEIVE_FUNDS_AFTER_COLLECTION[ProviderEnum.GOCARDLESS]
    );
  }

  return DateUtil.getCompletionPercentage(document.directDebit.collectionRequestDate, estimatedCompletionDate);
});

// True when the clients should show the mandate step when user clicks on the relevant transaction.
// Does not control whether the step appears as completed or not, just its presence.
depositCashTransactionSchema.virtual("shouldIncludeMandateStep").get(function (): boolean {
  const document = this as DepositCashTransactionDocument;

  return document.createdWhilePendingMandate;
});

depositCashTransactionSchema.virtual("hasFailedDirectDebit").get(function (): boolean {
  const document = this as DepositCashTransactionDocument;

  const WEALTHKERNEL_FAILED_STATUSES: DirectDebitPaymentStatusType[] = ["Cancelled", "Failed"];
  const GOCARDLESS_FAILED_STATUSES: GoCardlessPaymentStatusType[] = [
    "cancelled",
    "customer_approval_denied",
    "failed",
    "charged_back"
  ];

  const hasFailedWealthkernel =
    document.directDebit?.providers?.wealthkernel?.status &&
    WEALTHKERNEL_FAILED_STATUSES.includes(document.directDebit.providers.wealthkernel.status);

  const hasFailedGoCardless =
    document.directDebit?.providers?.gocardless?.status &&
    GOCARDLESS_FAILED_STATUSES.includes(document.directDebit.providers.gocardless.status);

  return (
    (document.directDebit?.providers?.wealthkernel && hasFailedWealthkernel) ||
    (document.directDebit?.providers?.gocardless && hasFailedGoCardless)
  );
});

depositCashTransactionSchema.virtual("isMoneyReceived").get(function (): boolean {
  const document = this as DepositCashTransactionDocument;

  if (document.depositMethod === DepositMethodEnum.DIRECT_DEBIT) {
    return document.status === "Settled";
  } else if (document.depositMethod === DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER) {
    return (
      document.transferWithIntermediary?.collection?.incomingPayment?.providers?.devengo?.status === "confirmed"
    );
  }
});

// The display status property is the status we should display on the client for the transaction.
// It is always the same as the persisted status unless the deposit's linked credit ticket is credited, in
// which case we return pending deposits as 'Settled'.
depositCashTransactionSchema.virtual("displayStatus").get(function (): TransactionStatusType {
  const document = this as DepositCashTransactionDocument;

  if (
    (document.linkedCreditTicket as CreditTicketDocument)?.status === "Credited" &&
    document.status === "Pending"
  ) {
    return "Settled";
  }

  return document.status;
});

// The displayed amount property is the amount we should display on the client for the transaction
depositCashTransactionSchema.virtual("displayAmount").get(function (): number {
  const document = this as DepositCashTransactionDocument;
  return document.consideration.amount;
});

withdrawalCashTransactionSchema.virtual("displayAmount").get(function (): number {
  const document = this as unknown as WithdrawalCashTransactionDocument;
  return document.consideration.amount;
});

dividendTransactionSchema.virtual("displayAmount").get(function (): number {
  const document = this as DividendTransactionDocument;
  return document.consideration.amount;
});

chargeTransactionSchema.virtual("displayAmount").get(function (): number {
  const document = this as ChargeTransactionDocument;
  return document.originalChargeAmount;
});

cashbackTransactionSchema.virtual("displayAmount").get(function (): number {
  const document = this as CashbackTransactionDocument;
  return document.consideration.amount;
});

wealthyhoodDividendTransactionSchema.virtual("displayAmount").get(function (): number {
  const document = this as WealthyhoodDividendTransactionDocument;
  return document.consideration.amount;
});

/**
 * @description
 * The displayAmount we're showing users in asset transactions depends on the transaction category and status.
 *  1. Portfolio Buy: we're showing the amount the user paid (e.g. £10, even if some of that was fees)
 *  2. Portfolio Sell:
 *     a) If the transaction is settled, we show the actual amount the user received as cash from the transaction.
 *     b) In any other than settled, we show the original investment amount (the amount the user submitted when creating
 *     the transaction (e.g. £10)
 *  3. Single Asset Buy / Sell: We use the displayAmount of the order document.
 * The amount is returned in cents and is up to clients to convert to GBP.
 */
assetTransactionSchema.virtual("getDisplayAmount").get(function (): (
  userCurrency: currenciesConfig.MainCurrencyType,
  investmentProductsDict: { [isin: string]: InvestmentProductDocument }
) => number {
  const document = this as unknown as AssetTransactionDocument;

  return (userCurrency, investmentProductsDict) => {
    if (this.portfolioTransactionCategory === "buy") {
      if (["Cancelled", "Rejected", "DepositFailed"].includes(document.status))
        return document.originalInvestmentAmount;
      return document.orders
        .filter((order) => order.status !== "Cancelled")
        .map((order) => order.getDisplayAmount(userCurrency, investmentProductsDict[order.isin], document))
        .reduce((sum, amount) => sum.plus(amount), new Decimal(0))
        .toNumber();
    } else if (document.portfolioTransactionCategory === "sell") {
      if (document.status === "Settled") return document.consideration.amount;
      else return document.originalInvestmentAmount;
    } else if (document.portfolioTransactionCategory === "update" && document.orders.length === 1) {
      return document.orders[0].getDisplayAmount(
        userCurrency,
        investmentProductsDict[document.orders[0].isin],
        document
      );
    }
  };
});

depositCashTransactionSchema.virtual("displayTag").get(function (): DisplayTagEnum {
  const document = this as DepositCashTransactionDocument;

  if (document.linkedAutomation) {
    return DisplayTagEnum.AUTOPILOT;
  } else if (document.depositAction === DepositActionEnum.DEPOSIT_AND_INVEST) {
    return DisplayTagEnum.INSTANT_INVEST;
  }
});

depositCashTransactionSchema.virtual("inInstantMoneyFlow").get(function (): boolean {
  const document = this as DepositCashTransactionDocument;
  const creditTicket = document.linkedCreditTicket as CreditTicketDocument;

  if (!creditTicket) {
    return false;
  }

  if (creditTicket?.status === "Credited") {
    return true;
  } else if (creditTicket?.status === "Rejected") {
    return false;
  }

  // if status is Pending we return nothing
});

/**
 * @description
 * The display quantinty we're showing users in asset transactions depends on the transaction category and status.
 *  1. Portfolio Sell: we do not show any quantinty
 *  2. Portfolio Buy: we do not show any quantinty
 *  3. Asset Sell: We use the display quantity of the order document
 *  4. Asset Buy: We use the display quantity of the order document
 */
assetTransactionSchema.virtual("getDisplayQuantity").get(function (): (
  userCurrency: currenciesConfig.MainCurrencyType,
  investmentProductsDict: { [isin: string]: InvestmentProductDocument }
) => number {
  const document = this as unknown as AssetTransactionDocument;

  return (userCurrency, investmentProductsDict) => {
    if (document.portfolioTransactionCategory === "update" && document.orders.length === 1) {
      return document.orders[0].getDisplayQuantity(
        userCurrency,
        investmentProductsDict[document.orders[0].isin],
        document
      );
    }
    return undefined;
  };
});

transactionSchema.virtual("hasTerminalStatus").get(function () {
  const transaction = this as TransactionDocument;

  return ["Cancelled", "Rejected", "DepositFailed", "Settled"].includes(transaction.status);
});

transactionSchema.virtual("displayStatus").get(function (): TransactionStatusType {
  const transaction = this as TransactionDocument;

  return transaction.status;
});

transactionSchema.virtual("displayDate").get(function () {
  const transaction = this as TransactionDocument;
  if (
    ["SavingsTopupTransaction", "SavingsWithdrawalTransaction", "SavingsDividendTransaction"].includes(
      transaction.category
    )
  ) {
    return transaction.createdAt;
  } else if (transaction.category === "ChargeTransaction") {
    return transaction.displayDate;
  } else {
    if (this.settledAt) {
      return this.settledAt;
    } else {
      return this.createdAt;
    }
  }
});

chargeTransactionSchema.virtual("displayDate").get(function () {
  const transaction = this as ChargeTransactionDocument;
  if (transaction.isCustody) {
    return new Date(`${transaction.chargeMonth}-15`);
  } else {
    return transaction.settledAt ?? transaction.createdAt;
  }
});

transactionSchema.virtual("sortingField").get(function () {
  const transaction = this as TransactionDocument;

  if (
    ["SavingsTopupTransaction", "SavingsWithdrawalTransaction", "SavingsDividendTransaction"].includes(
      transaction.category
    )
  ) {
    return transaction.createdAt;
  } else {
    // SortingField is not used for non-savings transactions.
    // The logic here is being added to match the 'displayDate' virtual.
    // We may need to modify it when we start using this field to sort
    // other transactions too.
    if (this.settledAt) {
      return this.settledAt;
    } else {
      return this.createdAt;
    }
  }
});

transactionSchema.virtual("activityFilter").get(function (): TransactionActivityFilterEnum {
  const category: TransactionCategoryType = (this as TransactionDocument).category;
  if (typeof category === "string" && category in CATEGORY_TO_ACTIVITY_FILTER) {
    return CATEGORY_TO_ACTIVITY_FILTER[category];
  }

  // Handle the case where 'category' is not a valid key
  return undefined; // or any default value you deem appropriate
});

transactionSchema.virtual("cashActivityFilter").get(function (): TransactionCashActivityFilterEnum {
  const category: TransactionCategoryType = (this as TransactionDocument).category;
  return CATEGORY_TO_CASH_ACTIVITY_FILTER[category];
});

transactionSchema.virtual("isCashFlowPositive").get(function (): boolean {
  const transaction = this as TransactionDocument;
  return POSITIVE_CASH_FLOW_CATEGORIES.includes(transaction.category);
});

transactionSchema.virtual("investmentActivityFilter").get(function (): TransactionInvestmentActivityFilterEnum {
  const category: TransactionCategoryType = (this as TransactionDocument).category;
  return CATEGORY_TO_INVESTMENT_ACTIVITY_FILTER[category];
});

// Activity filters for assetTransactions are Buy or Sell
assetTransactionSchema.virtual("activityFilter").get(function (): TransactionActivityFilterEnum {
  if (this.portfolioTransactionCategory === "buy") return TransactionActivityFilterEnum.Buy;
  else if (this.portfolioTransactionCategory === "sell") return TransactionActivityFilterEnum.Sell;
  else if ((this?.orders?.[0] as unknown as OrderDocument)?.side === "Buy")
    return TransactionActivityFilterEnum.Buy;
  else if ((this?.orders?.[0] as unknown as OrderDocument)?.side === "Sell")
    return TransactionActivityFilterEnum.Sell;
  else return TransactionActivityFilterEnum.Asset;
});

assetTransactionSchema
  .virtual("investmentActivityFilter")
  .get(function (): TransactionInvestmentActivityFilterEnum {
    if (this.portfolioTransactionCategory === "buy") return TransactionInvestmentActivityFilterEnum.Buy;
    else if (this.portfolioTransactionCategory === "sell") return TransactionInvestmentActivityFilterEnum.Sell;
    else if ((this?.orders?.[0] as unknown as OrderDocument)?.side === "Buy")
      return TransactionInvestmentActivityFilterEnum.Buy;
    else if ((this?.orders?.[0] as unknown as OrderDocument)?.side === "Sell")
      return TransactionInvestmentActivityFilterEnum.Sell;
  });

assetTransactionSchema.virtual("isCashFlowPositive").get(function (): boolean {
  const transaction = this as unknown as AssetTransactionDocument;
  if (transaction.portfolioTransactionCategory === "buy") return false;
  else if (transaction.portfolioTransactionCategory === "sell") return true;
  else if ((transaction?.orders?.[0] as unknown as OrderDocument)?.side === "Buy") return false;
  else if ((transaction?.orders?.[0] as unknown as OrderDocument)?.side === "Sell") return true;
});

chargeTransactionSchema.virtual("isCustody").get(function (): boolean {
  const transaction = this as ChargeTransactionDocument;
  return transaction.chargeType === "custody";
});

withdrawalCashTransactionSchema.virtual("status").get(function (): WithdrawalStatusType {
  if (!this.providers?.wealthkernel?.status) {
    return "Pending";
  }

  if (["Pending", "Active", "Cancelling", "Rejected"].includes(this.providers?.wealthkernel.status)) {
    return "Pending";
  } else if (this.withdrawalMethod === WithdrawalMethodEnum.WITH_INTERMEDIARY) {
    return this.transferWithIntermediary?.collection?.outgoingPayment?.providers?.devengo?.status === "confirmed"
      ? "Settled"
      : "Pending";
  } else {
    return this.providers?.wealthkernel.status;
  }
});

rebalanceTransactionSchema.virtual("status").get(function (): TransactionStatusType {
  if (["NotStarted", "PendingSell", "PendingBuy", "Rejected"].includes(this.rebalanceStatus)) {
    return "Pending";
  } else if (this.rebalanceStatus === "Cancelled") {
    return "Cancelled";
  } else if (this.rebalanceStatus === "Settled") {
    return "Settled";
  }
});

assetTransactionSchema.virtual("cashback", {
  ref: "CashbackTransaction",
  localField: "_id",
  foreignField: "linkedAssetTransaction",
  justOne: true
});

/**
 * @description: Checks whether a transaction is cancellable. A transaction is cancellable if:
 * - It is an asset/rebalance transaction.
 * - Its status is either PendingDeposit, PendingGift, or Pending.
 * - For asset transactions, there should be at least one cancellable order.
 * - For rebalances, all orders should be not submitted to our broker
 *
 * For this method to work orders must be populated!
 */
transactionSchema.virtual("getIsCancellable").get(function (): (
  user: UserDocument,
  investmentProductsDict: InvestmentProductsDictType
) => boolean {
  return (user, investmentProductsDict) => {
    const transaction = this as AssetTransactionDocument | RebalanceTransactionDocument;

    if (
      !["AssetTransaction", "RebalanceTransaction"].includes(transaction.category) ||
      !["PendingDeposit", "PendingGift", "Pending"].includes(transaction.status) ||
      !transaction.populated("orders")
    ) {
      return false;
    }

    if (transaction.category === "AssetTransaction") {
      return transaction.orders.some((order) =>
        order.getIsCancellable(user, investmentProductsDict[order.isin], transaction)
      );
    } else if (transaction.category === "RebalanceTransaction") {
      return transaction.orders.every((order) => !order.isSubmittedToBroker);
    }
  };
});

assetTransactionSchema.virtual("hasExecutionStarted").get(function (): boolean {
  const transaction = this as unknown as AssetTransactionDocument;

  const someOrdersAreSubmittedToWK = transaction.orders?.some((order) => order.isSubmittedToBroker);
  if (someOrdersAreSubmittedToWK) return true;
});

assetTransactionSchema.virtual("executionProgress").get(function (): ExecutionProgressType {
  const assetTransaction = this as unknown as AssetTransactionDocument;

  if (assetTransaction.status === "Pending") {
    const numberOfMatchedOrders = assetTransaction.orders.filter((order) => order.isMatched).length;
    const numberOfOrders = assetTransaction.orders.length;

    if (numberOfMatchedOrders > 0 && numberOfOrders !== numberOfMatchedOrders) {
      return {
        matched: numberOfMatchedOrders,
        total: numberOfOrders,
        label: "Partially executed"
      };
    }
  }
});

rebalanceTransactionSchema.virtual("hasExecutionStarted").get(function (): boolean {
  return (this as unknown as RebalanceTransactionDocument).hasSellExecutionStarted;
});

rebalanceTransactionSchema.virtual("hasSellExecutionStarted").get(function (): boolean {
  const transaction = this as unknown as RebalanceTransactionDocument;

  if (transaction.sellExecutionWindow?.start && transaction?.sellExecutionWindow?.start.getTime() < Date.now()) {
    return true;
  }

  return transaction.orders?.some((order) => order.side === "Sell" && order.isSubmittedToBroker);
});

rebalanceTransactionSchema.virtual("hasBuyExecutionStarted").get(function (): boolean {
  const transaction = this as unknown as RebalanceTransactionDocument;

  if (transaction.buyExecutionWindow?.start && transaction?.buyExecutionWindow?.start.getTime() < Date.now()) {
    return true;
  }

  return transaction.orders?.some((order) => order.side === "Buy" && order.isSubmittedToBroker);
});

depositCashTransactionSchema.virtual("linkedAssetTransaction", {
  ref: "AssetTransaction",
  localField: "_id",
  foreignField: "pendingDeposit",
  justOne: true
});
depositCashTransactionSchema.virtual("linkedSavingsTopup", {
  ref: "SavingsTopupTransaction",
  localField: "_id",
  foreignField: "pendingDeposit",
  justOne: true
});

rebalanceTransactionSchema.virtual("orders", {
  ref: "Order",
  localField: "_id",
  foreignField: "transaction",
  justOne: false
});

chargeTransactionSchema.virtual("orders", {
  ref: "Order",
  localField: "_id",
  foreignField: "transaction",
  justOne: false
});

revertRewardTransactionSchema.virtual("orders", {
  ref: "Order",
  localField: "_id",
  foreignField: "transaction",
  justOne: false
});

savingsTopupTransactionSchema.virtual("orders", {
  ref: "Order",
  localField: "_id",
  foreignField: "transaction",
  justOne: false
});
savingsTopupTransactionSchema.virtual("linkedSavingsDividend", {
  ref: "SavingsDividendTransaction",
  localField: "_id",
  foreignField: "linkedSavingsTopup",
  justOne: true
});

savingsTopupTransactionSchema.virtual("displayAmount").get(function (): number {
  const document = this as SavingsTopupTransactionDocument;
  return document.consideration.amount;
});

savingsTopupTransactionSchema.virtual("displayTitle").get(function (): string {
  const document = this as SavingsTopupTransactionDocument;

  if (document.pendingDeposit) {
    return "Deposit";
  } else {
    const savingsLabel = SAVINGS_PRODUCT_CONFIG_GLOBAL[document.savingsProduct].label;
    return `Cash Balance -> ${savingsLabel}`;
  }
});
// Requires 'orders' to be populated
savingsTopupTransactionSchema.virtual("internallyFilledAmount").get(function (): number {
  const document = this as SavingsTopupTransactionDocument;

  if (document.orders?.length > 0) {
    return document.orders
      .filter((order) => order.status === "InternallyFilled")
      .reduce((sum, order) => sum.plus(order.consideration.amount), new Decimal(0))
      .toNumber();
  }
  return 0;
});
savingsTopupTransactionSchema.virtual("remainingAmountToSubmit").get(function (): number {
  const document = this as SavingsTopupTransactionDocument;

  if (document.orders?.length > 0) {
    const amountRectrictedFromOrders = document.orders.reduce(
      (sum, order) => sum.plus(order.consideration.amount),
      new Decimal(0)
    );
    const remainingAmountToSubmit = new Decimal(document.consideration.amount).minus(amountRectrictedFromOrders);
    return remainingAmountToSubmit.toNumber();
  }

  return document.consideration.amount;
});

savingsWithdrawalTransactionSchema.virtual("orders", {
  ref: "Order",
  localField: "_id",
  foreignField: "transaction",
  justOne: false
});

savingsWithdrawalTransactionSchema.virtual("displayAmount").get(function (): number {
  const document = this as SavingsWithdrawalTransactionDocument;
  return document.consideration.amount;
});

savingsWithdrawalTransactionSchema.virtual("displayTitle").get(function (): string {
  const document = this as SavingsWithdrawalTransactionDocument;

  const savingsLabel = SAVINGS_PRODUCT_CONFIG_GLOBAL[document.savingsProduct].label;
  return `${savingsLabel} -> Cash Balance`;
});
// Requires 'orders' to be populated
savingsWithdrawalTransactionSchema.virtual("internallyFilledAmount").get(function (): number {
  const document = this as SavingsWithdrawalTransactionDocument;

  if (document.orders?.length > 0) {
    return document.orders
      .filter((order) => order.status === "InternallyFilled")
      .reduce((sum, order) => sum.plus(order.consideration.amount), new Decimal(0))
      .toNumber();
  }
  return 0;
});
savingsWithdrawalTransactionSchema.virtual("remainingAmountToSubmit").get(function (): number {
  const document = this as SavingsWithdrawalTransactionDocument;

  if (document.orders?.length > 0) {
    const amountRectrictedFromOrders = document.orders.reduce(
      (sum, order) => sum.plus(order.consideration.amount),
      new Decimal(0)
    );
    const remainingAmountToSubmit = new Decimal(document.consideration.amount).minus(amountRectrictedFromOrders);
    return remainingAmountToSubmit.toNumber();
  }

  return document.consideration.amount;
});

savingsDividendTransactionSchema.virtual("displayAmount").get(function (): number {
  const document = this as SavingsDividendTransactionDocument;
  return document.consideration.amount;
});

savingsDividendTransactionSchema.virtual("displayTitle").get(function (): string {
  return "Interest paid";
});

dividendTransactionSchema.virtual("status").get(function ():
  | TransactionStatusType
  | WealthkernelTransactionStatusType {
  const document = this as DividendTransactionDocument;
  return document.providers?.wealthkernel?.status || "Pending";
});

assetDividendTransactionSchema.virtual("status").get(function ():
  | TransactionStatusType
  | WealthkernelTransactionStatusType {
  const document = this as AssetDividendTransactionDocument;
  return document.providers?.wealthkernel?.status || "Pending";
});

wealthyhoodDividendTransactionSchema.virtual("status").get(function (): TransactionStatusType {
  const document = this as WealthyhoodDividendTransactionDocument;
  return document.deposit?.providers?.wealthkernel?.status == "Settled" ? "Settled" : "Pending";
});

chargeTransactionSchema.virtual("paymentStatus").get(function (): ChargePaymentStatusType {
  const document = this as ChargeTransactionDocument;

  if (document.chargeMethod !== "direct-debit") {
    return undefined;
  }

  if (
    ["cancelled", "customer_approval_denied", "failed", "charged_back"].includes(
      document.providers?.gocardless?.status
    )
  ) {
    return "Failed";
  } else if (document.providers?.gocardless?.status === "confirmed") {
    return "Confirmed";
  } else if (document.providers?.gocardless?.status === "paid_out") {
    return "PaidOut";
  } else return "Pending";
});

// Backwards compatibility for providers
depositCashTransactionSchema.virtual("truelayer").get(function (): any {
  return this?.providers?.truelayer;
});

chargeTransactionSchema.virtual("truelayer").get(function (): any {
  return this?.providers?.truelayer;
});

/**
 * INDEXES
 */
transactionSchema.index({ owner: 1, category: 1, status: 1 });
transactionSchema.index({ portfolio: 1, category: 1, status: 1 });
transactionSchema.index({ owner: 1, category: 1, rebalanceStatus: 1 });
transactionSchema.index({ category: 1, pendingDeposit: 1 });
transactionSchema.index({ category: 1, linkedSavingsTopup: 1 });
transactionSchema.index({ category: 1, "providers.wealthkernel.id": 1, status: 1 });
assetTransactionSchema.index({ pendingGift: 1 });
chargeTransactionSchema.index({ "providers.stripe.id": 1 });
// For transactionService.getPendingCashflowTransactions
depositCashTransactionSchema.index({
  portfolio: 1,
  linkedAutomation: 1,
  "providers.truelayer.status": 1,
  "providers.wealthkernel.status": 1
});
depositCashTransactionSchema.index({
  portfolio: 1,
  linkedAutomation: 1,
  depositMethod: 1,
  "providers.wealthkernel.status": 1
});
// For savingsProductService._getUserSavingsItem
savingsDividendTransactionSchema.index({
  portfolio: 1,
  dividendMonth: 1,
  savingsProduct: 1
});

// Add these indexes to better support the getTransactionsForReturnsUpBy queries
rebalanceTransactionSchema.index({ owner: 1, rebalanceStatus: 1 }); // For RebalanceTransaction query
chargeTransactionSchema.index({ owner: 1, chargeMethod: 1, status: 1 }); // For ChargeTransaction query
dividendTransactionSchema.index({ owner: 1, "providers.wealthkernel.status": 1 }); // For DividendTransaction query

/**
 * MODELS
 */

export const Transaction = mongoose.model<TransactionDocument>("Transaction", transactionSchema);

export const DepositCashTransaction = Transaction.discriminator<DepositCashTransactionDocument>(
  "DepositCashTransaction",
  depositCashTransactionSchema
);

export const WithdrawalCashTransaction = Transaction.discriminator<WithdrawalCashTransactionDocument>(
  "WithdrawalCashTransaction",
  withdrawalCashTransactionSchema
);

export const AssetTransaction = Transaction.discriminator<AssetTransactionDocument>(
  "AssetTransaction",
  assetTransactionSchema
);

export const RebalanceTransaction = Transaction.discriminator<RebalanceTransactionDocument>(
  "RebalanceTransaction",
  rebalanceTransactionSchema
);

export const StockSplitTransaction = Transaction.discriminator<StockSplitTransactionDocument>(
  "StockSplitTransaction",
  stockSplitTransactionSchema
);

export const DividendTransaction = Transaction.discriminator<DividendTransactionDocument>(
  "DividendTransaction",
  dividendTransactionSchema
);

export const AssetDividendTransaction = Transaction.discriminator<AssetDividendTransactionDocument>(
  "AssetDividendTransaction",
  assetDividendTransactionSchema
);

export const WealthyhoodDividendTransaction = Transaction.discriminator<WealthyhoodDividendTransactionDocument>(
  "WealthyhoodDividendTransaction",
  wealthyhoodDividendTransactionSchema
);

export const ChargeTransaction = Transaction.discriminator<ChargeTransactionDocument>(
  "ChargeTransaction",
  chargeTransactionSchema
);

export const RevertRewardTransaction = Transaction.discriminator<RevertRewardTransactionDocument>(
  "RevertRewardTransaction",
  revertRewardTransactionSchema
);

export const CashbackTransaction = Transaction.discriminator<CashbackTransactionDocument>(
  "CashbackTransaction",
  cashbackTransactionSchema
);

export const SavingsTopupTransaction = Transaction.discriminator<SavingsTopupTransactionDocument>(
  "SavingsTopupTransaction",
  savingsTopupTransactionSchema
);

export const SavingsWithdrawalTransaction = Transaction.discriminator<SavingsWithdrawalTransactionDocument>(
  "SavingsWithdrawalTransaction",
  savingsWithdrawalTransactionSchema
);

export const SavingsDividendTransaction = Transaction.discriminator<SavingsDividendTransactionDocument>(
  "SavingsDividendTransaction",
  savingsDividendTransactionSchema
);

/**
 * CHANGE STREAMS
 */
DbUtil.listenToChangeStream<TransactionDocument>(
  Transaction,
  "insert",
  async (insertedTransaction: TransactionDocument) => {
    await AccountingService.generateAccountingEntriesOnTransactionInsert(insertedTransaction);
  }
);

DbUtil.listenToChangeStream<TransactionDocument>(
  Transaction,
  "update",
  async (updatedTransaction: TransactionDocument, oldTransaction?: TransactionDocument | null) => {
    await AccountingService.generateAccountingEntriesOnTransactionUpdate(updatedTransaction, oldTransaction);
  }
);
