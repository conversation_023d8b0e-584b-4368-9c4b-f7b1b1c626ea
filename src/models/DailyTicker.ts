import mongoose, { Document, Schema } from "mongoose";
import { currenciesConfig, plansConfig } from "@wealthyhood/shared-configs";
import logger from "../external-services/loggerService";
import { PortfolioDocument } from "./Portfolio";
import { SavingsProductDocument } from "./SavingsProduct";
import { MultiCurrencyPriceType } from "currencies";

const { MainCurrencies } = currenciesConfig;

/**
 * DOCUMENTS
 */
export interface DailyTickerDocument extends Document {
  currency: currenciesConfig.MainCurrencyType;
  // NOTE: comparing to asset tickers, for portfolios we are using date instead of timestamp,
  // for backwards compatibility and because timestamp does not have any added
  // value for daily portfolio tickers.
  date: Date;
  returnPercentage: number;
  monthlyReturnPercentage: number;

  // virtuals
  readonly dateLabel: string;
  readonly getPrice: (currency: currenciesConfig.MainCurrencyType) => number;
}

export interface DailyPortfolioTickerDocument extends DailyTickerDocument {
  portfolio: mongoose.Types.ObjectId;
  pricePerCurrency: MultiCurrencyPriceType;
  openingPricePerCurrency?: MultiCurrencyPriceType;
  closingPricePerCurrency?: MultiCurrencyPriceType;
}

export interface DailySavingsProductTickerDocument extends DailyTickerDocument {
  savingsProduct: mongoose.Types.ObjectId | SavingsProductDocument;
  dailyDistributionFactor: number;
  oneDayYield: number;
  /**
   * This represents the date that the fund manager issued the fixing.
   * On weekends and holidays, we use the fixing from the last work day.
   * We want to keep this information for historical reasons.
   */
  fixingDate: Date;
}

/**
 * For every savings product the user has we create a daily ticker,
 * associating the portfolio with the savings product
 */
export interface DailyPortfolioSavingsTickerDocument extends DailyTickerDocument {
  portfolio: mongoose.Types.ObjectId | PortfolioDocument;
  savingsProduct: mongoose.Types.ObjectId | SavingsProductDocument;
  planPrice: plansConfig.PriceType;
  holdingAmount: number;
  planFee: number;
  // In Cents
  dailyAccrual: number;
}

/**
 * SCHEMA
 */
const multiCurrencyPriceType: Schema = new mongoose.Schema(
  {
    GBP: { type: Number, required: true },
    EUR: { type: Number, required: false },
    USD: { type: Number, required: false }
  },
  { _id: false }
);

const dailyTickerSchema: Schema = new mongoose.Schema(
  {
    currency: {
      type: String,
      enum: MainCurrencies,
      default: "GBP"
    },
    date: {
      type: Date,
      default: Date.now
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

const dailyPortfolioTickerSchema: Schema = new mongoose.Schema({
  portfolio: { type: mongoose.Schema.Types.ObjectId, ref: "Portfolio", required: true, index: true },
  wealthkernelValue: Number,
  pricePerCurrency: {
    type: multiCurrencyPriceType,
    required: [true, "Ticker price cannot be empty"]
  },
  openingPricePerCurrency: multiCurrencyPriceType,
  closingPricePerCurrency: multiCurrencyPriceType,
  returnPercentage: { type: Number, default: 0 },
  monthlyReturnPercentage: { type: Number, default: 0 }
});

const dailySavingsProductTickerSchema: Schema = new mongoose.Schema({
  dailyDistributionFactor: {
    type: Number,
    required: true
  },
  oneDayYield: {
    type: Number,
    required: true
  },
  savingsProduct: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "SavingsProduct",
    required: true
  },
  fixingDate: {
    type: Date,
    required: true
  }
});
dailySavingsProductTickerSchema.index({ savingsProduct: 1 }, { name: "DailySavingsProductTicker_savingsProduct" });

const dailyPortfolioSavingsTickerSchema: Schema = new mongoose.Schema({
  holdingAmount: {
    type: Number,
    required: true
  },
  planFee: {
    type: Number,
    required: true
  },
  dailyAccrual: {
    type: Number,
    required: true
  },
  planPrice: {
    type: String,
    enum: plansConfig.PriceArrayConst,
    required: true
  },
  savingsProduct: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "SavingsProduct",
    required: true
  },
  portfolio: { type: mongoose.Schema.Types.ObjectId, ref: "Portfolio", required: true }
});
dailyPortfolioSavingsTickerSchema.index(
  { savingsProduct: 1 },
  { name: "DailyPortfolioSavingsTicker_savingsProduct" }
);
dailyPortfolioSavingsTickerSchema.index({ portfolio: 1 }, { name: "DailyPortfolioSavingsTicker_portfolio" });

/**
 * VIRTUALS
 */
dailyPortfolioTickerSchema.virtual("getPrice").get(function (): (
  currency: currenciesConfig.MainCurrencyType
) => number {
  return (currency) => {
    const portfolioTicker = this as DailyPortfolioTickerDocument;
    return portfolioTicker.pricePerCurrency[currency];
  };
});

dailyTickerSchema.post("save", function (error: Error, doc: DailyTickerDocument, next: (err?: Error) => void) {
  if (error) {
    logger.error(`Error when saving ticker ${doc._id}`, { module: "DailyTicker", method: "post.save" });
    error.message += ` - for ticker ${doc._id}`;
    next(error);
  } else {
    next();
  }
});

dailyTickerSchema.virtual("dateLabel").get(function (): string {
  return new Date(this.date).toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "short"
  });
});

const DailyTicker = mongoose.model<DailyTickerDocument>("DailyTicker", dailyTickerSchema);

export const DailyPortfolioTicker = DailyTicker.discriminator<DailyPortfolioTickerDocument>(
  "DailyPortfolioTicker",
  dailyPortfolioTickerSchema
);
export const DailySavingsProductTicker = DailyTicker.discriminator<DailySavingsProductTickerDocument>(
  "DailySavingsProductTicker",
  dailySavingsProductTickerSchema
);
export const DailyPortfolioSavingsTicker = DailyTicker.discriminator<DailyPortfolioSavingsTickerDocument>(
  "DailyPortfolioSavingsTicker",
  dailyPortfolioSavingsTickerSchema
);
