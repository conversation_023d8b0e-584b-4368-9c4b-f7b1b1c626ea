import { UserDocument } from "./User";
import mongoose, { Document } from "mongoose";
import { ProviderEnum } from "../configs/providersConfig";
import {
  ReviewAnswerArray,
  ReviewAnswerType,
  ReviewStatusArray,
  ReviewStatusType
} from "../external-services/sumsubService";
import {
  WorkflowExecutionDecisionArray,
  WorkflowExecutionDecisionType,
  WorkflowStatusArray,
  WorkflowStatusType
} from "../external-services/jumioService";

/**
 * TYPES
 */
const KycOperationStatusArray = ["Pending", "Passed", "Failed", "ManuallyPassed"] as const;
export type KycOperationStatusType = (typeof KycOperationStatusArray)[number];

/**
 * @description
 * Maps Sumsub workflow decision to KYC operation statuses
 */
export const SUMSUB_ANSWER_TO_KYC_STATUS: Record<ReviewAnswerType, KycOperationStatusType> = {
  GREEN: "Passed",
  RED: "Failed"
};

/**
 * INTERFACES
 */
export interface KycOperationDTOInterface {
  updatedAt?: Date;
  owner: mongoose.Types.ObjectId;
  activeProviders: ProviderEnum[];
  status?: KycOperationStatusType;
  isManualAmlWorkflowSubmitted?: boolean;
  providers: {
    jumio?: {
      id: string;
      status: WorkflowStatusType;
      decision?: WorkflowExecutionDecisionType;
      webUrl: string;
      sdkToken: string;
    };
    sumsub?: {
      id?: string;
      submittedAt?: Date;
      status?: ReviewStatusType;
      decision?: ReviewAnswerType;
    };
  };
}

export interface KycOperationInterface extends Omit<KycOperationDTOInterface, "owner" | "status"> {
  createdAt: Date;
  owner: mongoose.Types.ObjectId | UserDocument;
  status?: KycOperationStatusType;

  // virtual
  isJourneyCompleted: boolean;
  isProcessed: boolean;
}

export interface KycOperationDocument extends KycOperationInterface, Document {}

/**
 * SCHEMA
 */

const kycOperationSchema = new mongoose.Schema(
  {
    activeProviders: {
      type: Object.values(ProviderEnum)
    },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.JUMIO]: {
          _id: false,
          id: { type: String },
          submittedAt: { type: Date },
          status: { type: String, enum: WorkflowStatusArray },
          decision: { type: String, enum: WorkflowExecutionDecisionArray },
          webUrl: { type: String },
          sdkToken: { type: String }
        },
        [ProviderEnum.SUMSUB]: {
          _id: false,
          id: { type: String },
          submittedAt: { type: Date },
          status: { type: String, enum: ReviewStatusArray },
          decision: { type: String, enum: ReviewAnswerArray }
        }
      }
    },
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    },
    status: { type: String, enum: KycOperationStatusArray, default: "Pending" as KycOperationStatusType },
    isManualAmlWorkflowSubmitted: { type: Boolean, default: false }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

kycOperationSchema.virtual("isJourneyCompleted").get(function (): boolean {
  if (this.activeProviders.includes(ProviderEnum.JUMIO)) {
    const journeyCompletionStatuses: WorkflowStatusType[] = ["PROCESSED", "ACQUIRED"];
    return journeyCompletionStatuses.includes(this.providers?.jumio?.status as WorkflowStatusType);
  } else return !!this.providers?.sumsub?.status && this.providers?.sumsub?.status !== "init";
});

// For Jumio, we needed to distinguish from the moment the journey was completed until Jumio had extracted the data.
// In Sumsub, we only update the status of the KYC operation from 'init' to 'pending' when the data is extracted,
// therefore, we don't need to distinguish between those two moments.
kycOperationSchema.virtual("isProcessed").get(function (): boolean {
  const document = this as KycOperationDocument;

  if (document.activeProviders.includes(ProviderEnum.JUMIO)) {
    return document.providers?.jumio?.status === "PROCESSED";
  } else return document.isJourneyCompleted;
});

export const KycOperation = mongoose.model<KycOperationDocument>("KycOperation", kycOperationSchema);
