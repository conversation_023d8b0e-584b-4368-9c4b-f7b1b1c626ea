import mongoose, { Document } from "mongoose";
import { ProviderEnum } from "../configs/providersConfig";
import {
  PayoutStatusArray as GoCardlessPayoutStatusArray,
  PayoutStatusType as GoCardlessPayoutStatusType
} from "../external-services/goCardlessPaymentsService";
import { IncomingPaymentStatusArray, IncomingPaymentStatusType } from "../external-services/devengoService";

/**
 * TYPES
 */
export const PayoutStatusArray = ["Pending", "Completed"] as const;
export type PayoutStatusType = (typeof PayoutStatusArray)[number];

/**
 * INTERFACES
 */
export interface PayoutDTOInterface {
  status?: PayoutStatusType;
  reference: string;
  activeProviders: ProviderEnum[];
  providers: {
    gocardless?: {
      id?: string;
      status?: GoCardlessPayoutStatusType;
    };
    devengo?: {
      id?: string;
      status?: IncomingPaymentStatusType;
    };
  };
}

export interface PayoutInterface extends Omit<PayoutDTOInterface, "status"> {
  status: PayoutStatusType;

  // VIRTUALS
  readonly hasTerminalGoCardlessStatus: boolean;
}

export interface PayoutDocument extends PayoutInterface, Document {}

/**
 * SCHEMA
 */
const payoutSchema = new mongoose.Schema(
  {
    status: {
      type: String,
      enum: PayoutStatusArray,
      default: "Pending"
    },
    reference: { required: true, type: String },
    activeProviders: {
      type: Object.values(ProviderEnum),
      required: true
    },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.GOCARDLESS]: {
          _id: false,
          id: { type: String },
          status: { type: String, enum: GoCardlessPayoutStatusArray }
        },
        [ProviderEnum.DEVENGO]: {
          _id: false,
          id: { type: String },
          status: { type: String, enum: IncomingPaymentStatusArray }
        }
      }
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

payoutSchema.virtual("hasTerminalGoCardlessStatus").get(function () {
  const payout = this as unknown as PayoutDocument;

  return ["paid", "bounced"].includes(payout.providers?.gocardless?.status);
});

export const Payout = mongoose.model<PayoutDocument>("Payout", payoutSchema);
