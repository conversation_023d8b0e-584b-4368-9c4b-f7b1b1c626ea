import mongoose, { Document } from "mongoose";
import { ProviderEnum } from "../configs/providersConfig";
import { BonusStatusType } from "../external-services/wealthkernelService";
import { UserDocument } from "./User";

/**
 * ENUMS
 */
export enum PaymentMethodTypeEnum {
  CARD = "card"
}

export enum WalletEnum {
  APPLE_PAY = "apple_pay",
  GOOGLE_PAY = "google_pay"
}

export enum PaymentMethodBrandEnum {
  VISA = "visa",
  MASTERCARD = "mastercard",
  AMEX = "amex",
  DISCOVER = "discover",
  DINERS = "diners"
}

/**
 * INTERFACES
 */
export interface PaymentMethodDTOInterface {
  owner: mongoose.Types.ObjectId;
  type: PaymentMethodTypeEnum;
  brand: PaymentMethodBrandEnum;
  lastFourDigits: string;
  fingerprint: string;
  providers: {
    stripe: {
      id?: string;
      status?: BonusStatusType;
      submittedAt?: Date;
    };
  };

  // OPTIONALS
  wallet?: WalletEnum;
}

export interface PaymentMethodInterface extends Omit<PaymentMethodDTOInterface, "owner"> {
  owner: mongoose.Types.ObjectId | UserDocument;
}

export interface PaymentMethodDocument extends PaymentMethodInterface, Document {}

/**
 * SCHEMA
 */
const paymentMethodSchema = new mongoose.Schema(
  {
    type: { type: String, enum: PaymentMethodTypeEnum, required: true },
    brand: { type: String, enum: PaymentMethodBrandEnum, required: true },
    wallet: { type: String, enum: WalletEnum, required: false },
    lastFourDigits: String,
    fingerprint: String,
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.STRIPE]: {
          _id: false,
          id: { type: String }
        }
      }
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

export const PaymentMethod = mongoose.model<PaymentMethodDocument>("PaymentMethod", paymentMethodSchema);
