import mongoose, { Document } from "mongoose";

export enum AssetNewsSentimentEnum {
  Positive = "Positive",
  Neutral = "Neutral",
  Negative = "Negative"
}

import { InvestmentProductDocument } from "./InvestmentProduct";
import DateUtil from "../utils/dateUtil";

export interface AssetNewsDTOInterface {
  investmentProducts: mongoose.Types.ObjectId[] | InvestmentProductDocument[];
  newsUrl: string;
  imageUrl: string;
  title: string;
  text: string;
  source: string;
  topics?: string[];
  tickers?: string[];
  date: Date;
  sentiment: AssetNewsSentimentEnum;
  type: string;
  providers: {
    stockNews: {
      id: string;
    };
  };
}

export interface AssetNewsInterface extends Omit<AssetNewsDTOInterface, "investmentProducts"> {
  investmentProducts: mongoose.Types.ObjectId[] | InvestmentProductDocument[];
  createdAt: Date;

  // virtuals
  displayDate: string;
}

export interface AssetNewsDocument extends AssetNewsInterface, Document {}

const assetNewsSchema = new mongoose.Schema(
  {
    providers: {
      _id: false,
      type: {
        stockNews: {
          _id: false,
          id: { type: String }
        }
      }
    },
    newsUrl: { type: String },
    imageUrl: { type: String },
    title: { type: String },
    text: { type: String },
    source: { type: String },
    date: { type: Date },
    sentiment: { type: String, enum: Object.values(AssetNewsSentimentEnum) },
    type: { type: String },
    topics: { type: [String] },
    tickers: { type: [String] },
    investmentProducts: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "InvestmentProduct",
        required: true
      }
    ]
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: { createdAt: true, updatedAt: false } }
);

/**
 * INDEXES
 */
assetNewsSchema.index({ investmentProducts: 1, date: -1 });
assetNewsSchema.index({ "providers.stockNews.id": 1 });

assetNewsSchema.virtual("displayDate").get(function () {
  const now = new Date(Date.now());
  const diffInMinutes = DateUtil.dateDiffInWholeMinutes(this.date, now);
  const diffInHours = DateUtil.dateDiffInWholeHours(this.date, now);

  if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  } else return DateUtil.formatDateToDDMONYYYYHHMM(this.date);
});

export const AssetNews = mongoose.model<AssetNewsDocument>("AssetNews", assetNewsSchema);
