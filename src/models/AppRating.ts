import { UserDocument } from "./User";
import mongoose, { Document } from "mongoose";

export enum AppRatingStatusEnum {
  INCOMPLETE = "incomplete",
  COMPLETED = "completed"
}

export const StarRatingAllowedValues = [1, 2, 3, 4, 5];

/**
 * INTERFACES
 */
export interface AppRatingDTOInterface {
  owner: mongoose.Types.ObjectId;
  status?: AppRatingStatusEnum;
  feedback?: string;
  starRating?: number;
  createdAt?: Date;
}

export interface AppRatingInterface extends Omit<AppRatingDTOInterface, "owner" | "createdAt" | "status"> {
  owner: mongoose.Types.ObjectId | UserDocument;
  status: AppRatingStatusEnum;
  createdAt: Date;
}

export interface AppRatingDocument extends AppRatingInterface, Document {}

/**
 * SCHEMA
 */
const appRatingSchema = new mongoose.Schema(
  {
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    },
    status: { type: String, enum: Object.values(AppRatingStatusEnum), default: AppRatingStatusEnum.INCOMPLETE },
    feedback: { type: String, required: false },
    starRating: { type: Number, enum: StarRatingAllowedValues, required: false }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);
appRatingSchema.index({ owner: 1, createdAt: -1 });

export const AppRating = mongoose.model<AppRatingDocument>("AppRating", appRatingSchema);
