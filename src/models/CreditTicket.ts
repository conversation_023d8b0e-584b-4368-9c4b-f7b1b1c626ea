import { currenciesConfig } from "@wealthyhood/shared-configs";
import mongoose, { Document } from "mongoose";
import { PortfolioDocument } from "./Portfolio";
import { DepositCashTransactionDocument } from "./Transaction";
import { UserDocument } from "./User";
import { ProviderEnum } from "../configs/providersConfig";
import { InternalTransferStatusArray, InternalTransferStatusType } from "../external-services/wealthkernelService";

/**
 * ENUMS
 */
export enum CreditTicketPopulationFieldsEnum {
  OWNER = "owner",
  PORTFOLIO = "portfolio",
  DEPOSIT = "linkedDeposit"
}

export const CreditTicketStatusArray = ["Pending", "Credited", "Settled", "Rejected"] as const;
export type CreditTicketStatusType = (typeof CreditTicketStatusArray)[number];

export interface CreditTicketDTOInterface {
  owner: mongoose.Types.ObjectId;
  portfolio: mongoose.Types.ObjectId;
  consideration: {
    currency: currenciesConfig.MainCurrencyType;
    amount: number; // stored in cents
  };
  status: CreditTicketStatusType;
  creditedAt?: Date;
  settledAt?: Date;
  deposit?: {
    activeProviders: ProviderEnum[];
    providers?: {
      wealthkernel: {
        id?: string;
        status?: InternalTransferStatusType;
        submittedAt?: Date;
      };
    };
  };
  createdAt?: Date;
}

export interface CreditTicketInterface extends Omit<CreditTicketDTOInterface, "owner" | "portfolio"> {
  owner: UserDocument | mongoose.Types.ObjectId;
  portfolio: PortfolioDocument | mongoose.Types.ObjectId;

  // VIRTUALS
  readonly linkedDeposit?: DepositCashTransactionDocument;
}

export interface CreditTicketDocument extends CreditTicketInterface, Document {}

const creditTicketSchema = new mongoose.Schema(
  {
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    },
    portfolio: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Portfolio",
      required: true
    },
    consideration: {
      currency: {
        type: String,
        enum: Object.values(currenciesConfig.MainCurrencies),
        required: true
      },
      amount: {
        type: Number,
        required: true
      }
    },
    status: {
      type: String,
      enum: CreditTicketStatusArray,
      required: true
    },
    creditedAt: {
      type: Date,
      required: false
    },
    settledAt: {
      type: Date,
      required: false
    },
    deposit: {
      activeProviders: { type: Object.values(ProviderEnum), required: true },
      providers: {
        _id: false,
        type: {
          [ProviderEnum.WEALTHKERNEL]: {
            _id: false,
            id: { type: String },
            status: { type: String, enum: InternalTransferStatusArray },
            submittedAt: { type: Date }
          }
        }
      }
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

creditTicketSchema.virtual("linkedDeposit", {
  ref: "DepositCashTransaction",
  localField: "_id",
  foreignField: "linkedCreditTicket",
  justOne: true
});

export const CreditTicket = mongoose.model<CreditTicketDocument>("CreditTicket", creditTicketSchema);
