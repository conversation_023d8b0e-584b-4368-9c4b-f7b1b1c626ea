import mongoose, { Document, Schema } from "mongoose";
import Decimal from "decimal.js";
import { currenciesConfig, giftsConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { UserDocument } from "./User";
import {
  DepositStatusArray,
  DepositStatusType,
  WealthkernelOrderStatusArray,
  WealthkernelOrderStatusType
} from "../external-services/wealthkernelService";
import { FeesType } from "./Transaction";
import { getTotalFeeAmount } from "../utils/feesUtil";
import { ProviderEnum } from "../configs/providersConfig";
import DbUtil from "../utils/dbUtil";
import { AccountingService } from "../services/accountingService";
import { generateUserFriendlyId } from "../utils/hashUtil";

const { AssetArrayConst } = investmentUniverseConfig;
const { RESTRICTED_HOLDING_PERIOD_DAYS } = giftsConfig;
const FALLBACK_EXCHANGE_RATE = 1;

/**
 * ENUMS
 */
export enum RewardPopulationFieldsEnum {
  TARGET_USER = "targetUser"
}

/**
 * TYPES
 */
const RewardDepositStatusArray = ["Empty", "Pending", "Settled"] as const;
type RewardDepositStatusType = (typeof RewardDepositStatusArray)[number];
const RewardOrderStatusArray = ["Empty", "Pending", "Settled"] as const;
type RewardOrderStatusType = (typeof RewardOrderStatusArray)[number];
export const RewardStatusArray = ["Pending", "Settled"] as const;
export type RewardStatusType = (typeof RewardStatusArray)[number];

type DisplayExchangeRateType = {
  rate: number;
  currency: currenciesConfig.MainCurrencyType;
};

export type UnitPriceType = {
  amount: number;
  currency: currenciesConfig.MainCurrencyType;
};

export enum RewardActivityFilterEnum {
  Rewards = "Rewards"
}

export enum RewardInvestmentActivityFilterEnum {
  Rewards = "Rewards"
}

/**
 * INTERFACES
 */
export interface RewardDTOInterface {
  // the ETF that we'll give as a reward
  asset: investmentUniverseConfig.AssetType;
  isin: string;
  consideration: {
    currency: currenciesConfig.MainCurrencyType;
    // the total value of the ETF that we'll reward - stored in cents
    amount: number;
  };
  // asset quantity that user buys when the ETF order settles
  quantity?: number;
  displayFxFee?: number; // FX fee displayed to the user (in whole currency)
  hasViewedAppModal?: boolean;
  accepted?: boolean;
  unrestrictedAt?: Date;
  // the user that made the referral (in case of double reward)
  referrer?: mongoose.Types.ObjectId;
  // the campaign the reward came from (in case of single reward)
  referralCampaign?: string;
  // the user that got referred
  referral: mongoose.Types.ObjectId;
  // the user that the reward is for (is referrer or referral)
  targetUser: mongoose.Types.ObjectId;
  // the date when updates takes place on the document
  // currently used to store the date when the reward is Settled
  updatedAt?: Date;
  // The current status of the reward.
  // This field tracks the progression of the reward through its lifecycle.
  // 'Pending' indicates that the reward process is ongoing, either waiting for
  // the deposit transaction to be settled or the order transaction to be matched.
  // 'Settled' signifies that the reward process is complete, with both the deposit
  // and order transactions successfully finalized.
  status?: RewardStatusType;
  // This represents the actual order settlement in the market which usually happens in T+1 where T is the
  // execution date. This is **NOT** related to us marking the parent transaction as 'Settled'.
  marketSettledAt?: Date;
  // The exchange rate is our provider exchange rate **modified** by our FX spread.
  exchangeRate?: number;
  // the associated deposit transaction that we make from the
  // Wealthyhood wallet to the user's e-money wallet
  deposit?: {
    activeProviders: ProviderEnum[];
    providers?: {
      wealthkernel: {
        id?: string;
        status?: DepositStatusType;
        submittedAt?: Date;
      };
    };
  };
  // the order request to move the money from the user's e-wallet
  // to buy the corresponding ETF
  order?: {
    activeProviders: ProviderEnum[];
    providers?: {
      wealthkernel: {
        id?: string;
        status?: WealthkernelOrderStatusType;
        submittedAt?: Date;
        brokerFxRate?: number; // Raw FX rate from WealthKernel
        baseExchangeRate?: number; // Base exchange rate from WealthKernel
        accountingBrokerFxFee?: number; // FX fee charged by WealthKernel (in cents)
      };
    };
  };
  userFriendlyId?: string;
  unitPrice?: UnitPriceType;
}

export interface RewardInterface
  extends Omit<RewardDTOInterface, "hasViewedAppModal" | "accepted" | "referrer" | "referral" | "targetUser"> {
  createdAt: Date;
  unrestrictedAt: Date;
  referrer: mongoose.Types.ObjectId | UserDocument;
  referral: mongoose.Types.ObjectId | UserDocument;
  targetUser: mongoose.Types.ObjectId | UserDocument;
  hasViewedAppModal: boolean;
  accepted: boolean;
  fees: FeesType;
  unitPrice?: UnitPriceType;

  // virtuals
  readonly depositStatus: RewardDepositStatusType;
  readonly orderStatus: RewardOrderStatusType;
  readonly totalFeeAmount: number;
  readonly displayDate: Date | string;
  readonly displayAmount: number;
  readonly displayUnitPrice?: UnitPriceType;
  readonly wealthyhoodUnitPrice?: UnitPriceType;
  readonly activityFilter: RewardActivityFilterEnum;
  readonly investmentActivityFilter?: RewardInvestmentActivityFilterEnum;
  readonly isReward: boolean;
  readonly displayExchangeRate?: DisplayExchangeRateType;
  readonly displayUserFriendlyId?: string;
}

export interface RewardDocument extends RewardInterface, Document {}

/**
 * SCHEMA
 */
const feesSchema: Schema = new mongoose.Schema(
  {
    fx: {
      currency: {
        type: String,
        enum: ["EUR", "GBP", "USD"],
        default: "GBP"
      },
      amount: { type: Number }
    },
    commission: {
      currency: {
        type: String,
        enum: ["EUR", "GBP", "USD"],
        default: "GBP"
      },
      amount: { type: Number }
    },
    executionSpread: {
      currency: {
        type: String,
        enum: ["EUR", "GBP", "USD"],
        default: "GBP"
      },
      amount: { type: Number }
    }
  },
  { _id: false }
);

const rewardSchema = new mongoose.Schema(
  {
    asset: { type: String, enum: AssetArrayConst, required: true },
    isin: { type: String, required: true },
    consideration: {
      currency: {
        type: String,
        enum: ["EUR", "GBP", "USD"],
        default: "GBP"
      },
      amount: { type: Number, required: true }
    },
    fees: feesSchema,
    displayFxFee: { type: Number },
    status: {
      type: String,
      enum: RewardStatusArray,
      default: "Pending"
    },
    createdAt: { type: Date, default: Date.now },
    deposit: {
      activeProviders: { type: Object.values(ProviderEnum), required: true },
      providers: {
        _id: false,
        type: {
          [ProviderEnum.WEALTHKERNEL]: {
            _id: false,
            id: { type: String },
            status: { type: String, enum: DepositStatusArray },
            submittedAt: { type: Date }
          }
        }
      }
    },
    hasViewedAppModal: { type: Boolean, default: false },
    accepted: { type: Boolean },
    order: {
      activeProviders: { type: Object.values(ProviderEnum), required: true },
      providers: {
        _id: false,
        type: {
          [ProviderEnum.WEALTHKERNEL]: {
            _id: false,
            id: { type: String },
            status: { type: String, enum: WealthkernelOrderStatusArray },
            submittedAt: { type: Date },
            brokerFxRate: { type: Number },
            baseExchangeRate: { type: Number },
            accountingBrokerFxFee: { type: Number }
          }
        }
      }
    },
    quantity: Number,
    marketSettledAt: { type: Date, required: false },
    exchangeRate: { type: Number, required: false },
    unitPrice: {
      currency: {
        type: String,
        enum: ["EUR", "GBP", "USD"]
      },
      amount: { type: Number }
    },
    referrer: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User"
    },
    referralCampaign: String,
    referral: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    },
    targetUser: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    },
    unrestrictedAt: {
      type: Date,
      default: new Date(+new Date() + RESTRICTED_HOLDING_PERIOD_DAYS * 24 * 60 * 60 * 1000)
    },
    updatedAt: { type: Date, default: Date.now },
    userFriendlyId: { type: String }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

/**
 * VIRTUALS
 */
rewardSchema.virtual("depositStatus").get(function (): RewardDepositStatusType {
  if (!this.deposit?.providers?.wealthkernel?.id) {
    return "Empty";
  } else {
    if (this.deposit?.providers?.wealthkernel?.status === "Settled") {
      return "Settled";
    } else {
      return "Pending";
    }
  }
});
rewardSchema.virtual("orderStatus").get(function (): RewardOrderStatusType {
  if (!this.order?.providers?.wealthkernel?.id) {
    return "Empty";
  } else {
    if (this.order.providers?.wealthkernel.status === "Matched") {
      return "Settled";
    } else {
      return "Pending";
    }
  }
});

rewardSchema.virtual("displayDate").get(function () {
  // Example logic (modify according to your needs)
  if (this.updatedAt) {
    return this.updatedAt;
  } else {
    return this.createdAt;
  }
});

rewardSchema.virtual("displayAmount").get(function (): number {
  const document = this as RewardDocument;
  return document.consideration.amount;
});

rewardSchema.virtual("displayExchangeRate").get(function (): DisplayExchangeRateType {
  const reward = this as RewardDocument;

  if (reward.exchangeRate) {
    return {
      rate: new Decimal(reward.exchangeRate).toDecimalPlaces(2).toNumber(),
      currency: reward.unitPrice.currency
    };
  }
});

/**
 * @description This is used for display purposes by the clients. It may seem as if there is
 * overlap with the unitPrice in the document, but the document field is the source of truth for
 * the actual unit price at the time of execution. This virtual field is the unit price that
 * we *want* to display to the users.
 *
 * In pounds.
 */
rewardSchema.virtual("displayUnitPrice").get(function (): UnitPriceType {
  const reward = this as RewardDocument;
  if (reward.consideration?.amount && reward.quantity) {
    return {
      amount: Decimal.div(reward.consideration.amount, reward.quantity)
        .mul(reward.exchangeRate ?? FALLBACK_EXCHANGE_RATE)
        .round()
        .div(100)
        .toNumber(),
      currency: reward.unitPrice?.currency
    };
  } else {
    return reward.unitPrice;
  }
});

/**
 * @description This field just returns the displayUnitPrice for now. The reason why we do that
 * is because the 'displayUnitPrice' is intended for display purposes by the clients only
 * and should not be used for calculations. So in the future we may make changes on how we calc
 * the wealthyhood unit price and that may be different than the unit price we display in the client
 * so let's keep those fields separate for flexibility.
 */
rewardSchema.virtual("wealthyhoodUnitPrice").get(function (): UnitPriceType {
  return (this as RewardDocument).displayUnitPrice;
});

rewardSchema.virtual("activityFilter").get(function (): RewardActivityFilterEnum {
  return RewardActivityFilterEnum.Rewards;
});

rewardSchema.virtual("investmentActivityFilter").get(function (): RewardInvestmentActivityFilterEnum {
  return RewardInvestmentActivityFilterEnum.Rewards;
});

rewardSchema.virtual("totalFeeAmount").get(function (): number {
  const reward = (this as RewardDocument).toObject({ virtuals: false });
  return getTotalFeeAmount(reward.fees);
});

rewardSchema.virtual("isReward").get(function (): boolean {
  return true;
});

/**
 * If present, this is a shortened hash based on the Mongo document ID. Otherwise this is the regular document ID.
 */
rewardSchema.virtual("displayUserFriendlyId").get(function (): string {
  return this.userFriendlyId ? `ORD-${this.userFriendlyId.toUpperCase()}` : this.id;
});

/**
 * INDEXES
 */
rewardSchema.index({ targetUser: 1, status: 1 });

/**
 * HOOKS
 */
rewardSchema.pre("save", function (next) {
  if (this.isNew && !this.userFriendlyId) {
    this.userFriendlyId = generateUserFriendlyId(this._id.toString());
  }
  next();
});

export const Reward = mongoose.model<RewardDocument>("Reward", rewardSchema);

/**
 * CHANGE STREAMS
 */
DbUtil.listenToChangeStream<RewardDocument>(
  Reward,
  "update",
  async (updatedReward: RewardDocument, oldReward?: RewardDocument | null) => {
    await AccountingService.generateAccountingEntriesOnRewardUpdate(updatedReward, oldReward);
  }
);
