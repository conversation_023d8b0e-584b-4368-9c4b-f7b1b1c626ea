import mongoose, { Document } from "mongoose";
import validator from "validator";
import { customAlphabet } from "nanoid";
import { UserDocument } from "./User";

const alphabet = "0123456789abcdefghijklmnopqrstuvwxyz";
const nanoidShort = customAlphabet(alphabet, 8);
const nanoidLong = customAlphabet(alphabet, 20);
const nanoidNum10 = customAlphabet("0123456789", 10);

/**
 * TYPES
 */
export type GoogleAdsMetadataType = {
  adSetId?: string;
  campaign?: string;
  campaignId?: string;
  gclid?: string;
};
export type ParticipantMetadataType = {
  financeAds?: { influencerId: string };
  googleAds?: GoogleAdsMetadataType;
  submissionTech?: { clickId: string };
};
const PlatformArray = ["android", "ios"] as const;
export type PlatformType = (typeof PlatformArray)[number];
export const ParticipantRoleArray = ["BASIC", "AMBASSADOR"] as const;
export type ParticipantRoleType = (typeof ParticipantRoleArray)[number];
const TrackingSourceArray = ["apple", "google", "meta", "organic"] as const;
export type TrackingSourceType = (typeof TrackingSourceArray)[number];

/**
 * INTERFACES
 */
export interface ParticipantDTOInterface {
  appInstallInfo?: {
    createdAt: Date;
    platform: PlatformType;
  };
  // This field is currently used for E2E tracking from Landing Page to Server
  // only on the GA Service. For events sent through segment to third party services
  // we use appsflyerId as anonymousId when we don't have user's id yet.
  anonymousId?: string;
  appsflyerId?: string;
  attributionErrorMsg?: string;
  email?: string;
  gaClientId?: string;
  pageUserLanded?: string;
  grsfId?: string;
  metadata?: ParticipantMetadataType;
  participantRole?: ParticipantRoleType;
  referrer?: mongoose.Types.ObjectId;
  trackingSource?: TrackingSourceType;
}

export interface ParticipantInterface extends Omit<ParticipantDTOInterface, "referrer"> {
  wlthdId: string;
  participantRole: ParticipantRoleType;
  referrer?: mongoose.Types.ObjectId | ParticipantDocument;

  // virtuals
  readonly isAmbassador: boolean;
  readonly owner?: UserDocument;
}

export interface ParticipantDocument extends ParticipantInterface, Document {}

/**
 * SCHEMA
 */
const participantSchema = new mongoose.Schema(
  {
    appInstallInfo: {
      createdAt: Date,
      platform: {
        type: String,
        enum: PlatformArray
      }
    },
    anonymousId: {
      type: String,
      default: () => nanoidLong(),
      unique: true
    },
    appsflyerId: {
      type: String,
      required: false
    },
    attributionErrorMsg: { type: String, required: false },
    email: {
      type: String,
      unique: true,
      lowercase: true,
      trim: true,
      validate: [validator.isEmail, "Invalid Email Address"],
      required: false
    },
    gaClientId: {
      type: String,
      default: () => `${nanoidNum10()}.${nanoidNum10()}`
    },
    pageUserLanded: {
      type: String,
      required: false
    },
    grsfId: {
      type: String,
      required: false
    },
    wlthdId: {
      type: String,
      required: true,
      default: () => nanoidShort(),
      unique: true
    },
    participantRole: {
      type: String,
      default: "BASIC",
      enum: ParticipantRoleArray
    },
    referrer: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Participant"
    },
    metadata: {
      submissionTech: { clickId: { type: String, required: false } },
      financeAds: { influencerId: { type: String, required: false } },
      googleAds: {
        type: {
          gclid: { type: String, required: false },
          campaign: { type: String, required: false },
          adSetId: { type: String, required: false },
          campaignId: { type: String, required: false }
        },
        required: false
      }
    },
    trackingSource: {
      type: String,
      default: "organic",
      enum: TrackingSourceArray
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

/**
 * VIRTUALS
 */
participantSchema.virtual("isAmbassador").get(function (): boolean {
  return this.participantRole === "AMBASSADOR";
});
participantSchema.virtual("owner", {
  ref: "User",
  localField: "email",
  foreignField: "email",
  justOne: true
});

export const Participant = mongoose.model<ParticipantDocument>("Participant", participantSchema);
