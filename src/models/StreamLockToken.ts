import mongoose, { Document, Schema } from "mongoose";

export interface StreamLockTokenInterface {
  processedAt: Date;
}

export interface StreamLockTokenDocument extends StreamLockTokenInterface, Document {}

const streamLockTokenSchema: Schema = new mongoose.Schema(
  {
    _id: {
      type: String,
      required: true
    },
    processedAt: {
      type: Date,
      default: Date.now,
      expires: 60 * 60 * 24 * 7 // Auto-delete after 7 days (TTL index)
    }
  },
  {
    timestamps: false, // We don't need createdAt/updatedAt since we have processedAt
    _id: false // We're setting _id manually
  }
);

// Create unique index on _id to prevent duplicate processing
streamLockTokenSchema.index({ _id: 1 }, { unique: true });

export const StreamLockToken = mongoose.model<StreamLockTokenDocument>("StreamLockToken", streamLockTokenSchema);
