import { ProviderEnum } from "../configs/providersConfig";
import { FinimizeContentTypeEnum } from "../external-services/finimizeService";
import mongoose, { Document } from "mongoose";
import NotificationService from "../services/notificationService";
import DbUtil from "../utils/dbUtil";

export enum ContentEntryContentTypeEnum {
  // used for analyst insights
  ANALYSIS = "ANALYSIS",
  WEEKLY_REVIEW = "WEEKLY_REVIEW",
  QUICK_TAKE = "QUICK_TAKE",
  // used for guides
  GUIDE = "GUIDE"
}

export enum ContentEntryCategoryEnum {
  ANALYST_INSIGHTS = "ANALYST_INSIGHTS",
  GUIDES = "GUIDES"
}

/**
 * INTERFACES
 */
export interface ContentEntryDTOInterface {
  contentType: ContentEntryContentTypeEnum;
  category: ContentEntryCategoryEnum;
  activeProviders: ProviderEnum[];
  title: string;
  // This field is different from Finimize's publishedAt field, which is the date the content was published on Finimize.
  // This field is the date the content was published on Wealthyhood (uploaded to Contentful).
  publishAt?: Date;
  providers: {
    finimize?: {
      id: string;
      /**
       * Finimize doesn't support search by id, se we store the content type and published date
       * to be able to retrieve the finimize entry in case we need to.
       */
      publishedAt: Date;
      contentType: FinimizeContentTypeEnum;
      originalTitle?: string; // Store the original title for reference
    };
    contentful?: {
      id: string;
      spaceId: string;
      environmentId: string;
    };
  };
  shouldNotifyUsers: boolean;
  createdAt?: Date;
}

interface ContentEntryInterface extends Omit<ContentEntryDTOInterface, "createdAt"> {
  createdAt: Date;
  updatedAt: Date;
}

export interface ContentEntryDocument extends ContentEntryInterface, Document {}

/**
 * SCHEMA
 */
const contentEntrySchema = new mongoose.Schema(
  {
    contentType: { type: String, enum: Object.values(ContentEntryContentTypeEnum) },
    category: { type: String, enum: Object.values(ContentEntryCategoryEnum) },
    activeProviders: { type: Object.values(ProviderEnum) },
    shouldNotifyUsers: { type: Boolean },
    // We should not have duplicate titles in the database
    title: { type: String, unique: true },
    publishAt: { type: Date, default: Date.now },
    providers: {
      _id: false,
      type: {
        [ProviderEnum.FINIMIZE]: {
          _id: false,
          id: { type: String, unique: true, sparse: true },
          publishedAt: Date,
          contentType: { type: String, enum: Object.values(FinimizeContentTypeEnum) },
          originalTitle: { type: String }
        },
        [ProviderEnum.CONTENTFUL]: {
          _id: false,
          id: { type: String },
          spaceId: { type: String },
          environmentId: { type: String }
        }
      }
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);
contentEntrySchema.index({ publishAt: -1 });

export const ContentEntry = mongoose.model<ContentEntryDocument>("ContentEntry", contentEntrySchema);

/**
 * CHANGE STREAMS
 */
DbUtil.listenToChangeStream<ContentEntryDocument>(
  ContentEntry,
  "insert",
  async (contentEntry: ContentEntryDocument) => {
    await NotificationService.createContentEntryNotificationForUsers(contentEntry);
  }
);
