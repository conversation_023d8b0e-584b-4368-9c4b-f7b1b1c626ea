import mongoose, { Document, Schema } from "mongoose";
// eslint-disable-next-line @typescript-eslint/no-require-imports
const AutoIncrement = require("mongoose-sequence")(mongoose);

export interface AccountingRecordIndexInterface {
  aaIndex: number; // This is the auto-incrementing field
  linkedDocumentId: mongoose.Types.ObjectId;
  sourceDocumentType: "Transaction" | "Order" | "Reward";
  createdAt?: Date;
  updatedAt?: Date;
}

export interface AccountingRecordIndexDocument extends AccountingRecordIndexInterface, Document {}

const accountingRecordIndexSchema: Schema = new mongoose.Schema(
  {
    aaIndex: { type: Number, unique: true }, // Auto-incremented by plugin; not required at creation
    linkedDocumentId: {
      type: Schema.Types.ObjectId,
      required: true,
      index: true
    },
    sourceDocumentType: {
      type: String,
      required: true,
      index: true
    }
  },
  { timestamps: true }
);

// Attach the auto-increment plugin (works even if the connection opens later)
accountingRecordIndexSchema.plugin(AutoIncrement, {
  id: "accounting_record_index_counter", // Name of the counter document
  inc_field: "aaIndex", // Field to increment
  reference_fields: [], // Global counter
  start_seq: 1 // Start from 1
});

export const AccountingRecordIndex = mongoose.model<AccountingRecordIndexDocument>(
  "AccountingRecordIndex",
  accountingRecordIndexSchema
);
