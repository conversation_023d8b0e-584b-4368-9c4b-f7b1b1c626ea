import mongoose, { Document } from "mongoose";
import { UserDocument } from "./User";
import { DepositStatusArray, DepositStatusType } from "../external-services/wealthkernelService";
import { AssetTransactionDocument } from "./Transaction";
import { ProviderEnum } from "../configs/providersConfig";
import { currenciesConfig } from "@wealthyhood/shared-configs";

const { MainCurrencies } = currenciesConfig;

/**
 * TYPES
 */
export const GiftStatusArray = ["Pending", "Settled"] as const;
export type GiftStatusType = (typeof GiftStatusArray)[number];

/**
 * INTERFACES
 */
export interface GiftDTOInterface {
  consideration: {
    currency: currenciesConfig.MainCurrencyType;
    // the total value of the gift - stored in cents
    amount: number;
  };
  message: string;
  gifter: mongoose.Types.ObjectId;
  targetUserEmail: string;
  // the associated deposit transaction that we make from the Wealthyhood wallet to the user's e-money wallet ONLY
  // after the gift is used by the user for a transaction
  deposit?: {
    activeProviders?: ProviderEnum[]; // Active providers can be missing until we know the region of the target user
    providers?: {
      wealthkernel?: {
        id?: string;
        status?: DepositStatusType;
        submittedAt?: Date;
      };
    };
  };
}

export interface GiftInterface extends Omit<GiftDTOInterface, "gifter"> {
  createdAt: Date;
  gifter: mongoose.Types.ObjectId | UserDocument;
  hasViewedAppModal: boolean;
  status: GiftStatusType;

  // VIRTUALS
  readonly linkedAssetTransaction: mongoose.Types.ObjectId | AssetTransactionDocument;
  readonly used: boolean;
}

export interface GiftDocument extends GiftInterface, Document {}

/**
 * SCHEMA
 */

const giftSchema = new mongoose.Schema(
  {
    consideration: {
      currency: {
        type: String,
        enum: MainCurrencies,
        default: "GBP"
      },
      amount: { type: Number, required: true }
    },
    deposit: {
      activeProviders: Object.values(ProviderEnum),
      providers: {
        _id: false,
        type: {
          [ProviderEnum.WEALTHKERNEL]: {
            _id: false,
            id: { type: String },
            status: { type: String, enum: DepositStatusArray },
            submittedAt: { type: Date }
          }
        }
      }
    },
    hasViewedAppModal: { type: Boolean, default: false },
    message: { type: String },
    gifter: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User"
    },
    targetUserEmail: {
      type: String,
      required: true
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

/**
 * VIRTUALS
 */
giftSchema.virtual("status").get(function (): GiftStatusType {
  if (this.deposit?.providers?.wealthkernel?.status === "Settled") {
    return "Settled";
  } else {
    return "Pending";
  }
});

giftSchema.virtual("linkedAssetTransaction", {
  ref: "Transaction",
  localField: "_id",
  foreignField: "pendingGift",
  justOne: true
});

giftSchema.virtual("used").get(function (): boolean {
  return !!(this as unknown as GiftInterface).linkedAssetTransaction;
});

export const Gift = mongoose.model<GiftDocument>("Gift", giftSchema);
