import mongoose, { Document } from "mongoose";
import { UserDocument } from "./User";

/**
 * INTERFACES
 */
export interface RewardInvitationDTOInterface {
  // the user that made the invitation
  referrer?: mongoose.Types.ObjectId;
  // the user that the invitation is for
  targetUserEmail?: string;
}

export interface RewardInvitationInterface extends Omit<RewardInvitationDTOInterface, "referrer"> {
  createdAt: Date;
  referrer: mongoose.Types.ObjectId | UserDocument;
}

export interface RewardInvitationDocument extends RewardInvitationInterface, Document {}

/**
 * SCHEMA
 */
const rewardInvitationSchema = new mongoose.Schema(
  {
    referrer: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User"
    },
    targetUserEmail: {
      type: String,
      required: true
    }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

export const RewardInvitation = mongoose.model<RewardInvitationDocument>(
  "RewardInvitation",
  rewardInvitationSchema
);
