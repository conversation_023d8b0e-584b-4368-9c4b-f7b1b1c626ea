import mongoose, { Document, Schema } from "mongoose";
import { currenciesConfig } from "@wealthyhood/shared-configs";
import { PortfolioDocument } from "./Portfolio";
import { InvestmentProductDocument } from "./InvestmentProduct";
import { ASSET_TICKER_DAYS_TTL } from "../configs/tickerConfig";
import { MultiCurrencyPriceType } from "currencies";
import DateUtil from "../utils/dateUtil";

const { MainCurrencies } = currenciesConfig;

/**
 * DTO INTERFACES
 */
interface IntraDayTickerDTOInterface {
  // This could have been omitted. The reason why we make it persistent is to be able
  // to get the traded price without having to populate the corresponding investment
  // product (or pass it as an argument to the virtual). So this field represents the
  // asset traded currency. For portfolios it's not really needed but we have it for
  // consistency and represents the portfolio settlement currency.
  currency: currenciesConfig.MainCurrencyType;
  // Refers to the exact timestamp that corresponds to the stored price and can
  // be different from the timestamp when the document was created.
  timestamp: Date;
  pricePerCurrency: MultiCurrencyPriceType;
  dailyReturnPercentage: number;
  monthlyReturnPercentage: number;
}

export interface IntraDayAssetTickerDTOInterface extends IntraDayTickerDTOInterface {
  investmentProduct: mongoose.Types.ObjectId | InvestmentProductDocument;
}

export interface IntraDayPortfolioTickerDTOInterface extends IntraDayTickerDTOInterface {
  portfolio: mongoose.Types.ObjectId | PortfolioDocument;
}

interface IntraDayTickerInterface extends IntraDayTickerDTOInterface {
  // virtuals
  readonly getPrice: (currency: currenciesConfig.MainCurrencyType) => number;
  readonly tradedPrice: number;
}
interface IntraDayAssetTickerInterface extends IntraDayTickerInterface, IntraDayAssetTickerDTOInterface {}
interface IntraDayPortfolioTickerInterface extends IntraDayTickerInterface, IntraDayPortfolioTickerDTOInterface {}

/**
 * DOCUMENTS
 */
export interface IntraDayTickerDocument extends IntraDayTickerInterface, Document {}
export interface IntraDayAssetTickerDocument extends IntraDayAssetTickerInterface, Document {}
export interface IntraDayPortfolioTickerDocument extends IntraDayPortfolioTickerInterface, Document {}

/**
 * SCHEMAS
 */
const multiCurrencyPriceType: Schema = new mongoose.Schema(
  {
    GBP: { type: Number, required: true },
    EUR: { type: Number, required: false },
    USD: { type: Number, required: false }
  },
  { _id: false }
);

const intraDayTickerSchema: Schema = new mongoose.Schema(
  {
    currency: {
      type: String,
      enum: MainCurrencies,
      required: true
    },
    timestamp: {
      type: Date,
      required: true
    },
    pricePerCurrency: {
      type: multiCurrencyPriceType,
      required: [true, "Ticker price cannot be empty"]
    },
    dailyReturnPercentage: { type: Number, default: 0 },
    monthlyReturnPercentage: { type: Number, default: 0 }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

const intraDayAssetTickerSchema: Schema = new mongoose.Schema({
  investmentProduct: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "InvestmentProduct",
    required: true,
    index: true
  }
});

const intraDayPortfolioTickerSchema: Schema = new mongoose.Schema({
  portfolio: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Portfolio",
    required: true,
    index: true
  }
});

/**
 * INDEXES
 */
// needed for mongo online archive
intraDayTickerSchema.index({ timestamp: 1 });

intraDayAssetTickerSchema.index({ investmentProduct: 1, timestamp: 1 });
intraDayAssetTickerSchema.index(
  { createdAt: 1 },
  { expireAfterSeconds: DateUtil.convertDaysToSeconds(ASSET_TICKER_DAYS_TTL) }
);
intraDayPortfolioTickerSchema.index({ portfolio: 1, timestamp: 1 });

/**
 * VIRTUALS
 */
// Used to return the price of an asset in the **user** currency
intraDayAssetTickerSchema.virtual("getPrice").get(function (): (
  currency: currenciesConfig.MainCurrencyType
) => number {
  return (currency) => {
    const assetTicker = this as IntraDayAssetTickerDocument;
    return assetTicker.pricePerCurrency[currency];
  };
});

intraDayAssetTickerSchema.virtual("tradedPrice").get(function (): number {
  const doc = this as IntraDayAssetTickerDocument;
  return doc.pricePerCurrency[doc.currency];
});

intraDayPortfolioTickerSchema.virtual("getPrice").get(function (): (
  currency: currenciesConfig.MainCurrencyType
) => number {
  return (currency) => {
    const portfolioTicker = this as IntraDayPortfolioTickerDocument;
    return portfolioTicker.pricePerCurrency[currency];
  };
});

intraDayPortfolioTickerSchema.virtual("tradedPrice").get(function (): number {
  const doc = this as IntraDayPortfolioTickerDocument;
  return doc.pricePerCurrency[doc.currency];
});

/**
 * MODELS
 */
const IntraDayTicker = mongoose.model<IntraDayTickerDocument>("IntraDayTicker", intraDayTickerSchema);
export const IntraDayAssetTicker = IntraDayTicker.discriminator<IntraDayAssetTickerDocument>(
  "IntraDayAssetTicker",
  intraDayAssetTickerSchema
);
export const IntraDayPortfolioTicker = IntraDayTicker.discriminator<IntraDayPortfolioTickerDocument>(
  "IntraDayPortfolioTicker",
  intraDayPortfolioTickerSchema
);
