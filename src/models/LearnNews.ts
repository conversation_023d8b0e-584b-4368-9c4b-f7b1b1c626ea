import mongoose, { Document } from "mongoose";

export interface LearnNewsDTOInterface {
  hash: string;
  imageUrl: string;
  title: string;
  htmlContent: string;
  tickers: string;
  date: Date;
  readingTime: string;
}

export interface LearnNewsInterface extends LearnNewsDTOInterface {
  createdAt: Date;
}

export interface LearnNewsDocument extends LearnNewsInterface, Document {}

const learnNewsSchema = new mongoose.Schema(
  {
    hash: { type: String, required: true, unique: true },
    imageUrl: { type: String },
    title: { type: String },
    htmlContent: { type: String },
    tickers: { type: String },
    date: { type: Date },
    readingTime: { type: String }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

export const LearnNews = mongoose.model<LearnNewsDocument>("LearnNews", learnNewsSchema);
