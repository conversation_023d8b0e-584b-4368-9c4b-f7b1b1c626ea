import { Server as SocketIOServer } from "socket.io";
import { Server as HTTPServer } from "http";
import { Server as HTTPSServer } from "https";
import logger from "../external-services/loggerService";
import { RedisAdapterService } from "./adapters/redis";
import { RoomActionEnum, RoomEnum } from "./rooms";

class SocketServer {
  public static initialize(server: HTTPServer | HTTPSServer): void {
    const io = new SocketIOServer(server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      },
      transports: ["websocket"],
      allowEIO3: true,
      adapter: RedisAdapterService.Instance.adapter
    });

    logger.info("Initializing websocket server...");

    io.on("connection", (socket) => {
      logger.info("New client connected", {
        module: "socket:service"
      });

      Object.values(RoomEnum).forEach((room) => {
        socket.on(`${room}:${RoomActionEnum.SUBSCRIBE}`, () => {
          socket.join(room);

          logger.info(`Client subscribed to ${room}`, {
            module: "socket:service"
          });
        });

        socket.on(`${room}:${RoomActionEnum.UNSUBSCRIBE}`, () => {
          socket.leave(room);

          logger.info(`Client unsubscribed from ${room}`, {
            module: "socket:service"
          });
        });
      });

      socket.on("disconnect", (reason) => {
        logger.info(`Client disconnected: ${reason}`, {
          module: "socket:service"
        });
      });
    });
  }
}

export default SocketServer;
