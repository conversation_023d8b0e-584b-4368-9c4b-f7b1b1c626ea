import eventEmitter from "../loaders/eventEmitter";
import events from "./events";
import { UserDocument } from "../models/User";
import analytics from "../external-services/segmentAnalyticsService";

class BankAccountEventHandler {
  constructor() {
    eventEmitter.on(events.bankAccount.bankAccountStagnant.eventId, this._handleBankAccountStagnant.bind(this));
  }

  private _handleBankAccountStagnant(user: UserDocument): void {
    analytics.track(user, events.bankAccount.bankAccountStagnant.name, {
      All: false,
      SlackActions: true
    });
  }
}

export default BankAccountEventHandler;
