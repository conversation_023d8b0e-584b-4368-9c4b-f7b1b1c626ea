import eventEmitter from "../loaders/eventEmitter";
import events from "./events";
import { UserDocument } from "../models/User";
import analytics, {
  TrackGiftPropertiesType,
  TrackNotificationGiftReceived
} from "../external-services/segmentAnalyticsService";
import { TransactionalNotificationEventEnum } from "./notificationEvents";
import NotificationService from "../services/notificationService";

class GiftEventHandler {
  constructor() {
    eventEmitter.on(events.gift.giftCreation.eventId, this._handleGiftCreation.bind(this));
  }

  private async _handleGiftCreation(
    user: UserDocument,
    targetUser: UserDocument,
    properties: TrackGiftPropertiesType
  ): Promise<void> {
    analytics.track(user, events.gift.giftCreation.name, { All: false, Mixpanel: true }, properties);

    if (targetUser) {
      analytics.identify(user, { gifted: "Existing", giftedBy: user.email }, { All: false, Mixpanel: true });
      const notificationTrackProperties: TrackNotificationGiftReceived = {
        gifted_by: `${user.firstName} ${user.lastName}`
      };

      await NotificationService.createAppNotification(
        user.id,
        {
          notificationId: targetUser.hasPassedKyc
            ? TransactionalNotificationEventEnum.GIFT_RECEIPT_VERIFIED
            : TransactionalNotificationEventEnum.GIFT_RECEIPT_UNVERIFIED,
          properties: new Map(Object.entries(notificationTrackProperties))
        },
        { sendImmediately: true }
      );
    }
  }
}

export default GiftEventHandler;
