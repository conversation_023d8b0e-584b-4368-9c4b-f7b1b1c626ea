export default {
  general: {
    finimizeRefIdentified: { eventId: "onFinimizeRefIdentified", name: "Finimize Ref Identified" },
    install: { eventId: "onAppInstall", name: "App Installed" },
    appsflyerConversionSuccess: {
      eventId: "onAppsflyerConversionSuccess",
      name: "Appsflyer Conversion Succeeded"
    },
    appOpened: { eventId: "onAppOpened", name: "App Opened" }
  },
  participant: {
    emailSubmitted: { eventId: "onEmailSubmitted", name: "Email Submitted" }
  },
  creditTickets: {
    creditedAmountReported: { eventId: "onCreditedAmountReported", name: "Credited Amount Reported" }
  },
  portfolio: {
    // Do not change event name, it's considered legacy and we cannot change.
    portfolioAllocation: { eventId: "onVirtualPortfolioCreation", name: "Virtual Portfolio Created" },
    portfolioValuation: { eventId: "onPortfolioValuation", name: "Portfolio Valuated" }
  },
  transaction: {
    firstDepositCreation: { eventId: "onFirstDepositCreation", name: "First Deposit Created" },
    depositCreation: { eventId: "onDepositCreation", name: "Deposit Created" },
    depositFailure: { eventId: "onDepositFailure", name: "Deposit Failed" },
    depositSuccess: { eventId: "onDepositSuccess", name: "Deposit Succeeded" },
    firstInvestmentCreation: {
      eventId: "onFirstAssetTransactionCreation",
      name: "First Investment Created"
    },
    investmentCreation: { eventId: "onAssetTransactionCreation", name: "Investment Created" },
    investmentSuccess: { eventId: "onAssetTransactionSuccess", name: "Investment Succeeded" },
    rebalanceTransactionSuccess: { eventId: "onRebalanceTransactionSuccess", name: "Rebalance Succeeded" },
    withdrawalCreation: { eventId: "onWithdrawalCreation", name: "Withdrawal Created" },
    transactionCancellation: { eventId: "onTransactionCancellation", name: "Transaction Cancelled" },
    savingsDividendCreation: {
      eventId: "onSavingsDividendCreation",
      name: "Savings Dividend Created"
    },
    dividendSuccess: { eventId: "onDividendSuccess", name: "Dividend Succeeded" },
    assetDividendSuccess: { eventId: "onAssetDividendSuccess", name: "Asset Dividend Succeeded" },
    wealthyhoodDividendSuccess: {
      eventId: "onWealthyhoodDividendSuccess",
      name: "Wealthyhood Dividend Succeeded"
    },
    custodyChargeSuccess: { eventId: "onCustodyChargeSuccess", name: "Custody Charge Succeeded" },
    subscriptionChargeSuccess: { eventId: "onSubscriptionChargeSuccess", name: "Subscription Charge Succeeded" },
    savingsDividendChargeSuccess: {
      eventId: "onSavingsDividendChargeSuccess",
      name: "Savings Interest Charge Succeeded"
    }
  },
  bankAccount: {
    bankAccountStagnant: { eventId: "onBankAccountStagnant", name: "Bank Account Stagnant" }
  },
  corporateEvents: {
    stockSplitCreation: { eventId: "onStockSplitCreation", name: "Stock Split Created" },
    assetIsinChange: { eventId: "onAssetIsinChange", name: "Asset ISIN Changed" }
  },
  user: {
    bankAccountLinking: { eventId: "onBankAccountLinking", name: "Bank Account Linked" },
    bankAccountRemoval: { eventId: "onBankAccountRemoval", name: "Bank Account Removed" },
    deletionFeedbackSubmission: { eventId: "onDeletionFeedbackSubmission", name: "Deletion Feedback Submitted" },
    logIn: { eventId: "onUserLogin", name: "Logged In" },
    personalDetailsSubmission: { eventId: "onPersonalDetailsSubmission", name: "Personal Details Submitted" },
    passportDetailsSubmission: { eventId: "onPassportDetailsSubmission", name: "Passport Details Submitted" },
    addressSubmission: { eventId: "onAddressSubmission", name: "Address Submitted" },
    taxDetailsSubmission: { eventId: "onTaxDetailsSubmission", name: "Tax Details Submitted" },
    signUp: { eventId: "onUserSignUp", name: "Signed Up" },
    residencyCountryChange: { eventId: "onResidencyCountryChange", name: "Residency Country Changed" },
    verification: { eventId: "onUserVerification", name: "Verified" },
    verificationFailure: { eventId: "onUserVerificationFailure", name: "Verification Failed" },
    accountSuspended: { eventId: "onAccountSuspended", name: "Account Suspended" },
    welcome: { eventId: "onUserWelcome", name: "Email Verified" },
    disassociation: { eventId: "onDisassociation", name: "Disassociation Requested" },
    referralCodeSubmission: { eventId: "onReferralCodeSubmission", name: "Referral Code Submitted" },
    friendInvitation: { eventId: "onFriendInvitation", name: "Friend Invited" },
    employmentInfoSubmission: { eventId: "onEmploymentInfoSubmission", name: "Employment Info Submitted" },
    termsAccepted: { eventId: "onAcceptedTerms", name: "Terms Accepted" },
    sumsubKycStarted: { eventId: "onSumsubKycStart", name: "Sumsub KYC Started" },
    sumsubKycFinished: { eventId: "onSumsubKycFinish", name: "Sumsub KYC Finished" },
    riskAssessmentCreated: { eventId: "onRiskAssessmentCreation", name: "Risk Assessment Created" },
    highRiskAssessmentDetected: {
      eventId: "onHighRiskAssessmentDetection",
      name: "High Risk Assessment Detected"
    },
    wkAccountClosed: { eventId: "onWkAccountClosed", name: "Wealthkernel Account Closed" },
    wealthybitesSubscription: { eventId: "onWealthybitesSubscription", name: "Wealthybites Subscribed" },
    promotionalEmailSubscription: {
      eventId: "onPromotionalEmailSubscription",
      name: "Promotional Email Subscribed"
    },
    whAccountStatusUpdate: { eventId: "onWealthyhoodAccountStatusUpdate", name: "WH Account Status Updated" },
    joinedWaitingList: { eventId: "onJoinedWaitingList", name: "Joined Waiting List" }
  },
  automation: {
    recurringInvestmentCreation: {
      eventId: "onRecurringInvestmentCreation",
      name: "Recurring Investment Created"
    },
    recurringInvestmentCancellation: {
      eventId: "onRecurringInvestmentCancellation",
      name: "Recurring Investment Cancelled"
    },
    recurringSavingsCreation: {
      eventId: "onRecurringSavingsCreation",
      name: "Recurring Savings Created"
    },
    recurringSavingsCancellation: {
      eventId: "onRecurringSavingsCancellation",
      name: "Recurring Savings Cancelled"
    },
    recurringRebalanceCreation: {
      eventId: "onRecurringRebalanceCreation",
      name: "Recurring Rebalance Created"
    },
    recurringRebalanceCancellation: {
      eventId: "onRecurringRebalanceCancellation",
      name: "Recurring Rebalance Cancelled"
    }
  },
  order: {
    orderCancellation: {
      eventId: "onOrderCancellation",
      name: "Order Cancelled"
    },
    orderRejection: {
      eventId: "onOrderRejection",
      name: "Order Rejected"
    }
  },
  gift: {
    giftCreation: {
      eventId: "onGiftCreation",
      name: "Gift Created"
    }
  },
  plan: {
    // Both in plan downgrade init and plan upgrade we have the same event name because both the events represent
    // a user action regarding plan change and we want to track that to Mixpanel with the same event name.
    planDowngradeInit: { eventId: "onPlanDowngradeInit", name: "Plan Updated" },
    planDowngradeCompletion: { eventId: "onPlanDowngradeCompletion", name: "Plan Downgrade Completed" },
    planUpgrade: {
      eventId: "onPlanUpgraded",
      // Name remains 'Plan Updated' to not break mixpanel tracking that was set up for that name.
      name: "Plan Updated"
    }
  },
  referral: {
    referralRewardCreation: {
      eventId: "onReferralRewardCreation",
      name: "Referral Reward Created"
    },
    referrerRewardCreation: {
      eventId: "onReferrerRewardCreation",
      name: "Referrer Reward Created"
    },
    rewardSettled: {
      eventId: "onRewardSettled",
      name: "Reward Settled"
    }
  },
  transactionMonitoring: {
    aggregateAmountForHighRiskUser: {
      eventId: "onAggregateAmountForHighRiskUser",
      name: "Aggregate Amount For High Risk User"
    },
    aggregateAmountForLowMediumRiskUser: {
      eventId: "onAggregateAmountForLowMediumRiskUser",
      name: "Aggregate Amount For Low/Medium Risk User"
    },
    withdrawalAfterDeposit: { eventId: "onWithdrawalAfterDeposit", name: "Withdrawal After Deposit" },
    transactionAfterAccountInactivity: {
      eventId: "ontransactionAfterAccountInactivity",
      name: "Transaction After Account Inactivity"
    },
    highVolumeDeposits: {
      eventId: "onHighVolumeDeposits",
      name: "High Volume Deposits"
    },
    netAggregateAmountForHighRiskUser: {
      eventId: "onNetAggregateAmountForHighRiskUser",
      name: "Net Aggregate Amount For High Risk User"
    }
  },
  appRating: {
    appRatingSubmitted: {
      eventId: "onAppRatingSubmitted",
      name: "App Rating Submitted"
    }
  },
  investmentProduct: {
    staleTicker: {
      eventId: "onStaleTicker",
      name: "Stale Ticker"
    }
  },
  savingsProduct: {
    savingProductDataUpdate: {
      eventId: "onSavingProductDataUpdate",
      name: "Savings Product Data Update"
    }
  }
};
