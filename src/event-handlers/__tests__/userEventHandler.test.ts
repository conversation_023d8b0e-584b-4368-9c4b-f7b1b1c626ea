import eventEmitter from "../../loaders/eventEmitter";
import { UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildNotificationSettings, buildUser } from "../../tests/utils/generateModels";
import events from "../events";
import UserEventHandler from "../userEventHandler";
import NotificationService from "../../services/notificationService";

jest.unmock("../../loaders/eventEmitter");

describe("UserEventHandler", () => {
  beforeAll(async () => {
    new UserEventHandler();
    await connectDb("UserEventHandler");
  });
  afterAll(async () => await closeDb());

  describe("onUserVerification", () => {
    describe("email notification is enabled", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(NotificationService, "createEmailNotification").mockResolvedValue();

        user = await buildUser();

        await buildNotificationSettings({ owner: user.id });

        eventEmitter.emit(events.user.verification.eventId, user, { emailNotification: true });
      });
      afterAll(async () => await clearDb());

      it("should create email notification", async () => {
        expect(NotificationService.createEmailNotification).toHaveBeenCalledWith(
          user.id,
          {
            notificationId: "userVerification",
            properties: new Map(Object.entries({ payment_url: "/" }))
          },
          { sendImmediately: true }
        );
      });
    });

    describe("email notification is disabled", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(NotificationService, "createEmailNotification").mockResolvedValue();

        user = await buildUser();

        await buildNotificationSettings({ owner: user.id });

        eventEmitter.emit(events.user.verification.eventId, user, { emailNotification: false });
      });
      afterAll(async () => await clearDb());

      it("should NOT send email notification", () => {
        expect(NotificationService.createEmailNotification).not.toHaveBeenCalled();
      });
    });
  });
});
