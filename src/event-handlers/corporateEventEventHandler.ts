import { nanoid } from "nanoid";
import eventEmitter from "../loaders/eventEmitter";
import events from "./events";
import analytics, {
  TrackAssetIsinChangePropertiesType,
  TrackStockSplitPropertiesType
} from "../external-services/segmentAnalyticsService";
import DateUtil from "../utils/dateUtil";
import logger from "../external-services/loggerService";

class CorporateEventEventHandler {
  constructor() {
    eventEmitter.on(events.corporateEvents.stockSplitCreation.eventId, this._handleStockSplitCreation.bind(this));
    eventEmitter.on(events.corporateEvents.assetIsinChange.eventId, this._handleAssetIsinChange.bind(this));
  }

  private _handleStockSplitCreation(properties: TrackStockSplitPropertiesType): void {
    const { assetName, date } = properties;

    logger.info(`Stock split created for asset ${assetName} - ${date.toISOString()}`, {
      module: "CorporateEventEventHandler",
      method: "_handleStockSplitCreation"
    });

    analytics.rawTrack(
      {
        anonymousId: nanoid(),
        event: events.corporateEvents.stockSplitCreation.name,
        properties: {
          asset: assetName,
          date: DateUtil.formatDateToDDMONYYYY(date)
        }
      },
      {
        All: false,
        SlackActions: true
      }
    );
  }

  private _handleAssetIsinChange(properties: TrackAssetIsinChangePropertiesType): void {
    const { assetName, oldISIN, newISIN, date } = properties;

    logger.info(`Asset ISIN changed for asset ${assetName} - ${date.toISOString()}`, {
      module: "CorporateEventEventHandler",
      method: "_handleAssetIsinChange"
    });

    analytics.rawTrack(
      {
        anonymousId: nanoid(),
        event: events.corporateEvents.assetIsinChange.name,
        properties: {
          asset: assetName,
          oldISIN: oldISIN,
          newISIN: newISIN,
          date: DateUtil.formatDateToDDMONYYYY(date)
        }
      },
      {
        All: false,
        SlackActions: true
      }
    );
  }
}

export default CorporateEventEventHandler;
