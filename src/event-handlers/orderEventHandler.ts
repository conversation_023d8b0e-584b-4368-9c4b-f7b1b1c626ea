import eventEmitter from "../loaders/eventEmitter";
import events from "./events";
import { UserDocument } from "../models/User";
import analytics, { TrackOrderPropertiesType } from "../external-services/segmentAnalyticsService";
import logger from "../external-services/loggerService";

class OrderEventHandler {
  constructor() {
    eventEmitter.on(events.order.orderCancellation.eventId, this._handleOrderCancellation.bind(this));
    eventEmitter.on(events.order.orderRejection.eventId, this._handleOrderRejection.bind(this));
  }

  private async _handleOrderCancellation(user: UserDocument, properties: TrackOrderPropertiesType): Promise<void> {
    logger.info(`Order cancelled for user ${user.email}`, {
      module: "OrderEventHandler",
      method: "_handleOrderCancellation"
    });

    analytics.track(
      user,
      events.order.orderCancellation.name,
      {
        All: false,
        Mixpanel: true
      },
      properties
    );
  }

  private async _handleOrderRejection(user: UserDocument, properties: TrackOrderPropertiesType): Promise<void> {
    logger.info(`Order rejected for user ${user.email}`, {
      module: "OrderEventHandler",
      method: "_handleOrderRejection"
    });

    analytics.track(
      user,
      events.order.orderRejection.name,
      {
        All: false,
        SlackActions: true
      },
      properties
    );
  }
}

export default OrderEventHandler;
