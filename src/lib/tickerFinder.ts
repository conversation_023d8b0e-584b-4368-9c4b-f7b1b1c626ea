import { generateObject } from "ai";
import { anthropic } from "@ai-sdk/anthropic";
import { z } from "zod";
import { captureException } from "@sentry/node";
import logger from "../external-services/loggerService";

const ANTHROPIC_MODEL_NAME = "claude-3-7-sonnet-20250219";

const TICKER_INPUT_SCHEMA = z.string().nonempty().describe("Company name");
const TICKER_OUTPUT_SCHEMA = z.string().optional().describe("Primary stock ticker symbol for the company");

const TICKER_FINDER_SYSTEM_PROMPT = `You are an AI assistant specializing in financial data retrieval.
Your task is to find the primary stock ticker symbol for the company name provided in the input.
If a company is not publicly traded or you cannot confidently determine its ticker symbol, omit the ticker field for that company.
Return the results as a string which is the corresponding ticker symbol (if found).
`;

export default class TickerFinder {
  /**
   * @description Finds ticker symbol for a single company name using AI.
   * @param companyName A company name.
   * @returns A promise that resolves to a ticker symbol or undefined.
   */
  public static async findTickerForCompany(companyName: string): Promise<string | undefined> {
    // Validate input using Zod schema
    TICKER_INPUT_SCHEMA.parse(companyName);

    try {
      const { object: tickerData } = await generateObject({
        model: anthropic(ANTHROPIC_MODEL_NAME),
        system: TICKER_FINDER_SYSTEM_PROMPT,
        prompt: `Find the ticker symbol for the following company: ${companyName}`,
        schema: TICKER_OUTPUT_SCHEMA,
        temperature: 0.6 // Lower temperature for more deterministic results
      });

      const parsedSchemaData = TICKER_OUTPUT_SCHEMA.parse(tickerData);

      // Filter out entries without a ticker and convert the array to a Record
      return parsedSchemaData;
    } catch (err) {
      captureException(err);
      logger.error("Error finding ticker symbols:", {
        module: "TickerFinder",
        method: "findTickerForCompany",
        data: {
          companyName,
          err
        }
      });
    }
  }
}
