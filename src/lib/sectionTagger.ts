import { generateObject } from "ai";
import { anthropic } from "@ai-sdk/anthropic";
import { z } from "zod";
import { captureException } from "@sentry/node";
import logger from "../external-services/loggerService";

const ANTHROPIC_MODEL_NAME = "claude-3-7-sonnet-20250219";

// Define schema for the tagger output
const SECTION_TAGGER_SCHEMA = z.object({
  tag: z
    .string()
    .describe(
      "The tag categorizing this news section (e.g., macroeconomy, acquisition, economy, political, regulation, technology, market_trend, global_event, industry_news, etc.)"
    )
});

// System prompt for the section tagger
const SECTION_TAGGER_SYSTEM_PROMPT = `You are an AI assistant specializing in financial news analysis.
Your task is to categorize news sections that don't refer to specific companies.

IMPORTANT: Analyze the news content and assign a relevant tag that best categorizes the topic.
Choose the most specific and relevant tag from categories like:
- Macroeconomy (broad economic trends, GDP, inflation, etc.)
- Interest Rates (central bank decisions, rate changes)
- Acquisition (mergers, buyouts, corporate transactions)
- Market Trend (market movements, index performances)
- Regulation (government regulations, compliance issues)
- Political (government decisions, elections, policy changes)
- Global Event (international events, geopolitical developments)
- Technology (tech trends, innovations not tied to a specific company)
- Industry News (sector-wide developments)
- Commodities (oil, gold, natural resources)
- Currency (forex markets, currency movements)
- Real Estate (housing market, property trends)
- Employment (jobs reports, labor market)
- Consumer Trends (spending patterns, consumer behavior)

Return a single, most appropriate tag for the content.

The tag should be a single word or phrase that best describes the content (not consumer_trends but "Consumer Trends" instead).
`;

export default class SectionTagger {
  /**
   * Tags a news section with a category when no ticker symbol is available
   * @param content The content of the news section
   * @returns A tag categorizing the section
   */
  public static async tagSection(content: string): Promise<string> {
    try {
      // Generate the prompt based on the content
      const prompt = `Please analyze the following financial news section and assign a relevant category tag:

        "${content}"

        Based on the content, what would be the most appropriate tag for categorizing this news item?`;

      // Generate the tag using Claude AI
      const { object: tagResult } = await generateObject({
        model: anthropic(ANTHROPIC_MODEL_NAME),
        system: SECTION_TAGGER_SYSTEM_PROMPT,
        prompt,
        schema: SECTION_TAGGER_SCHEMA,
        temperature: 0.2
      });

      return tagResult.tag;
    } catch (err) {
      logger.error("Error tagging section content:", {
        module: "SectionTagger",
        method: "tagSection",
        data: {
          error: err
        }
      });
      captureException(err);
      return "";
    }
  }
}
