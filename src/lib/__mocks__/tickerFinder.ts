import { generateObject } from "ai";
import { z } from "zod";
import { tickerFinderModelMock } from "../../tests/utils/aiModelMocks";

const TICKER_OUTPUT_SCHEMA = z.string().describe("Primary stock ticker symbol for the company");

export default class TickerFinder {
  public static async findTickerForCompany(companyName: string): Promise<string | undefined> {
    const { object: rawData } = await generateObject({
      model: tickerFinderModelMock,
      prompt: companyName,
      schema: TICKER_OUTPUT_SCHEMA,
      temperature: 0.7
    });

    const ticker = TICKER_OUTPUT_SCHEMA.parse(rawData);
    return ticker || undefined;
  }
}
