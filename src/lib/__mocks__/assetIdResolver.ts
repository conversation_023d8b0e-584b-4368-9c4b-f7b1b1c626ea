import { generateObject } from "ai";
import { z } from "zod";
import { assetIdResolverModelMock } from "../../tests/utils/aiModelMocks";
import { publicInvestmentUniverseConfig } from "@wealthyhood/shared-configs";

export default class AssetIdResolver {
  public static async resolveTickerToAssetId({
    companyName,
    companyTicker
  }: {
    companyName: string;
    companyTicker: string;
  }): Promise<publicInvestmentUniverseConfig.PublicAssetType> {
    if (companyTicker === "GM" && companyName?.toLowerCase().includes("general motors")) {
      return "equities_general_motors" as publicInvestmentUniverseConfig.PublicAssetType;
    }
    if (companyTicker === "INTC" && companyName?.toLowerCase().includes("intel")) {
      return "equities_intel" as publicInvestmentUniverseConfig.PublicAssetType;
    }

    const { object: rawData } = await generateObject({
      model: assetIdResolverModelMock,
      prompt: JSON.stringify({ companyName, companyTicker }),
      schema: z.object({ assetId: z.string() }),
      temperature: 0.7
    });

    return rawData.assetId as publicInvestmentUniverseConfig.PublicAssetType;
  }
}
