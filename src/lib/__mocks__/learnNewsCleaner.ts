import { generateObject } from "ai";
import { z } from "zod";
import { learnNewsCleanerModelMock } from "../../tests/utils/aiModelMocks";

const LEARN_NEWS_CLEANER_OUTPUT_SCHEMA = z.object({
  htmlContent: z.string().describe("Cleaned HTML content")
});

export default class LearnNewsCleaner {
  public static async clean(content: string): Promise<string> {
    const { object: rawData } = await generateObject({
      model: learnNewsCleanerModelMock,
      prompt: JSON.stringify(content),
      schema: LEARN_NEWS_CLEANER_OUTPUT_SCHEMA,
      temperature: 0.7
    });

    return rawData.htmlContent;
  }
}
