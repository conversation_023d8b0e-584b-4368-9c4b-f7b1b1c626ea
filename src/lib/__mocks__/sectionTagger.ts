import { generateObject } from "ai";
import { z } from "zod";
import { sectionTaggerModelMock } from "../../tests/utils/aiModelMocks";

const SECTION_TAGGER_SCHEMA = z.object({
  tag: z.string().describe("Tag categorizing the news section")
});

const SectionTagger = {
  tagSection: jest.fn().mockImplementation(async (content: string) => {
    const { object: rawData } = await generateObject({
      model: sectionTaggerModelMock,
      prompt: content,
      schema: SECTION_TAGGER_SCHEMA,
      temperature: 0
    });

    const result = SECTION_TAGGER_SCHEMA.parse(rawData);
    return result.tag;
  })
};

export default SectionTagger;
