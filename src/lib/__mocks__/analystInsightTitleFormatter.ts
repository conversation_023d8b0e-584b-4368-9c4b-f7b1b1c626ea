export default class AnalystInsightTitleFormatter {
  public static async format(title: string): Promise<string> {
    // Simple mock implementation that preserves the original title
    // but adds a prefix to indicate it was processed
    if (!title) {
      return title;
    }

    return title
      .split(" ")
      .map((word, index) => {
        // Preserve acronyms (all caps)
        if (word === word.toUpperCase() && word.length > 1) {
          return word;
        }
        if (index === 0) {
          return word;
        }
        return word.toLowerCase();
      })
      .join(" ");
  }
}
