import { generateObject } from "ai";
import { anthropic } from "@ai-sdk/anthropic";
import { z } from "zod";
import { captureException } from "@sentry/node";
import { investmentUniverseConfig, publicInvestmentUniverseConfig } from "@wealthyhood/shared-configs";
import logger from "../external-services/loggerService";

const ANTHROPIC_MODEL_NAME = "claude-3-7-sonnet-20250219";

interface TickerCompanyPair {
  companyTicker: string;
  companyName: string;
}

const ASSET_ID_RESOLVER_SCHEMA = z.object({
  companyTicker: z.string().nonempty().describe("The ticker symbol"),
  companyName: z.string().nonempty().describe("The company name from the article"),
  resolvedAssetId: z.string().optional().describe("The resolved asset ID that best matches the company name")
});

export type AssetIdResolverOutputType = Record<string, string>;

const ASSET_ID_RESOLVER_SYSTEM_PROMPT = `You are an AI assistant specializing in financial data resolution.
Your task is to match ticker symbols and company names to the correct asset IDs from our investment universe.

Some ticker symbols may exist in multiple exchanges, representing different companies.
For each ticker-company pair, you'll be given potential asset matches from our universe.

IMPORTANT: Determine which asset best matches the provided company name based on:
1. Company name similarity
2. Exchange context and ticker
3. Market relevance and recognition

Return the asset ID that best matches each ticker-company pair. If none of the options match well, omit the resolvedAssetId field.
`;

export default class AssetIdResolver {
  /**
   * Resolves a single ticker symbol to the correct asset ID for a specific company name
   * @param tickerCompanyPair Object containing a ticker symbol and company name
   * @returns The resolved asset ID that best matches the company name, or undefined if no match
   */
  public static async resolveTickerToAssetId(
    tickerCompanyPair: TickerCompanyPair
  ): Promise<publicInvestmentUniverseConfig.PublicAssetType | undefined> {
    // Get all possible options for this ticker
    const assetConfigs = this._collectAssetConfigs(tickerCompanyPair);

    // If no options found or only one option exists, return direct match or undefined
    if (assetConfigs.length === 0) {
      return;
    } else if (assetConfigs.length === 1) {
      return assetConfigs[0].assetId as publicInvestmentUniverseConfig.PublicAssetType;
    }

    try {
      const prompt = this._generatePrompt(assetConfigs);

      const { object: resolvedData } = await generateObject({
        model: anthropic(ANTHROPIC_MODEL_NAME),
        system: ASSET_ID_RESOLVER_SYSTEM_PROMPT,
        prompt,
        schema: ASSET_ID_RESOLVER_SCHEMA,
        temperature: 0.2
      });

      return resolvedData.resolvedAssetId as publicInvestmentUniverseConfig.PublicAssetType;
    } catch (err) {
      captureException(err);
      logger.error("Error resolving asset ID:", {
        module: "AssetIdResolver",
        method: "resolveTickerToAssetId",
        data: {
          tickerCompanyPair,
          err
        }
      });
      return undefined;
    }
  }

  /**
   * Collects all possible asset configurations from the public investment universe that match a given ticker
   * @param tickerCompanyPair Object containing a ticker symbol and company name
   * @returns Array of matching asset configurations from the investment universe
   * @private
   */
  private static _collectAssetConfigs(tickerCompanyPair: TickerCompanyPair): {
    assetId: publicInvestmentUniverseConfig.PublicAssetType;
    assetConfig: investmentUniverseConfig.AssetConfigType;
  }[] {
    const { PUBLIC_ASSET_CONFIG } = publicInvestmentUniverseConfig;
    const { companyTicker } = tickerCompanyPair;

    const assetConfigs: {
      assetId: publicInvestmentUniverseConfig.PublicAssetType;
      assetConfig: investmentUniverseConfig.AssetConfigType;
    }[] = [];

    Object.entries(PUBLIC_ASSET_CONFIG).forEach(([assetId, config]) => {
      if (config.formalTicker === companyTicker) {
        assetConfigs.push({
          assetId: assetId as publicInvestmentUniverseConfig.PublicAssetType,
          assetConfig: config as investmentUniverseConfig.AssetConfigType
        });
      }
    });

    return assetConfigs;
  }

  /**
   * Generates a prompt for the model to resolve asset IDs based on company names
   * @param assetConfigs Array of asset configurations with their IDs
   * @returns A formatted prompt string
   * @private
   */
  private static _generatePrompt(
    assetConfigs: {
      assetId: publicInvestmentUniverseConfig.PublicAssetType;
      assetConfig: investmentUniverseConfig.AssetConfigType;
    }[]
  ): string {
    // Extract ticker from the first config
    const ticker = assetConfigs[0].assetConfig.formalTicker;
    // Use the simpleName as the company name
    const companyName = assetConfigs[0].assetConfig.simpleName || "";

    // Format each asset config as a possible match
    const possibleMatches = assetConfigs
      .map((config) => {
        return `Asset ID: ${config.assetId}, Name: ${config.assetConfig.simpleName || ""}, Exchange: ${config.assetConfig.formalExchange || ""}}`;
      })
      .join("\n");

    return `Resolve the following ticker symbol to the correct asset ID based on company name:
      Ticker: ${ticker}
      Company Name: ${companyName}
      Possible Matches:
      ${possibleMatches}
      `;
  }
}
