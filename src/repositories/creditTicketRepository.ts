import mongoose from "mongoose";
import { CreditTicket, CreditTicketDocument } from "../models/CreditTicket";

export class CreditTicketRepository {
  public static async rejectCreditTicket(
    id: string,
    options?: { session?: mongoose.ClientSession }
  ): Promise<CreditTicketDocument> {
    return CreditTicket.findByIdAndUpdate(id, { status: "Rejected" }, { session: options?.session });
  }

  public static async settleCreditTicket(id: string): Promise<CreditTicketDocument> {
    return CreditTicket.findByIdAndUpdate(id, { status: "Settled", settledAt: new Date(Date.now()) });
  }

  public static async getCreditTicketByDepositId(depositId: string): Promise<CreditTicketDocument | null> {
    return CreditTicket.findOne({
      "deposit.providers.wealthkernel.id": depositId
    });
  }
}
