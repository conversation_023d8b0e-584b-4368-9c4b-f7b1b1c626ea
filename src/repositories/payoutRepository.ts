import mongoose from "mongoose";
import { Payout, PayoutDocument, PayoutDTOInterface, PayoutInterface } from "../models/Payout";

export class PayoutRepository {
  public static async createPayout(payoutData: PayoutDTOInterface): Promise<PayoutDocument> {
    return new Payout(payoutData).save();
  }

  public static async completePayout(payoutId: string): Promise<void> {
    await Payout.findByIdAndUpdate(payoutId, { status: "Completed" });
  }

  public static async getPayoutByGoCardlessId(id: string): Promise<PayoutDocument> {
    return Payout.findOne({ "providers.gocardless.id": id });
  }

  /**
   * Retrieves a pending payout by matching its bank reference within a description string.
   *
   * This method performs a case-insensitive search to find payouts where the bank reference is
   * contained anywhere within the provided description.

   * @example
   * If a payout in DB has reference "ABC123"
   * This would match even with description "Payment from GoCardless: ABC123"
   */
  public static async getPendingPayoutByContainedReference(
    description: string
  ): Promise<PayoutInterface & { _id: mongoose.Types.ObjectId }> {
    const lowercaseDescription = description.toLowerCase();

    const matches = await Payout.aggregate([
      {
        $match: {
          reference: { $ne: null },
          status: "Pending"
        }
      },
      {
        $addFields: {
          lowercaseReference: { $toLower: "$reference" },
          lowercaseDescription: lowercaseDescription
        }
      },
      {
        $match: {
          $expr: {
            $regexMatch: {
              input: "$lowercaseDescription",
              regex: "$lowercaseReference"
            }
          }
        }
      }
    ]);

    if (matches.length > 1) {
      throw new Error(`We have received more than 1 match for payout with description ${description}`);
    }

    return matches.length > 0 ? matches[0] : null;
  }
}
