import { ContentEntryDocument } from "../models/ContentEntry";
import { BankAccount, WealthyhoodBankAccountStatusType } from "../models/BankAccount";

export default class BankAccountRepository {
  public static async updateWealthyhoodStatus(
    id: string,
    status: WealthyhoodBankAccountStatusType
  ): Promise<ContentEntryDocument | null> {
    return BankAccount.findByIdAndUpdate(id, { "providers.wealthyhood.status": status }, { new: true });
  }
}
