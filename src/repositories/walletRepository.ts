import { AccountStatusType, IdentifierType } from "../external-services/devengoService";
import { Wallet, WalletDocument } from "../models/Wallet";
import { ProviderEnum } from "../configs/providersConfig";
import { UserDocument } from "../models/User";
import { entitiesConfig } from "@wealthyhood/shared-configs";

export class WalletRepository {
  public static async createWallet(user: UserDocument): Promise<WalletDocument | null> {
    if (user.companyEntity !== entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE) {
      return;
    }

    return Wallet.findOneAndUpdate(
      { owner: user.id },
      {
        owner: user.id,
        activeProviders: [ProviderEnum.DEVENGO]
      },
      {
        runValidators: true,
        setDefaultsOnInsert: true,
        upsert: true,
        new: true
      }
    );
  }

  public static async updateWalletDevengoData(
    walletId: string,
    data: { devengo: { id: string; status: AccountStatusType }; identifiers: IdentifierType[] }
  ): Promise<WalletDocument> {
    return Wallet.findByIdAndUpdate(
      walletId,
      {
        providers: { [ProviderEnum.DEVENGO]: { id: data.devengo.id, status: data.devengo.status } },
        iban: data.identifiers.find((identifier) => identifier.type === "iban")?.iban
      },
      { new: true }
    );
  }

  public static async getWalletByDevengoId(devengoId: string): Promise<WalletDocument> {
    return Wallet.findOne({ "providers.devengo.id": devengoId });
  }
}
