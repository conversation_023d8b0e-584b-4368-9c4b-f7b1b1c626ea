import { AppNotificationSettingEnum, EmailNotificationSettingEnum } from "../models/NotificationSettings";
import {
  LearningNotificationEventEnum,
  MailchimpWebhookNotificationEventType,
  TransactionalNotificationEventEnum
} from "../event-handlers/notificationEvents";
import { EmailType } from "../external-services/mailerService";
import { ContentEntryContentTypeEnum } from "../models/ContentEntry";

/**
 * ENUMS
 */
export enum AppNotificationSettingsCategoryEnum {
  ACTIVITY = "app_activity",
  PROMOTIONAL = "app_promotional",
  MARKET_INSIGHTS = "app_market_insights"
}

export enum EmailNotificationSettingsCategoryEnum {
  ACTIVITY = "email_activity",
  PROMOTIONAL = "email_promotional",
  NEWSLETTERS = "email_newsletters"
}

/**
 * TYPES
 */
export type NotificationEventType =
  | MailchimpWebhookNotificationEventType
  | TransactionalNotificationEventEnum
  | LearningNotificationEventEnum;

export const APP_NOTIFICATIONS_CONFIG: Record<NotificationEventType, AppNotificationSettingEnum> = {
  learning_analysis_created: AppNotificationSettingEnum.ANALYST_INSIGHT,
  learning_quick_take_created: AppNotificationSettingEnum.QUICK_TAKE,
  learning_weekly_review_created: AppNotificationSettingEnum.WEEKLY_REVIEW,
  learning_guide_created: AppNotificationSettingEnum.LEARNING_GUIDE,
  learning_daily_market_recap: AppNotificationSettingEnum.DAILY_RECAP,
  prompt_autopilot_1a: AppNotificationSettingEnum.PROMOTIONAL,
  prompt_autopilot_1b: AppNotificationSettingEnum.PROMOTIONAL,
  prompt_free_etf_campaign_1a: AppNotificationSettingEnum.PROMOTIONAL,
  prompt_invest_4a: AppNotificationSettingEnum.PROMOTIONAL,
  prompt_invest_4b: AppNotificationSettingEnum.PROMOTIONAL,
  prompt_referrals_1a: AppNotificationSettingEnum.PROMOTIONAL,
  prompt_send_gift: AppNotificationSettingEnum.PROMOTIONAL,
  prompt_verify_3a: AppNotificationSettingEnum.PROMOTIONAL,
  prompt_verify_3b: AppNotificationSettingEnum.PROMOTIONAL,
  prompt_receive_gift_unverified: AppNotificationSettingEnum.TRANSACTIONAL,
  prompt_receive_gift_verified: AppNotificationSettingEnum.TRANSACTIONAL,
  prompt_referrals_2a: AppNotificationSettingEnum.TRANSACTIONAL,
  prompt_referrals_2b: AppNotificationSettingEnum.TRANSACTIONAL,
  prompt_referrals_3: AppNotificationSettingEnum.TRANSACTIONAL,
  transactional_automated_rebalancing_settled: AppNotificationSettingEnum.TRANSACTIONAL,
  transactional_deposit_failed: AppNotificationSettingEnum.TRANSACTIONAL,
  transactional_deposit_success: AppNotificationSettingEnum.TRANSACTIONAL,
  transactional_dividend_received: AppNotificationSettingEnum.TRANSACTIONAL,
  transactional_kyc_success: AppNotificationSettingEnum.TRANSACTIONAL,
  transactional_order_settled: AppNotificationSettingEnum.TRANSACTIONAL,
  transactional_rebalance_completed: AppNotificationSettingEnum.TRANSACTIONAL,
  transactional_repeating_investment_settled: AppNotificationSettingEnum.TRANSACTIONAL,
  transactional_savings_dividend_received: AppNotificationSettingEnum.TRANSACTIONAL,
  transactional_wealthyhood_dividend_created: AppNotificationSettingEnum.TRANSACTIONAL
};

export const EMAIL_NOTIFICATIONS_CONFIG: Record<EmailType, EmailNotificationSettingEnum> = {
  automatedRebalance: EmailNotificationSettingEnum.TRANSACTIONAL,
  deletionCreation: EmailNotificationSettingEnum.TRANSACTIONAL,
  deletionSuccess: EmailNotificationSettingEnum.TRANSACTIONAL,
  deletionSuccessForInactiveUser: EmailNotificationSettingEnum.TRANSACTIONAL,
  gifterGiftCreation: EmailNotificationSettingEnum.TRANSACTIONAL,
  notExistingTargetUserGiftCreation: EmailNotificationSettingEnum.TRANSACTIONAL,
  referralRewardCreation: EmailNotificationSettingEnum.TRANSACTIONAL,
  referrerRewardCreation: EmailNotificationSettingEnum.TRANSACTIONAL,
  repeatingInvestment: EmailNotificationSettingEnum.TRANSACTIONAL,
  rewardSuccess: EmailNotificationSettingEnum.TRANSACTIONAL,
  unverifiedTargetUserGiftCreation: EmailNotificationSettingEnum.TRANSACTIONAL,
  userInvitation: EmailNotificationSettingEnum.TRANSACTIONAL,
  userVerification: EmailNotificationSettingEnum.TRANSACTIONAL,
  verifiedTargetUserGiftCreation: EmailNotificationSettingEnum.TRANSACTIONAL,
  wealthyhoodDividendCreation: EmailNotificationSettingEnum.TRANSACTIONAL
};

export const APP_NOTIFICATION_CATEGORY_CONFIG: Record<
  AppNotificationSettingsCategoryEnum,
  { name: string; notifications: AppNotificationSettingEnum[] }
> = {
  [AppNotificationSettingsCategoryEnum.ACTIVITY]: {
    name: "Activity",
    notifications: [AppNotificationSettingEnum.TRANSACTIONAL]
  },
  [AppNotificationSettingsCategoryEnum.MARKET_INSIGHTS]: {
    name: "Market insights",
    notifications: [
      AppNotificationSettingEnum.LEARNING_GUIDE,
      AppNotificationSettingEnum.ANALYST_INSIGHT,
      AppNotificationSettingEnum.QUICK_TAKE,
      AppNotificationSettingEnum.DAILY_RECAP,
      AppNotificationSettingEnum.WEEKLY_REVIEW
    ]
  },
  [AppNotificationSettingsCategoryEnum.PROMOTIONAL]: {
    name: "Offers",
    notifications: [AppNotificationSettingEnum.PROMOTIONAL]
  }
};

export const APP_NOTIFICATION_SETTINGS_CONFIG: Record<
  AppNotificationSettingEnum,
  { name: string; description: string }
> = {
  [AppNotificationSettingEnum.TRANSACTIONAL]: {
    name: "Transactional notifications",
    description: "Buys, sells, deposits, dividends, etc"
  },
  [AppNotificationSettingEnum.PROMOTIONAL]: {
    name: "Promos & special offers",
    description: "Free shares, rewards & gifts"
  },
  [AppNotificationSettingEnum.LEARNING_GUIDE]: {
    name: "Learning guides",
    description: "Updates for new educational guides"
  },
  [AppNotificationSettingEnum.ANALYST_INSIGHT]: {
    name: "Analysis",
    description: "Investment ideas, research, insights"
  },
  [AppNotificationSettingEnum.QUICK_TAKE]: {
    name: "Quick takes",
    description: "Bit-sized insights to start your day"
  },
  [AppNotificationSettingEnum.DAILY_RECAP]: {
    name: "Daily market recaps",
    description: "Portfolio summary & news digest"
  },
  [AppNotificationSettingEnum.WEEKLY_REVIEW]: {
    name: "Weekly reviews",
    description: "A weekly summary of market news"
  }
};

export const EMAIL_NOTIFICATION_CATEGORY_CONFIG: Record<
  EmailNotificationSettingsCategoryEnum,
  { name: string; notifications: EmailNotificationSettingEnum[] }
> = {
  [EmailNotificationSettingsCategoryEnum.ACTIVITY]: {
    name: "Activity",
    notifications: [EmailNotificationSettingEnum.TRANSACTIONAL]
  },
  [EmailNotificationSettingsCategoryEnum.NEWSLETTERS]: {
    name: "Newsletters",
    notifications: [EmailNotificationSettingEnum.WEALTHYBITES]
  },
  [EmailNotificationSettingsCategoryEnum.PROMOTIONAL]: {
    name: "Offers",
    notifications: [EmailNotificationSettingEnum.PROMOTIONAL]
  }
};

export const EMAIL_NOTIFICATION_SETTINGS_CONFIG: Record<
  EmailNotificationSettingEnum,
  { name: string; description: string }
> = {
  [EmailNotificationSettingEnum.TRANSACTIONAL]: {
    name: "Transactional emails",
    description: "Updates on your account & activity"
  },
  [EmailNotificationSettingEnum.PROMOTIONAL]: {
    name: "Promos & special offers",
    description: "Free shares, rewards & gifts"
  },
  [EmailNotificationSettingEnum.WEALTHYBITES]: {
    name: "Wealthybites",
    description: "Every day's news & insights"
  }
};

export const CONTENT_ENTRY_CATEGORY_TO_NOTIFICATION_MAPPING: Record<
  ContentEntryContentTypeEnum,
  LearningNotificationEventEnum
> = {
  ANALYSIS: LearningNotificationEventEnum.ANALYSIS_CREATED,
  QUICK_TAKE: LearningNotificationEventEnum.QUICK_TAKE_CREATED,
  WEEKLY_REVIEW: LearningNotificationEventEnum.WEEKLY_REVIEW_CREATED,
  GUIDE: LearningNotificationEventEnum.GUIDE_CREATED
};
