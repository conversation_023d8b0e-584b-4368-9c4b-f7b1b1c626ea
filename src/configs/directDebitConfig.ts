import { entitiesConfig } from "@wealthyhood/shared-configs";
import { ProviderEnum } from "./providersConfig";
import { PartialRecord } from "utils";

export const WORK_DAYS_TO_CREATE_DEPOSITS_BEFORE_AUTOMATION_DATE: Record<
  entitiesConfig.CompanyEntityEnum,
  number
> = {
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK]: 7,
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE]: 10
};

export const ESTIMATED_WORK_DAYS_TO_RECEIVE_FUNDS_AFTER_COLLECTION: PartialRecord<ProviderEnum, number> = {
  [ProviderEnum.WEALTHKERNEL]: 4,
  [ProviderEnum.GOCARDLESS]: 6
};
