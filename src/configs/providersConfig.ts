enum KycEnum {
  JUMIO = "jumio",
  SUMSUB = "sumsub",
  COMPLY_ADVANTAGE = "complyAdvantage"
}

enum BrokerageEnum {
  WEALTHKERNEL = "wealthkernel"
}

enum PaymentsEnum {
  DEVENGO = "devengo",
  TRUELAYER = "truelayer",
  GOCARDLESS = "gocardless",
  STRIPE = "stripe",
  SALTEDGE = "saltedge"
}

enum BankAccountsEnum {
  GOCARDLESS_DATA = "gocardlessData",
  WEALTHYHOOD = "wealthyhood"
}

enum ContentEnum {
  CONTENTFUL = "contentful",
  FINIMIZE = "finimize",
  STOCK_NEWS = "stockNews"
}

enum NotificationsEnum {
  INTERCOM = "intercom",
  ONESIGNAL = "onesignal",
  POSTMARK = "postmark"
}

export const ProviderEnum = {
  ...KycEnum,
  ...BrokerageEnum,
  ...PaymentsEnum,
  ...BankAccountsEnum,
  ...ContentEnum,
  ...NotificationsEnum
};
export type ProviderEnum = KycEnum | BrokerageEnum | PaymentsEnum | BankAccountsEnum | ContentEnum;
