export enum ContentfulAccountEnum {
  /**
   * First contentful account, contains:
   * - Landing page blog content
   * - Wealthybites
   * - Glossary, Help Centre, Legal Pages
   * - Initial learn hub content (before migration to the second account)
   */
  LANDING_PAGE = "LANDING_PAGE",
  /**
   * Second contentful account, contains:
   * - Learn hub content:
   *  - Analyst Insights: Analysis, Quick Takes, Weekly Reviews
   */
  LEARN_HUB = "LEARN_HUB"
}

export enum ContentfulContentTypeEnum {
  // LANDING PAGE account
  ANALYST_INSIGHTS = "analystInsights",
  NEWS = "newsWealthyhub",
  GLOSSARY = "glossary",
  FAQ_CATEGORY = "categoryHelpCentre",

  // LEARN HUB account
  CONTENT_ENTRY = "contentEntry", // these correspond to analyst insights, quick takes, weekly reviews
  LEARNING_GUIDE = "learningGuide"
}
