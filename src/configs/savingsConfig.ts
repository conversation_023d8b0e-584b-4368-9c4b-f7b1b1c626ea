import { entitiesConfig, savingsUniverseConfig } from "@wealthyhood/shared-configs";

/**
 * For now, and until we introduce multi-currency MMFs, we have a single default savings product per company entity.
 */
export const DEFAULT_SAVINGS_PRODUCT_CONFIG: Record<
  entitiesConfig.CompanyEntityEnum,
  savingsUniverseConfig.SavingsProductType
> = {
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE]: "mmf_dist_eur",
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK]: "mmf_dist_gbp"
};
