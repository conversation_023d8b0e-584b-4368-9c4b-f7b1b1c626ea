import { PartialRecord } from "utils";
import { EnvironmentType } from "../utils/environmentUtil";
import { plansConfig, currenciesConfig } from "@wealthyhood/shared-configs";

export enum StripePricesEnum {
  // GBP Prices
  STRIPE_TEST_PAID_LOW_MONTHLY_GBP = "price_1OURkCJ54lNDIU9BIEmhtwKZ",
  STRIPE_TEST_PAID_LOW_YEARLY_GBP = "price_1OgVDxJ54lNDIU9B28RFnfB6",
  STRIPE_TEST_PAID_MID_MONTHLY_GBP = "price_1OURkYJ54lNDIU9Bqqh5NPhv",
  STRIPE_TEST_PAID_MID_YEARLY_GBP = "price_1OgVDEJ54lNDIU9BEx25J7nV",
  STRIPE_PRODUCTION_PAID_LOW_MONTHLY_GBP = "price_1Of3M5J54lNDIU9BgaewYWhK",
  STRIPE_PRODUCTION_PAID_LOW_YEARLY_GBP = "price_1OgVFNJ54lNDIU9BiR1Hq4Z7",
  STRIPE_PRODUCTION_PAID_MID_MONTHLY_GBP = "price_1Of3LtJ54lNDIU9BtdbYzG6l",
  STRIPE_PRODUCTION_PAID_MID_YEARLY_GBP = "price_1OgVEaJ54lNDIU9BNrwmXbdM",

  // EUR Prices
  STRIPE_TEST_PAID_LOW_MONTHLY_EUR = "price_1PJHrsJ54lNDIU9BMtOG8L64",
  STRIPE_TEST_PAID_LOW_YEARLY_EUR = "price_1PJHs4J54lNDIU9BIFVVHQkG",
  STRIPE_TEST_PAID_MID_MONTHLY_EUR = "price_1PJHscJ54lNDIU9B9dAvEBgm",
  STRIPE_TEST_PAID_MID_YEARLY_EUR = "price_1PJHsoJ54lNDIU9BYli5pnuq",
  STRIPE_PRODUCTION_PAID_LOW_MONTHLY_EUR = "price_1PJHnLJ54lNDIU9BAYgIUOZs",
  STRIPE_PRODUCTION_PAID_LOW_YEARLY_EUR = "price_1PJHn7J54lNDIU9BM1E7LNku",
  STRIPE_PRODUCTION_PAID_MID_MONTHLY_EUR = "price_1PJHrNJ54lNDIU9BPaWOSl5b",
  STRIPE_PRODUCTION_PAID_MID_YEARLY_EUR = "price_1PJHrGJ54lNDIU9Bdpv3oiAG"
}

export const WealthyhoodToStripePrices: PartialRecord<
  currenciesConfig.MainCurrencyType,
  PartialRecord<plansConfig.PriceType, Record<EnvironmentType, StripePricesEnum>>
> = {
  GBP: {
    paid_low_monthly: {
      development: StripePricesEnum.STRIPE_TEST_PAID_LOW_MONTHLY_GBP,
      staging: StripePricesEnum.STRIPE_TEST_PAID_LOW_MONTHLY_GBP,
      production: StripePricesEnum.STRIPE_PRODUCTION_PAID_LOW_MONTHLY_GBP
    },
    paid_low_yearly: {
      development: StripePricesEnum.STRIPE_TEST_PAID_LOW_YEARLY_GBP,
      staging: StripePricesEnum.STRIPE_TEST_PAID_LOW_YEARLY_GBP,
      production: StripePricesEnum.STRIPE_PRODUCTION_PAID_LOW_YEARLY_GBP
    },
    paid_mid_monthly: {
      development: StripePricesEnum.STRIPE_TEST_PAID_MID_MONTHLY_GBP,
      staging: StripePricesEnum.STRIPE_TEST_PAID_MID_MONTHLY_GBP,
      production: StripePricesEnum.STRIPE_PRODUCTION_PAID_MID_MONTHLY_GBP
    },
    paid_mid_yearly: {
      development: StripePricesEnum.STRIPE_TEST_PAID_MID_YEARLY_GBP,
      staging: StripePricesEnum.STRIPE_TEST_PAID_MID_YEARLY_GBP,
      production: StripePricesEnum.STRIPE_PRODUCTION_PAID_MID_YEARLY_GBP
    }
  },
  EUR: {
    paid_low_monthly: {
      development: StripePricesEnum.STRIPE_TEST_PAID_LOW_MONTHLY_EUR,
      staging: StripePricesEnum.STRIPE_TEST_PAID_LOW_MONTHLY_EUR,
      production: StripePricesEnum.STRIPE_PRODUCTION_PAID_LOW_MONTHLY_EUR
    },
    paid_low_yearly: {
      development: StripePricesEnum.STRIPE_TEST_PAID_LOW_YEARLY_EUR,
      staging: StripePricesEnum.STRIPE_TEST_PAID_LOW_YEARLY_EUR,
      production: StripePricesEnum.STRIPE_PRODUCTION_PAID_LOW_YEARLY_EUR
    },
    paid_mid_monthly: {
      development: StripePricesEnum.STRIPE_TEST_PAID_MID_MONTHLY_EUR,
      staging: StripePricesEnum.STRIPE_TEST_PAID_MID_MONTHLY_EUR,
      production: StripePricesEnum.STRIPE_PRODUCTION_PAID_MID_MONTHLY_EUR
    },
    paid_mid_yearly: {
      development: StripePricesEnum.STRIPE_TEST_PAID_MID_YEARLY_EUR,
      staging: StripePricesEnum.STRIPE_TEST_PAID_MID_YEARLY_EUR,
      production: StripePricesEnum.STRIPE_PRODUCTION_PAID_MID_YEARLY_EUR
    }
  }
};

export const StripeToWealthyhoodPrices: Record<StripePricesEnum, plansConfig.PriceType> = {
  // GBP Prices
  [StripePricesEnum.STRIPE_TEST_PAID_LOW_MONTHLY_GBP]: "paid_low_monthly",
  [StripePricesEnum.STRIPE_PRODUCTION_PAID_LOW_MONTHLY_GBP]: "paid_low_monthly",
  [StripePricesEnum.STRIPE_TEST_PAID_MID_MONTHLY_GBP]: "paid_mid_monthly",
  [StripePricesEnum.STRIPE_PRODUCTION_PAID_MID_MONTHLY_GBP]: "paid_mid_monthly",
  [StripePricesEnum.STRIPE_TEST_PAID_LOW_YEARLY_GBP]: "paid_low_yearly",
  [StripePricesEnum.STRIPE_PRODUCTION_PAID_LOW_YEARLY_GBP]: "paid_low_yearly",
  [StripePricesEnum.STRIPE_TEST_PAID_MID_YEARLY_GBP]: "paid_mid_yearly",
  [StripePricesEnum.STRIPE_PRODUCTION_PAID_MID_YEARLY_GBP]: "paid_mid_yearly",

  // EUR Prices
  [StripePricesEnum.STRIPE_TEST_PAID_LOW_MONTHLY_EUR]: "paid_low_monthly",
  [StripePricesEnum.STRIPE_PRODUCTION_PAID_LOW_MONTHLY_EUR]: "paid_low_monthly",
  [StripePricesEnum.STRIPE_TEST_PAID_MID_MONTHLY_EUR]: "paid_mid_monthly",
  [StripePricesEnum.STRIPE_PRODUCTION_PAID_MID_MONTHLY_EUR]: "paid_mid_monthly",
  [StripePricesEnum.STRIPE_TEST_PAID_LOW_YEARLY_EUR]: "paid_low_yearly",
  [StripePricesEnum.STRIPE_PRODUCTION_PAID_LOW_YEARLY_EUR]: "paid_low_yearly",
  [StripePricesEnum.STRIPE_TEST_PAID_MID_YEARLY_EUR]: "paid_mid_yearly",
  [StripePricesEnum.STRIPE_PRODUCTION_PAID_MID_YEARLY_EUR]: "paid_mid_yearly"
};
