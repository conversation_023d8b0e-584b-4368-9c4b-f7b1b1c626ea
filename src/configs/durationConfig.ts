import { PartialRecord } from "utils";

export enum TenorEnum {
  ALL_TIME = "max",
  ONE_YEAR = "1y",
  SIX_MONTHS = "6m",
  THREE_MONTHS = "3m",
  ONE_MONTH = "1m",
  ONE_WEEK = "1w",
  ONE_DAY = "1d",
  TODAY = "today"
}
export const DURATIONS_MAP: PartialRecord<TenorEnum, number> = {
  [TenorEnum.TODAY]: 0,
  [TenorEnum.ONE_DAY]: 1,
  [TenorEnum.ONE_WEEK]: 7,
  [TenorEnum.ONE_MONTH]: 30,
  [TenorEnum.THREE_MONTHS]: 91,
  [TenorEnum.SIX_MONTHS]: 182,
  [TenorEnum.ONE_YEAR]: 365,
  [TenorEnum.ALL_TIME]: 10 * 365 // 10 years
};

export const ONE_WEEK_IN_DAYS = DURATIONS_MAP[TenorEnum.ONE_WEEK];

export const DISPLAY_IN_INVESTMENTS_CHART_CONFIG: Record<TenorEnum, boolean> = {
  [TenorEnum.ALL_TIME]: true,
  [TenorEnum.ONE_YEAR]: true,
  [TenorEnum.SIX_MONTHS]: true,
  [TenorEnum.THREE_MONTHS]: true,
  [TenorEnum.ONE_MONTH]: true,
  [TenorEnum.ONE_WEEK]: true,
  [TenorEnum.ONE_DAY]: false,
  [TenorEnum.TODAY]: false
};
export const INTRADAY_DISPLAY_CONFIG: Record<TenorEnum, boolean> = {
  [TenorEnum.ALL_TIME]: false,
  [TenorEnum.ONE_YEAR]: false,
  [TenorEnum.SIX_MONTHS]: false,
  [TenorEnum.THREE_MONTHS]: false,
  [TenorEnum.ONE_MONTH]: true,
  [TenorEnum.ONE_WEEK]: true,
  [TenorEnum.ONE_DAY]: true,
  [TenorEnum.TODAY]: true
};
