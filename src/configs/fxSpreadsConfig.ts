import { plansConfig } from "@wealthyhood/shared-configs";

// Target spreads (m_target) per subscription plan. These are the spreads that drive the FX rates we show to the user.
export const FX_TARGET_SPREADS = {
  paid_mid: 0.00325,
  paid_low: 0.004,
  free: 0.0055
} as const satisfies Record<plansConfig.PlanType, number>;

// Extra FX fee spreads (m_hide) that Wealthyhood charges on top of WK's 40 bp.
// These are applied on the cash amount we submit to WK and therefore drive the FX fee charged to the user.
export const FX_FEE_SPREADS_WH = {
  paid_mid: 0,
  paid_low: 0.0005,
  free: 0.0015
} as const satisfies Record<plansConfig.PlanType, number>;
