import { faker } from "@faker-js/faker";
import { SearchType, SearchWithDetailsType } from "../../external-services/complyAdvantageService";

function buildSearchWithDetails(overrides?: Partial<SearchWithDetailsType>): SearchWithDetailsType {
  return {
    hits: [
      {
        doc: {
          id: faker.string.uuid(),
          types: ["pep", "pep-class-2"]
        },
        is_whitelisted: false
      },
      {
        doc: {
          id: faker.string.uuid(),
          types: ["adverse-media", "adverse-media-v2-financial-aml-cft"]
        },
        is_whitelisted: false
      },
      {
        doc: {
          id: faker.string.uuid(),
          types: ["warning"]
        },
        is_whitelisted: false
      },
      {
        doc: {
          id: faker.string.uuid(),
          types: ["fitness-probity"]
        },
        is_whitelisted: false
      },
      {
        doc: {
          id: faker.string.uuid(),
          types: ["sanction"]
        },
        is_whitelisted: false
      }
    ],
    ...overrides
  };
}

function buildSearch(overrides?: Partial<SearchType>): SearchType {
  return {
    ref: faker.string.uuid(),
    ...overrides
  };
}

export { buildSearchWithDetails, buildSearch };
