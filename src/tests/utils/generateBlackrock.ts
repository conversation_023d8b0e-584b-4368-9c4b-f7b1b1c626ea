import { faker } from "@faker-js/faker";
import { BlackrockFundDataType } from "../../external-services/blackrockService";
import Decimal from "decimal.js";

function buildBlackrockFundData(overrides: Partial<BlackrockFundDataType> = {}): BlackrockFundDataType {
  const dailyDistributionFactor = faker.number.float({ min: 0, max: 1, multipleOf: 0.0001 });
  const oneDayYield = Decimal.mul(dailyDistributionFactor, 365).mul(100).toNumber();

  return {
    dailyDistributionFactor: dailyDistributionFactor,
    oneDayYield: oneDayYield,
    fixingDate: new Date(),
    ...overrides
  };
}

export { buildBlackrockFundData };
