import {
  AccountDetailsType,
  AccountType,
  EndUserAgreementType,
  InstitutionType,
  RequisitionType
} from "../../external-services/goCardlessDataService";
import { faker } from "@faker-js/faker";
import {
  BillingRequestType,
  CustomerBankAccountType,
  CustomerType,
  FulfiledBillingRequestType,
  MandateType,
  PaymentType,
  PayoutItemType,
  PayoutType
} from "../../external-services/goCardlessPaymentsService";

function buildGoCardlessProvidersResponse(): InstitutionType[] {
  return [
    {
      id: "ALPHABANK_CRBAGRAA",
      name: "Alpha Bank",
      bic: "CRBAGRAA",
      transaction_total_days: "540",
      countries: ["GR"],
      logo: "https://cdn-logos.gocardless.com/ais/ALPHABANK_CRBAGRAA.png"
    },
    {
      id: "ATTICA_ATTIGRAA",
      name: "Attica Bank",
      bic: "SOME-BIC-WITHOUT-LOGO", // Bic that we do not have an icon mapping
      transaction_total_days: "365",
      countries: ["GR"],
      logo: "https://cdn-logos.gocardless.com/ais/ATTICA_ATTIGRAA.png"
    }
  ];
}

function buildGoCardlessEndUserAgreementResponse(overrides?: Partial<EndUserAgreementType>): EndUserAgreementType {
  return {
    id: faker.string.uuid(),
    ...overrides
  };
}

function buildGoCardlessRequisitionResponse(overrides?: Partial<RequisitionType>): RequisitionType {
  return {
    id: faker.string.uuid(),
    status: {
      long: "CREATED",
      description: "Requisition has been succesfully created"
    },
    reference: faker.string.uuid(),
    accounts: [],
    link: "https://ob.gocardless.com/psd2/start/3fa85f64-5717-4562-b3fc-2c963f66afa6/{$INSTITUTION_ID}",
    ...overrides
  };
}

function buildGoCardlessAccountResponse(overrides?: Partial<AccountType>): AccountType {
  return {
    id: faker.string.uuid(),
    iban: faker.finance.iban(),
    institution_id: "ALPHABANK_CRBAGRAA",
    owner_name: faker.person.fullName(),
    ...overrides
  };
}

function buildGoCardlessAccountDetailsResponse(overrides?: Partial<AccountDetailsType>): AccountDetailsType {
  return {
    account: {
      currency: "EUR"
    },
    ...overrides
  };
}

function buildGoCardlessCustomer(overrides?: Partial<CustomerType>): CustomerType {
  return {
    id: faker.string.uuid(),
    ...overrides
  };
}

function buildGoCardlessBankAccount(overrides?: Partial<CustomerBankAccountType>): CustomerBankAccountType {
  return {
    id: faker.string.uuid(),
    ...overrides
  };
}

function buildGoCardlessMandate(): MandateType {
  return {
    id: faker.string.uuid(),
    status: faker.helpers.arrayElement(["pending_submission", "submitted", "active", "failed"]),
    next_possible_charge_date: new Date(Date.now()).toISOString()
  };
}

function buildGoCardlessBillingRequest(overrides?: Partial<FulfiledBillingRequestType>): BillingRequestType {
  return {
    id: faker.string.uuid(),
    ...overrides
  };
}

function buildGoCardlessFulfiledBillingRequest(
  overrides?: Partial<FulfiledBillingRequestType>
): FulfiledBillingRequestType {
  return {
    id: faker.string.uuid(),
    mandate_request: {
      links: {
        mandate: faker.string.uuid()
      }
    },
    ...overrides
  };
}

function buildGoCardlessPayment(overrides?: Partial<PaymentType>): PaymentType {
  return {
    id: faker.string.uuid(),
    amount: faker.number.int({ min: 100, max: 10000 }),
    status: "pending_submission",
    reference: faker.string.uuid(),
    metadata: {},
    ...overrides
  };
}

function buildGoCardlessPayout(overrides?: Partial<PayoutType>): PayoutType {
  return {
    id: faker.string.uuid(),
    status: "pending",
    reference: faker.string.uuid(),
    ...overrides
  };
}

function buildGoCardlessPayoutItem(overrides?: Partial<PayoutItemType>): PayoutItemType {
  return {
    type: "payment_paid_out",
    links: {
      payment: faker.string.uuid()
    },
    ...overrides
  };
}

export {
  buildGoCardlessProvidersResponse,
  buildGoCardlessEndUserAgreementResponse,
  buildGoCardlessRequisitionResponse,
  buildGoCardlessAccountResponse,
  buildGoCardlessAccountDetailsResponse,
  buildGoCardlessCustomer,
  buildGoCardlessBankAccount,
  buildGoCardlessMandate,
  buildGoCardlessBillingRequest,
  buildGoCardlessFulfiledBillingRequest,
  buildGoCardlessPayment,
  buildGoCardlessPayout,
  buildGoCardlessPayoutItem
};
