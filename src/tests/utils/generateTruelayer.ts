import { faker } from "@faker-js/faker";
import { IResult } from "truelayer-client";
import {
  AccountType,
  PaymentStatusTypeV3,
  PaymentTypeV3,
  ProviderTypeV2
} from "../../external-services/truelayerService";

function buildPaymentType(overrides: any = {}): PaymentTypeV3 {
  return {
    simp_id: faker.string.uuid(),
    created_at: faker.date.anytime().toString(),
    amount: faker.number.int(),
    currency: faker.string.sample(),
    beneficiary_reference: faker.string.sample(),
    beneficiary_name: faker.string.sample(),
    beneficiary_sort_code: faker.string.sample(),
    beneficiary_account_number: faker.string.sample(),
    remitter_reference: faker.string.sample(),
    redirect_uri: faker.string.sample(),
    remitter_provider_id: faker.string.sample(),
    status: "authorized" as PaymentStatusTypeV3,
    webhook_uri: faker.string.sample(),
    ...overrides
  };
}

function buildProviderType(overrides: any = {}): ProviderTypeV2 {
  return {
    provider_id: faker.string.uuid(),
    logo_url: faker.commerce.productName(),
    icon_url: faker.string.sample(),
    display_name: faker.person.firstName(),
    country: faker.location.countryCode(),
    divisions: [],
    ...overrides
  };
}

function buildPaymentTypeResults(overrides: any = {}): IResult<PaymentTypeV3> {
  return { results: [buildPaymentType(), buildPaymentType()], ...overrides };
}

function buildAccountType(overrides: Partial<AccountType> = {}): AccountType {
  return {
    update_timestamp: faker.date.anytime().toString(),
    account_id: faker.string.uuid(),
    account_type: "TRANSACTION",
    display_name: faker.finance.accountName(),
    currency: "GBP",
    account_number: {
      iban: faker.finance.iban(),
      swift_bic: faker.finance.bic(),
      number: faker.finance.accountNumber(),
      sort_code: "00-00-00"
    },
    provider: {
      display_name: "Mock",
      provider_id: "mock-payments-gb-redirect",
      logo_uri: faker.internet.url()
    },
    ...overrides
  };
}

export { buildAccountType, buildPaymentType, buildPaymentTypeResults, buildProviderType };
