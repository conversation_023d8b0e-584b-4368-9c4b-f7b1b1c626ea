import * as React from "react";
import { Section, Text, Container, Row, Column } from "@react-email/components";

interface HeaderProps {
  firstName?: string;
}

export const Header: React.FC<HeaderProps> = ({ firstName }) => (
  <Container style={styles.container}>
    <Section style={styles.logoSection}>
      {/* <PERSON><PERSON> logo */}
      <img
        src="https://email-assets.wealthyhood.cloud/wealthybites.png"
        alt="Wealthybites cookie logo"
        width={94}
        style={styles.cookieLogo}
      />

      {/* Wealthy + bites text logo */}
      <Row style={styles.textLogoRow}>
        <Column style={styles.textLogoColumn}>
          <img
            src="https://email-assets.wealthyhood.cloud/wealthy.png"
            alt="Wealthy"
            width={130}
            style={styles.wealthyLogo}
          />
        </Column>
        <Column style={styles.textLogoColumn}>
          <img
            src="https://email-assets.wealthyhood.cloud/bites.png"
            alt="bites"
            width={80}
            style={styles.bitesLogo}
          />
        </Column>
      </Row>
    </Section>
    <Section style={styles.welcomeSection}>
      <Text style={styles.welcomeText}>
        {firstName ? `Good morning ${firstName}!` : "Good morning!"} 👋 Your weekly roundup has arrived. Here's
        what you need to know about the top stories that moved the markets this week.
      </Text>
    </Section>
  </Container>
);

const styles = {
  container: {
    width: "100%",
    maxWidth: "600px",
    margin: "0 auto",
    borderRadius: "8px",
    overflow: "hidden"
  },
  logoSection: {
    padding: "32px 16px",
    textAlign: "center" as const,
    backgroundColor: "#FFFFFF"
  },
  cookieLogo: {
    margin: "0 auto",
    paddingBottom: "7.92px",
    display: "block"
  },
  textLogoRow: {
    width: "211.39px",
    margin: "0 auto",
    paddingTop: "7.92px",
    paddingBottom: "8.81px"
  },
  textLogoColumn: {
    verticalAlign: "top" as const
  },
  wealthyLogo: {
    width: "130px",
    maxWidth: "initial",
    display: "block"
  },
  bitesLogo: {
    width: "80px",
    maxWidth: "initial",
    display: "block",
    paddingLeft: "1.39px"
  },
  welcomeSection: {
    padding: "8.81px 20.76px 24px",
    backgroundColor: "#FFFFFF"
  },
  welcomeText: {
    fontFamily: "Inter, sans-serif",
    fontSize: "14px",
    lineHeight: "21px",
    color: "#333333",
    textAlign: "center" as const
  }
};

export default Header;
