import * as React from "react";
import { Section, Text, Container, Link } from "@react-email/components";

interface ReferralSectionProps {
  firstName?: string;
}

export const ReferralSection: React.FC<ReferralSectionProps> = ({ firstName }) => (
  <Container style={styles.container}>
    <Section style={styles.content}>
      <Text style={styles.heading}>Share with a friend! 💙</Text>
      <Text style={styles.message}>
        {firstName ? <>Thanks for reading {firstName}!</> : <>Thanks for reading!</>}
        {" If you enjoyed this week's Wealthybites, we'd love for you to share it with a friend."}
      </Text>
      <Text style={styles.message}>
        🎉 Don't forget that you can always invite friends to join Wealthyhood and earn free shares! 🎉
      </Text>
      <Text>
        <span>👉</span>{" "}
        <Link href="https://wealthyhood.com/help-centre/referrals-faq/" style={styles.link}>
          How can I invite my friends?
        </Link>
      </Text>
    </Section>
  </Container>
);

const styles = {
  container: {
    backgroundColor: "#FFFFFF",
    borderRadius: "8px",
    marginTop: 15,
    marginBottom: 15,
    paddingTop: 16,
    paddingBottom: 16
  },
  content: {
    textAlign: "left" as const,
    paddingLeft: "16px",
    paddingRight: "16px",
    paddingTop: "0",
    paddingBottom: "0"
  },
  heading: {
    fontSize: "20px",
    fontWeight: 700,
    lineHeight: "23px",
    color: "#333333",
    margin: "0 0 12px 0",
    padding: 0
  },
  message: {
    fontSize: "14px",
    fontWeight: 400,
    lineHeight: "21px",
    color: "#333333",
    margin: "0 0 12px 0",
    padding: "6px 0"
  },
  link: {
    fontSize: "14px",
    fontWeight: 400,
    color: "#536AE3",
    display: "inline-block",
    lineHeight: "21px",
    margin: "0 0 0 5px",
    padding: 0,
    textDecoration: "underline"
  }
};

export default ReferralSection;
