import * as React from "react";
import { Section, Text } from "@react-email/components";

interface SectionHeadingProps {
  label?: string;
  title: string;
  paddingTop?: string;
}

export const SectionHeading: React.FC<SectionHeadingProps> = ({ label, title, paddingTop = "32px" }) => (
  <Section style={{ ...styles.sectionHeader, paddingTop }}>
    {label && <Text style={styles.sectionLabel}>{label}</Text>}
    <Text style={styles.sectionTitle}>{title}</Text>
  </Section>
);

const styles = {
  sectionHeader: {
    paddingLeft: "16px",
    paddingRight: "16px",
    paddingBottom: "16px",
    backgroundColor: "#FFFFFF",
    borderRadius: "8px"
  },
  sectionLabel: {
    fontSize: "12px",
    fontWeight: "700",
    lineHeight: "14px",
    textTransform: "uppercase" as const,
    color: "#536AE3",
    margin: "0",
    paddingBottom: "4px"
  },
  sectionTitle: {
    fontSize: "20px",
    fontWeight: "700",
    lineHeight: "23px",
    margin: "0",
    paddingTop: "4px"
  }
};

export default SectionHeading;
