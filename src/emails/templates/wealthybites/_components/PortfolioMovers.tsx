import * as React from "react";
import { Section, Text, Container } from "@react-email/components";
import { investmentUniverseConfig, localeConfig } from "@wealthyhood/shared-configs";
import type { TopPortfolioMoverItem } from "types/wealthybites";
import { formatPercentage } from "../../../../utils/formatterUtil";

const { ASSET_CONFIG } = investmentUniverseConfig;

interface PortfolioMoversProps {
  winners?: TopPortfolioMoverItem[];
  losers?: TopPortfolioMoverItem[];
  userLocale: localeConfig.LocaleType;
}

export const PortfolioMovers: React.FC<PortfolioMoversProps> = ({ winners = [], losers = [], userLocale }) => (
  <Container style={styles.container}>
    {/* Best Performers */}
    {winners.length > 0 && (
      <Section style={styles.performersContainer}>
        <Container style={styles.bestPerformersBox}>
          <Text style={styles.performersTitle}>Your best performers</Text>
          <Text style={styles.moversText}>
            {winners.map((mover, index) => (
              <React.Fragment key={mover.assetId + "-winner" + index}>
                <span style={styles.greenArrow}>↑</span> {ASSET_CONFIG[mover.assetId].simpleName}{" "}
                <span style={styles.greenText}>
                  {formatPercentage(mover.weeklyReturns, userLocale, 2, 2, "always")}
                </span>
                {index < winners.length - 1 && <br />}
              </React.Fragment>
            ))}
          </Text>
        </Container>
      </Section>
    )}

    {/* Worst Performers */}
    {losers.length > 0 && (
      <Section style={styles.performersContainer}>
        <Container style={styles.worstPerformersBox}>
          <Text style={styles.performersTitle}>Your worst performers</Text>
          <Text style={styles.moversText}>
            {losers.map((mover, index) => (
              <React.Fragment key={mover.assetId + "-loser" + index}>
                <span style={styles.redArrow}>↓</span> {ASSET_CONFIG[mover.assetId].simpleName}{" "}
                <span style={styles.redText}>
                  {formatPercentage(mover.weeklyReturns, userLocale, 2, 2, "always")}
                </span>
                {index < losers.length - 1 && <br />}
              </React.Fragment>
            ))}
          </Text>
        </Container>
      </Section>
    )}
  </Container>
);

const styles = {
  container: {
    backgroundColor: "#FFFFFF",
    borderRadius: "8px"
  },
  performersContainer: {
    backgroundColor: "#FFFFFF",
    padding: "0px 16px 24px",
    borderRadius: "8px"
  },
  bestPerformersBox: {
    borderRadius: "16px",
    border: "1px solid rgba(35, 132, 106, 0.3)",
    backgroundColor: "#FFFFFF",
    padding: "23px"
  },
  worstPerformersBox: {
    borderRadius: "16px",
    border: "1px solid rgba(214, 60, 60, 0.3)",
    backgroundColor: "#FFFFFF",
    padding: "23px"
  },
  performersTitle: {
    fontSize: "16px",
    fontWeight: "700",
    lineHeight: "18px",
    margin: "0",
    paddingBottom: "8px"
  },
  moversText: {
    fontFamily: "Inter, Tahoma, sans-serif",
    fontSize: "14px",
    fontWeight: "400",
    textAlign: "left" as const,
    lineHeight: "21px",
    color: "#333333",
    margin: "0",
    paddingTop: "8px"
  },
  greenArrow: {
    color: "#23846A"
  },
  redArrow: {
    color: "#D63C3C"
  },
  greenText: {
    color: "#23846A"
  },
  redText: {
    color: "#D63C3C"
  }
};

export default PortfolioMovers;
