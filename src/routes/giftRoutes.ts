import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import GiftController from "../controllers/giftController";

const router = express.Router();

/**
 * GET REQUESTS
 */
router.get("/", ErrorMiddleware.catchAsyncErrors(GiftController.getGifts));

/**
 * POST REQUESTS
 */
router.post("/", ErrorMiddleware.catchAsyncErrors(GiftController.createGift));
router.post("/:id", ErrorMiddleware.catchAsyncErrors(GiftController.updateGift));

export default router;
