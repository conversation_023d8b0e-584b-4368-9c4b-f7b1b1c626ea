import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import { AdminBankAccountController } from "../controllers/adminBankAccountController";

const router = express.Router();

/**
 * GET REQUESTS
 */
router.get("/suspended", ErrorMiddleware.catchAsyncErrors(AdminBankAccountController.getSuspendedBankAccounts));
router.get("/pending", ErrorMiddleware.catchAsyncErrors(AdminBankAccountController.getPendingBankAccounts));

/**
 * POST REQUESTS
 */
router.post(
  "/create-wealthkernel",
  ErrorMiddleware.catchAsyncErrors(AdminBankAccountController.createAllWkBankAccounts)
);

router.post(
  "/sync-wealthkernel",
  ErrorMiddleware.catchAsyncErrors(AdminBankAccountController.syncAllWkBankAccounts)
);

router.post(
  "/:id/wealthyhood-status",
  ErrorMiddleware.catchAsyncErrors(AdminBankAccountController.updateBankAccountWealthyhoodStatus)
);

export default router;
