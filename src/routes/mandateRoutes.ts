import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import MandateController from "../controllers/mandateController";

const router = express.Router();

/**
 * GET REQUESTS
 */
router.get("/", ErrorMiddleware.catchAsyncErrors(MandateController.getMandates));

/**
 * POST REQUESTS
 */
router.post("/", ErrorMiddleware.catchAsyncErrors(MandateController.setupMandate));

export default router;
