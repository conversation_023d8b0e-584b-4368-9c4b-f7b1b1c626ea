import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import InvestmentProductController from "../controllers/investmentProductController";

const router = express.Router();

router.get("/", ErrorMiddleware.catchAsyncErrors(InvestmentProductController.getInvestmentProducts));
router.get(
  "/etf-data",
  ErrorMiddleware.catchAsyncErrors(InvestmentProductController.getInvestmentProductFundamentalsData)
);
router.get(
  "/prices-by-tenor",
  ErrorMiddleware.catchAsyncErrors(InvestmentProductController.getInvestmentProductPricesByTenor)
);
router.get(
  "/recent-activity",
  ErrorMiddleware.catchAsyncErrors(InvestmentProductController.getAssetRecentActivity)
);
router.get(
  "/investment-details",
  ErrorMiddleware.catchAsyncErrors(InvestmentProductController.getUserInvestmentDetails)
);

export default router;
