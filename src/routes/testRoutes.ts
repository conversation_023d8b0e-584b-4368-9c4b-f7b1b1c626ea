import express from "express";
import TestController from "../controllers/testController";
import AuthMiddleware from "../middlewares/authMiddleware";
import EnvironmentMiddleware from "../middlewares/environmentMiddleware";
import ErrorMiddleware from "../middlewares/errorMiddleware";

const testRoutes = express.Router();

/**
 * GET REQUESTS
 */
testRoutes.get("/help/statuses", ErrorMiddleware.catchAsyncErrors(TestController.getAvailableStatuses));

/**
 * POST REQUESTS
 */
testRoutes.post("/users", ErrorMiddleware.catchAsyncErrors(TestController.createUser));

testRoutes.delete("/users", ErrorMiddleware.catchAsyncErrors(TestController.deleteUser));

const testRouter = express.Router();
testRouter.use(
  "/",
  EnvironmentMiddleware.envIsDevOrStaging,
  AuthMiddleware.setPlatform,
  AuthMiddleware.handleErrors,
  testRoutes
);

export default testRouter;
