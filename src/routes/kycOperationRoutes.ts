import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import KycOperationController from "../controllers/kycOperationController";

const router = express.Router();

/**
 * GET REQUESTS
 */
router.get("/me", ErrorMiddleware.catchAsyncErrors(KycOperationController.retrieveKycOperation));

/**
 * POST REQUESTS
 */
router.post("/initiate", ErrorMiddleware.catchAsyncErrors(KycOperationController.initiateKycOperation));

export default router;
