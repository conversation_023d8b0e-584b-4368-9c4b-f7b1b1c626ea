import request from "supertest";
import app from "../../app";
import { faker } from "@faker-js/faker";
import { KycStatusEnum, UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildChargeTransaction,
  buildMandate,
  buildPaymentMethod,
  buildPortfolio,
  buildSubscription,
  buildUser
} from "../../tests/utils/generateModels";
import { Subscription, SubscriptionDocument } from "../../models/Subscription";
import { MandateDocument } from "../../models/Mandate";
import { ChargeTransaction } from "../../models/Transaction";
import { PaymentMethod, PaymentMethodDocument } from "../../models/PaymentMethod";
import { StripeService } from "../../external-services/stripeService";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import { StripePricesEnum } from "../../configs/stripeConfig";

describe("SubscriptionRoutes", () => {
  beforeAll(async () => await connectDb("SubscriptionRoutes"));
  afterAll(async () => await closeDb());

  describe("GET /subscriptions", () => {
    describe("when user does not have subscription", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      });
      afterAll(async () => await clearDb());

      it("should return 204", async () => {
        const response = await request(app)
          .get("/api/m2m/subscriptions")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");

        expect(response.status).toBe(204);
      });
    });

    describe("when user has a fee-based subscription", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        subscription = await buildSubscription({
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly",
          owner: owner.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return a 200 with the user's subscription", async () => {
        const response = await request(app)
          .get("/api/m2m/subscriptions")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");

        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            _id: subscription.id,
            active: true,
            category: "FeeBasedSubscription",
            price: "free_monthly"
          })
        );
      });
    });

    describe("when user has a direct debit subscription", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;
      let mandate: MandateDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        mandate = await buildMandate({ owner: owner.id, bankAccount: owner.bankAccounts[0].id });
        subscription = await buildSubscription({
          category: "DirectDebitSubscription",
          active: true,
          price: "free_monthly",
          owner: owner.id,
          mandate: mandate.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return a 200 with the user's subscription and a populated mandate", async () => {
        const response = await request(app)
          .get("/api/m2m/subscriptions")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");

        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            _id: subscription.id,
            active: true,
            category: "DirectDebitSubscription",
            price: "free_monthly",
            mandate: expect.objectContaining({ _id: mandate.id })
          })
        );
      });
    });
  });

  describe("POST /subscriptions", () => {
    describe("when request body is empty", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post("/api/m2m/subscriptions")
          .send({})
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "Missing field 'price'"
            }
          })
        );
      });
    });

    describe("when user already has subscription", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        await buildSubscription({
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly",
          owner: owner.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post("/api/m2m/subscriptions")
          .send({ price: "free_monthly" })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "User already has a subscription"
            }
          })
        );
      });
    });

    describe("when request is for card-based free subscription", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      });
      afterAll(async () => await clearDb());

      it("should return 500", async () => {
        const response = await request(app)
          .post("/api/m2m/subscriptions")
          .send({ price: "free_monthly", category: "CardPaymentSubscription" })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(500);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "An error occurred",
              message: "Invalid price/subscription category combination"
            }
          })
        );
      });
    });

    describe("when request is for fee-based paid_low subscription", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycPassed: true });
      });
      afterAll(async () => await clearDb());

      it("should return 500", async () => {
        const response = await request(app)
          .post("/api/m2m/subscriptions")
          .send({ price: "paid_low_monthly", category: "FeeBasedSubscription" })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(500);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "An error occurred",
              message: "Invalid price/subscription category combination"
            }
          })
        );
      });
    });

    describe("when request body is valid for free subscription", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      });
      afterAll(async () => await clearDb());

      it("should return 204 and successfully create a subscription for a valid body", async () => {
        const response = await request(app)
          .post("/api/m2m/subscriptions")
          .send({ price: "free_monthly", category: "FeeBasedSubscription" })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            category: "FeeBasedSubscription",
            owner: owner.id,
            price: "free_monthly",
            active: true
          })
        );

        const subscription = await Subscription.findOne({ owner: owner.id });
        expect(subscription).toEqual(
          expect.objectContaining({
            price: "free_monthly",
            owner: owner._id,
            category: "FeeBasedSubscription",
            active: true
          })
        );
      });
    });

    describe("when request body is valid for free subscription and the user has failed verification", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.FAILED });
      });
      afterAll(async () => await clearDb());

      it("should return 204 and successfully create an inactive subscription", async () => {
        const response = await request(app)
          .post("/api/m2m/subscriptions")
          .send({ price: "free_monthly", category: "FeeBasedSubscription" })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);

        const subscription = await Subscription.findOne({ owner: owner.id });
        expect(subscription).toEqual(
          expect.objectContaining({
            price: "free_monthly",
            owner: owner._id,
            category: "FeeBasedSubscription",
            active: false
          })
        );
      });
    });
  });

  describe("POST /subscriptions/:id", () => {
    describe("when request body is empty", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        subscription = await buildSubscription({
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly",
          owner: owner.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post(`/api/m2m/subscriptions/${subscription.id}`)
          .send({})
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "Request body cannot be empty"
            }
          })
        );
      });
    });

    describe("when category in body is not a valid subscription category", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        subscription = await buildSubscription({
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly",
          owner: owner.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post(`/api/m2m/subscriptions/${subscription.id}`)
          .send({ category: "SomeOtherSubscription" })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Invalid parameter",
              message:
                "Param 'category' has invalid value 'SomeOtherSubscription', must be one of [FeeBasedSubscription,DirectDebitSubscription,CardPaymentSubscription,SinglePaymentSubscription]"
            }
          })
        );
      });
    });

    describe("when new category is SinglePaymentSubscription", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        subscription = await buildSubscription({
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly",
          owner: owner.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the same subscription", async () => {
        const response = await request(app)
          .post(`/api/m2m/subscriptions/${subscription.id}`)
          .send({ category: "SinglePaymentSubscription", price: "paid_low_lifetime_blackfriday_2023" })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            category: "FeeBasedSubscription",
            price: "free_monthly"
          })
        );
      });
    });
  });

  describe("POST /subscriptions/:id/renew", () => {
    describe("when category of subscription is fee-based", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        subscription = await buildSubscription({
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly",
          owner: owner.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post(`/api/m2m/subscriptions/${subscription.id}/renew`)
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "Subscription has to be a recurring paid subscription that's expiring."
            }
          })
        );
      });
    });

    describe("when subscription is not expiring", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        subscription = await buildSubscription({
          category: "DirectDebitSubscription",
          active: true,
          price: "paid_low_monthly",
          owner: owner.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post(`/api/m2m/subscriptions/${subscription.id}/renew`)
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "Subscription has to be a recurring paid subscription that's expiring."
            }
          })
        );
      });
    });

    describe("when subscription's expiration date is in the past", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;

      beforeAll(async () => {
        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        subscription = await buildSubscription({
          category: "DirectDebitSubscription",
          active: true,
          price: "paid_low_monthly",
          owner: owner.id,
          expiration: {
            date: new Date("2022-08-30T11:00:00Z"), // Expiration date is in the past
            downgradesTo: "free_monthly"
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post(`/api/m2m/subscriptions/${subscription.id}/renew`)
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "Subscription's expiration date has to be in the future"
            }
          })
        );
      });
    });

    describe("when subscription's expiration date is in the future", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;

      beforeAll(async () => {
        const TODAY = new Date("2022-08-31T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        subscription = await buildSubscription({
          category: "DirectDebitSubscription",
          active: true,
          price: "paid_low_monthly",
          owner: owner.id,
          expiration: {
            date: new Date("2022-09-01T11:00:00Z"), // Expiration date is in the past
            downgradesTo: "free_monthly"
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the renewed subscription", async () => {
        const response = await request(app)
          .post(`/api/m2m/subscriptions/${subscription.id}/renew`)
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
      });
    });
  });

  describe("POST /subscriptions/payment-method", () => {
    describe("when category of subscription is fee-based", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;
      let response: request.Response;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        subscription = await buildSubscription({
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly",
          owner: owner.id
        });
        const paymentMethod = await buildPaymentMethod({
          owner: owner.id
        });

        response = await request(app)
          .post("/api/m2m/subscriptions/payment-method")
          .send({
            paymentMethod: paymentMethod.id
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 500", async () => {
        expect(response.status).toBe(500);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "An error occurred",
              message: `Subscription ${subscription.id} is not card-based!`
            }
          })
        );
      });
    });

    describe("when category of subscription is lifetime", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;
      let response: request.Response;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        const paymentMethod = await buildPaymentMethod({
          owner: owner.id
        });
        subscription = await buildSubscription({
          category: "SinglePaymentSubscription",
          active: true,
          price: "paid_mid_lifetime_blackfriday_2023",
          owner: owner.id
        });

        response = await request(app)
          .post("/api/m2m/subscriptions/payment-method")
          .send({
            paymentMethod: paymentMethod.id
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 500", async () => {
        expect(response.status).toBe(500);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "An error occurred",
              message: `Subscription ${subscription.id} is not card-based!`
            }
          })
        );
      });
    });

    describe("when passed payment method does not exist", () => {
      let owner: UserDocument;
      let response: request.Response;

      const STRIPE_PAYMENT_METHOD_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.spyOn(StripeService.Instance, "retrievePaymentMethod").mockResolvedValue({
          type: "card"
        } as any);

        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        await buildSubscription({
          category: "CardPaymentSubscription",
          active: true,
          price: "paid_low_monthly",
          owner: owner.id,
          providers: {
            stripe: {
              id: faker.string.uuid()
            }
          }
        });

        response = await request(app)
          .post("/api/m2m/subscriptions/payment-method")
          .send({
            paymentMethod: STRIPE_PAYMENT_METHOD_ID
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 500", async () => {
        expect(response.status).toBe(500);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "An error occurred",
              message: `Could not find payment method ${STRIPE_PAYMENT_METHOD_ID}!`
            }
          })
        );
      });
    });

    describe("when category of subscription is a card payment subscription", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;
      let response: request.Response;

      const STRIPE_PAYMENT_METHOD_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.spyOn(StripeService.Instance, "retrievePaymentMethod").mockResolvedValue({
          id: faker.string.uuid(),
          type: "card",
          card: {
            last4: "4242",
            brand: "visa",
            wallet: {
              type: "apple_pay"
            }
          }
        } as any);

        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        subscription = await buildSubscription({
          category: "CardPaymentSubscription",
          active: true,
          price: "paid_low_monthly",
          owner: owner.id,
          providers: {
            stripe: {
              id: faker.string.uuid()
            }
          }
        });

        response = await request(app)
          .post("/api/m2m/subscriptions/payment-method")
          .send({
            paymentMethod: STRIPE_PAYMENT_METHOD_ID
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should create a new payment method for this card", async () => {
        const paymentMethods = await PaymentMethod.find({ owner: owner.id });
        expect(paymentMethods.length).toBe(1);
        expect(paymentMethods[0]).toMatchObject(
          expect.objectContaining({
            owner: owner._id,
            type: "card",
            brand: "visa",
            lastFourDigits: "4242",
            wallet: "apple_pay"
          })
        );
      });

      it("should return 200 with the updated subscription", async () => {
        const paymentMethod = await PaymentMethod.findOne({ owner: owner.id });

        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            category: "CardPaymentSubscription",
            active: true,
            price: "paid_low_monthly",
            paymentMethod: paymentMethod.id
          })
        );
      });

      it("should call the Stripe API to update the subscription payment method", async () => {
        const paymentMethod = await PaymentMethod.findOne({ owner: owner.id });

        expect(
          StripeService.Instance.updateSubscription(subscription.providers.stripe.id, {
            default_payment_method: paymentMethod.providers.stripe.id
          })
        );
      });

      it("should update the subscription document with the new payment method", async () => {
        const paymentMethod = await PaymentMethod.findOne({ owner: owner.id });

        const updatedSubscription = await Subscription.findById(subscription.id);
        expect(updatedSubscription.paymentMethod).toEqual(paymentMethod._id);
      });
    });
  });

  describe("POST /subscriptions/initiate-stripe", () => {
    describe("when client has not passed price", () => {
      let owner: UserDocument;
      let paymentMethod: PaymentMethodDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        paymentMethod = await buildPaymentMethod({ owner: owner.id });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post("/api/m2m/subscriptions/initiate-stripe")
          .send({
            paymentMethod: paymentMethod.id
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "Price has not been passed!"
            }
          })
        );
      });
    });

    describe("when client has not passed payment method", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post("/api/m2m/subscriptions/initiate-stripe")
          .send({
            price: "paid_mid_monthly"
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "Payment method has not been passed!"
            }
          })
        );
      });
    });

    describe("when user does not have Stripe customer ID", () => {
      let owner: UserDocument;
      let paymentMethod: PaymentMethodDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        paymentMethod = await buildPaymentMethod({ owner: owner.id });
      });
      afterAll(async () => await clearDb());

      it("should return 500", async () => {
        const response = await request(app)
          .post("/api/m2m/subscriptions/initiate-stripe")
          .send({
            paymentMethod: paymentMethod.id,
            price: "paid_mid_monthly"
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(500);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "An error occurred",
              message: "User does not have Stripe customer ID!"
            }
          })
        );
      });
    });

    describe("when user has passed payment method that does not exist", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        jest.spyOn(StripeService.Instance, "retrievePaymentMethod").mockResolvedValue({
          type: "card"
        } as any);

        owner = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          providers: {
            stripe: {
              id: faker.string.uuid()
            }
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should return 500", async () => {
        const response = await request(app)
          .post("/api/m2m/subscriptions/initiate-stripe")
          .send({
            paymentMethod: faker.string.uuid(),
            price: "paid_mid_monthly"
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(500);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "An error occurred",
              message: "There is no such payment method!"
            }
          })
        );
      });
    });

    describe("when user does not have a subscription", () => {
      let owner: UserDocument;
      let paymentMethod: PaymentMethodDocument;
      let response: request.Response;

      const EXPIRATION_DATE_SECONDS = **********;
      const STRIPE_PAYMENT_METHOD_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.spyOn(StripeService.Instance, "createSubscription").mockResolvedValue({
          current_period_end: EXPIRATION_DATE_SECONDS
        } as any);

        owner = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          providers: {
            stripe: {
              id: faker.string.uuid()
            }
          }
        });
        await buildPortfolio({ owner: owner.id });
        paymentMethod = await buildPaymentMethod({
          owner: owner.id,
          providers: {
            stripe: {
              id: STRIPE_PAYMENT_METHOD_ID
            }
          }
        });

        response = await request(app)
          .post("/api/m2m/subscriptions/initiate-stripe")
          .send({
            paymentMethod: STRIPE_PAYMENT_METHOD_ID,
            price: "paid_mid_monthly"
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 with a hasUsedFreeTrial flag", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            subscription: expect.objectContaining({
              price: "paid_mid_monthly",
              hasUsedFreeTrial: true,
              active: true,
              category: "CardPaymentSubscription",
              paymentMethod: paymentMethod.id
            })
          })
        );
      });

      it("should create a new Stripe subscription with a free trial", async () => {
        expect(StripeService.Instance.createSubscription).toHaveBeenCalledWith(
          owner.providers.stripe.id,
          StripePricesEnum.STRIPE_TEST_PAID_MID_MONTHLY_GBP,
          paymentMethod.providers.stripe.id,
          { withFreeTrial: true }
        );
      });

      it("should create a new paid_mid_monthly subscription", async () => {
        const subscription = await Subscription.findOne({ owner: owner.id });
        expect(subscription).toEqual(
          expect.objectContaining({
            price: "paid_mid_monthly",
            hasUsedFreeTrial: true,
            active: true,
            category: "CardPaymentSubscription",
            nextChargeAt: new Date("2024-02-26T15:23:20.000Z"),
            paymentMethod: paymentMethod._id
          })
        );
      });

      it("should NOT create a new charge transaction", async () => {
        const chargeTransactions = await ChargeTransaction.find({ owner: owner.id });
        expect(chargeTransactions.length).toBe(0);
      });

      it("should emit a 'onPlanUpdated' event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.plan.planUpgrade.eventId,
          expect.objectContaining({ email: owner.email }),
          {
            from: "",
            fromRecurrence: "",
            to: "paid_mid",
            toRecurrence: "monthly"
          }
        );
      });
    });

    describe("when user already has a subscription and had NOT used a free trial in the past", () => {
      let owner: UserDocument;
      let paymentMethod: PaymentMethodDocument;
      let response: request.Response;

      const EXPIRATION_DATE_SECONDS = **********;
      const STRIPE_PAYMENT_METHOD_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.spyOn(StripeService.Instance, "createSubscription").mockResolvedValue({
          current_period_end: EXPIRATION_DATE_SECONDS
        } as any);

        owner = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          providers: {
            stripe: {
              id: faker.string.uuid()
            }
          }
        });
        await buildSubscription({
          owner: owner.id,
          price: "free_monthly",
          hasUsedFreeTrial: false
        });
        await buildPortfolio({ owner: owner.id });
        paymentMethod = await buildPaymentMethod({
          owner: owner.id,
          providers: {
            stripe: {
              id: STRIPE_PAYMENT_METHOD_ID
            }
          }
        });

        const subscriptions = await Subscription.find();
        expect(subscriptions.length).toBe(1);

        response = await request(app)
          .post("/api/m2m/subscriptions/initiate-stripe")
          .send({
            paymentMethod: STRIPE_PAYMENT_METHOD_ID,
            price: "paid_mid_monthly"
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 with a hasUsedFreeTrial flag", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            subscription: expect.objectContaining({
              price: "paid_mid_monthly",
              hasUsedFreeTrial: true,
              active: true,
              category: "CardPaymentSubscription",
              paymentMethod: paymentMethod.id
            })
          })
        );
      });

      it("should create a new Stripe subscription with a free trial", async () => {
        expect(StripeService.Instance.createSubscription).toHaveBeenCalledWith(
          owner.providers.stripe.id,
          StripePricesEnum.STRIPE_TEST_PAID_MID_MONTHLY_GBP,
          paymentMethod.providers.stripe.id,
          { withFreeTrial: true }
        );
      });

      it("should update the subscription to paid_mid_monthly", async () => {
        const subscription = await Subscription.findOne({ owner: owner.id });
        expect(subscription).toEqual(
          expect.objectContaining({
            price: "paid_mid_monthly",
            hasUsedFreeTrial: true,
            active: true,
            category: "CardPaymentSubscription",
            nextChargeAt: new Date("2024-02-26T15:23:20.000Z"),
            paymentMethod: paymentMethod._id
          })
        );
      });

      it("should NOT create a new charge transaction", async () => {
        const chargeTransactions = await ChargeTransaction.find({ owner: owner.id });
        expect(chargeTransactions.length).toBe(0);
      });

      it("should emit a 'onPlanUpdated' event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.plan.planUpgrade.eventId,
          expect.objectContaining({ email: owner.email }),
          {
            from: "free",
            fromRecurrence: "monthly",
            to: "paid_mid",
            toRecurrence: "monthly"
          }
        );
      });
    });

    describe("when user already has a subscription and had used a free trial in the past", () => {
      let owner: UserDocument;
      let paymentMethod: PaymentMethodDocument;
      let response: request.Response;

      const STRIPE_PAYMENT_METHOD_ID = faker.string.uuid();
      const STRIPE_SUBSCRIPTION_PAYMENT_INTENT_ID = faker.string.uuid();
      const STRIPE_SUBSCRIPTION_PAYMENT_INTENT_CLIENT_SECRET = faker.string.uuid();

      beforeAll(async () => {
        jest.spyOn(StripeService.Instance, "createSubscription").mockResolvedValue({
          latest_invoice: {
            payment_intent: {
              id: STRIPE_SUBSCRIPTION_PAYMENT_INTENT_ID,
              client_secret: STRIPE_SUBSCRIPTION_PAYMENT_INTENT_CLIENT_SECRET,
              status: "processing"
            }
          }
        } as any);

        owner = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          providers: {
            stripe: {
              id: faker.string.uuid()
            }
          }
        });
        await buildSubscription({
          owner: owner.id,
          price: "free_monthly",
          hasUsedFreeTrial: true
        });
        await buildPortfolio({ owner: owner.id });
        paymentMethod = await buildPaymentMethod({
          owner: owner.id,
          providers: {
            stripe: {
              id: STRIPE_PAYMENT_METHOD_ID
            }
          }
        });

        const subscriptions = await Subscription.find();
        expect(subscriptions.length).toBe(1);

        response = await request(app)
          .post("/api/m2m/subscriptions/initiate-stripe")
          .send({
            paymentMethod: STRIPE_PAYMENT_METHOD_ID,
            price: "paid_mid_monthly"
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 with a client secret and payment intent ID", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            clientSecret: STRIPE_SUBSCRIPTION_PAYMENT_INTENT_CLIENT_SECRET,
            paymentIntentId: STRIPE_SUBSCRIPTION_PAYMENT_INTENT_ID
          })
        );
      });

      it("should create a new Stripe subscription without a free trial", async () => {
        expect(StripeService.Instance.createSubscription).toHaveBeenCalledWith(
          owner.providers.stripe.id,
          StripePricesEnum.STRIPE_TEST_PAID_MID_MONTHLY_GBP,
          paymentMethod.providers.stripe.id,
          { isApplePay: false, withFreeTrial: false }
        );
      });

      it("should NOT create a new subscription", async () => {
        const subscriptions = await Subscription.find();
        expect(subscriptions.length).toBe(1);
      });

      it("should create a new charge transaction", async () => {
        const chargeTransaction = await ChargeTransaction.findOne({ owner: owner.id });
        expect(chargeTransaction).toEqual(
          expect.objectContaining({
            chargeType: "subscription",
            chargeMethod: "card",
            paymentMethod: paymentMethod._id,
            providers: expect.objectContaining({
              stripe: {
                id: STRIPE_SUBSCRIPTION_PAYMENT_INTENT_ID,
                status: "processing"
              }
            })
          })
        );
      });
    });

    describe("when user already has a subscription, had used a free trial in the past but they have a credit balance in Stripe", () => {
      let owner: UserDocument;
      let paymentMethod: PaymentMethodDocument;
      let response: request.Response;

      const STRIPE_PAYMENT_METHOD_ID = faker.string.uuid();
      const EXPIRATION_DATE_SECONDS = **********;

      beforeAll(async () => {
        // When user has a Stripe credit balance, then Stripe creates their subscription but without
        // creating a payment intent.
        jest.spyOn(StripeService.Instance, "createSubscription").mockResolvedValue({
          current_period_end: EXPIRATION_DATE_SECONDS
        } as any);

        owner = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          providers: {
            stripe: {
              id: faker.string.uuid()
            }
          }
        });
        await buildSubscription({
          owner: owner.id,
          price: "free_monthly",
          hasUsedFreeTrial: true
        });
        await buildPortfolio({ owner: owner.id });
        paymentMethod = await buildPaymentMethod({
          owner: owner.id,
          providers: {
            stripe: {
              id: STRIPE_PAYMENT_METHOD_ID
            }
          }
        });

        const subscriptions = await Subscription.find();
        expect(subscriptions.length).toBe(1);

        response = await request(app)
          .post("/api/m2m/subscriptions/initiate-stripe")
          .send({
            paymentMethod: STRIPE_PAYMENT_METHOD_ID,
            price: "paid_mid_monthly"
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 without a hasUsedFreeTrial flag", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            subscription: expect.objectContaining({
              price: "paid_mid_monthly",
              hasUsedFreeTrial: true,
              active: true,
              category: "CardPaymentSubscription",
              paymentMethod: paymentMethod.id
            })
          })
        );
      });

      it("should create a new Stripe subscription without a free trial", async () => {
        expect(StripeService.Instance.createSubscription).toHaveBeenCalledWith(
          owner.providers.stripe.id,
          StripePricesEnum.STRIPE_TEST_PAID_MID_MONTHLY_GBP,
          paymentMethod.providers.stripe.id,
          { isApplePay: false, withFreeTrial: false }
        );
      });

      it("should update the subscription to paid_mid_monthly", async () => {
        const subscription = await Subscription.findOne({ owner: owner.id });
        expect(subscription).toEqual(
          expect.objectContaining({
            price: "paid_mid_monthly",
            hasUsedFreeTrial: true,
            active: true,
            category: "CardPaymentSubscription",
            nextChargeAt: new Date("2024-02-26T15:23:20.000Z"),
            paymentMethod: paymentMethod._id
          })
        );
      });

      it("should NOT create a new charge transaction", async () => {
        const chargeTransactions = await ChargeTransaction.find({ owner: owner.id });
        expect(chargeTransactions.length).toBe(0);
      });

      it("should emit a 'onPlanUpdated' event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.plan.planUpgrade.eventId,
          expect.objectContaining({ email: owner.email }),
          {
            from: "free",
            fromRecurrence: "monthly",
            to: "paid_mid",
            toRecurrence: "monthly"
          }
        );
      });
    });

    describe("when user has passed payment method that is Apple Pay and had used a free trial in the past", () => {
      let owner: UserDocument;
      let response: request.Response;

      const EXPIRATION_DATE_SECONDS = **********;
      const STRIPE_PAYMENT_METHOD_ID = faker.string.uuid();
      const STRIPE_SUBSCRIPTION_PAYMENT_INTENT_ID = faker.string.uuid();
      const STRIPE_SUBSCRIPTION_PAYMENT_INTENT_CLIENT_SECRET = faker.string.uuid();

      beforeAll(async () => {
        jest.spyOn(StripeService.Instance, "retrievePaymentMethod").mockResolvedValue({
          id: STRIPE_PAYMENT_METHOD_ID,
          type: "card",
          card: {
            last4: "4242",
            brand: "visa",
            wallet: {
              type: "apple_pay"
            }
          }
        } as any);

        jest.spyOn(StripeService.Instance, "createSubscription").mockResolvedValue({
          current_period_end: EXPIRATION_DATE_SECONDS,
          latest_invoice: {
            payment_intent: {
              id: STRIPE_SUBSCRIPTION_PAYMENT_INTENT_ID,
              client_secret: STRIPE_SUBSCRIPTION_PAYMENT_INTENT_CLIENT_SECRET,
              status: "processing"
            }
          }
        } as any);

        owner = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          providers: {
            stripe: {
              id: faker.string.uuid()
            }
          }
        });
        await buildSubscription({
          owner: owner.id,
          price: "free_monthly",
          hasUsedFreeTrial: true
        });
        await buildPortfolio({ owner: owner.id });

        response = await request(app)
          .post("/api/m2m/subscriptions/initiate-stripe")
          .send({
            paymentMethod: STRIPE_PAYMENT_METHOD_ID,
            price: "paid_mid_monthly"
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should create a new payment method for this card", async () => {
        const paymentMethods = await PaymentMethod.find({ owner: owner.id });
        expect(paymentMethods.length).toBe(1);
        expect(paymentMethods[0].toObject()).toMatchObject(
          expect.objectContaining({
            owner: owner._id,
            type: "card",
            brand: "visa",
            lastFourDigits: "4242",
            wallet: "apple_pay",
            providers: {
              stripe: {
                id: STRIPE_PAYMENT_METHOD_ID
              }
            }
          })
        );
      });

      it("should return 200 with a client secret and payment intent ID", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            clientSecret: STRIPE_SUBSCRIPTION_PAYMENT_INTENT_CLIENT_SECRET,
            paymentIntentId: STRIPE_SUBSCRIPTION_PAYMENT_INTENT_ID
          })
        );
      });

      it("should create a new Stripe subscription without a free trial", async () => {
        expect(StripeService.Instance.createSubscription).toHaveBeenCalledWith(
          owner.providers.stripe.id,
          StripePricesEnum.STRIPE_TEST_PAID_MID_MONTHLY_GBP,
          STRIPE_PAYMENT_METHOD_ID,
          { isApplePay: true, withFreeTrial: false }
        );
      });

      it("should NOT create a new subscription", async () => {
        const subscriptions = await Subscription.find();
        expect(subscriptions.length).toBe(1);
      });

      it("should create a new charge transaction", async () => {
        const paymentMethod = await PaymentMethod.findOne({ owner: owner.id });

        const chargeTransaction = await ChargeTransaction.findOne({ owner: owner.id });
        expect(chargeTransaction).toEqual(
          expect.objectContaining({
            chargeType: "subscription",
            chargeMethod: "card",
            paymentMethod: paymentMethod._id,
            providers: expect.objectContaining({
              stripe: {
                id: STRIPE_SUBSCRIPTION_PAYMENT_INTENT_ID,
                status: "processing"
              }
            })
          })
        );
      });
    });

    describe("when user has passed payment method that is Google Pay and had used a free trial in the past", () => {
      let owner: UserDocument;
      let response: request.Response;

      const EXPIRATION_DATE_SECONDS = **********;
      const STRIPE_PAYMENT_METHOD_ID = faker.string.uuid();
      const STRIPE_SUBSCRIPTION_PAYMENT_INTENT_ID = faker.string.uuid();
      const STRIPE_SUBSCRIPTION_PAYMENT_INTENT_CLIENT_SECRET = faker.string.uuid();

      beforeAll(async () => {
        jest.spyOn(StripeService.Instance, "retrievePaymentMethod").mockResolvedValue({
          id: STRIPE_PAYMENT_METHOD_ID,
          type: "card",
          card: {
            last4: "4242",
            brand: "visa",
            wallet: {
              type: "google_pay"
            }
          }
        } as any);

        jest.spyOn(StripeService.Instance, "createSubscription").mockResolvedValue({
          current_period_end: EXPIRATION_DATE_SECONDS,
          latest_invoice: {
            payment_intent: {
              id: STRIPE_SUBSCRIPTION_PAYMENT_INTENT_ID,
              client_secret: STRIPE_SUBSCRIPTION_PAYMENT_INTENT_CLIENT_SECRET,
              status: "processing"
            }
          }
        } as any);

        owner = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          providers: {
            stripe: {
              id: faker.string.uuid()
            }
          }
        });
        await buildSubscription({
          owner: owner.id,
          price: "free_monthly",
          hasUsedFreeTrial: true
        });
        await buildPortfolio({ owner: owner.id });

        response = await request(app)
          .post("/api/m2m/subscriptions/initiate-stripe")
          .send({
            paymentMethod: STRIPE_PAYMENT_METHOD_ID,
            price: "paid_mid_monthly"
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should create a new payment method for this card", async () => {
        const paymentMethods = await PaymentMethod.find({ owner: owner.id });
        expect(paymentMethods.length).toBe(1);
        expect(paymentMethods[0].toObject()).toMatchObject(
          expect.objectContaining({
            owner: owner._id,
            type: "card",
            brand: "visa",
            lastFourDigits: "4242",
            wallet: "google_pay",
            providers: {
              stripe: {
                id: STRIPE_PAYMENT_METHOD_ID
              }
            }
          })
        );
      });

      it("should return 200 with a client secret and payment intent ID", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            clientSecret: STRIPE_SUBSCRIPTION_PAYMENT_INTENT_CLIENT_SECRET,
            paymentIntentId: STRIPE_SUBSCRIPTION_PAYMENT_INTENT_ID
          })
        );
      });

      it("should create a new Stripe subscription without a free trial", async () => {
        expect(StripeService.Instance.createSubscription).toHaveBeenCalledWith(
          owner.providers.stripe.id,
          StripePricesEnum.STRIPE_TEST_PAID_MID_MONTHLY_GBP,
          STRIPE_PAYMENT_METHOD_ID,
          { isApplePay: false, withFreeTrial: false }
        );
      });

      it("should NOT create a new subscription", async () => {
        const subscriptions = await Subscription.find();
        expect(subscriptions.length).toBe(1);
      });

      it("should create a new charge transaction", async () => {
        const paymentMethod = await PaymentMethod.findOne({ owner: owner.id });

        const chargeTransaction = await ChargeTransaction.findOne({ owner: owner.id });
        expect(chargeTransaction).toEqual(
          expect.objectContaining({
            chargeType: "subscription",
            chargeMethod: "card",
            paymentMethod: paymentMethod._id,
            providers: expect.objectContaining({
              stripe: {
                id: STRIPE_SUBSCRIPTION_PAYMENT_INTENT_ID,
                status: "processing"
              }
            })
          })
        );
      });
    });
  });

  describe("POST /subscriptions/complete-stripe", () => {
    describe("when client has not passed payment intent", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });

        await buildPaymentMethod({ owner: owner.id });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post("/api/m2m/subscriptions/complete-stripe")
          .send({})
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "Payment intent ID has not been passed!"
            }
          })
        );
      });
    });

    describe("when user does not have Stripe customer ID", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      });
      afterAll(async () => await clearDb());

      it("should return 500", async () => {
        const response = await request(app)
          .post("/api/m2m/subscriptions/complete-stripe")
          .send({
            paymentIntentId: faker.string.uuid()
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(500);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "An error occurred",
              message: "User does not have Stripe customer ID!"
            }
          })
        );
      });
    });

    describe("when user has passed a payment intent that does not belong to a charge", () => {
      let owner: UserDocument;

      const REQUEST_PAYMENT_INTENT = faker.string.uuid();

      beforeAll(async () => {
        jest.spyOn(StripeService.Instance, "retrievePaymentIntent").mockResolvedValue({
          id: REQUEST_PAYMENT_INTENT,
          status: "succeeded"
        } as any);

        owner = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          providers: {
            stripe: {
              id: faker.string.uuid()
            }
          }
        });
        const subscription = await buildSubscription({ owner: owner.id });

        await buildChargeTransaction({
          owner: owner.id,
          subscription: subscription.id,
          providers: {
            stripe: {
              id: faker.string.uuid(),
              status: "processing"
            }
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should return 500", async () => {
        const response = await request(app)
          .post("/api/m2m/subscriptions/complete-stripe")
          .send({
            paymentIntentId: REQUEST_PAYMENT_INTENT
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(500);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "An error occurred",
              message: `Stripe payment intent ID ${REQUEST_PAYMENT_INTENT} did not match any charge transaction`
            }
          })
        );
      });
    });

    describe("when user has passed a payment intent that belongs to a charge", () => {
      let owner: UserDocument;
      let subscription: SubscriptionDocument;
      let paymentMethod: PaymentMethodDocument;
      let response: request.Response;

      const REQUEST_PAYMENT_INTENT = faker.string.uuid();
      const STRIPE_SUBSCRIPTION_ID = faker.string.uuid();
      const STRIPE_SUBSCRIPTION_STATUS = "active";

      beforeAll(async () => {
        jest.spyOn(StripeService.Instance, "retrievePaymentIntent").mockResolvedValue({
          id: REQUEST_PAYMENT_INTENT,
          status: "succeeded",
          invoice: {
            subscription: {
              current_period_end: **********,
              id: STRIPE_SUBSCRIPTION_ID,
              status: STRIPE_SUBSCRIPTION_STATUS
            }
          }
        } as any);

        owner = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          providers: {
            stripe: {
              id: faker.string.uuid()
            }
          }
        });
        paymentMethod = await buildPaymentMethod();
        subscription = await buildSubscription({ owner: owner.id });

        await buildChargeTransaction({
          owner: owner.id,
          subscription: subscription.id,
          price: "paid_low_monthly",
          paymentMethod: paymentMethod.id,
          providers: {
            stripe: {
              id: REQUEST_PAYMENT_INTENT,
              status: "requires_action"
            }
          }
        });

        response = await request(app)
          .post("/api/m2m/subscriptions/complete-stripe")
          .send({
            paymentIntentId: REQUEST_PAYMENT_INTENT
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 and return the active subscription", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            _id: subscription.id
          })
        );
      });

      it("should move the user to the paid plan", async () => {
        const updatedSubscription = await Subscription.findOne({ owner: owner.id });
        expect(updatedSubscription).toEqual(
          expect.objectContaining({
            category: "CardPaymentSubscription",
            price: "paid_low_monthly",
            paymentMethod: paymentMethod._id,
            nextChargeAt: new Date("2024-01-30T14:58:02.000Z"),
            providers: expect.objectContaining({
              stripe: {
                id: STRIPE_SUBSCRIPTION_ID,
                status: STRIPE_SUBSCRIPTION_STATUS
              }
            })
          })
        );
      });

      it("should emit a 'onPlanUpdated' event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.plan.planUpgrade.eventId,
          expect.objectContaining({ email: owner.email }),
          {
            from: "free",
            fromRecurrence: "monthly",
            to: "paid_low",
            toRecurrence: "monthly"
          }
        );
      });
    });
  });
});
