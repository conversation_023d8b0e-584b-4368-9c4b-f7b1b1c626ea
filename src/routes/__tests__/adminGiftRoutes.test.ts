import request from "supertest";
import app from "../../app";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildGift, buildUser } from "../../tests/utils/generateModels";
import { Gift, GiftDocument } from "../../models/Gift";

const isArraySortedDesc = (arr: any[]) => arr.slice(1).every((item, i) => arr[i] >= item); //desc order

describe("AdminGiftRoutes", () => {
  beforeAll(async () => await connectDb("AdminGiftRoutes"));
  afterAll(async () => await closeDb());

  describe("GET /gifts", () => {
    let firstGift: GiftDocument;
    let secondGift: GiftDocument;
    let response: request.Response;

    afterAll(async () => await clearDb());

    it("should return status 400 when 'page' query param is invalid", async () => {
      const user = await buildUser();
      const response = await request(app)
        .get("/api/admin/m2m/gifts?page=esa&@&pageSize=50")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for param 'page' , should be numeric"
          }
        })
      );
    });

    it("should return status 400 when 'pageSize' query param is invalid", async () => {
      const user = await buildUser();
      const response = await request(app)
        .get("/api/admin/m2m/gifts?page=1&pageSize=*@D")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for param 'pageSize' , should be numeric"
          }
        })
      );
    });

    it("(should return status 400 when 'pageSize' query param is missing while 'page' query param is valid", async () => {
      const user = await buildUser();
      const response = await request(app)
        .get("/api/admin/m2m/gifts?page=1")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Param 'pageSize' is required"
          }
        })
      );
    });

    it("should return status 200 with a list of all gifts sorted by date in descending order", async () => {
      firstGift = await buildGift();
      secondGift = await buildGift();

      response = await request(app).get("/api/admin/m2m/gifts").set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const data = JSON.parse(response.text).data as GiftDocument[];

      expect(data.length).toBe(2);
      expect(isArraySortedDesc(data.map((gift) => new Date(gift.createdAt)))).toEqual(true);
      expect(data[0]._id.toString()).toEqual(secondGift._id.toString());
      expect(data[1]._id.toString()).toEqual(firstGift._id.toString());
    });

    it("should return status 200 with a list of all gifts by user with targetUserEmail", async () => {
      firstGift = await buildGift();
      secondGift = await buildGift();

      response = await request(app)
        .get(`/api/admin/m2m/gifts?targetUserEmail=${firstGift.targetUserEmail}`)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data as GiftDocument[]).toMatchObject([
        expect.objectContaining({
          _id: firstGift.id
        })
      ]);
    });

    it("should return status 200 with paginated response containing gift array when pagination query params are both valid", async () => {
      await Promise.all([buildGift(), buildGift()]);

      const response = await request(app)
        .get("/api/admin/m2m/gifts?pageSize=1&page=2&populatePortfolio=true&sort=-createdAt")
        .set("Accept", "application/json");

      const expectedData = await Gift.find({}).sort({ createdAt: -1 }).skip(1).limit(1).populate("gifter");

      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).gifts).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });
  });

  describe("GET /gifts/:id", () => {
    let gift: GiftDocument;
    let response: request.Response;

    beforeAll(async () => {
      gift = await buildGift();

      response = await request(app).get(`/api/admin/m2m/gifts/${gift.id}`).set("Accept", "application/json");
    });
    afterAll(async () => await clearDb());

    it("should return the gift document for the corresponding id", () => {
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          _id: gift.id
        })
      );
    });
  });
});
