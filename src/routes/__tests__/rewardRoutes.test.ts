import request from "supertest";
import { faker } from "@faker-js/faker";
import app from "../../app";
import { Reward, RewardDocument } from "../../models/Reward";
import { KycStatusEnum, UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildReward, buildUser, buildAddress, buildInvestmentProduct } from "../../tests/utils/generateModels";
import { ProviderEnum } from "../../configs/providersConfig";
import CloudflareService from "../../external-services/cloudflareService";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";

const { ASSET_CONFIG } = investmentUniverseConfig;

describe("RewardRoutes", () => {
  beforeAll(async () => await connectDb("RewardRoutes"));
  afterAll(async () => await closeDb());

  describe("GET /rewards", () => {
    describe("and the targetUser is different than the user making the request", () => {
      let response: request.Response;

      beforeAll(async () => {
        const differentUser = await buildUser();
        const referrer = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        const referral = await buildUser({ kycStatus: KycStatusEnum.PENDING });
        await buildReward({
          referrer: referrer.id,
          referral: referral.id,
          targetUser: referrer.id
        });

        response = await request(app)
          .get("/api/m2m/rewards")
          .set("external-user-id", differentUser._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return an empty array", () => {
        expect(response.status).toBe(200);
        const data = JSON.parse(response.text).data;
        expect(data.length).toBe(0);
      });
    });

    describe("and the targetUser is the user making the request", () => {
      let referrer: UserDocument;
      let settledRestrictedReward: RewardDocument;

      beforeAll(async () => {
        referrer = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        const referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });

        // 1. reward with status settled & hasViewedAppModal false & restricted
        settledRestrictedReward = await buildReward({
          referrer: referrer.id,
          referral: referral.id,
          targetUser: referrer.id,
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched" } }
          },
          unrestrictedAt: new Date(+new Date() + 1 * 24 * 60 * 60 * 1000),
          status: "Settled"
        });
        expect(settledRestrictedReward.hasViewedAppModal).toBe(false);
        expect(settledRestrictedReward.status).toBe("Settled");

        // 2. reward with status pending & hasViewedAppModal false
        const pendingReward = await buildReward({
          referrer: referrer.id,
          referral: referral.id,
          targetUser: referrer.id,
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          }
        });
        expect(settledRestrictedReward.hasViewedAppModal).toBe(false);
        expect(pendingReward.status).toBe("Pending");

        // 3. reward with status settled & hasViewedAppModal true & unrestricted
        const settledViewedUnrestrictedReward = await buildReward({
          referrer: referrer.id,
          referral: referral.id,
          targetUser: referrer.id,
          status: "Settled",
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched" } }
          },
          unrestrictedAt: new Date(+new Date() - 1 * 24 * 60 * 60 * 1000)
        });
        settledViewedUnrestrictedReward.hasViewedAppModal = true;
        await settledViewedUnrestrictedReward.save();
        expect(settledViewedUnrestrictedReward.status).toBe("Settled");

        // => 3 reward documents in total
        const allRewards = await Reward.find();
        expect(allRewards.length).toBe(3);
      });
      afterAll(async () => await clearDb());

      it("should return all reward documents for request without params", async () => {
        const response = await request(app)
          .get("/api/m2m/rewards")
          .set("external-user-id", referrer._id)
          .set("Accept", "application/json");

        expect(response.status).toBe(200);
        const data = JSON.parse(response.text).data;
        expect(data.length).toBe(3);
      });

      it("should return only Settled rewards for request /rewards?status=Settled", async () => {
        const response = await request(app)
          .get("/api/m2m/rewards?status=Settled")
          .set("external-user-id", referrer._id)
          .set("Accept", "application/json");

        expect(response.status).toBe(200);
        const data = JSON.parse(response.text).data;
        expect(data.length).toBe(2);
        data.forEach((reward: RewardDocument) => {
          expect(reward.status).toBe("Settled");
        });
      });

      it("should return only Pending rewards for request /rewards?status=Pending", async () => {
        const response = await request(app)
          .get("/api/m2m/rewards?status=Pending")
          .set("external-user-id", referrer._id)
          .set("Accept", "application/json");

        expect(response.status).toBe(200);
        const data = JSON.parse(response.text).data;
        expect(data.length).toBe(1);
        expect(data[0].status).toBe("Pending");
      });

      it("should return 400 if status param is invalid", async () => {
        const response = await request(app)
          .get("/api/m2m/rewards?status=asflkjsdf")
          .set("external-user-id", referrer._id)
          .set("Accept", "application/json");

        expect(response.status).toBe(400);
      });

      it("should return all reward documents that have not been viewed for request /rewards?hasViewedAppModal=false", async () => {
        const response = await request(app)
          .get("/api/m2m/rewards?hasViewedAppModal=false")
          .set("external-user-id", referrer._id)
          .set("Accept", "application/json");

        expect(response.status).toBe(200);
        const data = JSON.parse(response.text).data;
        expect(data.length).toBe(2);
        data.forEach((reward: RewardDocument) => {
          expect(reward.hasViewedAppModal).toBe(false);
        });
      });

      it("should return all reward documents that have been viewed for request /rewards?hasViewedAppModal=true", async () => {
        const response = await request(app)
          .get("/api/m2m/rewards?hasViewedAppModal=true")
          .set("external-user-id", referrer._id)
          .set("Accept", "application/json");

        expect(response.status).toBe(200);
        const data = JSON.parse(response.text).data;
        expect(data.length).toBe(1);
        expect(data[0].hasViewedAppModal).toBe(true);
      });

      it("should return all reward documents that are Settled and that have not been viewed for request /rewards?status=Settled&hasViewedAppModal=false", async () => {
        const response = await request(app)
          .get("/api/m2m/rewards?status=Settled&hasViewedAppModal=false")
          .set("external-user-id", referrer._id)
          .set("Accept", "application/json");

        expect(response.status).toBe(200);
        const data = JSON.parse(response.text).data;
        expect(data.length).toBe(1);
        expect(data[0]).toMatchObject(
          expect.objectContaining({
            status: "Settled",
            hasViewedAppModal: false
          })
        );
      });

      it("should return all reward documents that are Settled and that are still restricted for request /rewards?status=Settled&restrictedOnly=true", async () => {
        const response = await request(app)
          .get("/api/m2m/rewards?status=Settled&restrictedOnly=true")
          .set("external-user-id", referrer._id)
          .set("Accept", "application/json");

        expect(response.status).toBe(200);
        const data = JSON.parse(response.text).data;
        expect(data.length).toBe(1);
        expect(data[0].id.toString()).toEqual(settledRestrictedReward.id.toString());
      });
    });
  });

  describe("POST /rewards/:id", () => {
    let referrer: UserDocument;
    let reward: RewardDocument;

    beforeAll(async () => {
      referrer = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      const referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      reward = await buildReward({
        referrer: referrer.id,
        referral: referral.id,
        targetUser: referrer.id,
        status: "Settled",
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched" } }
        }
      });
      expect(reward.hasViewedAppModal).toBe(false);
    });
    afterAll(async () => await clearDb());

    it("should return 400 if the request body is empty", async () => {
      const response = await request(app)
        .post(`/api/m2m/rewards/${reward.id}`)
        .send({})
        .set("external-user-id", referrer._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(400);
    });

    it("should return 204 and successfully update the reward document for valid body with hasViewedAppModal field", async () => {
      const response = await request(app)
        .post(`/api/m2m/rewards/${reward.id}`)
        .send({ hasViewedAppModal: true })
        .set("external-user-id", referrer._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(204);

      const updatedReward = (await Reward.findOne({ _id: reward.id })) as RewardDocument;
      expect(updatedReward.hasViewedAppModal).toBe(true);
    });

    it("should return 204 and successfully update the reward document for valid body with accepted field", async () => {
      const response = await request(app)
        .post(`/api/m2m/rewards/${reward.id}`)
        .send({ accepted: true })
        .set("external-user-id", referrer._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(204);

      const updatedReward = (await Reward.findOne({ _id: reward.id })) as RewardDocument;
      expect(updatedReward.accepted).toBe(true);
    });
  });

  describe("POST /rewards/:id/trade-confirmations/generate", () => {
    let response: request.Response;

    const CLOUDFLARE_LINK = "https://cloudflare.link";

    beforeAll(async () => {
      jest.spyOn(CloudflareService.Instance, "uploadObject").mockResolvedValue({ fileUri: CLOUDFLARE_LINK });

      await buildInvestmentProduct(true, { assetId: "equities_apple" });

      const user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      const referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildAddress({ owner: user.id });

      const reward = await buildReward({
        referrer: user.id,
        referral: referral.id,
        targetUser: user.id,
        asset: "equities_apple",
        isin: ASSET_CONFIG["equities_apple"].isin,
        status: "Settled",
        deposit: { activeProviders: [ProviderEnum.WEALTHKERNEL] },
        order: { activeProviders: [ProviderEnum.WEALTHKERNEL] },
        unitPrice: {
          currency: "GBP",
          amount: 100
        }
      });

      response = await request(app)
        .post(`/api/m2m/rewards/${reward.id}/trade-confirmations/generate`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
    });

    afterAll(async () => {
      jest.restoreAllMocks();
      await clearDb();
    });

    it("should return 200 with fileUri", () => {
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text)).toMatchObject({
        fileUri: CLOUDFLARE_LINK
      });
    });
  });
});
