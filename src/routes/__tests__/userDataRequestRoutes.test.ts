import request from "supertest";
import app from "../../app";
import { KycStatusEnum, UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildNotificationSettings,
  buildPortfolio,
  buildUser,
  buildUserDataRequest
} from "../../tests/utils/generateModels";
import { UserDataRequest } from "../../models/UserDataRequest";
import { PortfolioModeEnum } from "../../models/Portfolio";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";

describe("UserDataRequestRoutes", () => {
  beforeAll(async () => await connectDb("UserDataRequestRoutes"));
  afterAll(async () => await closeDb());

  describe("GET /user-data-requests", () => {
    describe("and the owner is different than the user making the request", () => {
      let response: request.Response;

      beforeAll(async () => {
        const user = await buildUser();
        const differentUser = await buildUser();
        await buildUserDataRequest({
          requestType: "disassociation",
          owner: differentUser.id
        });

        response = await request(app)
          .get("/api/m2m/user-data-requests")
          .set("external-user-id", user._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return an empty array", () => {
        expect(response.status).toBe(200);
        const data = JSON.parse(response.text).data;
        expect(data.length).toBe(0);
      });
    });

    describe("and the owner is different than the user making the request", () => {
      let response: request.Response;
      let user: UserDocument;

      beforeAll(async () => {
        user = await buildUser();
        await buildUserDataRequest({
          requestType: "disassociation",
          owner: user.id
        });

        response = await request(app)
          .get("/api/m2m/user-data-requests")
          .set("external-user-id", user._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return the user's data request", () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({
          data: [
            expect.objectContaining({
              owner: user.id,
              requestType: "disassociation",
              status: "Created"
            })
          ]
        });
      });
    });
  });

  describe("POST /user-data-requests", () => {
    describe("when request body is empty", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post("/api/m2m/user-data-requests")
          .send({})
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "Missing field 'requestType'"
            }
          })
        );
      });
    });

    describe("when user already has a user data request", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        await buildUserDataRequest({
          requestType: "disassociation",
          owner: owner.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post("/api/m2m/user-data-requests")
          .send({ requestType: "disassociation" })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "User already has a user data request"
            }
          })
        );
      });
    });

    describe("when request body is valid", () => {
      let owner: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });

        await Promise.all([
          buildPortfolio({
            owner: owner.id,
            cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
            holdings: [],
            mode: PortfolioModeEnum.REAL
          }),
          buildNotificationSettings({ owner: owner.id })
        ]);

        response = await request(app)
          .post("/api/m2m/user-data-requests")
          .send({ requestType: "disassociation" })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 204 and successfully create a disassociation request for the user", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            owner: owner.id,
            requestType: "disassociation",
            status: "Created"
          })
        );

        const userDataRequest = await UserDataRequest.findOne({ owner: owner.id });
        expect(userDataRequest).toEqual(
          expect.objectContaining({
            owner: owner._id,
            requestType: "disassociation",
            reason: "user-request"
          })
        );
      });

      it("should emit event for disassociation", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.disassociation.eventId,
          expect.objectContaining({ email: owner.email }),
          { reason: "user-request" }
        );
      });
    });
  });
});
