import request from "supertest";
import app from "../../app";
import { UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildAssetNews, buildInvestmentProduct, buildUser } from "../../tests/utils/generateModels";
import { faker } from "@faker-js/faker";
import DateUtil from "../../utils/dateUtil";
import { InvestmentProductDocument } from "../../models/InvestmentProduct";
import { AssetNewsDocument } from "../../models/AssetNews";

describe("AssetNewsRoutes", () => {
  beforeAll(async () => await connectDb("AssetNewsRoutes"));
  afterAll(async () => await closeDb());

  describe("GET /asset-news", () => {
    const TODAY = new Date("2023-12-07T09:00:00Z");

    describe("when assetId param is invalid", () => {
      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.resetAllMocks();
        await clearDb();
        Date.now = jest.fn(() => TODAY.valueOf());
        user = await buildUser();

        response = await request(app)
          .get("/api/m2m/asset-news?assetId=garbageId")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      it("should return status 400 with proper message for invalid assetId param", async () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Invalid parameter",
              message: "garbageId is not a valid asset"
            }
          })
        );
      });
    });

    describe("when there are no news for the asset", () => {
      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.resetAllMocks();
        await clearDb();
        Date.now = jest.fn(() => TODAY.valueOf());
        user = await buildUser();
        await buildInvestmentProduct(false, {
          assetId: "equities_amd"
        });

        response = await request(app)
          .get("/api/m2m/asset-news?assetId=equities_amd")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      it("should return status 200 with an empty array", async () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toEqual([]);
      });
    });

    describe("when there is one news item for the asset that is latest ", () => {
      let user: UserDocument;
      let response: request.Response;
      let investmentProduct: InvestmentProductDocument;
      let assetNewsDocument: AssetNewsDocument;
      const EXTERNAL_SERVICE_NEWS_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.resetAllMocks();
        await clearDb();
        Date.now = jest.fn(() => TODAY.valueOf());
        user = await buildUser();
        investmentProduct = await buildInvestmentProduct(false, {
          assetId: "equities_amd"
        });

        assetNewsDocument = await buildAssetNews({
          investmentProducts: [investmentProduct._id],
          date: DateUtil.getDateOfHoursAgo(TODAY, 4),
          providers: {
            stockNews: {
              id: EXTERNAL_SERVICE_NEWS_ID
            }
          }
        });

        response = await request(app)
          .get("/api/m2m/asset-news?assetId=equities_amd")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      it("should return status 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should return only one section", async () => {
        expect(JSON.parse(response.text)).toHaveLength(1);
      });

      it("should return the section named Latest with the news item inside", async () => {
        const firstSection = JSON.parse(response.text)[0];
        expect(firstSection.data).toHaveLength(1);
        expect(firstSection).toMatchObject({
          sectionTitle: "Latest",
          data: expect.arrayContaining([
            expect.objectContaining({
              investmentProducts: expect.arrayContaining([investmentProduct._id.toString()]),
              _id: assetNewsDocument._id.toString(),
              newsUrl: assetNewsDocument.newsUrl,
              imageUrl: assetNewsDocument.imageUrl,
              title: assetNewsDocument.title,
              text: assetNewsDocument.text,
              source: assetNewsDocument.source,
              topics: assetNewsDocument.topics,
              date: expect.any(String),
              sentiment: assetNewsDocument.sentiment,
              type: assetNewsDocument.type,
              displayDate: "4h ago"
            })
          ])
        });
      });
    });

    describe("when there are 2 news item for the asset that is latest ", () => {
      let user: UserDocument;
      let response: request.Response;
      let investmentProduct: InvestmentProductDocument;
      let assetNewsDocument1: AssetNewsDocument;
      let assetNewsDocument2: AssetNewsDocument;
      const EXTERNAL_SERVICE_NEWS_ID1 = faker.string.uuid();
      const EXTERNAL_SERVICE_NEWS_ID2 = faker.string.uuid();

      beforeAll(async () => {
        jest.resetAllMocks();
        await clearDb();
        Date.now = jest.fn(() => TODAY.valueOf());
        user = await buildUser();
        investmentProduct = await buildInvestmentProduct(false, {
          assetId: "equities_amd"
        });

        [assetNewsDocument1, assetNewsDocument2] = await Promise.all([
          buildAssetNews({
            investmentProducts: [investmentProduct._id],
            date: DateUtil.getDateOfHoursAgo(TODAY, 4),
            providers: {
              stockNews: {
                id: EXTERNAL_SERVICE_NEWS_ID1
              }
            }
          }),
          buildAssetNews({
            investmentProducts: [investmentProduct._id],
            date: DateUtil.getDateOfHoursAgo(TODAY, 2),
            providers: {
              stockNews: {
                id: EXTERNAL_SERVICE_NEWS_ID2
              }
            }
          })
        ]);

        response = await request(app)
          .get("/api/m2m/asset-news?assetId=equities_amd")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      it("should return status 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should return only one section", async () => {
        expect(JSON.parse(response.text)).toHaveLength(1);
      });

      it("should return the section named Latest with the 2 news item inside order by newest to oldest", async () => {
        const firstSection = JSON.parse(response.text)[0];
        expect(firstSection.data).toHaveLength(2);
        expect(firstSection.data[0]._id).toEqual(assetNewsDocument2._id.toString());
        expect(firstSection).toMatchObject({
          sectionTitle: "Latest",
          data: expect.arrayContaining([
            expect.objectContaining({
              investmentProducts: expect.arrayContaining([investmentProduct._id.toString()]),
              _id: assetNewsDocument2._id.toString(),
              newsUrl: assetNewsDocument2.newsUrl,
              imageUrl: assetNewsDocument2.imageUrl,
              title: assetNewsDocument2.title,
              text: assetNewsDocument2.text,
              source: assetNewsDocument2.source,
              topics: assetNewsDocument2.topics,
              date: expect.any(String),
              sentiment: assetNewsDocument2.sentiment,
              type: assetNewsDocument2.type,
              displayDate: "2h ago"
            }),
            expect.objectContaining({
              investmentProducts: expect.arrayContaining([investmentProduct._id.toString()]),
              _id: assetNewsDocument1._id.toString(),
              newsUrl: assetNewsDocument1.newsUrl,
              imageUrl: assetNewsDocument1.imageUrl,
              title: assetNewsDocument1.title,
              text: assetNewsDocument1.text,
              source: assetNewsDocument1.source,
              topics: assetNewsDocument1.topics,
              date: expect.any(String),
              sentiment: assetNewsDocument1.sentiment,
              type: assetNewsDocument1.type,
              displayDate: "4h ago"
            })
          ])
        });
      });
    });

    describe("when there is one news item for the asset that is not latest", () => {
      let user: UserDocument;
      let response: request.Response;
      let investmentProduct: InvestmentProductDocument;
      let assetNewsDocument: AssetNewsDocument;
      const EXTERNAL_SERVICE_NEWS_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.resetAllMocks();
        await clearDb();
        Date.now = jest.fn(() => TODAY.valueOf());
        user = await buildUser();
        investmentProduct = await buildInvestmentProduct(false, {
          assetId: "equities_amd"
        });

        assetNewsDocument = await buildAssetNews({
          investmentProducts: [investmentProduct._id],
          date: DateUtil.getDateOfMonthsAgo(TODAY, 2),
          providers: {
            stockNews: {
              id: EXTERNAL_SERVICE_NEWS_ID
            }
          }
        });

        response = await request(app)
          .get("/api/m2m/asset-news?assetId=equities_amd")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      it("should return status 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should return only one section", async () => {
        expect(JSON.parse(response.text)).toHaveLength(1);
      });

      it("should return the section named based of the date of the news item with the news item inside", async () => {
        const firstSection = JSON.parse(response.text)[0];

        expect(firstSection.data).toHaveLength(1);
        expect(firstSection).toMatchObject({
          sectionTitle: "Oct 2023",
          data: expect.arrayContaining([
            expect.objectContaining({
              investmentProducts: expect.arrayContaining([investmentProduct._id.toString()]),
              _id: assetNewsDocument._id.toString(),
              newsUrl: assetNewsDocument.newsUrl,
              imageUrl: assetNewsDocument.imageUrl,
              title: assetNewsDocument.title,
              text: assetNewsDocument.text,
              source: assetNewsDocument.source,
              topics: assetNewsDocument.topics,
              date: expect.any(String),
              sentiment: assetNewsDocument.sentiment,
              type: assetNewsDocument.type,
              displayDate: "07 Oct 2023, 09:00"
            })
          ])
        });
      });
    });

    describe("when there are multiple news item published in different months for the asset", () => {
      let user: UserDocument;
      let response: request.Response;
      let investmentProduct: InvestmentProductDocument;
      let assetNewsToday: AssetNewsDocument;
      let assetNews10DaysAgo: AssetNewsDocument;
      let assetNews2MonthsAgo: AssetNewsDocument;
      const EXTERNAL_SERVICE_NEWS_ID1 = faker.string.uuid();
      const EXTERNAL_SERVICE_NEWS_ID2 = faker.string.uuid();
      const EXTERNAL_SERVICE_NEWS_ID3 = faker.string.uuid();

      beforeAll(async () => {
        jest.resetAllMocks();
        await clearDb();
        Date.now = jest.fn(() => TODAY.valueOf());
        user = await buildUser();
        investmentProduct = await buildInvestmentProduct(false, {
          assetId: "equities_amd"
        });

        [assetNewsToday, assetNews10DaysAgo, assetNews2MonthsAgo] = await Promise.all([
          buildAssetNews({
            investmentProducts: [investmentProduct._id],
            date: DateUtil.getDateOfMinutesAgo(20),
            providers: {
              stockNews: {
                id: EXTERNAL_SERVICE_NEWS_ID1
              }
            }
          }),
          buildAssetNews({
            investmentProducts: [investmentProduct._id],
            date: DateUtil.getDateOfDaysAgo(TODAY, 10),
            providers: {
              stockNews: {
                id: EXTERNAL_SERVICE_NEWS_ID2
              }
            }
          }),
          buildAssetNews({
            investmentProducts: [investmentProduct._id],
            date: DateUtil.getDateOfMonthsAgo(TODAY, 2),
            providers: {
              stockNews: {
                id: EXTERNAL_SERVICE_NEWS_ID3
              }
            }
          })
        ]);

        response = await request(app)
          .get("/api/m2m/asset-news?assetId=equities_amd")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      it("should return status 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should return 3 sections", async () => {
        expect(JSON.parse(response.text)).toHaveLength(3);
      });

      it("should return the section ordered from newest to oldest with the asset news inside", async () => {
        const firstSection = JSON.parse(response.text)[0];
        const secondSection = JSON.parse(response.text)[1];
        const thirdSection = JSON.parse(response.text)[2];

        expect(firstSection.data).toHaveLength(1);
        expect(firstSection).toMatchObject({
          sectionTitle: "Latest",
          data: expect.arrayContaining([
            expect.objectContaining({
              investmentProducts: expect.arrayContaining([investmentProduct._id.toString()]),
              _id: assetNewsToday._id.toString(),
              newsUrl: assetNewsToday.newsUrl,
              imageUrl: assetNewsToday.imageUrl,
              title: assetNewsToday.title,
              text: assetNewsToday.text,
              source: assetNewsToday.source,
              topics: assetNewsToday.topics,
              date: expect.any(String),
              sentiment: assetNewsToday.sentiment,
              type: assetNewsToday.type,
              displayDate: "20m ago"
            })
          ])
        });

        expect(secondSection).toMatchObject({
          sectionTitle: "Nov 2023",
          data: expect.arrayContaining([
            expect.objectContaining({
              investmentProducts: expect.arrayContaining([investmentProduct._id.toString()]),
              _id: assetNews10DaysAgo._id.toString(),
              newsUrl: assetNews10DaysAgo.newsUrl,
              imageUrl: assetNews10DaysAgo.imageUrl,
              title: assetNews10DaysAgo.title,
              text: assetNews10DaysAgo.text,
              source: assetNews10DaysAgo.source,
              topics: assetNews10DaysAgo.topics,
              date: expect.any(String),
              sentiment: assetNews10DaysAgo.sentiment,
              type: assetNews10DaysAgo.type,
              displayDate: "27 Nov 2023, 09:00"
            })
          ])
        });

        expect(thirdSection).toMatchObject({
          sectionTitle: "Oct 2023",
          data: expect.arrayContaining([
            expect.objectContaining({
              investmentProducts: expect.arrayContaining([investmentProduct._id.toString()]),
              _id: assetNews2MonthsAgo._id.toString(),
              newsUrl: assetNews2MonthsAgo.newsUrl,
              imageUrl: assetNews2MonthsAgo.imageUrl,
              title: assetNews2MonthsAgo.title,
              text: assetNews2MonthsAgo.text,
              source: assetNews2MonthsAgo.source,
              topics: assetNews2MonthsAgo.topics,
              date: expect.any(String),
              sentiment: assetNews2MonthsAgo.sentiment,
              type: assetNews2MonthsAgo.type,
              displayDate: "07 Oct 2023, 09:00"
            })
          ])
        });
      });
    });

    describe("when there are 2 news item for the asset but a limit of 1 is applied", () => {
      let user: UserDocument;
      let response: request.Response;
      let investmentProduct: InvestmentProductDocument;
      let assetNewsDocument1: AssetNewsDocument;
      let assetNewsDocument2: AssetNewsDocument;
      const EXTERNAL_SERVICE_NEWS_ID1 = faker.string.uuid();
      const EXTERNAL_SERVICE_NEWS_ID2 = faker.string.uuid();

      beforeAll(async () => {
        jest.resetAllMocks();
        await clearDb();
        Date.now = jest.fn(() => TODAY.valueOf());
        user = await buildUser();
        investmentProduct = await buildInvestmentProduct(false, {
          assetId: "equities_amd"
        });

        [assetNewsDocument1, assetNewsDocument2] = await Promise.all([
          buildAssetNews({
            investmentProducts: [investmentProduct._id],
            date: DateUtil.getDateOfHoursAgo(TODAY, 6),
            providers: {
              stockNews: {
                id: EXTERNAL_SERVICE_NEWS_ID1
              }
            }
          }),
          buildAssetNews({
            investmentProducts: [investmentProduct._id],
            date: DateUtil.getDateOfHoursAgo(TODAY, 2),
            providers: {
              stockNews: {
                id: EXTERNAL_SERVICE_NEWS_ID2
              }
            }
          })
        ]);

        response = await request(app)
          .get("/api/m2m/asset-news?assetId=equities_amd&limit=1")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      it("should return status 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should return only one section", async () => {
        expect(JSON.parse(response.text)).toHaveLength(1);
      });

      it("should return only latest news item", async () => {
        const firstSection = JSON.parse(response.text)[0];

        expect(firstSection.data).toHaveLength(1);
        expect(firstSection).toMatchObject({
          sectionTitle: "Latest",
          data: expect.arrayContaining([
            expect.objectContaining({
              investmentProducts: expect.arrayContaining([investmentProduct._id.toString()]),
              _id: assetNewsDocument2._id.toString(),
              newsUrl: assetNewsDocument2.newsUrl,
              imageUrl: assetNewsDocument2.imageUrl,
              title: assetNewsDocument2.title,
              text: assetNewsDocument2.text,
              source: assetNewsDocument2.source,
              topics: assetNewsDocument2.topics,
              date: expect.any(String),
              sentiment: assetNewsDocument2.sentiment,
              type: assetNewsDocument2.type,
              displayDate: "2h ago"
            })
          ])
        });
      });
    });
  });
});
