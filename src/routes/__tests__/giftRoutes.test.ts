import request from "supertest";
import { faker } from "@faker-js/faker";
import app from "../../app";
import { KycStatusEnum, UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildGift, buildNotificationSettings, buildPortfolio, buildUser } from "../../tests/utils/generateModels";
import { Gift, GiftDocument } from "../../models/Gift";
import MailchimpService from "../../external-services/mailchimpService";
import DateUtil from "../../utils/dateUtil";

describe("GiftRoutes", () => {
  beforeAll(async () => await connectDb("GiftRoutes"));
  afterAll(async () => await closeDb());

  describe("POST /gifts", () => {
    let gifter: UserDocument;
    let targetUser: UserDocument;

    beforeAll(async () => {
      const TODAY = new Date("2022-08-31T11:00:00Z");
      Date.now = jest.fn(() => TODAY.valueOf());

      gifter = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        canSendGiftUntil: DateUtil.getDateAfterNdays(TODAY, 1)
      });
      targetUser = await buildUser({ kycStatus: KycStatusEnum.PASSED });

      await Promise.all([
        buildNotificationSettings({ owner: gifter.id }),
        buildNotificationSettings({ owner: targetUser.id })
      ]);

      jest.spyOn(MailchimpService, "updateMember").mockResolvedValue();

      await buildPortfolio({ owner: targetUser.id });
    });
    afterAll(async () => await clearDb());

    it("should return 400 if the request body is empty", async () => {
      const response = await request(app)
        .post("/api/m2m/gifts")
        .send({})
        .set("external-user-id", gifter._id)
        .set("Accept", "application/json");

      expect(response.status).toBe(400);
    });

    it("should return 400 if email is not valid", async () => {
      const response = await request(app)
        .post("/api/m2m/gifts")
        .send({
          targetUserEmail: "some-invalid-email",
          message: "Yay!"
        })
        .set("external-user-id", gifter._id)
        .set("Accept", "application/json");

      expect(response.status).toBe(400);
    });

    it("should return 400 if user tries to gift himself", async () => {
      const response = await request(app)
        .post("/api/m2m/gifts")
        .send({
          targetUserEmail: gifter.email,
          message: "Yay!"
        })
        .set("external-user-id", gifter._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(400);
    });

    it("should return 200 and the created gift with removed line breaks and lowercase target e-mail for valid body", async () => {
      const response = await request(app)
        .post("/api/m2m/gifts")
        .send({
          // We also put an empty space after the e-mail to ensure it's trimmed when stored
          targetUserEmail: `${targetUser.email.toUpperCase()} `,
          message: "Hello!\nI'd like to give this gift to you!\r\nThanks!!!"
        })
        .set("external-user-id", gifter._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(200);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          gifter: gifter.id,
          targetUserEmail: targetUser.email.toLowerCase(),
          message: "Hello! I'd like to give this gift to you! Thanks!!!",
          consideration: {
            amount: 2000,
            currency: "GBP"
          }
        })
      );
    });
  });

  describe("POST /gifts/:id", () => {
    let gifter: UserDocument;
    let gift: GiftDocument;

    beforeAll(async () => {
      gifter = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      const targetUser = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      gift = await buildGift({
        gifter: gifter.id,
        targetUserEmail: targetUser.email,
        deposit: { providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } } }
      });
      expect(gift.hasViewedAppModal).toBe(false);
    });
    afterAll(async () => await clearDb());

    it("should return 400 if the request body is empty", async () => {
      const response = await request(app)
        .post(`/api/m2m/gifts/${gift.id}`)
        .send({})
        .set("external-user-id", gifter._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(400);
    });

    it("should return 204 and successfully update the gift document for valid body with hasViewedAppModal field", async () => {
      const response = await request(app)
        .post(`/api/m2m/gifts/${gift.id}`)
        .send({ hasViewedAppModal: true })
        .set("external-user-id", gifter._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(204);

      const updatedGift = (await Gift.findById(gift.id)) as GiftDocument;
      expect(updatedGift.hasViewedAppModal).toBe(true);
    });
  });
  1;
});
