import request from "supertest";
import app from "../../app";
import { UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildAddress, buildUser } from "../../tests/utils/generateModels";
import { buildWealthkernelAddressResponse } from "../../tests/utils/generateWealthkernel";
import mongoose from "mongoose";
import { Address, AddressDocument, AddressDTOInterface } from "../../models/Address";
import { faker } from "@faker-js/faker";
import { WealthkernelService, AddressType } from "../../external-services/wealthkernelService";

describe("AdminAddressRoutes", () => {
  beforeEach(() => jest.clearAllMocks());
  beforeAll(async () => await connectDb("AdminAddressRoutes"));
  afterEach(async () => await clearDb());
  afterAll(async () => await closeDb());

  describe("/addresses", function () {
    let user: UserDocument;
    let validAddressDataWithoutWealthkernel: Partial<AddressDTOInterface>;

    beforeEach(async () => {
      user = await buildUser();
      validAddressDataWithoutWealthkernel = {
        owner: new mongoose.Types.ObjectId(user._id),
        line1: "8 West St",
        city: "Crewkerne",
        countryCode: "GB",
        postalCode: "TA18 8AX"
      };
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    describe("POST /addresses", () => {
      it("should return 204 and create new address when owner does not already have an address", async () => {
        const existingAddresses = await Address.find({});
        expect(existingAddresses.length).toEqual(0);

        const response = await request(app)
          .post(`/api/admin/m2m/addresses?owner=${user._id}`)
          .send(validAddressDataWithoutWealthkernel)
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const newAddresses = await Address.find({ owner: user._id });
        expect(newAddresses.length).toEqual(1);
        expect(newAddresses[0]).toMatchObject(expect.objectContaining({ ...validAddressDataWithoutWealthkernel }));
      });

      it("should return 204 and update existing address with specified fields when owner already has an address", async () => {
        await buildAddress({ ...validAddressDataWithoutWealthkernel });

        const response = await request(app)
          .post(`/api/admin/m2m/addresses?owner=${user._id}`)
          .send({ ...validAddressDataWithoutWealthkernel, line1: "7 West St" })
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const addresses = await Address.find({ owner: user._id });
        expect(addresses.length).toEqual(1);
        expect(addresses[0]).toMatchObject(
          expect.objectContaining({ ...validAddressDataWithoutWealthkernel, line1: "7 West St" })
        );
      });

      it("should return 400 when owner in body does not match owner in query params", async () => {
        const response = await request(app)
          .post(`/api/admin/m2m/addresses?owner=${user._id}`)
          .send({ ...validAddressDataWithoutWealthkernel, owner: faker.string.uuid() })
          .set("Accept", "application/json");

        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "Owner in query param should match owner in body",
              description: "Operation failed"
            }
          })
        );
        const addresses = await Address.find({ owner: user._id });
        expect(addresses.length).toEqual(0);
      });

      it("should return 400 when owner query param is not included", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/addresses")
          .send(validAddressDataWithoutWealthkernel)
          .set("Accept", "application/json");

        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "Owner in query param should match owner in body",
              description: "Operation failed"
            }
          })
        );
        const addresses = await Address.find({ owner: user._id });
        expect(addresses.length).toEqual(0);
      });

      it("should return 400 when owner query param is invalid", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/addresses?owner=ldkafjalkdfjadlk")
          .send(validAddressDataWithoutWealthkernel)
          .set("Accept", "application/json");

        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "Owner in query param should match owner in body",
              description: "Operation failed"
            }
          })
        );
        const addresses = await Address.find({ owner: user._id });
        expect(addresses.length).toEqual(0);
      });

      it("should return 400 when required body field is empty", async () => {
        const response = await request(app)
          .post(`/api/admin/m2m/addresses?owner=${user._id}`)
          .send({ ...validAddressDataWithoutWealthkernel, line1: "" })
          .set("Accept", "application/json");

        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "Missing field 'line1'",
              description: "Operation failed"
            }
          })
        );
        const addresses = await Address.find({ owner: user._id });
        expect(addresses.length).toEqual(0);
      });

      it("should return 400 when required body field is undefined", async () => {
        const response = await request(app)
          .post(`/api/admin/m2m/addresses?owner=${user._id}`)
          .send({ owner: user.id, line1: "line1", countryCode: "GB", postalCode: "EC1R3AL" })
          .set("Accept", "application/json");

        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "Missing field 'city'",
              description: "Operation failed"
            }
          })
        );
        const addresses = await Address.find({ owner: user._id });
        expect(addresses.length).toEqual(0);
      });
    });

    describe("POST /addresses/create-wealthkernel", () => {
      describe("when we have a single address that already has a WK ID", () => {
        beforeEach(async () => {
          jest.spyOn(WealthkernelService.UKInstance, "listAddresses");
          jest.spyOn(WealthkernelService.UKInstance, "addAddress");
        });

        it("should return 204, not hit Wealthkernel for any operations and not edit our address", async () => {
          const addressWithWKId = await buildAddress({
            ...validAddressDataWithoutWealthkernel,
            providers: { wealthkernel: { id: faker.string.uuid() } }
          });

          const response = await hitCreateWealthkernel();
          expect(response.status).toEqual(204);

          await expectNoActionsWereTaken(addressWithWKId);
        });
      });

      describe("when we have a single address whose owner has submitted all required info less than 10 minutes ago", () => {
        const testWealthkernelAddress: AddressType = buildWealthkernelAddressResponse();
        const newWkId = faker.string.uuid();

        beforeEach(async () => {
          jest.spyOn(WealthkernelService.UKInstance, "listAddresses").mockResolvedValue([]);
          jest.spyOn(WealthkernelService.UKInstance, "addAddress").mockResolvedValue({ id: newWkId });
        });

        it("should return 204, not hit Wealthkernel for any operations and not edit our address", async () => {
          const userWithPartyID: UserDocument = await buildUser({
            providers: {
              wealthkernel: {
                id: testWealthkernelAddress.partyId
              }
            },
            submittedRequiredInfoAt: new Date()
          });
          const address = await buildAddress({
            ...validAddressDataWithoutWealthkernel,
            owner: userWithPartyID.id
          });

          const response = await hitCreateWealthkernel();
          expect(response.status).toEqual(204);

          await expectNoActionsWereTaken(address);
        });
      });

      describe("when we have a single address whose owner does not have a populated submittedRequiredInfoAt field", () => {
        const testWealthkernelAddress: AddressType = buildWealthkernelAddressResponse();
        const newWkId = faker.string.uuid();

        beforeEach(async () => {
          jest.spyOn(WealthkernelService.UKInstance, "listAddresses").mockResolvedValue([]);
          jest.spyOn(WealthkernelService.UKInstance, "addAddress").mockResolvedValue({ id: newWkId });
        });

        it("should return 204, not hit Wealthkernel for any operations and not edit our address", async () => {
          const userWithPartyID: UserDocument = await buildUser({
            providers: {
              wealthkernel: {
                id: testWealthkernelAddress.partyId
              }
            },
            submittedRequiredInfoAt: undefined
          });
          const address = await buildAddress({
            ...validAddressDataWithoutWealthkernel,
            owner: userWithPartyID.id
          });

          const response = await hitCreateWealthkernel();
          expect(response.status).toEqual(204);

          await expectNoActionsWereTaken(address);
        });
      });

      describe("when we have a single address for a user that does not have a WK party ID", () => {
        beforeEach(async () => {
          jest.spyOn(WealthkernelService.UKInstance, "listAddresses");
          jest.spyOn(WealthkernelService.UKInstance, "addAddress");
        });

        it("should return 204, not hit Wealthkernel for any operations and not edit our address", async () => {
          const userWithoutPartyID: UserDocument = await buildUser();
          const addressWithWKId = await buildAddress({
            ...validAddressDataWithoutWealthkernel,
            owner: userWithoutPartyID.id
          });

          const response = await hitCreateWealthkernel();
          expect(response.status).toEqual(204);

          await expectNoActionsWereTaken(addressWithWKId);
        });
      });

      describe("when we have a single address without WK as an active provider", () => {
        const testWealthkernelAddress: AddressType = buildWealthkernelAddressResponse();
        const newWkId = faker.string.uuid();

        beforeEach(async () => {
          jest.spyOn(WealthkernelService.UKInstance, "listAddresses").mockResolvedValue([]);
          jest.spyOn(WealthkernelService.UKInstance, "addAddress").mockResolvedValue({ id: newWkId });
        });

        it("should return 204, not hit Wealthkernel for any operations and not edit our address", async () => {
          const userWithPartyID: UserDocument = await buildUser({
            providers: {
              wealthkernel: {
                id: testWealthkernelAddress.partyId
              }
            }
          });
          const address = await buildAddress({
            ...validAddressDataWithoutWealthkernel,
            owner: userWithPartyID.id,
            activeProviders: []
          });

          const response = await hitCreateWealthkernel();
          expect(response.status).toEqual(204);

          await expectNoActionsWereTaken(address);
        });
      });

      describe("when we have a single address already in WK", () => {
        const testWealthkernelAddress: AddressType = buildWealthkernelAddressResponse();

        beforeEach(async () => {
          jest.spyOn(WealthkernelService.UKInstance, "listAddresses").mockResolvedValue([testWealthkernelAddress]);
          jest.spyOn(WealthkernelService.UKInstance, "addAddress");
        });

        it("should return 204, and update our Wealthkernel address with the new ID", async () => {
          const userWithPartyID: UserDocument = await buildUser({
            providers: {
              wealthkernel: {
                id: testWealthkernelAddress.partyId
              }
            }
          });
          await buildAddress({
            ...validAddressDataWithoutWealthkernel,
            owner: userWithPartyID.id
          });

          const response = await hitCreateWealthkernel();
          expect(response.status).toEqual(204);

          const allAddresses = await Address.find({});
          expect(allAddresses.length).toEqual(1);
          expect(allAddresses[0]?.providers?.wealthkernel?.id).toEqual(testWealthkernelAddress.id);

          expect(WealthkernelService.UKInstance.listAddresses).toBeCalledTimes(1);
          expect(WealthkernelService.UKInstance.addAddress).toBeCalledTimes(0);
        });
      });

      describe("when we do not have an address in WK", () => {
        const testWealthkernelAddress: AddressType = buildWealthkernelAddressResponse();
        const newWkId = faker.string.uuid();

        beforeEach(async () => {
          jest.spyOn(WealthkernelService.UKInstance, "listAddresses").mockResolvedValue([]);
          jest.spyOn(WealthkernelService.UKInstance, "addAddress").mockResolvedValue({ id: newWkId });
        });

        it("should return 204, create a new address in WK and update our address with its ID", async () => {
          const userWithPartyID: UserDocument = await buildUser({
            providers: {
              wealthkernel: {
                id: testWealthkernelAddress.partyId
              }
            }
          });
          const address = await buildAddress({
            ...validAddressDataWithoutWealthkernel,
            owner: userWithPartyID.id
          });

          const response = await hitCreateWealthkernel();
          expect(response.status).toEqual(204);

          const allAddresses = await Address.find({});
          expect(allAddresses.length).toEqual(1);
          expect(allAddresses[0]?.providers?.wealthkernel?.id).toEqual(newWkId);

          expect(WealthkernelService.UKInstance.listAddresses).toBeCalledTimes(1);
          expect(WealthkernelService.UKInstance.addAddress).toBeCalledTimes(1);
          expect(WealthkernelService.UKInstance.addAddress).toBeCalledWith({
            partyId: testWealthkernelAddress.partyId,
            line1: address.line1,
            line2: address.line2,
            city: address.city,
            countryCode: address.countryCode,
            postalCode: address.postalCode
          });
        });
      });
    });
  });
});

async function hitCreateWealthkernel() {
  return request(app)
    .post("/api/admin/m2m/addresses/create-wealthkernel")
    .send({})
    .set("Accept", "application/json");
}

async function expectNoActionsWereTaken(initialAddress: AddressDocument) {
  const newAddresses = await Address.find({});

  expect(newAddresses.length).toEqual(1);
  expect(newAddresses[0]?.providers?.wealthkernel?.id).toEqual(initialAddress?.providers?.wealthkernel?.id);
  expect(WealthkernelService.UKInstance.listAddresses).toBeCalledTimes(0);
  expect(WealthkernelService.UKInstance.addAddress).toBeCalledTimes(0);
}
