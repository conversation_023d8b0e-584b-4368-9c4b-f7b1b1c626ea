import request from "supertest";
import app from "../../app";
import { UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildReferralCode, buildUser } from "../../tests/utils/generateModels";
import { LifetimeEnum, ReferralCode } from "../../models/ReferralCode";

describe("ReferralCodeRoutes", () => {
  beforeAll(async () => await connectDb("ReferralCodeRoutes"));
  afterAll(async () => await closeDb());

  describe("POST /referral-codes", () => {
    describe("when no other referral code exists for the user", () => {
      let user: UserDocument;

      beforeAll(async () => {
        user = await buildUser();
      });
      afterAll(async () => await clearDb());

      it("should return 200 and create new referral code document", async () => {
        const oldReferralCodes = await ReferralCode.find();
        expect(oldReferralCodes.length).toBe(0);

        const response = await request(app)
          .post("/api/m2m/referral-codes/")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        expect(response.status).toBe(200);
        const referralCodeResponse = JSON.parse(response.text);
        expect(referralCodeResponse.owner.toString()).toEqual(user.id.toString());
        expect(referralCodeResponse).toMatchObject(
          expect.objectContaining({
            active: true,
            lifetime: LifetimeEnum.EXPIRING
          })
        );

        const newReferralCodes = await ReferralCode.find();
        expect(newReferralCodes.length).toBe(1);
        const newReferralCode = newReferralCodes[0];
        expect(newReferralCode.owner.toString()).toEqual(user.id.toString());
        expect(newReferralCode).toMatchObject(
          expect.objectContaining({
            active: true,
            lifetime: LifetimeEnum.EXPIRING
          })
        );
      });
    });

    describe("when an active referral code exists already for the user", () => {
      let user: UserDocument;

      beforeAll(async () => {
        user = await buildUser();
        await buildReferralCode({ active: true, owner: user.id });
      });
      afterAll(async () => await clearDb());

      it("should return 200 and create additional referral code document", async () => {
        const response = await request(app)
          .post("/api/m2m/referral-codes/")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        expect(response.status).toBe(200);
        const referralCodeResponse = JSON.parse(response.text);
        expect(referralCodeResponse.owner.toString()).toEqual(user.id.toString());
        expect(referralCodeResponse).toMatchObject(
          expect.objectContaining({
            active: true,
            lifetime: LifetimeEnum.EXPIRING
          })
        );

        const newReferralCodes = await ReferralCode.find();
        expect(newReferralCodes.length).toBe(2);
        newReferralCodes.forEach((newReferralCode) => {
          expect(newReferralCode.owner.toString()).toEqual(user.id.toString());
          expect(newReferralCode).toMatchObject(
            expect.objectContaining({
              active: true,
              lifetime: LifetimeEnum.EXPIRING
            })
          );
        });
      });
    });
  });
});
