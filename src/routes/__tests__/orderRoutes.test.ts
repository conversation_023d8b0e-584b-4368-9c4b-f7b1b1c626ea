import { faker } from "@faker-js/faker";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import request from "supertest";
import app from "../../app";
import { ProviderEnum } from "../../configs/providersConfig";
import CloudflareService from "../../external-services/cloudflareService";
import eventEmitter from "../../loaders/eventEmitter";
import { OrderDocument } from "../../models/Order";
import { UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildAddress,
  buildAssetTransaction,
  buildInvestmentProduct,
  buildOrder,
  buildPortfolio,
  buildRebalanceTransaction,
  buildSubscription,
  buildUser
} from "../../tests/utils/generateModels";
import DateUtil from "../../utils/dateUtil";

const { ASSET_CONFIG } = investmentUniverseConfig;

describe("OrderRoutes", () => {
  beforeAll(async () => await connectDb("OrderRoutes"));
  afterAll(async () => await closeDb());

  describe("GET /orders/pending", () => {
    describe("when user has no transactions", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();
      });
      afterAll(async () => await clearDb());

      it("should return 200 with an empty data array", async () => {
        const response = await request(app)
          .get("/api/m2m/orders/pending")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({ data: [] });
      });
    });

    describe("when user has a settled asset transaction", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();
        const portfolio = await buildPortfolio({ owner: owner.id });

        const assetTransaction = await buildAssetTransaction({
          owner: owner.id,
          portfolio: portfolio.id,
          status: "Settled"
        });
        assetTransaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched",
                submittedAt: new Date()
              }
            },
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            },
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          })
        ];
        assetTransaction.save();
      });
      afterAll(async () => await clearDb());

      it("should return 200 with an empty data array", async () => {
        const response = await request(app)
          .get("/api/m2m/orders/pending")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({ data: [] });
      });
    });

    describe("when user has a pending asset transaction but it has no pending orders", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();
        const portfolio = await buildPortfolio({ owner: owner.id });

        const assetTransaction = await buildAssetTransaction({
          owner: owner.id,
          portfolio: portfolio.id,
          status: "Pending"
        });
        assetTransaction.orders = [
          await buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched",
                submittedAt: new Date()
              }
            },
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            },
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          })
        ];
        assetTransaction.save();
      });
      afterAll(async () => await clearDb());

      it("should return 200 with an empty data array", async () => {
        const response = await request(app)
          .get("/api/m2m/orders/pending")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({ data: [] });
      });
    });

    describe("when user has a pending asset transaction with pending orders", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();
        const portfolio = await buildPortfolio({ owner: owner.id });

        const assetTransaction = await buildAssetTransaction({
          owner: owner.id,
          portfolio: portfolio.id,
          status: "Pending"
        });
        assetTransaction.orders = [
          await buildOrder({
            status: "Pending",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Open",
                submittedAt: new Date()
              }
            },
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            },
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          }),
          await buildOrder({
            status: "Pending",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Open",
                submittedAt: new Date()
              }
            },
            consideration: {
              amount: 200, // stored in cents
              currency: "GBP"
            },
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_us"].isin
          }),
          await buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched",
                submittedAt: new Date()
              }
            },
            consideration: {
              amount: 200, // stored in cents
              currency: "GBP"
            },
            side: "Sell",
            transaction: assetTransaction.id,
            isin: ASSET_CONFIG["equities_jp"].isin
          })
        ];
        assetTransaction.save();
      });
      afterAll(async () => await clearDb());

      it("should return 200 with an empty data array", async () => {
        const response = await request(app)
          .get("/api/m2m/orders/pending")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({
          data: [
            expect.objectContaining({ isin: ASSET_CONFIG["equities_uk"].isin }),
            expect.objectContaining({ isin: ASSET_CONFIG["equities_us"].isin })
          ]
        });
      });
    });

    describe("when user has a pending rebalance transaction with pending orders", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();
        const portfolio = await buildPortfolio({ owner: owner.id });

        const rebalanceTransaction = await buildRebalanceTransaction({
          owner: owner.id,
          portfolio: portfolio.id,
          rebalanceStatus: "PendingSell"
        });

        await Promise.all([
          await buildOrder({
            status: "Pending",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Open",
                submittedAt: new Date()
              }
            },
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            },
            side: "Sell",
            transaction: rebalanceTransaction.id,
            isin: ASSET_CONFIG["equities_uk"].isin
          }),
          await buildOrder({
            status: "Pending",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Open",
                submittedAt: new Date()
              }
            },
            consideration: {
              amount: 200, // stored in cents
              currency: "GBP"
            },
            side: "Sell",
            transaction: rebalanceTransaction.id,
            isin: ASSET_CONFIG["equities_us"].isin
          }),
          await buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched",
                submittedAt: new Date()
              }
            },
            consideration: {
              amount: 200, // stored in cents
              currency: "GBP"
            },
            side: "Sell",
            transaction: rebalanceTransaction.id,
            isin: ASSET_CONFIG["equities_us"].isin
          })
        ]);
      });
      afterAll(async () => await clearDb());

      it("should return 200 with an empty data array", async () => {
        const response = await request(app)
          .get("/api/m2m/orders/pending")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({
          data: [
            expect.objectContaining({ isin: ASSET_CONFIG["equities_uk"].isin }),
            expect.objectContaining({ isin: ASSET_CONFIG["equities_us"].isin })
          ]
        });
      });
    });
  });

  describe("GET /orders/matched/latest", () => {
    describe("when user has no transactions", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();
      });
      afterAll(async () => await clearDb());

      it("should return 204", async () => {
        const response = await request(app)
          .get("/api/m2m/orders/matched/latest")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(204);
      });
    });

    describe("when user has transactions, but without matched orders", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();
        const portfolio = await buildPortfolio({ owner: owner.id });

        const assetTransaction = await buildAssetTransaction({
          owner: owner.id,
          portfolio: portfolio.id,
          status: "Pending"
        });
        assetTransaction.orders = [
          await buildOrder({
            status: "Pending",
            transaction: assetTransaction.id
          })
        ];
        assetTransaction.save();
      });
      afterAll(async () => await clearDb());

      it("should return 204", async () => {
        const response = await request(app)
          .get("/api/m2m/orders/matched/latest")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(204);
      });
    });

    describe("when user has multiple asset transactions with settled and pending orders", () => {
      let owner: UserDocument;
      let matchedOrder2: OrderDocument;

      beforeAll(async () => {
        owner = await buildUser();
        const portfolio = await buildPortfolio({ owner: owner.id });

        const assetTransaction1 = await buildAssetTransaction({
          owner: owner.id,
          portfolio: portfolio.id,
          status: "Pending"
        });
        const pendingOrder = await buildOrder({
          status: "Pending",
          transaction: assetTransaction1.id
        });
        assetTransaction1.orders = [pendingOrder.id];
        assetTransaction1.save();

        const assetTransaction2 = await buildAssetTransaction({
          owner: owner.id,
          portfolio: portfolio.id,
          status: "Settled"
        });
        const matchedOrder = await buildOrder({
          status: "Matched",
          transaction: assetTransaction2.id,
          updatedAt: DateUtil.getDateOfMinutesAgo(1)
        });
        assetTransaction2.orders = [matchedOrder.id];
        assetTransaction2.save();

        const assetTransaction3 = await buildAssetTransaction({
          owner: owner.id,
          portfolio: portfolio.id,
          status: "Settled"
        });
        matchedOrder2 = await buildOrder({
          status: "Matched",
          transaction: assetTransaction3.id,
          updatedAt: new Date()
        });
        assetTransaction3.orders = [matchedOrder2.id];
        assetTransaction3.save();
      });
      afterAll(async () => await clearDb());

      it("should return 200 with the id of the last matched order", async () => {
        const response = await request(app)
          .get("/api/m2m/orders/matched/latest")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({ id: matchedOrder2.id });
      });
    });
  });

  describe("POST /orders/:id/cancel", () => {
    describe("when a order ID that belongs to another user is passed", () => {
      let response: request.Response;

      beforeAll(async () => {
        jest.spyOn(eventEmitter, "emit");

        const user = await buildUser();
        const transaction = await buildAssetTransaction({
          owner: user.id,
          status: "Pending",
          consideration: {
            amount: 1000
          },
          executionWindow: {
            etfs: {
              start: new Date("2024-10-06"),
              end: new Date("2024-10-07"),
              executionType: "MARKET_HOURS"
            }
          }
        });
        const order = await buildOrder({
          transaction: transaction.id,
          status: "Pending"
        });
        transaction.orders = [order];
        await transaction.save();

        const anotherUser = await buildUser();

        response = await request(app)
          .post(`/api/m2m/orders/${order.id}/cancel`)
          .set("external-user-id", anotherUser.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 403 with proper message", () => {
        expect(response.status).toEqual(403);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Inaccessible resource",
              message: expect.stringContaining("Order does not belong to user")
            }
          })
        );
      });
    });

    describe("when an invalid order ID is passed", () => {
      let response: request.Response;

      beforeAll(async () => {
        jest.spyOn(eventEmitter, "emit");

        const user = await buildUser();

        response = await request(app)
          .post("/api/m2m/orders/some-invalid-id/cancel")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 403 with proper message", () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Invalid parameter",
              message: expect.stringContaining("Order ID is invalid")
            }
          })
        );
      });
    });

    describe("when a valid order ID is passed", () => {
      let response: request.Response;
      const TODAY = new Date("2022-07-17T11:00:00Z");

      beforeAll(async () => {
        jest.spyOn(eventEmitter, "emit");
        Date.now = jest.fn(() => TODAY.valueOf());

        await buildInvestmentProduct(true, { assetId: "equities_apple" });

        const user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id
        });
        await buildSubscription({
          owner: user.id
        });
        const transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            amount: 1000
          },
          portfolioTransactionCategory: "update"
        });
        const order = await buildOrder({
          transaction: transaction.id,
          status: "Pending",
          side: "Buy",
          consideration: {
            originalAmount: 1000,
            amount: 1000,
            currency: "GBP"
          },
          isin: ASSET_CONFIG["equities_apple"].isin
        });
        transaction.orders = [order];
        await transaction.save();

        response = await request(app)
          .post(`/api/m2m/orders/${order.id}/cancel`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return 200 with the cancelled order", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            status: "Cancelled",
            transaction: expect.objectContaining({
              orders: expect.arrayContaining([
                expect.objectContaining({
                  status: "Cancelled"
                })
              ])
            })
          })
        );
      });
    });
  });

  describe("POST /orders/:id/trade-confirmations/generate", () => {
    let response: request.Response;

    const CLOUDFLARE_LINK = "https://cloudflare.link";

    beforeAll(async () => {
      jest.spyOn(CloudflareService.Instance, "uploadObject").mockResolvedValue({ fileUri: CLOUDFLARE_LINK });

      await buildInvestmentProduct(true, { assetId: "equities_apple" });

      const user = await buildUser();
      await buildAddress({ owner: user.id });
      const portfolio = await buildPortfolio({ owner: user.id });

      const transaction = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled"
      });

      const order = await buildOrder({
        side: "Buy",
        status: "Matched",
        consideration: {
          currency: "GBP",
          amount: 100,
          originalAmount: 100
        },
        quantity: 1,
        settlementCurrency: "GBP",
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        transaction: transaction._id,
        unitPrice: { currency: "GBP", amount: 100 },
        exchangeRate: 1,
        fees: {
          fx: { currency: "GBP", amount: 0 }
        },
        createdAt: new Date(),
        updatedAt: new Date(),
        isin: ASSET_CONFIG["equities_apple"].isin
      });
      transaction.orders = [order];
      await transaction.save();

      response = await request(app)
        .post(`/api/m2m/orders/${order.id}/trade-confirmations/generate`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
    });

    afterAll(async () => {
      jest.restoreAllMocks();
      await clearDb();
    });

    it("should return 200 with fileUri", () => {
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text)).toMatchObject({
        fileUri: CLOUDFLARE_LINK
      });
    });
  });
});
