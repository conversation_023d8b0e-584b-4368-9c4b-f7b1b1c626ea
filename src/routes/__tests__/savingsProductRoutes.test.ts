import request from "supertest";
import { faker } from "@faker-js/faker";
import app from "../../app";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildDailyPortfolioSavingsTicker,
  buildDepositCashTransaction,
  buildPortfolio,
  buildSavingsDividend,
  buildSavingsProduct,
  buildSavingsTopup,
  buildSavingsWithdrawal,
  buildSubscription,
  buildUser
} from "../../tests/utils/generateModels";
import { UserDocument } from "../../models/User";
import {
  SavingsDividendTransactionDocument,
  SavingsTopupTransactionDocument,
  SavingsWithdrawalTransactionDocument
} from "../../models/Transaction";
import { PortfolioDocument } from "../../models/Portfolio";
import { SavingsProductDocument } from "../../models/SavingsProduct";
import Decimal from "decimal.js";
import { DailyPortfolioSavingsTickerDocument } from "../../models/DailyTicker";
import CurrencyUtil from "../../utils/currencyUtil";
import { entitiesConfig } from "@wealthyhood/shared-configs";

describe("SavingsProductRoutes", () => {
  beforeAll(async () => await connectDb("SavingsProductRoutes"));
  afterAll(async () => await closeDb());

  describe("GET /me", () => {
    describe("when user has a European company entity", () => {
      const SAVINGS_AMOUNT = 1000; // 10.00 EUR
      let user: UserDocument;
      let savingsProduct: SavingsProductDocument;
      let dailyPortfolioSavingsTicker: DailyPortfolioSavingsTickerDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-02-05");
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          currency: "EUR"
        });
        const portfolio = await buildPortfolio({
          owner: user.id,
          savings: new Map([["mmf_dist_eur", { amount: SAVINGS_AMOUNT, currency: "EUC" }]])
        });
        await buildSubscription({ owner: user.id, price: "free_monthly" });
        savingsProduct = await buildSavingsProduct(true, { commonId: "mmf_dist_eur" });
        dailyPortfolioSavingsTicker = await buildDailyPortfolioSavingsTicker({
          portfolio: portfolio.id,
          savingsProduct: savingsProduct.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return 200 with the user's savings", async () => {
        const response = await request(app)
          .get("/api/m2m/savings-products/me")
          .set("external-user-id", user._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);

        const savings = JSON.parse(response.text);

        expect(savings).toEqual([
          {
            savingsProductId: "mmf_dist_eur",
            netInterestRate: `${Decimal.sub(savingsProduct.currentTicker.oneDayYield, 0.6).toFixed(2)}%`,
            unrealisedInterest: Decimal.div(dailyPortfolioSavingsTicker.dailyAccrual, 100).toDP(2).toNumber(),
            displayUnrealisedInterest: `+${CurrencyUtil.formatCurrency(
              Decimal.div(dailyPortfolioSavingsTicker.dailyAccrual, 100).toDP(2).toNumber(),
              "EUR",
              "en"
            )}`,
            savingsAmount: Decimal.div(SAVINGS_AMOUNT, 100).toDP(2).toNumber(),
            displaySavingsAmount: `${CurrencyUtil.formatCurrency(
              Decimal.div(SAVINGS_AMOUNT, 100).toDP(2).toNumber(),
              "EUR",
              "en"
            )}`,
            currency: "EUR"
          }
        ]);
      });
    });

    describe("when user has a UK company entity", () => {
      const SAVINGS_AMOUNT = 1000; // 10.00 GBP
      let user: UserDocument;
      let savingsProduct: SavingsProductDocument;
      let dailyPortfolioSavingsTicker: DailyPortfolioSavingsTickerDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-02-05");
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK });
        const portfolio = await buildPortfolio({
          owner: user.id,
          savings: new Map([["mmf_dist_gbp", { amount: SAVINGS_AMOUNT, currency: "GBX" }]])
        });
        await buildSubscription({ owner: user.id, price: "free_monthly" });
        savingsProduct = await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
        dailyPortfolioSavingsTicker = await buildDailyPortfolioSavingsTicker({
          portfolio: portfolio.id,
          savingsProduct: savingsProduct.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return 200 with the user's savings", async () => {
        const response = await request(app)
          .get("/api/m2m/savings-products/me")
          .set("external-user-id", user._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);

        const savings = JSON.parse(response.text);

        expect(savings).toEqual([
          {
            savingsProductId: "mmf_dist_gbp",
            netInterestRate: `${Decimal.sub(savingsProduct.currentTicker.oneDayYield, 0.6).toFixed(2)}%`,
            unrealisedInterest: Decimal.div(dailyPortfolioSavingsTicker.dailyAccrual, 100).toDP(2).toNumber(),
            displayUnrealisedInterest: `+${CurrencyUtil.formatCurrency(
              Decimal.div(dailyPortfolioSavingsTicker.dailyAccrual, 100).toDP(2).toNumber(),
              "GBP",
              "en"
            )}`,
            savingsAmount: Decimal.div(SAVINGS_AMOUNT, 100).toDP(2).toNumber(),
            displaySavingsAmount: `${CurrencyUtil.formatCurrency(
              Decimal.div(SAVINGS_AMOUNT, 100).toDP(2).toNumber(),
              "GBP",
              "en"
            )}`,
            currency: "GBP"
          }
        ]);
      });
    });
  });

  describe("GET /activity", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let savingsTopup: SavingsTopupTransactionDocument;
    let savingsTopupPendingDeposit: SavingsTopupTransactionDocument;
    let savingsWithdrawal: SavingsWithdrawalTransactionDocument;
    let savingsDividend: SavingsDividendTransactionDocument;

    beforeAll(async () => {
      user = await buildUser();
      portfolio = await buildPortfolio({ owner: user.id });
      const depositCashTransaction = await buildDepositCashTransaction(
        {
          bankAccount: user.bankAccounts[0],
          status: "Settled",
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Created"
            }
          }
        },
        user,
        portfolio
      );
      [savingsTopup, savingsTopupPendingDeposit, savingsWithdrawal, savingsDividend] = await Promise.all([
        buildSavingsTopup({
          owner: user.id,
          portfolio: portfolio.id,
          createdAt: new Date("2024-01-04")
        }),
        buildSavingsTopup({
          owner: user.id,
          portfolio: portfolio.id,
          status: "PendingDeposit",
          pendingDeposit: depositCashTransaction.id,
          createdAt: new Date("2024-01-03")
        }),
        buildSavingsWithdrawal({
          owner: user.id,
          portfolio: portfolio.id,
          createdAt: new Date("2024-01-02")
        }),
        buildSavingsDividend({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          dividendMonth: "2023-12",
          settledAt: new Date("2024-01-01"),
          createdAt: new Date("2024-01-01")
        })
      ]);
    });
    afterAll(async () => await clearDb());

    it("should return 400 if the 'savingsProductId' query param is invalid", async () => {
      const response = await request(app)
        .get("/api/m2m/savings-products/activity?savingsProductId=mmf_wealthyhood")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(400);
    });

    it("should return 400 if the 'savingsProductId' query param is absent", async () => {
      const response = await request(app)
        .get("/api/m2m/savings-products/activity")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(400);
    });

    it("should return 400 if the 'limit' query param is invalid", async () => {
      const response = await request(app)
        .get("/api/m2m/savings-products/activity?savingsProductId=mmf_wealthyhood&limit=garbage")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(400);
    });

    it("should return 200 with all the transaction if the 'limit' query param is absent", async () => {
      const response = await request(app)
        .get("/api/m2m/savings-products/activity?savingsProductId=mmf_dist_gbp")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(200);

      const activity = JSON.parse(response.text);

      expect(activity).toEqual([
        {
          type: "cashToSavings",
          item: expect.objectContaining({
            id: savingsTopup.id,
            displayTitle: "Cash Balance -> Savings GBP",
            displayAmount: savingsTopup.consideration.amount,
            displayDate: savingsTopup.createdAt.toISOString() // stringified due to http request
          })
        },
        {
          type: "bankDepositToSavings",
          item: expect.objectContaining({
            id: savingsTopupPendingDeposit.id,
            displayTitle: "Deposit",
            displayAmount: savingsTopupPendingDeposit.consideration.amount,
            displayDate: savingsTopupPendingDeposit.createdAt.toISOString(), // stringified due to http request
            // Test that bankAccount is populated
            pendingDeposit: expect.objectContaining({
              bankAccount: expect.objectContaining({ id: user.bankAccounts[0].id })
            })
          })
        },
        {
          type: "savingsToCash",
          item: expect.objectContaining({
            id: savingsWithdrawal.id,
            displayTitle: "Savings GBP -> Cash Balance",
            displayAmount: savingsWithdrawal.consideration.amount,
            displayDate: savingsWithdrawal.createdAt.toISOString() // stringified due to http request
          })
        },
        {
          type: "savingsInterest",
          item: expect.objectContaining({
            id: savingsDividend.id,
            displayTitle: "Interest paid",
            displayAmount: savingsDividend.consideration.amount,
            displayDate: savingsDividend.settledAt?.toISOString() // stringified due to http request
          })
        }
      ]);
    });

    it("should return 200 with 2 transactions when limit is 2", async () => {
      const response = await request(app)
        .get("/api/m2m/savings-products/activity?savingsProductId=mmf_dist_gbp&limit=2")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(200);

      const activity = JSON.parse(response.text);

      // It should include the first 2 transactions
      expect(activity).toEqual([
        {
          type: "cashToSavings",
          item: expect.objectContaining({ id: savingsTopup.id })
        },
        {
          type: "bankDepositToSavings",
          item: expect.objectContaining({ id: savingsTopupPendingDeposit.id })
        }
      ]);
    });
  });

  describe("GET /fee-details", () => {
    const PLAN_FEE_PERCENTAGE = 0.6; // 0.6% for free plan
    let user: UserDocument;
    let savingsProduct: SavingsProductDocument;

    beforeAll(async () => {
      user = await buildUser();
      await buildSubscription({ owner: user.id, price: "free_monthly" });
      savingsProduct = await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
    });
    afterAll(async () => await clearDb());

    it("should return 400 if the 'savingsProductId' query param is invalid", async () => {
      const response = await request(app)
        .get("/api/m2m/savings-products/fee-details?savingsProductId=mmf_wealthyhood")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(400);
    });

    it("should return 400 if the 'savingsProductId' query param is absent", async () => {
      const response = await request(app)
        .get("/api/m2m/savings-products/fee-details")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(400);
    });

    it("should return 200 with correct fee details", async () => {
      const FUND_MANAGER_FEE_PERCENTAGE = 0.1;

      const response = await request(app)
        .get("/api/m2m/savings-products/fee-details?savingsProductId=mmf_dist_gbp")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(200);

      const data = JSON.parse(response.text);

      expect(data).toEqual({
        feeDetails: [
          {
            fundManagerAnnualFeePercentage: `${FUND_MANAGER_FEE_PERCENTAGE.toFixed(2)}%`,
            netInterestRate: `${Decimal.sub(savingsProduct.currentTicker.oneDayYield, 0.6).toFixed(2)}% p.a.`,
            netInterestRateValue: Decimal.sub(savingsProduct.currentTicker.oneDayYield, 0.6).toDP(2).toNumber(),
            plan: "free",
            wealthyhoodAnnualFeePercentage: "0.60%"
          },
          {
            fundManagerAnnualFeePercentage: `${FUND_MANAGER_FEE_PERCENTAGE.toFixed(2)}%`,
            netInterestRate: `${Decimal.sub(savingsProduct.currentTicker.oneDayYield, 0.2).toFixed(2)}% p.a.`,
            netInterestRateValue: Decimal.sub(savingsProduct.currentTicker.oneDayYield, 0.2).toDP(2).toNumber(),
            plan: "paid_low",
            wealthyhoodAnnualFeePercentage: "0.20%"
          },
          {
            fundManagerAnnualFeePercentage: `${FUND_MANAGER_FEE_PERCENTAGE.toFixed(2)}%`,
            netInterestRate: `${Decimal.sub(savingsProduct.currentTicker.oneDayYield, 0.1).toFixed(2)}% p.a.`,
            netInterestRateValue: Decimal.sub(savingsProduct.currentTicker.oneDayYield, 0.1).toDP(2).toNumber(),
            plan: "paid_mid",
            wealthyhoodAnnualFeePercentage: "0.10%"
          }
        ],
        fundManagerFeeColumnLabel: "Blackrock fee annually",
        grossInterestRate: `${Decimal.add(
          savingsProduct.currentTicker.oneDayYield,
          FUND_MANAGER_FEE_PERCENTAGE
        ).toFixed(2)}%`,
        netInterestRateOfCurrentPlan: `${Decimal.sub(
          savingsProduct.currentTicker.oneDayYield,
          PLAN_FEE_PERCENTAGE
        ).toFixed(2)}%`,
        netInterestRateColumnLabel: "Your 1-day yield (net)",
        planColumnLabel: "Your plan",
        wealthyhoodFeeColumnLabel: "Our fee annually"
      });
    });

    it("should return 200 even if the user doesn't have a subscription plan", async () => {
      const userWithNoSubscription = await buildUser();

      const FUND_MANAGER_FEE_PERCENTAGE = 0.1;

      const response = await request(app)
        .get("/api/m2m/savings-products/fee-details?savingsProductId=mmf_dist_gbp")
        .set("external-user-id", userWithNoSubscription._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(200);

      const data = JSON.parse(response.text);

      expect(data).toEqual({
        feeDetails: [
          {
            fundManagerAnnualFeePercentage: `${FUND_MANAGER_FEE_PERCENTAGE.toFixed(2)}%`,
            netInterestRate: `${Decimal.sub(savingsProduct.currentTicker.oneDayYield, 0.6).toFixed(2)}% p.a.`,
            netInterestRateValue: Decimal.sub(savingsProduct.currentTicker.oneDayYield, 0.6).toDP(2).toNumber(),
            plan: "free",
            wealthyhoodAnnualFeePercentage: "0.60%"
          },
          {
            fundManagerAnnualFeePercentage: `${FUND_MANAGER_FEE_PERCENTAGE.toFixed(2)}%`,
            netInterestRate: `${Decimal.sub(savingsProduct.currentTicker.oneDayYield, 0.2).toFixed(2)}% p.a.`,
            netInterestRateValue: Decimal.sub(savingsProduct.currentTicker.oneDayYield, 0.2).toDP(2).toNumber(),
            plan: "paid_low",
            wealthyhoodAnnualFeePercentage: "0.20%"
          },
          {
            fundManagerAnnualFeePercentage: `${FUND_MANAGER_FEE_PERCENTAGE.toFixed(2)}%`,
            netInterestRate: `${Decimal.sub(savingsProduct.currentTicker.oneDayYield, 0.1).toFixed(2)}% p.a.`,
            netInterestRateValue: Decimal.sub(savingsProduct.currentTicker.oneDayYield, 0.1).toDP(2).toNumber(),
            plan: "paid_mid",
            wealthyhoodAnnualFeePercentage: "0.10%"
          }
        ],
        fundManagerFeeColumnLabel: "Blackrock fee annually",
        grossInterestRate: `${Decimal.add(
          savingsProduct.currentTicker.oneDayYield,
          FUND_MANAGER_FEE_PERCENTAGE
        ).toFixed(2)}%`,
        netInterestRateColumnLabel: "Your 1-day yield (net)",
        planColumnLabel: "Your plan",
        wealthyhoodFeeColumnLabel: "Our fee annually"
      });
    });
  });

  describe("GET /data", () => {
    let user: UserDocument;
    let savingsProduct: SavingsProductDocument;
    let firstSavingsDividend: SavingsDividendTransactionDocument;
    let lastMonthSavingsDividend: SavingsDividendTransactionDocument;

    beforeAll(async () => {
      const TODAY = new Date("2024-02-05");
      jest.clearAllMocks();
      Date.now = jest.fn(() => +TODAY);
      user = await buildUser();
      await buildSubscription({ owner: user.id, price: "free_monthly" });
      savingsProduct = await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
      [firstSavingsDividend, lastMonthSavingsDividend] = await Promise.all([
        buildSavingsDividend({
          owner: user.id,
          status: "Settled",
          settledAt: new Date("2024-01-02"),
          dividendMonth: "2023-12",
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        }),
        buildSavingsDividend({
          owner: user.id,
          status: "Settled",
          settledAt: new Date("2024-02-02"),
          dividendMonth: "2024-01",
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        })
      ]);
    });
    afterAll(async () => await clearDb());

    it("should return 400 if the 'savingsProductId' query param is invalid", async () => {
      const response = await request(app)
        .get("/api/m2m/savings-products/data?savingsProductId=mmf_wealthyhood")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(400);
    });

    it("should return 400 if the 'savingsProductId' query param is absent", async () => {
      const response = await request(app)
        .get("/api/m2m/savings-products/data")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(400);
    });

    it("should return 200 with correct data", async () => {
      const FUND_MANAGER_FEE_PERCENTAGE = 0.1;
      const PLAN_FEE_PERCENTAGE = 0.6;

      const response = await request(app)
        .get("/api/m2m/savings-products/data?savingsProductId=mmf_dist_gbp")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toBe(200);

      const data = JSON.parse(response.text);

      const oneYield = savingsProduct.currentTicker.oneDayYield;
      const lifetimeEarnings = Decimal.div(
        firstSavingsDividend.consideration.amount + lastMonthSavingsDividend.consideration.amount,
        100
      )
        .toDP(2)
        .toNumber();
      const earnedLastMonth = Decimal.div(lastMonthSavingsDividend.consideration.amount, 100).toDP(2).toNumber();

      expect(data).toEqual({
        highlightsSection: {
          oneDayYieldGross: {
            value: `${Decimal.add(oneYield, FUND_MANAGER_FEE_PERCENTAGE).toFixed(2)}% p.a.`,
            label: "1-day yield (gross)"
          },
          oneDayYieldNet: {
            value: `${Decimal.sub(oneYield, PLAN_FEE_PERCENTAGE).toFixed(2)}% p.a.`,
            label: "1-day yield (net)"
          },
          earnedLastMonth: {
            value: `${CurrencyUtil.formatCurrency(earnedLastMonth, "GBP", "en")}`,
            label: "Earned last month"
          },
          lifetimeEarnings: {
            value: `${CurrencyUtil.formatCurrency(lifetimeEarnings, "GBP", "en")}`,
            label: "Lifetime earnings"
          }
        },
        informationSection: {
          baseCurrency: "GBP",
          benchmark: "Sterling Overnight Index Average Rate (SONIA)",
          distribution: "Monthly",
          fundManager: "Blackrock",
          fundName: "ICS Sterling Liquidity Fund (Premier)",
          income: "Distributing",
          isin: "IE00B3L10356"
        },
        fundQualitySection: {
          rating: "AAA",
          creditRatings: [
            {
              label: "Moody’s rating",
              rating: "AAA-mf"
            },
            {
              label: "Fitch rating",
              rating: "AAAmmf"
            },
            {
              label: "S&P rating",
              rating: "AAAm"
            }
          ],
          ratingSubtitle:
            "This is the highest possible quality rating and means this fund has the highest level of capital security and lowest level of interest rate sensitivity.",
          risk: {
            score: 1,
            scale: 7,
            subtitle: "A risk indicator of 1/7 represents the lowest possible level of risk."
          }
        }
      });
    });
  });
});
