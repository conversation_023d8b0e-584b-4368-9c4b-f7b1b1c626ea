import request from "supertest";
import app from "../../app";
import { UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildContentEntry, buildSubscription, buildUser, buildLearnNews } from "../../tests/utils/generateModels";
import ContentfulRetrievalService from "../../external-services/contentfulRetrievalService";
import {
  buildContentfulContentEntriesResponse,
  buildContentfulFaqCategoryResponse,
  buildContentfulGlossaryResponse,
  buildContentfulLearningGuidesResponse
} from "../../tests/utils/generateContentful";
import { RedisClientService } from "../../loaders/redis";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { ContentfulContentTypeEnum } from "../../configs/contentfulConfig";
import { ContentEntryDocument } from "../../models/ContentEntry";
import { faker } from "@faker-js/faker";
import { marked } from "marked";
import { FinimizeContentTypeEnum } from "../../external-services/finimizeService";
import WealthyhubService from "../../services/wealthyhubService";
import { LearnNewsDocument } from "../../models/LearnNews";

describe("WealthyhubRoutes", () => {
  beforeAll(async () => await connectDb("WealthyhubRoutes"));
  beforeEach(() => jest.clearAllMocks());
  afterAll(async () => await closeDb());

  describe("GET /wealthyhub/analyst-insights", () => {
    describe("when user is subscribed", () => {
      const CONTENT_ENTRIES_PAGE_SIZE = 50; // 50 per page + 2 to test pagination
      const CONTENT_ENTRIES_TO_CREATE = CONTENT_ENTRIES_PAGE_SIZE + 2; // 50 per page + 2 to test pagination

      let user: UserDocument;

      let contentEntries: ContentEntryDocument[];
      let contentfulResponse: any;

      beforeEach(async () => {
        user = await buildUser();
        // the user has to be subscribed to get all content of analyst insights
        await buildSubscription({ price: "paid_mid_monthly", owner: user._id });

        /**
         * Create content entries with time offsets to test sorting by createdAt
         */
        contentEntries = (
          await Promise.all(
            [...Array(CONTENT_ENTRIES_TO_CREATE).keys()].map((offset) =>
              buildContentEntry({
                title: faker.string.uuid(),
                providers: {
                  finimize: {
                    id: faker.string.uuid(),
                    contentType: FinimizeContentTypeEnum.INSIGHT,
                    publishedAt: new Date(Date.now() + offset * 10000)
                  },
                  contentful: {
                    id: faker.string.uuid(),
                    spaceId: faker.string.uuid(),
                    environmentId: faker.string.uuid()
                  }
                }
              })
            )
          )
        ).sort((a, b) => {
          const aTime = a.providers.finimize?.publishedAt?.getTime() || 0;
          const bTime = b.providers.finimize?.publishedAt?.getTime() || 0;
          return bTime - aTime;
        });

        // delete cache to reset state
        await WealthyhubService.deleteAnalystInsightsCache();

        contentfulResponse = buildContentfulContentEntriesResponse(
          contentEntries.map((contentEntry) => ({
            title: faker.string.uuid(),
            id: contentEntry.providers?.contentful?.id,
            analystInsightType: contentEntry.contentType,
            createdAt: contentEntry.providers.finimize?.publishedAt
          }))
        );

        jest
          .spyOn(ContentfulRetrievalService.LearnHubInstance, "getEntriesByIds")
          .mockImplementation(async (contentType: ContentfulContentTypeEnum, ids: string[]): Promise<any> => {
            if (contentType === ContentfulContentTypeEnum.CONTENT_ENTRY) {
              const matchingContentEntries = contentfulResponse.items.filter((contentEntry: any) =>
                ids.includes(contentEntry.sys.id)
              );
              return Promise.resolve({ items: matchingContentEntries });
            }
          });
      });
      afterEach(async () => await clearDb());

      it("should return 200 with analyst insights when page 1 is request", async () => {
        const response = await request(app)
          .get("/api/m2m/wealthyhub/analyst-insights?page=1")
          .set("external-user-id", user._id)
          .set("Accept", "application/json");

        const expectedData = contentEntries.slice(0, CONTENT_ENTRIES_PAGE_SIZE).map((contentEntry) => {
          const contentfulEntry = contentfulResponse.items.find(
            (entry: any) => entry.sys.id === contentEntry.providers?.contentful?.id
          )?.fields;

          const DISCLAIMER_ANALYST_INSIGHTS =
            "\n - \n\n Capital at risk. Our analyst insights are for educational purposes only. Wealthyhood does not render investment, financial, legal, tax, or accounting advice.";

          return {
            key: contentfulEntry.slug,
            createdAt: contentfulEntry.publishedAt,
            contentType: "analystInsights",
            id: contentEntry.id,
            title: contentfulEntry.title,
            contentHTML: expect.stringContaining(
              `${marked.parse(contentfulEntry.content + DISCLAIMER_ANALYST_INSIGHTS)}`
            ),
            previewImageURL: contentfulEntry.headerImage,
            fullImageURL: contentfulEntry.headerImage,
            bannerImageURL: contentfulEntry.bannerImage,
            readingTime: contentfulEntry.readingTime,
            analystInsightType: contentEntry.contentType
          };
        });

        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({
          data: expectedData,
          pagination: { page: 1, pageSize: CONTENT_ENTRIES_PAGE_SIZE, pages: 2, total: CONTENT_ENTRIES_TO_CREATE }
        });
      });

      it("should return 200 with cached analyst insights when page 1 is request twice", async () => {
        expect(await RedisClientService.Instance.get("analystInsights:page_1")).toBeUndefined();

        let response = await request(app)
          .get("/api/m2m/wealthyhub/analyst-insights?page=1")
          .set("external-user-id", user._id)
          .set("Accept", "application/json")
          .expect(200);

        const expectedData = contentEntries.slice(0, CONTENT_ENTRIES_PAGE_SIZE).map((contentEntry) => {
          const contentfulEntry = contentfulResponse.items.find(
            (entry: any) => entry.sys.id === contentEntry.providers?.contentful?.id
          )?.fields;

          const DISCLAIMER_ANALYST_INSIGHTS =
            "\n - \n\n Capital at risk. Our analyst insights are for educational purposes only. Wealthyhood does not render investment, financial, legal, tax, or accounting advice.";

          return {
            key: contentfulEntry.slug,
            createdAt: contentfulEntry.publishedAt,
            contentType: "analystInsights",
            id: contentEntry.id,
            title: contentfulEntry.title,
            contentHTML: expect.stringContaining(
              `${marked.parse(contentfulEntry.content + DISCLAIMER_ANALYST_INSIGHTS)}`
            ),
            previewImageURL: contentfulEntry.headerImage,
            fullImageURL: contentfulEntry.headerImage,
            bannerImageURL: contentfulEntry.bannerImage,
            readingTime: contentfulEntry.readingTime,
            analystInsightType: contentEntry.contentType
          };
        });

        expect(JSON.parse(response.text)).toMatchObject({
          data: expectedData,
          pagination: { page: 1, pageSize: CONTENT_ENTRIES_PAGE_SIZE, pages: 2, total: CONTENT_ENTRIES_TO_CREATE }
        });

        response = await request(app)
          .get("/api/m2m/wealthyhub/analyst-insights?page=1")
          .set("external-user-id", user._id)
          .set("Accept", "application/json")
          .expect(200);

        const cachedAnalystInsights = (await RedisClientService.Instance.get("analystInsights:page_1")) as any;

        expect(cachedAnalystInsights).toMatchObject({
          data: expectedData,
          pagination: { page: 1, pageSize: CONTENT_ENTRIES_PAGE_SIZE, pages: 2, total: CONTENT_ENTRIES_TO_CREATE }
        });
        expect(JSON.parse(response.text)).toMatchObject(cachedAnalystInsights);
      });

      it("should return 200 with cached analyst insights when page 1 is request twice", async () => {
        expect(await RedisClientService.Instance.get("analystInsights:page_1")).toBeUndefined();

        const response = await request(app)
          .get("/api/m2m/wealthyhub/analyst-insights?page=1")
          .set("external-user-id", user._id)
          .set("Accept", "application/json")
          .expect(200);

        const expectedData = contentEntries.slice(0, CONTENT_ENTRIES_PAGE_SIZE).map((contentEntry) => {
          const contentfulEntry = contentfulResponse.items.find(
            (entry: any) => entry.sys.id === contentEntry.providers?.contentful?.id
          )?.fields;

          const DISCLAIMER_ANALYST_INSIGHTS =
            "\n - \n\n Capital at risk. Our analyst insights are for educational purposes only. Wealthyhood does not render investment, financial, legal, tax, or accounting advice.";

          return {
            key: contentfulEntry.slug,
            createdAt: contentfulEntry.publishedAt,
            contentType: "analystInsights",
            id: contentEntry.id,
            title: contentfulEntry.title,
            contentHTML: expect.stringContaining(
              `${marked.parse(contentfulEntry.content + DISCLAIMER_ANALYST_INSIGHTS)}`
            ),
            previewImageURL: contentfulEntry.headerImage,
            fullImageURL: contentfulEntry.headerImage,
            bannerImageURL: contentfulEntry.bannerImage,
            readingTime: contentfulEntry.readingTime,
            analystInsightType: contentEntry.contentType
          };
        });

        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({
          data: expectedData,
          pagination: { page: 1, pageSize: CONTENT_ENTRIES_PAGE_SIZE, pages: 2, total: CONTENT_ENTRIES_TO_CREATE }
        });
      });
    });

    describe("when user is not subscribed", () => {
      let user: UserDocument;

      beforeEach(async () => {
        // Create a regular user without a subscription.
        user = await buildUser();
      });

      it("should return 200 with trimmed analyst insights when user is not subscribed", async () => {
        // Prepare a raw markdown with three paragraphs.
        const rawMarkdown = "Paragraph one.\n\nParagraph two.\n\nParagraph three.\n\nParagraph four.";

        // Create a content entry (note: we do not add a subscription to this user)
        const contentEntry = await buildContentEntry({
          title: "Test Analyst Insight",
          providers: {
            finimize: {
              id: faker.string.uuid(),
              contentType: FinimizeContentTypeEnum.INSIGHT,
              publishedAt: new Date()
            },
            contentful: {
              id: faker.string.uuid(),
              spaceId: faker.string.uuid(),
              environmentId: faker.string.uuid()
            }
          }
        });

        // Build a custom Contentful response with a controlled content field.
        const customContentfulResponse = {
          items: [
            {
              sys: { id: contentEntry.providers?.contentful?.id ?? "" },
              fields: {
                slug: "test-analyst-insight",
                publishedAt: new Date().toISOString(),
                title: "Test Analyst Insight",
                // Pass in our raw markdown so that the generated HTML contains four paragraphs (plus the disclaimer)
                content: rawMarkdown,
                headerImage: "http://example.com/header.png",
                bannerImage: "http://example.com/banner.png",
                readingTime: "1 min"
              }
            }
          ]
        };

        // Spy on ContentfulRetrievalService.LearnHubInstance.getEntriesByIds to return our custom response.
        jest
          .spyOn(ContentfulRetrievalService.LearnHubInstance, "getEntriesByIds")
          .mockImplementation(async (contentType: ContentfulContentTypeEnum, ids: string[]): Promise<any> => {
            if (contentType === ContentfulContentTypeEnum.CONTENT_ENTRY) {
              const matchingItems = customContentfulResponse.items.filter((item) => ids.includes(item.sys.id));
              return Promise.resolve({ items: matchingItems });
            }
          });

        // Clear any cached analyst insights before making the request.
        await WealthyhubService.deleteAnalystInsightsCache();

        const response = await request(app)
          .get("/api/m2m/wealthyhub/analyst-insights?page=1")
          .set("external-user-id", user._id)
          .set("Accept", "application/json")
          .expect(200);

        const responseBody = JSON.parse(response.text);

        // Since only one content entry exists, total items is 1.
        // The pagination utility returns the actual number of items on this page as pageSize.
        expect(responseBody.pagination).toMatchObject({
          page: 1,
          pageSize: 1,
          pages: 1,
          total: 1
        });

        // Count the number of <p> tags in the returned HTML.
        const contentHTML = responseBody.data[0].contentHTML;
        const paragraphMatches: RegExpMatchArray | null = contentHTML.match(/<p[^>]*>[\s\S]*?<\/p>/gi);
        // We expect only three paragraphs to be present (the trimmed content)
        expect(paragraphMatches?.length).toBe(3);

        // Verify that the trimmed content includes the first three paragraphs and excludes the fourth.
        expect(contentHTML).toContain("Paragraph one");
        expect(contentHTML).toContain("Paragraph two");
        expect(contentHTML).toContain("Paragraph three");
        expect(contentHTML).not.toContain("Paragraph four");
      });
    });
  });

  describe("GET /wealthyhub/learning-guides", () => {
    let user: UserDocument;

    const contentfulResponse = buildContentfulLearningGuidesResponse();

    beforeAll(async () => {
      user = await buildUser();

      jest
        .spyOn(ContentfulRetrievalService.LearnHubInstance, "getEntries")
        .mockImplementation(async (contentType: ContentfulContentTypeEnum): Promise<any> => {
          if (contentType === ContentfulContentTypeEnum.LEARNING_GUIDE) {
            return Promise.resolve(contentfulResponse);
          }
        });
    });
    afterAll(async () => await clearDb());

    it("should return 200 with learning guides if contentful returns valid entries and cache is empty", async () => {
      await RedisClientService.Instance.set("learningGuides", "");

      const response = await request(app)
        .get("/api/m2m/wealthyhub/learning-guides")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toBe(200);
      expect(JSON.parse(response.text)).toMatchObject({
        data: [
          {
            key: contentfulResponse.items[0].fields.slug,
            contentType: ContentfulContentTypeEnum.LEARNING_GUIDE,
            title: contentfulResponse.items[0].fields.title,
            description: contentfulResponse.items[0].fields.description,
            chapterCount: (contentfulResponse.items[0].fields.chapters as []).length,
            backgroundColor: contentfulResponse.items[0].fields.backgroundColor,
            guideIconURL: `https:${(contentfulResponse.items[0].fields.guideIcon as any)?.fields?.file?.url}`,
            mobileCoverImageURL: `https:${(contentfulResponse.items[0].fields.mobileCoverImage as any)?.fields?.file?.url}`,
            webCoverImageURL: `https:${(contentfulResponse.items[0].fields.webCoverImage as any)?.fields?.file?.url}`,
            slug: contentfulResponse.items[0].fields.slug
          }
        ]
      });
    });

    it("should return 200 with learning guides using cached response", async () => {
      await RedisClientService.Instance.set("learningGuides", contentfulResponse);

      const response = await request(app)
        .get("/api/m2m/wealthyhub/learning-guides")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(ContentfulRetrievalService.LearnHubInstance.getEntries).not.toHaveBeenCalled();

      expect(response.status).toBe(200);
      expect(JSON.parse(response.text)).toMatchObject({
        data: [
          {
            key: contentfulResponse.items[0].fields.slug,
            contentType: ContentfulContentTypeEnum.LEARNING_GUIDE,
            title: contentfulResponse.items[0].fields.title,
            description: contentfulResponse.items[0].fields.description,
            chapterCount: (contentfulResponse.items[0].fields.chapters as []).length,
            backgroundColor: contentfulResponse.items[0].fields.backgroundColor,
            guideIconURL: `https:${(contentfulResponse.items[0].fields.guideIcon as any)?.fields?.file?.url}`,
            mobileCoverImageURL: `https:${(contentfulResponse.items[0].fields.mobileCoverImage as any)?.fields?.file?.url}`,
            webCoverImageURL: `https:${(contentfulResponse.items[0].fields.webCoverImage as any)?.fields?.file?.url}`,
            slug: contentfulResponse.items[0].fields.slug
          }
        ]
      });
    });
  });

  describe("GET /wealthyhub/learning-guides/slug/:slug", () => {
    let userWithSubscription: UserDocument;
    let userWithoutSubscription: UserDocument;
    const learningGuideSlug = "test-learning-guide";

    // Create a fake rich text document that produces four paragraphs when transformed
    const fakeRichTextDoc = {
      nodeType: "document",
      data: {},
      content: [
        {
          nodeType: "paragraph",
          content: [{ nodeType: "text", value: "Paragraph one", marks: [] }],
          data: {}
        },
        {
          nodeType: "paragraph",
          content: [{ nodeType: "text", value: "Paragraph two", marks: [] }],
          data: {}
        },
        {
          nodeType: "paragraph",
          content: [{ nodeType: "text", value: "Paragraph three", marks: [] }],
          data: {}
        },
        {
          nodeType: "paragraph",
          content: [{ nodeType: "text", value: "Paragraph four", marks: [] }],
          data: {}
        }
      ]
    };

    // Prepare a fake Contentful response for the learning guide
    const contentfulGuideResponse = {
      sys: { id: faker.string.uuid() },
      fields: {
        slug: learningGuideSlug,
        title: "Test Learning Guide",
        description: "Test description",
        backgroundColor: "#FFFFFF",
        guideIcon: { fields: { file: { url: "/guide-icon.png" } } },
        mobileCoverImage: { fields: { file: { url: "/mobile-cover.png" } } },
        webCoverImage: { fields: { file: { url: "/web-cover.png" } } },
        chapters: [
          {
            fields: {
              title: "Chapter One",
              slug: "chapter-one",
              body: fakeRichTextDoc
            }
          }
        ]
      }
    };

    beforeAll(async () => {
      // Create one user without a subscription and one with a paid subscription.
      userWithoutSubscription = await buildUser();
      userWithSubscription = await buildUser();
      await buildSubscription({ owner: userWithSubscription._id, price: "paid_mid_monthly" });
    });

    afterEach(async () => {
      // Clean any cached learning guide for our slug.
      await RedisClientService.Instance.del(`learningGuide:${learningGuideSlug}`);
    });

    it("should return full chapter content for a subscribed user", async () => {
      // Spy on getEntryBySlug to control the Contentful response.
      jest
        .spyOn(ContentfulRetrievalService.LearnHubInstance, "getEntryBySlug")
        .mockImplementation(async (slug: string): Promise<any> => {
          if (slug === learningGuideSlug) {
            return Promise.resolve(contentfulGuideResponse);
          }
        });

      const response = await request(app)
        .get(`/api/m2m/wealthyhub/learning-guides/slug/${learningGuideSlug}`)
        .set("external-user-id", userWithSubscription._id)
        .set("Accept", "application/json")
        .expect(200);

      const responseBody = JSON.parse(response.text);

      // Verify that the guide fields match the Contentful response.
      expect(responseBody.data).toMatchObject({
        key: contentfulGuideResponse.fields.slug,
        contentType: ContentfulContentTypeEnum.LEARNING_GUIDE,
        title: contentfulGuideResponse.fields.title,
        description: contentfulGuideResponse.fields.description,
        backgroundColor: contentfulGuideResponse.fields.backgroundColor,
        guideIconURL: `https:${contentfulGuideResponse.fields.guideIcon.fields.file.url}`,
        mobileCoverImageURL: `https:${contentfulGuideResponse.fields.mobileCoverImage.fields.file.url}`,
        webCoverImageURL: `https:${contentfulGuideResponse.fields.webCoverImage.fields.file.url}`,
        slug: contentfulGuideResponse.fields.slug,
        chapterCount: contentfulGuideResponse.fields.chapters.length
      });

      // For a subscribed user the chapter content should not be trimmed.
      const chapterBody = responseBody.data.chapters[0].body;
      const paragraphs = chapterBody.match(/<p[^>]*>[\s\S]*?<\/p>/gi);
      expect(paragraphs?.length).toBe(4);
      expect(chapterBody).toContain("Paragraph one");
      expect(chapterBody).toContain("Paragraph two");
      expect(chapterBody).toContain("Paragraph three");
      expect(chapterBody).toContain("Paragraph four");
    });

    it("should return trimmed chapter content for a non-subscribed user", async () => {
      // Spy on getEntryBySlug to control the Contentful response.
      jest
        .spyOn(ContentfulRetrievalService.LearnHubInstance, "getEntryBySlug")
        .mockImplementation(async (slug: string): Promise<any> => {
          if (slug === learningGuideSlug) {
            return Promise.resolve(contentfulGuideResponse);
          }
        });

      const response = await request(app)
        .get(`/api/m2m/wealthyhub/learning-guides/slug/${learningGuideSlug}`)
        .set("external-user-id", userWithoutSubscription._id)
        .set("Accept", "application/json")
        .expect(200);

      const responseBody = JSON.parse(response.text);

      // For a non-subscribed user the chapter content should be trimmed to three paragraphs.
      const chapterBody = responseBody.data.chapters[0].body;
      const paragraphs = chapterBody.match(/<p[^>]*>[\s\S]*?<\/p>/gi);
      expect(paragraphs?.length).toBe(3);
      expect(chapterBody).toContain("Paragraph one");
      expect(chapterBody).toContain("Paragraph two");
      expect(chapterBody).toContain("Paragraph three");
      expect(chapterBody).not.toContain("Paragraph four");
    });

    describe("when using cached response", () => {
      it("should return full chapter content for a subscribed user using cached response", async () => {
        // Set the cached learning guide response without stringifying.
        await RedisClientService.Instance.set(`learningGuide:${learningGuideSlug}`, contentfulGuideResponse);

        // Spy on getEntryBySlug to ensure it is not called when using cache.
        const spy = jest.spyOn(ContentfulRetrievalService.LearnHubInstance, "getEntryBySlug");

        const response = await request(app)
          .get(`/api/m2m/wealthyhub/learning-guides/slug/${learningGuideSlug}`)
          .set("external-user-id", userWithSubscription._id)
          .set("Accept", "application/json")
          .expect(200);

        // Assert that the cached value is used and getEntryBySlug is not called.
        expect(spy).not.toHaveBeenCalled();

        const responseBody = JSON.parse(response.text);
        const chapterBody = responseBody.data.chapters[0].body;
        const paragraphs = chapterBody.match(/<p[^>]*>[\s\S]*?<\/p>/gi);
        expect(paragraphs?.length).toBe(4);
        spy.mockRestore();
      });

      it("should return trimmed chapter content for a non-subscribed user using cached response", async () => {
        // Set the cached learning guide response without stringifying.
        await RedisClientService.Instance.set(`learningGuide:${learningGuideSlug}`, contentfulGuideResponse);

        const spy = jest.spyOn(ContentfulRetrievalService.LearnHubInstance, "getEntryBySlug");

        const response = await request(app)
          .get(`/api/m2m/wealthyhub/learning-guides/slug/${learningGuideSlug}`)
          .set("external-user-id", userWithoutSubscription._id)
          .set("Accept", "application/json")
          .expect(200);

        // Assert that the cached value is used and getEntryBySlug is not called.
        expect(spy).not.toHaveBeenCalled();

        const responseBody = JSON.parse(response.text);
        const chapterBody = responseBody.data.chapters[0].body;
        const paragraphs = chapterBody.match(/<p[^>]*>[\s\S]*?<\/p>/gi);
        expect(paragraphs?.length).toBe(3);
        spy.mockRestore();
      });
    });
  });

  describe("GET /wealthyhub/news", () => {
    let user: UserDocument;
    let bondsNewsArticle, metaNewsArticle: LearnNewsDocument;

    beforeAll(async () => {
      user = await buildUser();
    });
    afterAll(async () => await clearDb());
    afterEach(async () => await clearDb());

    it("should return 200 with news if learn news documents exist", async () => {
      [bondsNewsArticle, metaNewsArticle] = await Promise.all([
        buildLearnNews({
          title: "Bond managers race to factor in weather woes",
          htmlContent: "<p>Bond news content</p>",
          date: new Date("2023-08-31T11:00:00Z"),
          imageUrl:
            "https://images.ctfassets.net/haw0z39aqcwg/3rypnZJKsnZotyz5VJ2nz7/3ee42fe7a3ff96d7249354de013dbd99/Featured_image__4x.png",
          readingTime: "1 min"
        }),
        buildLearnNews({
          title: "Meta to reorganise teams and reduce headcount for the first time ever",
          htmlContent: "<p>Meta news content</p>",
          date: new Date("2023-08-30T11:00:00Z"),
          imageUrl:
            "https://images.ctfassets.net/haw0z39aqcwg/3rypnZJKsnZotyz5VJ2nz7/3ee42fe7a3ff96d7249354de013dbd99/Featured_image__4x.png",
          readingTime: "1 min"
        })
      ]);

      const response = await request(app)
        .get("/api/m2m/wealthyhub/news")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toBe(200);

      expect(JSON.parse(response.text)).toMatchObject({
        data: [
          expect.objectContaining({
            key: bondsNewsArticle.hash,
            contentHTML: `<body style='font-family: Poppins,serif; font-size: 14px !important;'><style> p { color: #757575; } strong { color: black; font-weight: 500; } b { color: black; font-weight: 500; } h1,h2,h3,h4,h5,h6 { font-weight: 500; font-size: 18px; margin-top: 2em; margin-bottom: 1em; } h1:first-of-type, h2:first-of-type, h3:first-of-type, h4:first-of-type, h5:first-of-type, h6:first-of-type { margin-top: 0em !important; padding-top: 0 !important; }</style><link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet">${bondsNewsArticle.htmlContent}</body>`,
            contentType: ContentfulContentTypeEnum.NEWS,
            createdAt: bondsNewsArticle.date.toISOString(),
            fullImageURL: bondsNewsArticle.imageUrl,
            previewImageURL: `https://images.wealthyhood.cloud/cdn-cgi/image/width=288,height=512,fit=cover/${bondsNewsArticle.imageUrl}`,
            storyImageURL: `https://images.wealthyhood.cloud/cdn-cgi/image/width=288,height=288,fit=cover/${bondsNewsArticle.imageUrl}`,
            previewTitleMain: bondsNewsArticle.title,
            readingTime: bondsNewsArticle.readingTime,
            title: bondsNewsArticle.title
          }),
          expect.objectContaining({
            key: metaNewsArticle.hash,
            contentHTML: `<body style='font-family: Poppins,serif; font-size: 14px !important;'><style> p { color: #757575; } strong { color: black; font-weight: 500; } b { color: black; font-weight: 500; } h1,h2,h3,h4,h5,h6 { font-weight: 500; font-size: 18px; margin-top: 2em; margin-bottom: 1em; } h1:first-of-type, h2:first-of-type, h3:first-of-type, h4:first-of-type, h5:first-of-type, h6:first-of-type { margin-top: 0em !important; padding-top: 0 !important; }</style><link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet">${metaNewsArticle.htmlContent}</body>`,
            contentType: ContentfulContentTypeEnum.NEWS,
            createdAt: metaNewsArticle.date.toISOString(),
            fullImageURL: metaNewsArticle.imageUrl,
            previewImageURL: `https://images.wealthyhood.cloud/cdn-cgi/image/width=288,height=512,fit=cover/${metaNewsArticle.imageUrl}`,
            storyImageURL: `https://images.wealthyhood.cloud/cdn-cgi/image/width=288,height=288,fit=cover/${metaNewsArticle.imageUrl}`,
            previewTitleMain: metaNewsArticle.title,
            readingTime: metaNewsArticle.readingTime,
            title: metaNewsArticle.title
          })
        ]
      });
    });
  });

  describe("GET /wealthyhub/glossary", () => {
    let user: UserDocument;

    const CONTENTFUL_CONFIG = [
      {
        createdAt: new Date("2023-08-30T11:00:00Z"),
        definition: "Cash Flow definition",
        title: "Cash Flow"
      },
      {
        createdAt: new Date("2023-08-31T11:00:00Z"),
        definition: "Yield definition",
        title: "Yield"
      }
    ];
    const contentfulResponse = buildContentfulGlossaryResponse(CONTENTFUL_CONFIG);

    beforeAll(async () => {
      user = await buildUser();

      jest
        .spyOn(ContentfulRetrievalService.LandingPageInstance, "getEntries")
        .mockImplementation(async (contentType: ContentfulContentTypeEnum): Promise<any> => {
          if (contentType === ContentfulContentTypeEnum.GLOSSARY) {
            return Promise.resolve(contentfulResponse);
          }
        });
    });
    afterAll(async () => await clearDb());

    it("should return 200 with glossary items if contentful returns valid entries and cache is empty", async () => {
      await RedisClientService.Instance.set("glossaryItems", "");

      const response = await request(app)
        .get("/api/m2m/wealthyhub/glossary")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toBe(200);
      expect(JSON.parse(response.text)).toMatchObject({
        data: [
          {
            key: "cash-flow",
            definitionHTML: expect.stringContaining(CONTENTFUL_CONFIG[0].definition),
            contentType: ContentfulContentTypeEnum.GLOSSARY,
            createdAt: CONTENTFUL_CONFIG[0].createdAt.toISOString(),
            title: contentfulResponse.items[0].fields.title
          },
          {
            key: "yield",
            definitionHTML: expect.stringContaining(CONTENTFUL_CONFIG[1].definition),
            contentType: ContentfulContentTypeEnum.GLOSSARY,
            createdAt: CONTENTFUL_CONFIG[1].createdAt.toISOString(),
            title: contentfulResponse.items[1].fields.title
          }
        ]
      });
    });

    it("should return 200 with glossary items using cached response", async () => {
      await RedisClientService.Instance.set("glossaryItems", contentfulResponse);

      const response = await request(app)
        .get("/api/m2m/wealthyhub/glossary")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(ContentfulRetrievalService.LandingPageInstance.getEntries).not.toHaveBeenCalled();

      expect(response.status).toBe(200);
      expect(JSON.parse(response.text)).toMatchObject({
        data: [
          {
            key: "cash-flow",
            definitionHTML: expect.stringContaining(CONTENTFUL_CONFIG[0].definition),
            contentType: ContentfulContentTypeEnum.GLOSSARY,
            createdAt: CONTENTFUL_CONFIG[0].createdAt.toISOString(),
            title: contentfulResponse.items[0].fields.title
          },
          {
            key: "yield",
            definitionHTML: expect.stringContaining(CONTENTFUL_CONFIG[1].definition),
            contentType: ContentfulContentTypeEnum.GLOSSARY,
            createdAt: CONTENTFUL_CONFIG[1].createdAt.toISOString(),
            title: contentfulResponse.items[1].fields.title
          }
        ]
      });
    });
  });

  describe("GET /wealthyhub/me/help-centre", () => {
    let userUK: UserDocument;
    let userEU: UserDocument;

    const contentfulResponse = buildContentfulFaqCategoryResponse([
      {
        order: 1,
        title: "About Wealthyhood",
        subtitle: "About Wealthyhood subtitle",
        key: "ABOUT_WEALTHYHOOD",
        locale: "uk"
      },
      {
        order: 2,
        title: "Plans and Pricing",
        subtitle: "Plans and Pricing subtitle",
        key: "PLANS_AND_PRICING",
        locale: "uk"
      },
      {
        order: 1,
        title: "Execution Policy",
        subtitle: "Execution Policy subtitle",
        key: "EXECUTION_POLICY",
        locale: "eu"
      },
      {
        order: 2,
        title: "Referrals and free shares",
        subtitle: "Referrals and free shares subtitle",
        key: "REFERRALS",
        locale: "eu"
      }
    ]);

    beforeAll(async () => {
      userUK = await buildUser();
      userEU = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE });

      jest
        .spyOn(ContentfulRetrievalService.LandingPageInstance, "getEntries")
        .mockImplementation(async (contentType: ContentfulContentTypeEnum): Promise<any> => {
          if (contentType === ContentfulContentTypeEnum.FAQ_CATEGORY) {
            return Promise.resolve(contentfulResponse);
          }
        });
    });
    afterAll(async () => await clearDb());

    it("should return 200 with help centre categories if contentful returns valid entries and cache is empty", async () => {
      await RedisClientService.Instance.set("faqCategories", "");

      const response = await request(app)
        .get("/api/m2m/wealthyhub/me/help-centre")
        .set("external-user-id", userUK._id)
        .set("Accept", "application/json");

      expect(response.status).toBe(200);
      expect(JSON.parse(response.text)).toMatchObject({
        data: [
          expect.objectContaining({
            key: "ABOUT_WEALTHYHOOD",
            title: "About Wealthyhood",
            subtitle: "About Wealthyhood subtitle",
            faqs: expect.arrayContaining([
              expect.objectContaining({
                question: expect.any(String),
                answer: expect.any(String)
              })
            ])
          }),
          expect.objectContaining({
            title: "Plans and Pricing",
            subtitle: "Plans and Pricing subtitle",
            key: "PLANS_AND_PRICING",
            faqs: expect.arrayContaining([
              expect.objectContaining({
                question: expect.any(String),
                answer: expect.any(String)
              })
            ])
          })
        ]
      });
    });

    it("should return 200 with help centre categories using cached response", async () => {
      const responseBeforeCaching = await request(app)
        .get("/api/m2m/wealthyhub/me/help-centre")
        .set("external-user-id", userUK._id)
        .set("Accept", "application/json");

      await RedisClientService.Instance.set("faqCategories", JSON.parse(responseBeforeCaching.text).data);

      const response = await request(app)
        .get("/api/m2m/wealthyhub/me/help-centre")
        .set("external-user-id", userUK._id)
        .set("Accept", "application/json");

      expect(ContentfulRetrievalService.LandingPageInstance.getEntries).not.toHaveBeenCalled();

      expect(response.status).toBe(200);
      expect(JSON.parse(response.text)).toMatchObject({
        data: [
          expect.objectContaining({
            title: "About Wealthyhood",
            subtitle: "About Wealthyhood subtitle",
            key: "ABOUT_WEALTHYHOOD",
            faqs: expect.arrayContaining([
              expect.objectContaining({
                question: expect.any(String),
                answer: expect.any(String)
              })
            ])
          }),
          expect.objectContaining({
            title: "Plans and Pricing",
            subtitle: "Plans and Pricing subtitle",
            key: "PLANS_AND_PRICING",
            faqs: expect.arrayContaining([
              expect.objectContaining({
                question: expect.any(String),
                answer: expect.any(String)
              })
            ])
          })
        ]
      });
    });

    it("should return 200 with correct help centre categories when user's company entity is EUROPE", async () => {
      await RedisClientService.Instance.set("faqCategories", "");

      const response = await request(app)
        .get("/api/m2m/wealthyhub/me/help-centre")
        .set("external-user-id", userEU._id)
        .set("Accept", "application/json");

      expect(response.status).toBe(200);
      expect(JSON.parse(response.text)).toMatchObject({
        data: [
          expect.objectContaining({
            title: "Execution Policy",
            subtitle: "Execution Policy subtitle",
            key: "EXECUTION_POLICY",
            faqs: expect.arrayContaining([
              expect.objectContaining({
                question: expect.any(String),
                answer: expect.any(String)
              })
            ])
          }),
          expect.objectContaining({
            title: "Referrals and free shares",
            subtitle: "Referrals and free shares subtitle",
            key: "REFERRALS",
            faqs: expect.arrayContaining([
              expect.objectContaining({
                question: expect.any(String),
                answer: expect.any(String)
              })
            ])
          })
        ]
      });
    });
  });
});
