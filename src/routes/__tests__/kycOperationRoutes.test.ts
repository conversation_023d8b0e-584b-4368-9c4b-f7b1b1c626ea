import request from "supertest";
import app from "../../app";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildAccount, buildKycOperation, buildUser } from "../../tests/utils/generateModels";
import { faker } from "@faker-js/faker";
import { KycOperation } from "../../models/KycOperation";
import { ProviderEnum } from "../../configs/providersConfig";
import { SumsubService } from "../../external-services/sumsubService";

describe("KycOperationRoutes", () => {
  const _startup = () => {
    jest.resetAllMocks();
  };

  const _teardown = async () => {
    await clearDb();
  };

  beforeAll(async () => await connectDb("KycOperationRoutes"));
  afterAll(async () => await closeDb());

  describe("GET /kyc-operations/me", () => {
    beforeEach(_startup);
    afterEach(_teardown);

    it("should return 404, if user does not have a KYC operation", async () => {
      const user = await buildUser({
        providers: {
          sumsub: {
            id: faker.string.uuid()
          }
        },
        residencyCountry: "GR"
      });

      const response = await request(app)
        .get("/api/m2m/kyc-operations/me")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(404);
    });

    it("should return 200 if the user has a KYC operation", async () => {
      const ACCOUNT_ID = "sumsub-account-id";
      const SUBMISSION_DATE = new Date("2023-02-02");

      const user = await buildUser(
        { residencyCountry: "GB", providers: { sumsub: { id: ACCOUNT_ID } } },
        false,
        true
      );
      const [kycOperation] = await Promise.all([
        buildKycOperation({
          owner: user._id,
          activeProviders: [ProviderEnum.SUMSUB],
          providers: {
            sumsub: {
              submittedAt: SUBMISSION_DATE
            }
          }
        }),
        buildAccount({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        }) // Active WK Account
      ]);

      const response = await request(app)
        .get("/api/m2m/kyc-operations/me")
        .set("external-user-id", user.id)
        .set("Accept", "application/json")
        .expect(200);

      expect(JSON.parse(response.text)).toMatchObject({
        id: kycOperation?.id,
        status: kycOperation?.status,
        isJourneyCompleted: kycOperation?.isJourneyCompleted
      });
    });
  });

  describe("POST /kyc-operations/initiate", () => {
    beforeEach(_startup);
    afterEach(_teardown);

    it("should return 409, if user has already passed KYC", async () => {
      const user = await buildUser({
        providers: {
          sumsub: {
            id: faker.string.uuid()
          }
        },
        residencyCountry: "GR"
      });
      await buildKycOperation({
        owner: user.id,
        providers: {
          sumsub: {
            status: "completed",
            decision: "GREEN"
          }
        }
      });

      const response = await request(app)
        .post("/api/m2m/kyc-operations/initiate")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(409);
    });

    it("should return 200, if a kyc operation has been initiated", async () => {
      const SDK_TOKEN = "sdk-token";

      const user = await buildUser({ residencyCountry: "GR" });
      jest.spyOn(SumsubService.Instance, "generateAccessToken").mockResolvedValue({ token: SDK_TOKEN });

      const response = await request(app)
        .post("/api/m2m/kyc-operations/initiate")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text)).toEqual({
        sdkToken: SDK_TOKEN
      });

      const kycOperations = await KycOperation.find({ owner: user.id });
      expect(kycOperations.length).toEqual(1);
    });
  });
});
