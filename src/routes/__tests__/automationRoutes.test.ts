import request from "supertest";
import supertest from "supertest";
import app from "../../app";
import { faker } from "@faker-js/faker";
import { UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildDepositCashTransaction,
  buildHoldingDTO,
  buildMandate,
  buildPortfolio,
  buildRebalanceAutomation,
  buildSavingsTopUpAutomation,
  buildSubscription,
  buildTopUpAutomation,
  buildUser
} from "../../tests/utils/generateModels";
import { MandateDocument } from "../../models/Mandate";
import { AutomationDocument, TopUpAutomationDocument } from "../../models/Automation";
import {
  AssetTransaction,
  DepositCashTransaction,
  DepositCashTransactionDocument,
  SavingsTopupTransaction,
  Transaction
} from "../../models/Transaction";
import mongoose from "mongoose";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { Order } from "../../models/Order";
import { ASSET_CONFIG } from "@wealthyhood/shared-configs/dist/investmentUniverse";
import { PortfolioAllocationMethodEnum } from "../../services/portfolioService";
import DateUtil from "../../utils/dateUtil";

describe("AutomationRoutes", () => {
  beforeAll(async () => await connectDb("AutomationRoutes"));
  afterAll(async () => await closeDb());

  describe("GET /automations", () => {
    describe("when there is no automation for that user", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();
        await buildPortfolio({ owner: owner.id });
      });
      afterAll(async () => await clearDb());

      it("should return 200 and an empty data array", async () => {
        const response = await request(app)
          .get("/api/m2m/automations")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({
          data: []
        });
      });
    });

    describe("when there is an automation for that user", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let automation: TopUpAutomationDocument;

      beforeAll(async () => {
        owner = await buildUser();
        await buildPortfolio({ owner: owner.id });
        mandate = await buildMandate({ owner: owner.id, bankAccount: owner.bankAccounts[0].id });

        automation = await buildTopUpAutomation({ owner: owner.id, mandate: mandate.id });
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the automation", async () => {
        const response = await request(app)
          .get("/api/m2m/automations")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject({
          data: [
            expect.objectContaining({
              owner: owner.id,
              mandate: expect.objectContaining({
                _id: mandate.id
              }),
              category: "TopUpAutomation",
              frequency: "monthly",
              consideration: {
                amount: automation.consideration.amount,
                currency: "GBP"
              }
            })
          ]
        });
      });
    });
  });

  describe("POST /automations", () => {
    describe("when the request body is empty", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post("/api/m2m/automations")
          .send({})
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");

        expect(response.status).toBe(400);
      });
    });

    describe("when category is TopUpAutomation but the Allocation method is target and portfolio allocation is not setup", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser();
        await buildPortfolio({
          owner: owner.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          cash: { GBP: { available: 51, reserved: 0, settled: 0 } },
          initialHoldingsAllocation: [], // empty target allocation
          holdings: [await buildHoldingDTO(true, "equities_uk", 1, { price: 10 })]
        });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Subscription"
        });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "TopUpAutomation",
            frequency: "monthly",
            allocationMethod: PortfolioAllocationMethodEnum.TARGET_ALLOCATION,
            mandate: mandate.id,
            orderAmount: 50
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");

        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "Target allocation is not set up for this portfolio",
              description: "Operation failed"
            }
          })
        );
      });
    });

    describe("when category is TopUpAutomation but the mandate is not of Top-Up category", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser();
        await buildPortfolio({ owner: owner.id });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Subscription"
        });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "TopUpAutomation",
            frequency: "monthly",
            mandate: mandate.id,
            orderAmount: 50
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");

        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "You need to set-up recurring top-up with a valid top-up mandate",
              description: "Operation failed"
            }
          })
        );
      });
    });

    describe("when category is TopUpAutomation, there is already a cancelled automation for that user, there is day of month in the request body and it is in the next 3 days", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let existingAutomation: AutomationDocument;
      let response: supertest.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        const TODAY = new Date("2022-07-22T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser();
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Top-Up"
        });

        await buildSubscription({ owner: owner.id });
        const portfolio = await buildPortfolio({
          owner: owner.id,
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)],
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });

        existingAutomation = await buildTopUpAutomation({
          owner: owner.id,
          category: "TopUpAutomation",
          portfolio: portfolio.id,
          mandate: mandate.id,
          active: false
        });

        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "TopUpAutomation",
            dayOfMonth: -1,
            frequency: "monthly",
            mandate: mandate.id,
            orderAmount: 50
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the updated automation", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            _id: existingAutomation.id,
            owner: owner.id,
            mandate: expect.objectContaining({
              _id: mandate.id
            }),
            category: "TopUpAutomation",
            frequency: "monthly",
            dayOfMonth: -1,
            consideration: {
              amount: 5000,
              currency: "GBP"
            },
            active: true
          })
        );
      });

      it("should emit an 'recurringInvestmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.automation.recurringInvestmentCreation.eventId,
          expect.objectContaining({ id: owner.id }),
          {
            amount: 50,
            currency: "GBP"
          }
        );
      });
    });

    describe("when category is TopUpAutomation, there is already a cancelled automation for that user, there is day of month in the request body and it is NOT in the next 3 days", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let existingAutomation: AutomationDocument;
      let response: supertest.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        const TODAY = new Date("2022-07-22T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser();
        const portfolio = await buildPortfolio({ owner: owner.id });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Top-Up"
        });

        existingAutomation = await buildTopUpAutomation({
          owner: owner.id,
          category: "TopUpAutomation",
          portfolio: portfolio.id,
          mandate: mandate.id,
          active: false
        });

        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "TopUpAutomation",
            frequency: "monthly",
            dayOfMonth: 25,
            mandate: mandate.id,
            orderAmount: 50
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the updated automation", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            _id: existingAutomation.id,
            owner: owner.id,
            mandate: expect.objectContaining({
              _id: mandate.id
            }),
            category: "TopUpAutomation",
            frequency: "monthly",
            dayOfMonth: 25,
            consideration: {
              amount: 5000,
              currency: "GBP"
            },
            active: true
          })
        );
      });

      it("should emit an 'recurringInvestmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.automation.recurringInvestmentCreation.eventId,
          expect.objectContaining({ id: owner.id }),
          {
            amount: 50,
            currency: "GBP"
          }
        );
      });
    });

    describe("when category is TopUpAutomation and there is already an active automation for that user and there is day of month in the request body", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let response: supertest.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser();
        const portfolio = await buildPortfolio({ owner: owner.id });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Top-Up"
        });

        await buildTopUpAutomation({
          owner: owner.id,
          category: "TopUpAutomation",
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: 22,
          consideration: {
            amount: 2000,
            currency: "GBP"
          }
        });

        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "TopUpAutomation",
            frequency: "monthly",
            dayOfMonth: 25,
            mandate: mandate.id,
            orderAmount: 50
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the updated automation", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            owner: owner.id,
            mandate: expect.objectContaining({
              _id: mandate.id
            }),
            category: "TopUpAutomation",
            frequency: "monthly",
            dayOfMonth: 25,
            consideration: {
              amount: 5000,
              currency: "GBP"
            }
          })
        );
      });

      it("should NOT emit a 'recurringInvestmentCreation' event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.automation.recurringInvestmentCreation.eventId,
          expect.anything(),
          expect.anything(),
          expect.anything()
        );
      });
    });

    describe("when category is TopUpAutomation and there is already an active automation for that user and there is no day of month in the request body", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let response: supertest.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser();
        const portfolio = await buildPortfolio({ owner: owner.id });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Top-Up"
        });

        await buildTopUpAutomation({
          owner: owner.id,
          category: "TopUpAutomation",
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: 22,
          consideration: {
            amount: 2000,
            currency: "GBP"
          }
        });

        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "TopUpAutomation",
            frequency: "monthly",
            mandate: mandate.id,
            orderAmount: 50
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the updated automation, with unchanged day of month", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            owner: owner.id,
            mandate: expect.objectContaining({
              _id: mandate.id
            }),
            category: "TopUpAutomation",
            frequency: "monthly",
            dayOfMonth: 22,
            consideration: {
              amount: 5000,
              currency: "GBP"
            }
          })
        );
      });

      it("should NOT emit a 'recurringInvestmentCreation' event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.automation.recurringInvestmentCreation.eventId,
          expect.anything(),
          expect.anything(),
          expect.anything()
        );
      });
    });

    describe("when category is TopUpAutomation, there is no automation for that user, there is day of month in the request body and it is in the next 3 days", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let response: supertest.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");

        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser();
        await buildSubscription({ owner: owner.id });
        await buildPortfolio({
          owner: owner.id,
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)],
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Top-Up"
        });
        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "TopUpAutomation",
            frequency: "monthly",
            mandate: mandate.id,
            dayOfMonth: -1,
            orderAmount: 50
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 with the created automation, create a deposit linked to that automation & an asset transaction pending that deposit", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            owner: owner.id,
            mandate: expect.objectContaining({
              _id: mandate.id
            }),
            category: "TopUpAutomation",
            frequency: "monthly",
            dayOfMonth: -1,
            consideration: {
              amount: 5000,
              currency: "GBP"
            }
          })
        );

        const automationId = JSON.parse(response.text)._id;

        const createdDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automationId });
        expect(createdDeposit.toObject()).toEqual(
          expect.objectContaining({
            owner: owner._id,
            bankAccount: owner.bankAccounts[0]._id,
            consideration: {
              amount: 5000,
              currency: "GBP"
            },
            linkedAutomation: new mongoose.Types.ObjectId(automationId)
          })
        );

        const createdAssetTransaction = await AssetTransaction.findOne({ pendingDeposit: createdDeposit.id });
        expect(createdAssetTransaction).toEqual(
          expect.objectContaining({
            owner: owner._id,
            pendingDeposit: createdDeposit._id,
            consideration: {
              amount: 5000,
              currency: "GBP"
            },
            linkedAutomation: new mongoose.Types.ObjectId(automationId)
          })
        );
      });

      it("should emit an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ email: owner.email }),
          expect.objectContaining({
            isFirst: true,
            side: "buy",
            category: "portfolio",
            frequency: "repeating",
            amount: 50,
            currency: "GBP",
            fxFees: 0
          })
        );
      });

      it("should emit a 'recurringInvestmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.automation.recurringInvestmentCreation.eventId,
          expect.objectContaining({ id: owner.id }),
          {
            amount: 50,
            currency: "GBP"
          }
        );
      });

      it("should emit a first deposit created event and a deposit created event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.firstDepositCreation.eventId,
          expect.objectContaining({ email: owner.email })
        );

        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.depositCreation.eventId,
          expect.objectContaining({ email: owner.email }),
          { isFirst: true, amount: 50, currency: "GBP", truelayerId: owner.bankAccounts[0].truelayerProviderId },
          { triggeredByAutomation: true }
        );
      });
    });

    describe("when category is TopUpAutomation, there is no automation for that user, there is day of month in the request body and it is NOT in the next 3 days", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let response: supertest.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");

        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser();
        await buildSubscription({ owner: owner.id });
        await buildPortfolio({
          owner: owner.id,
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)],
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Top-Up"
        });
        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "TopUpAutomation",
            frequency: "monthly",
            mandate: mandate.id,
            dayOfMonth: 25,
            orderAmount: 50
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 with the created automation and not create any transaction", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            owner: owner.id,
            mandate: expect.objectContaining({
              _id: mandate.id
            }),
            category: "TopUpAutomation",
            frequency: "monthly",
            dayOfMonth: 25,
            consideration: {
              amount: 5000,
              currency: "GBP"
            }
          })
        );

        const automationId = JSON.parse(response.text)._id;

        const createdTransactions = await Transaction.find({ linkedAutomation: automationId });
        expect(createdTransactions.length).toBe(0);
      });

      it("should NOT emit an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ email: owner.email }),
          expect.anything()
        );
      });

      it("should emit a 'recurringInvestmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.automation.recurringInvestmentCreation.eventId,
          expect.objectContaining({ id: owner.id }),
          {
            amount: 50,
            currency: "GBP"
          }
        );
      });

      it("should NOT emit a first deposit created event and a deposit created event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transaction.firstDepositCreation.eventId,
          expect.objectContaining({ email: owner.email })
        );

        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transaction.depositCreation.eventId,
          expect.objectContaining({ email: owner.email }),
          expect.anything(),
          expect.anything()
        );
      });
    });

    describe("when category is TopUpAutomation, there is no automation for that user and there is no day of month in the request body", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let response: supertest.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");

        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser();
        await buildSubscription({ owner: owner.id });
        await buildPortfolio({
          owner: owner.id,
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)],
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Top-Up"
        });

        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "TopUpAutomation",
            frequency: "monthly",
            mandate: mandate.id,
            orderAmount: 50
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 with the created automation, create a deposit linked to that automation & an asset transaction pending that deposit", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            owner: owner.id,
            mandate: expect.objectContaining({
              _id: mandate.id
            }),
            category: "TopUpAutomation",
            frequency: "monthly",
            dayOfMonth: -1,
            consideration: {
              amount: 5000,
              currency: "GBP"
            }
          })
        );

        const automationId = JSON.parse(response.text)._id;

        const createdDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automationId });
        expect(createdDeposit.toObject()).toEqual(
          expect.objectContaining({
            owner: owner._id,
            bankAccount: owner.bankAccounts[0]._id,
            consideration: {
              amount: 5000,
              currency: "GBP"
            },
            linkedAutomation: new mongoose.Types.ObjectId(automationId)
          })
        );

        const createdAssetTransaction = await AssetTransaction.findOne({ pendingDeposit: createdDeposit.id });
        expect(createdAssetTransaction).toEqual(
          expect.objectContaining({
            owner: owner._id,
            pendingDeposit: createdDeposit._id,
            consideration: {
              amount: 5000,
              currency: "GBP"
            },
            linkedAutomation: new mongoose.Types.ObjectId(automationId)
          })
        );
      });

      it("should emit an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ email: owner.email }),
          expect.objectContaining({
            isFirst: true,
            side: "buy",
            category: "portfolio",
            frequency: "repeating",
            amount: 50,
            currency: "GBP",
            fxFees: 0
          })
        );
      });

      it("should emit a 'recurringInvestmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.automation.recurringInvestmentCreation.eventId,
          expect.objectContaining({ id: owner.id }),
          {
            amount: 50,
            currency: "GBP"
          }
        );
      });

      it("should emit a first deposit created event and a deposit created event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.firstDepositCreation.eventId,
          expect.objectContaining({ email: owner.email })
        );

        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.depositCreation.eventId,
          expect.objectContaining({ email: owner.email }),
          { isFirst: true, amount: 50, currency: "GBP", truelayerId: owner.bankAccounts[0].truelayerProviderId },
          { triggeredByAutomation: true }
        );
      });
    });

    describe("when category is TopUpAutomation with allocation method 'targetAllocation' and there is no automation for that user", () => {
      let response: supertest.Response;
      let owner: UserDocument;
      let mandate: MandateDocument;

      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
        percentage: number;
      }[] = [
        { assetId: "equities_global_clean_energy", quantity: 2, price: 10, percentage: 30 },
        { assetId: "equities_eu", quantity: 2, price: 10, percentage: 30 },
        { assetId: "real_estate_us", quantity: 2, price: 10, percentage: 40 }
      ];

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");

        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser();
        await buildSubscription({ owner: owner.id });

        const holdings = await Promise.all(
          ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
            buildHoldingDTO(true, assetId, quantity, { price })
          )
        );

        await buildPortfolio({
          owner: owner.id,
          holdings,
          initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          })),
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Top-Up"
        });

        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "TopUpAutomation",
            frequency: "monthly",
            mandate: mandate.id,
            allocationMethod: "targetAllocation",
            orderAmount: 100
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 with the created automation, create a deposit linked to that automation & an asset transaction pending that deposit", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            owner: owner.id,
            mandate: expect.objectContaining({
              _id: mandate.id
            }),
            category: "TopUpAutomation",
            frequency: "monthly",
            allocationMethod: "targetAllocation",
            dayOfMonth: -1,
            consideration: {
              amount: 10000,
              currency: "GBP"
            }
          })
        );

        const automationId = JSON.parse(response.text)._id;

        const createdDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automationId });
        expect(createdDeposit.toObject()).toEqual(
          expect.objectContaining({
            owner: owner._id,
            bankAccount: owner.bankAccounts[0]._id,
            consideration: {
              amount: 10000,
              currency: "GBP"
            },
            linkedAutomation: new mongoose.Types.ObjectId(automationId)
          })
        );

        const createdAssetTransaction = await AssetTransaction.findOne({ pendingDeposit: createdDeposit.id });
        expect(createdAssetTransaction).toEqual(
          expect.objectContaining({
            owner: owner._id,
            pendingDeposit: createdDeposit._id,
            consideration: {
              amount: 10000,
              currency: "GBP"
            },
            linkedAutomation: new mongoose.Types.ObjectId(automationId)
          })
        );
      });

      it("should create valid number of buy orders with properly weighted amount", async () => {
        const orders = await Order.find({});
        expect(orders.length).toBe(3);

        const sortByIsin = (arr: any[]) => arr.sort((a, b) => (a.isin > b.isin ? 1 : -1));

        const expectedOrders = sortByIsin(
          ASSET_COMMON_IDS_CONFIG.map((config) => ({
            isin: ASSET_CONFIG[config.assetId].isin,
            side: "Buy",
            consideration: expect.objectContaining({
              originalAmount: config.percentage * 100,
              currency: "GBP"
            })
          }))
        );

        expect(sortByIsin(orders)).toEqual(expectedOrders.map((order) => expect.objectContaining(order)));
      });

      it("should emit an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ email: owner.email }),
          expect.objectContaining({
            isFirst: true,
            side: "buy",
            category: "portfolio",
            frequency: "repeating",
            amount: 100,
            // FX fee: Only government_bonds_us (40%) requires FX conversion
            // equities_eu + equities_china are traded in GBP, so no FX fee
            // £100 × 40% × 0.0015 = £0.06
            fxFees: 0.06,
            commissionFees: 0
          })
        );
      });

      it("should emit a 'recurringInvestmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.automation.recurringInvestmentCreation.eventId,
          expect.objectContaining({ id: owner.id }),
          {
            amount: 100,
            currency: "GBP"
          }
        );
      });

      it("should emit a first deposit created event and a deposit created event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.firstDepositCreation.eventId,
          expect.objectContaining({ email: owner.email })
        );

        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.depositCreation.eventId,
          expect.objectContaining({ email: owner.email }),
          { isFirst: true, amount: 100, currency: "GBP", truelayerId: owner.bankAccounts[0].truelayerProviderId },
          { triggeredByAutomation: true }
        );
      });
    });

    describe("when category is TopUpAutomation with flag postponeActivation false and there is no automation for that user", () => {
      let response: supertest.Response;
      let owner: UserDocument;
      let mandate: MandateDocument;

      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
        percentage: number;
      }[] = [
        { assetId: "equities_global_clean_energy", quantity: 2, price: 10, percentage: 30 },
        { assetId: "equities_eu", quantity: 2, price: 10, percentage: 30 },
        { assetId: "real_estate_us", quantity: 2, price: 10, percentage: 40 }
      ];

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");

        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser();
        await buildSubscription({ owner: owner.id });

        const holdings = await Promise.all(
          ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
            buildHoldingDTO(true, assetId, quantity, { price })
          )
        );

        await buildPortfolio({
          owner: owner.id,
          holdings,
          initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          })),
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Top-Up"
        });

        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "TopUpAutomation",
            frequency: "monthly",
            mandate: mandate.id,
            allocationMethod: "targetAllocation",
            orderAmount: 100,
            postponeActivation: false
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 with the created automation, create a deposit linked to that automation & an asset transaction pending that deposit", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            owner: owner.id,
            mandate: expect.objectContaining({
              _id: mandate.id
            }),
            category: "TopUpAutomation",
            frequency: "monthly",
            allocationMethod: "targetAllocation",
            dayOfMonth: -1,
            consideration: {
              amount: 10000,
              currency: "GBP"
            }
          })
        );

        const automationId = JSON.parse(response.text)._id;

        const createdDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automationId });
        expect(createdDeposit.toObject()).toEqual(
          expect.objectContaining({
            owner: owner._id,
            bankAccount: owner.bankAccounts[0]._id,
            consideration: {
              amount: 10000,
              currency: "GBP"
            },
            linkedAutomation: new mongoose.Types.ObjectId(automationId)
          })
        );

        const createdAssetTransaction = await AssetTransaction.findOne({ pendingDeposit: createdDeposit.id });
        expect(createdAssetTransaction).toEqual(
          expect.objectContaining({
            owner: owner._id,
            pendingDeposit: createdDeposit._id,
            consideration: {
              amount: 10000,
              currency: "GBP"
            },
            linkedAutomation: new mongoose.Types.ObjectId(automationId)
          })
        );
      });

      it("should create valid number of buy orders with properly weighted amount", async () => {
        const orders = await Order.find({});
        expect(orders.length).toBe(3);

        const sortByIsin = (arr: any[]) => arr.sort((a, b) => (a.isin > b.isin ? 1 : -1));

        const expectedOrders = sortByIsin(
          ASSET_COMMON_IDS_CONFIG.map((config) => ({
            isin: ASSET_CONFIG[config.assetId].isin,
            side: "Buy",
            consideration: expect.objectContaining({
              originalAmount: config.percentage * 100,
              currency: "GBP"
            })
          }))
        );

        expect(sortByIsin(orders)).toEqual(expectedOrders.map((order) => expect.objectContaining(order)));
      });

      it("should emit an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ email: owner.email }),
          expect.objectContaining({
            isFirst: true,
            side: "buy",
            category: "portfolio",
            frequency: "repeating",
            amount: 100,
            // FX fee: Only government_bonds_us (40%) requires FX conversion
            // equities_eu + equities_china are traded in GBP, so no FX fee
            // £100 × 40% × 0.0015 = £0.06
            fxFees: 0.06,
            commissionFees: 0
          })
        );
      });

      it("should emit a 'recurringInvestmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.automation.recurringInvestmentCreation.eventId,
          expect.objectContaining({ id: owner.id }),
          {
            amount: 100,
            currency: "GBP"
          }
        );
      });

      it("should emit a first deposit created event and a deposit created event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.firstDepositCreation.eventId,
          expect.objectContaining({ email: owner.email })
        );

        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.depositCreation.eventId,
          expect.objectContaining({ email: owner.email }),
          { isFirst: true, amount: 100, currency: "GBP", truelayerId: owner.bankAccounts[0].truelayerProviderId },
          { triggeredByAutomation: true }
        );
      });
    });

    describe("when category is TopUpAutomation with flag postponeActivation true and there is no automation for that user", () => {
      let response: supertest.Response;
      let owner: UserDocument;
      let mandate: MandateDocument;

      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
        percentage: number;
      }[] = [
        { assetId: "equities_global_clean_energy", quantity: 2, price: 10, percentage: 30 },
        { assetId: "equities_eu", quantity: 2, price: 10, percentage: 30 },
        { assetId: "real_estate_us", quantity: 2, price: 10, percentage: 40 }
      ];

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");

        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser();
        await buildSubscription({ owner: owner.id });

        const holdings = await Promise.all(
          ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
            buildHoldingDTO(true, assetId, quantity, { price })
          )
        );

        await buildPortfolio({
          owner: owner.id,
          holdings,
          initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          })),
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Top-Up"
        });

        const createdDeposits = await DepositCashTransaction.find({});
        expect(createdDeposits.length).toBe(0);
        const createdAssetTransactions = await AssetTransaction.find({});
        expect(createdAssetTransactions.length).toBe(0);

        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "TopUpAutomation",
            frequency: "monthly",
            mandate: mandate.id,
            allocationMethod: "targetAllocation",
            orderAmount: 100,
            postponeActivation: true
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 with the created automation, create a deposit linked to that automation & an asset transaction pending that deposit", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            owner: owner.id,
            mandate: expect.objectContaining({
              _id: mandate.id
            }),
            category: "TopUpAutomation",
            frequency: "monthly",
            allocationMethod: "targetAllocation",
            dayOfMonth: -1,
            consideration: {
              amount: 10000,
              currency: "GBP"
            },
            initialiseAt: expect.any(String)
          })
        );
      });

      it("should not create a deposit or asset transaction linked to this automation", async () => {
        const automationId = JSON.parse(response.text)._id;

        const createdDeposits = (await DepositCashTransaction.find({
          linkedAutomation: automationId
        })) as DepositCashTransactionDocument[];
        expect(createdDeposits.length).toBe(0);

        const createdAssetTransactions = await AssetTransaction.find({ linkedAutomation: automationId });
        expect(createdAssetTransactions.length).toBe(0);
      });

      it("should not emit an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.anything(),
          expect.anything()
        );
      });

      it("should emit a 'recurringInvestmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.automation.recurringInvestmentCreation.eventId,
          expect.objectContaining({ id: owner.id }),
          {
            amount: 100,
            currency: "GBP"
          }
        );
      });

      it("should not emit a first deposit created event and a deposit created event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transaction.firstDepositCreation.eventId,
          expect.anything()
        );

        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transaction.depositCreation.eventId,
          expect.anything(),
          expect.anything(),
          expect.anything()
        );
      });
    });

    describe("when category is TopUpAutomation with flag postponeActivation true and there is already an active automation for that user", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let response: supertest.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser();
        const portfolio = await buildPortfolio({ owner: owner.id });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Top-Up"
        });

        await buildTopUpAutomation({
          owner: owner.id,
          category: "TopUpAutomation",
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: 22,
          consideration: {
            amount: 2000,
            currency: "GBP"
          }
        });
        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "TopUpAutomation",
            frequency: "monthly",
            mandate: mandate.id,
            orderAmount: 50,
            postponeActivation: true
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the updated automation, with unchanged day of month", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            owner: owner.id,
            mandate: expect.objectContaining({
              _id: mandate.id
            }),
            category: "TopUpAutomation",
            frequency: "monthly",
            dayOfMonth: 22,
            consideration: {
              amount: 5000,
              currency: "GBP"
            }
          })
        );
      });

      it("should NOT emit a 'recurringInvestmentCreation' event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.automation.recurringInvestmentCreation.eventId,
          expect.anything(),
          expect.anything(),
          expect.anything()
        );
      });
    });

    describe("when category is TopUpAutomation with flag postponeActivation true and there is already a cancelled automation for that user", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let existingAutomation: AutomationDocument;
      let response: supertest.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser();
        const portfolio = await buildPortfolio({ owner: owner.id });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Top-Up"
        });

        existingAutomation = await buildTopUpAutomation({
          owner: owner.id,
          category: "TopUpAutomation",
          portfolio: portfolio.id,
          mandate: mandate.id,
          active: false
        });
        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "TopUpAutomation",
            frequency: "monthly",
            mandate: mandate.id,
            orderAmount: 50,
            postponeActivation: true
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the updated automation", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            _id: existingAutomation.id,
            owner: owner.id,
            mandate: expect.objectContaining({
              _id: mandate.id
            }),
            category: "TopUpAutomation",
            frequency: "monthly",
            dayOfMonth: -1,
            consideration: {
              amount: 5000,
              currency: "GBP"
            },
            active: true,
            initialiseAt: expect.any(String)
          })
        );
      });

      it("should emit an 'recurringInvestmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.automation.recurringInvestmentCreation.eventId,
          expect.objectContaining({ id: owner.id }),
          {
            amount: 50,
            currency: "GBP"
          }
        );
      });
    });

    describe("when category is SavingsTopUpAutomation but the mandate is not of Top-Up category", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser();
        await buildPortfolio({ owner: owner.id });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Subscription"
        });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "SavingsTopUpAutomation",
            frequency: "monthly",
            mandate: mandate.id,
            orderAmount: 50,
            savingsProduct: "mmf_dist_gbp"
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");

        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "You need to set-up recurring top-up with a valid top-up mandate",
              description: "Operation failed"
            }
          })
        );
      });
    });

    describe("when category is SavingsTopUpAutomation but savingsProduct is missing", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser();
        await buildPortfolio({ owner: owner.id });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Top-Up"
        });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "SavingsTopUpAutomation",
            frequency: "monthly",
            mandate: mandate.id,
            orderAmount: 50
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");

        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "Param 'savingsProduct' is required",
              description: "Invalid parameter"
            }
          })
        );
      });
    });

    describe("when category is SavingsTopUpAutomation, there is already a cancelled automation for that user & savings product and selected day of month is in the next 3 days", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let existingAutomation: AutomationDocument;
      let response: supertest.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        const TODAY = new Date("2022-07-23T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser();
        const portfolio = await buildPortfolio({ owner: owner.id });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Top-Up"
        });

        existingAutomation = await buildSavingsTopUpAutomation({
          owner: owner.id,
          category: "SavingsTopUpAutomation",
          portfolio: portfolio.id,
          mandate: mandate.id,
          active: false
        });
        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "SavingsTopUpAutomation",
            frequency: "monthly",
            dayOfMonth: 25,
            mandate: mandate.id,
            orderAmount: 50,
            savingsProduct: "mmf_dist_gbp"
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the updated automation", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            _id: existingAutomation.id,
            owner: owner.id,
            mandate: expect.objectContaining({
              _id: mandate.id
            }),
            category: "SavingsTopUpAutomation",
            frequency: "monthly",
            dayOfMonth: 25,
            consideration: {
              amount: 5000,
              currency: "GBP"
            },
            active: true
          })
        );
      });

      it("should create a new deposit & savings top-up", async () => {
        const deposits = await DepositCashTransaction.find({ linkedAutomation: existingAutomation.id });
        expect(deposits.length).toBe(1);

        const savingsTopUps = await SavingsTopupTransaction.find({
          pendingDeposit: deposits[0].id
        });
        expect(savingsTopUps.length).toBe(1);
      });

      it("should emit an 'recurringInvestmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.automation.recurringSavingsCreation.eventId,
          expect.objectContaining({ id: owner.id }),
          {
            amount: 50,
            currency: "GBP"
          }
        );
      });
    });

    describe("when category is SavingsTopUpAutomation, there is already a cancelled automation for that user & savings product and selected day of month is in the next 3 days but there is already a top-up created for that month", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let existingAutomation: AutomationDocument;
      let response: supertest.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        const TODAY = new Date("2022-07-23T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser();
        const portfolio = await buildPortfolio({ owner: owner.id });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Top-Up"
        });

        existingAutomation = await buildSavingsTopUpAutomation({
          owner: owner.id,
          category: "SavingsTopUpAutomation",
          portfolio: portfolio.id,
          mandate: mandate.id,
          active: false
        });

        await buildDepositCashTransaction({
          owner: owner.id,
          portfolio: portfolio.id,
          linkedAutomation: existingAutomation._id,
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 2)
        });

        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "SavingsTopUpAutomation",
            frequency: "monthly",
            dayOfMonth: 25,
            mandate: mandate.id,
            orderAmount: 50,
            savingsProduct: "mmf_dist_gbp"
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the updated automation", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            _id: existingAutomation.id,
            owner: owner.id,
            mandate: expect.objectContaining({
              _id: mandate.id
            }),
            category: "SavingsTopUpAutomation",
            frequency: "monthly",
            dayOfMonth: 25,
            consideration: {
              amount: 5000,
              currency: "GBP"
            },
            active: true
          })
        );
      });

      it("should NOT create a new deposit & savings top-up", async () => {
        const deposits = await DepositCashTransaction.find({ linkedAutomation: existingAutomation.id });
        expect(deposits.length).toBe(1);

        const savingsTopUps = await SavingsTopupTransaction.find({
          pendingDeposit: deposits[0].id
        });
        expect(savingsTopUps.length).toBe(0);
      });

      it("should NOT emit an 'recurringInvestmentCreation' event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.automation.recurringSavingsCreation.eventId,
          expect.objectContaining({ id: owner.id }),
          expect.anything()
        );
      });
    });

    describe("when category is SavingsTopUpAutomation, there is already a cancelled automation for that user & savings product and selected day of month is NOT in the next 3 days", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let existingAutomation: AutomationDocument;
      let response: supertest.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser();
        const portfolio = await buildPortfolio({ owner: owner.id });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Top-Up"
        });

        existingAutomation = await buildSavingsTopUpAutomation({
          owner: owner.id,
          category: "SavingsTopUpAutomation",
          portfolio: portfolio.id,
          mandate: mandate.id,
          active: false
        });
        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "SavingsTopUpAutomation",
            frequency: "monthly",
            dayOfMonth: 25,
            mandate: mandate.id,
            orderAmount: 50,
            savingsProduct: "mmf_dist_gbp"
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the updated automation", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            _id: existingAutomation.id,
            owner: owner.id,
            mandate: expect.objectContaining({
              _id: mandate.id
            }),
            category: "SavingsTopUpAutomation",
            frequency: "monthly",
            dayOfMonth: 25,
            consideration: {
              amount: 5000,
              currency: "GBP"
            },
            active: true
          })
        );
      });

      it("should NOT create a new deposit & savings top-up", async () => {
        const deposits = await DepositCashTransaction.find({ linkedAutomation: existingAutomation.id });
        expect(deposits.length).toBe(0);

        const savingsTopUps = await SavingsTopupTransaction.find({ linkedAutomation: existingAutomation });
        expect(savingsTopUps.length).toBe(0);
      });

      it("should NOT emit an 'recurringInvestmentCreation' event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.automation.recurringSavingsCreation.eventId,
          expect.objectContaining({ id: owner.id }),
          expect.anything()
        );
      });
    });

    describe("when category is SavingsTopUpAutomation and there is already an active automation for that user & savings product", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let response: supertest.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser();
        const portfolio = await buildPortfolio({ owner: owner.id });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Top-Up"
        });

        await buildSavingsTopUpAutomation({
          owner: owner.id,
          category: "SavingsTopUpAutomation",
          portfolio: portfolio.id,
          mandate: mandate.id,
          dayOfMonth: 22,
          consideration: {
            amount: 2000,
            currency: "GBP"
          }
        });
        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "SavingsTopUpAutomation",
            frequency: "monthly",
            dayOfMonth: 25,
            mandate: mandate.id,
            orderAmount: 50,
            savingsProduct: "mmf_dist_gbp"
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 and the updated automation", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            owner: owner.id,
            mandate: expect.objectContaining({
              _id: mandate.id
            }),
            category: "SavingsTopUpAutomation",
            frequency: "monthly",
            dayOfMonth: 25,
            consideration: {
              amount: 5000,
              currency: "GBP"
            }
          })
        );
      });

      it("should NOT emit a 'recurringInvestmentCreation' event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.automation.recurringSavingsCreation.eventId,
          expect.anything(),
          expect.anything(),
          expect.anything()
        );
      });
    });

    describe("when category is SavingsTopUpAutomation, there is no automation for that user and selected day of month is NOT the next 3 days", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let response: supertest.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");

        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser();
        await buildSubscription({ owner: owner.id });
        await buildPortfolio({
          owner: owner.id,
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)],
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Top-Up"
        });
        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "SavingsTopUpAutomation",
            frequency: "monthly",
            mandate: mandate.id,
            dayOfMonth: 10,
            orderAmount: 50,
            savingsProduct: "mmf_dist_gbp"
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 with the created automation and NOT create any transactions linked to automation", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            owner: owner.id,
            mandate: expect.objectContaining({
              _id: mandate.id
            }),
            category: "SavingsTopUpAutomation",
            frequency: "monthly",
            dayOfMonth: 10,
            consideration: {
              amount: 5000,
              currency: "GBP"
            }
          })
        );

        const automationId = JSON.parse(response.text)._id;

        const createdDeposits = await DepositCashTransaction.find({ linkedAutomation: automationId });
        expect(createdDeposits.length).toBe(0);

        const createdSavingsTransactions = await SavingsTopupTransaction.find({ owner: owner.id });
        expect(createdSavingsTransactions.length).toBe(0);
      });

      it("should emit a 'recurringSavingsCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.automation.recurringSavingsCreation.eventId,
          expect.objectContaining({ id: owner.id }),
          {
            amount: 50,
            currency: "GBP"
          }
        );
      });

      it("should NOT emit an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.anything(),
          expect.anything()
        );
      });

      it("should NOT emit a first deposit created event and a deposit created event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transaction.firstDepositCreation.eventId,
          expect.anything()
        );

        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transaction.depositCreation.eventId,
          expect.anything(),
          expect.anything(),
          expect.anything()
        );
      });
    });

    describe("when category is SavingsTopUpAutomation, there is no automation for that user and selected day of month is in the next 3 days", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let response: supertest.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");

        const TODAY = new Date("2022-07-29T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser();
        await buildSubscription({ owner: owner.id });
        await buildPortfolio({
          owner: owner.id,
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)],
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        mandate = await buildMandate({
          owner: owner.id,
          bankAccount: owner.bankAccounts[0].id,
          category: "Top-Up"
        });
        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "SavingsTopUpAutomation",
            frequency: "monthly",
            mandate: mandate.id,
            dayOfMonth: -1,
            orderAmount: 50,
            savingsProduct: "mmf_dist_gbp"
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 with the created automation, create a deposit linked to that automation & a savings top up transaction pending that deposit", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            owner: owner.id,
            mandate: expect.objectContaining({
              _id: mandate.id
            }),
            category: "SavingsTopUpAutomation",
            frequency: "monthly",
            dayOfMonth: -1,
            consideration: {
              amount: 5000,
              currency: "GBP"
            }
          })
        );

        const automationId = JSON.parse(response.text)._id;

        const createdDeposit = await DepositCashTransaction.findOne({ linkedAutomation: automationId });
        expect(createdDeposit.toObject()).toEqual(
          expect.objectContaining({
            owner: owner._id,
            bankAccount: owner.bankAccounts[0]._id,
            consideration: {
              amount: 5000,
              currency: "GBP"
            },
            linkedAutomation: new mongoose.Types.ObjectId(automationId)
          })
        );

        const createdSavingsTopUpTransaction = await SavingsTopupTransaction.findOne({
          pendingDeposit: createdDeposit.id
        });
        expect(createdSavingsTopUpTransaction.toObject()).toEqual(
          expect.objectContaining({
            owner: owner._id,
            pendingDeposit: createdDeposit._id,
            consideration: {
              amount: 5000,
              currency: "GBP"
            },
            linkedAutomation: new mongoose.Types.ObjectId(automationId)
          })
        );
      });

      it("should emit a 'recurringSavingsCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.automation.recurringSavingsCreation.eventId,
          expect.objectContaining({ id: owner.id }),
          {
            amount: 50,
            currency: "GBP"
          }
        );
      });

      it("should emit an 'investmentCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ email: owner.email }),
          expect.objectContaining({
            side: "buy",
            category: "savings",
            assetName: "mmf_dist_gbp",
            frequency: "repeating",
            amount: 50,
            currency: "GBP",
            fxFees: 0
          })
        );
      });

      it("should emit a first deposit created event and a deposit created event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.firstDepositCreation.eventId,
          expect.objectContaining({ email: owner.email })
        );

        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.depositCreation.eventId,
          expect.objectContaining({ email: owner.email }),
          { isFirst: true, amount: 50, currency: "GBP", truelayerId: owner.bankAccounts[0].truelayerProviderId },
          { triggeredByAutomation: true }
        );
      });
    });

    describe("when category is RebalanceAutomation and there is already a disabled rebalance automation for that user", () => {
      let owner: UserDocument;
      let response: supertest.Response;
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
        percentage: number;
      }[] = [
        { assetId: "equities_global_clean_energy", quantity: 2, price: 10, percentage: 30 },
        { assetId: "equities_eu", quantity: 2, price: 10, percentage: 30 },
        { assetId: "real_estate_us", quantity: 2, price: 10, percentage: 40 }
      ];

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser();
        const holdings = await Promise.all(
          ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
            buildHoldingDTO(true, assetId, quantity, { price })
          )
        );

        await buildPortfolio({
          owner: owner.id,
          holdings,
          initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          })),
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });

        // User already has a rebalance automation
        await buildRebalanceAutomation({
          owner: owner.id,
          active: false
        });
        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "RebalanceAutomation",
            frequency: "monthly"
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 with the existing automation enabled", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            owner: owner.id,
            category: "RebalanceAutomation",
            frequency: "monthly",
            active: true
          })
        );
      });

      it("should NOT emit a 'recurringRebalanceCreation' event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.automation.recurringInvestmentCreation.eventId,
          expect.anything(),
          expect.anything(),
          expect.anything()
        );
      });
    });

    describe("when category is RebalanceAutomation and there is already an enabled rebalance automation for that user", () => {
      let owner: UserDocument;
      let response: supertest.Response;
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
        percentage: number;
      }[] = [
        { assetId: "equities_global_clean_energy", quantity: 2, price: 10, percentage: 30 },
        { assetId: "equities_eu", quantity: 2, price: 10, percentage: 30 },
        { assetId: "real_estate_us", quantity: 2, price: 10, percentage: 40 }
      ];

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser();
        const holdings = await Promise.all(
          ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
            buildHoldingDTO(true, assetId, quantity, { price })
          )
        );

        await buildPortfolio({
          owner: owner.id,
          holdings,
          initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          })),
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });

        // User already has a rebalance automation
        await buildRebalanceAutomation({
          owner: owner.id,
          active: true
        });
        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "RebalanceAutomation",
            frequency: "monthly"
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 with the existing automation", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            owner: owner.id,
            category: "RebalanceAutomation",
            frequency: "monthly",
            active: true
          })
        );
      });

      it("should NOT emit a 'recurringRebalanceCreation' event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.automation.recurringInvestmentCreation.eventId,
          expect.anything(),
          expect.anything(),
          expect.anything()
        );
      });
    });

    describe("when category is RebalanceAutomation and there is no automation for that user", () => {
      let owner: UserDocument;
      let response: supertest.Response;
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
        percentage: number;
      }[] = [
        { assetId: "equities_global_clean_energy", quantity: 2, price: 10, percentage: 30 },
        { assetId: "equities_eu", quantity: 2, price: 10, percentage: 30 },
        { assetId: "real_estate_us", quantity: 2, price: 10, percentage: 40 }
      ];

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser();
        const holdings = await Promise.all(
          ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
            buildHoldingDTO(true, assetId, quantity, { price })
          )
        );

        await buildPortfolio({
          owner: owner.id,
          holdings,
          initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          })),
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "RebalanceAutomation",
            frequency: "monthly"
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return 200 with the created automation", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            owner: owner.id,
            category: "RebalanceAutomation",
            frequency: "monthly"
          })
        );
      });

      it("should emit a 'recurringRebalanceCreation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.automation.recurringRebalanceCreation.eventId,
          expect.objectContaining({ id: owner.id })
        );
      });
    });

    describe("when category is RebalanceAutomation and there is a user with no portfolio target allocation", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser();
        await buildPortfolio({
          owner: owner.id,
          holdings: [await buildHoldingDTO(true, "equities_uk", 1)],
          initialHoldingsAllocation: []
        });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post("/api/m2m/automations")
          .send({
            category: "RebalanceAutomation"
          })
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");

        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "Target allocation is not set up for this portfolio",
              description: "Operation failed"
            }
          })
        );
      });
    });
  });

  describe("POST /automations/:id/cancel", () => {
    describe("when the automation does not belong to user", () => {
      let someOtherUser: UserDocument;
      let owner: UserDocument;
      let mandate: MandateDocument;
      let automation: AutomationDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");

        someOtherUser = await buildUser();
        owner = await buildUser();
        const portfolio = await buildPortfolio({ owner: owner.id });
        mandate = await buildMandate({ owner: owner.id, bankAccount: owner.bankAccounts[0].id });
        automation = await buildTopUpAutomation({ owner: owner.id, portfolio: portfolio.id, mandate: mandate.id });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .post(`/api/m2m/automations/${automation.id}/cancel`)
          .set("external-user-id", someOtherUser._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "User does not own this automation"
            }
          })
        );
      });
    });

    describe("when request is valid for top up automation", () => {
      let owner: UserDocument;
      let mandate: MandateDocument;
      let automation: AutomationDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser();
        const portfolio = await buildPortfolio({ owner: owner.id });
        mandate = await buildMandate({ owner: owner.id, bankAccount: owner.bankAccounts[0].id });
        automation = await buildTopUpAutomation({
          owner: owner.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          consideration: {
            amount: 3200,
            currency: "GBP"
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should return 200", async () => {
        const response = await request(app)
          .post(`/api/m2m/automations/${automation.id}/cancel`)
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
      });

      it("should emit a 'recurringInvestmentCancellation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.automation.recurringInvestmentCancellation.eventId,
          expect.objectContaining({ id: owner.id })
        );
      });
    });

    describe("when request is valid for rebalance automation", () => {
      let owner: UserDocument;
      let automation: AutomationDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(eventEmitter, "emit");

        owner = await buildUser();
        automation = await buildRebalanceAutomation({
          owner: owner.id,
          active: true
        });
      });
      afterAll(async () => await clearDb());

      it("should return 200", async () => {
        const response = await request(app)
          .post(`/api/m2m/automations/${automation.id}/cancel`)
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
      });

      it("should emit a 'recurringRebalanceCancellation' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.automation.recurringRebalanceCancellation.eventId,
          expect.objectContaining({ id: owner.id })
        );
      });
    });
  });
});
