import {
  buildAccount,
  buildAssetTransaction,
  buildBankAccount,
  buildCashbackTransaction,
  buildChargeTransaction,
  buildDepositCashTransaction,
  buildDividendTransaction,
  buildGift,
  buildHoldingDTO,
  buildInvestmentProduct,
  buildOrder,
  buildPortfolio,
  buildRebalanceTransaction,
  buildSubscription,
  buildUser,
  buildWealthyhoodDividendTransaction,
  buildWithdrawalCashTransaction
} from "../../tests/utils/generateModels";
import request from "supertest";
import supertest from "supertest";
import app from "../../app";
import {
  AssetTransaction,
  AssetTransactionDocument,
  CashbackTransactionDocument,
  ChargeTransaction,
  DepositCashTransaction,
  DepositCashTransactionDocument,
  DepositMethodEnum,
  RebalanceTransaction,
  RebalanceTransactionDocument,
  Transaction,
  TransactionCashActivityFilterEnum,
  TransactionDocument,
  TransactionStatusArray,
  WealthyhoodDividendTransaction,
  WealthyhoodDividendTransactionDocument
} from "../../models/Transaction";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { PaymentStatusTypeV3, TruelayerPaymentsClient } from "../../external-services/truelayerService";
import { buildPaymentType } from "../../tests/utils/generateTruelayer";
import { faker } from "@faker-js/faker";
import { TransactionService } from "../../services/transactionService";
import { Portfolio, PortfolioDocument, PortfolioModeEnum } from "../../models/Portfolio";
import logger from "../../external-services/loggerService";
import { BankAccountDocument } from "../../models/BankAccount";
import { KycStatusEnum, User, UserDocument } from "../../models/User";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import { PortfolioWrapperTypeEnum } from "../../external-services/wealthkernelService";
import { entitiesConfig, fees, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { DateTime } from "luxon";
import DateUtil from "../../utils/dateUtil";
import { ProviderEnum } from "../../configs/providersConfig";
import { Subscription } from "../../models/Subscription";
import { StripeService } from "../../external-services/stripeService";
import { RedisClientService } from "../../loaders/redis";
import { DepositActionEnum } from "../../configs/depositsConfig";
import { SaltedgeService } from "../../external-services/saltedgeService";

const { MINIMUM_FX_FEE } = fees;

describe("TransactionRoutes", () => {
  beforeAll(async () => await connectDb("TransactionRoutes"));
  afterAll(async () => await closeDb());

  describe("GET /transactions", () => {
    beforeEach(() => jest.clearAllMocks());
    afterEach(async () => await clearDb());

    it("/transactions?sort=-createdAt should return status 200 and transactions array", async () => {
      const user = await buildUser();
      await Promise.all([
        buildAssetTransaction({ owner: user.id }),
        buildDepositCashTransaction({ owner: user.id })
      ]);

      const response = await request(app)
        .get("/api/m2m/transactions?sort=-createdAt")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedData = await Transaction.find({}).sort({ createdAt: -1 });

      const transactionsReceived: TransactionDocument[] = JSON.parse(response.text);
      expect(transactionsReceived).toMatchObject(JSON.parse(JSON.stringify(expectedData)));

      const isArraySorted = (arr: any[]) => arr.slice(1).every((item, i) => arr[i] >= item); //desc order
      expect(isArraySorted(transactionsReceived.map((transaction) => new Date(transaction.createdAt)))).toEqual(
        true
      );
    });

    it("/transactions?portfolio=:portfolioId should return status 200 and transactions array", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });
      await Promise.all([
        buildAssetTransaction({ owner: user.id, portfolio }),
        buildDepositCashTransaction({ owner: user.id, portfolio }),
        buildAssetTransaction(),
        buildAssetTransaction()
      ]);

      const response = await request(app)
        .get(`/api/m2m/transactions?portfolio=${portfolio.id}`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedData = await Transaction.find({ portfolio: portfolio.id });

      expect(expectedData.length).toEqual(2);
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("/transactions?category=RebalanceTransaction should return status 200 and transactions array", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });
      await Promise.all([
        buildAssetTransaction({ owner: user.id, portfolio }),
        buildRebalanceTransaction({ owner: user.id, portfolio }),
        buildAssetTransaction(),
        buildAssetTransaction()
      ]);

      const response = await request(app)
        .get("/api/m2m/transactions?category=RebalanceTransaction")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedData = await Transaction.find({ category: "RebalanceTransaction" });
      expect(expectedData.length).toEqual(1);

      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("/transactions?category=RebalanceTransaction&status=Settled should return status 200 and transactions array", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });
      await Promise.all([
        buildAssetTransaction({ owner: user.id, portfolio }),
        buildRebalanceTransaction({ owner: user.id, portfolio, rebalanceStatus: "Settled" }),
        buildAssetTransaction(),
        buildAssetTransaction()
      ]);

      const response = await request(app)
        .get("/api/m2m/transactions?category=RebalanceTransaction&status=Settled")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedData = await Transaction.find({ category: "RebalanceTransaction" });
      expect(expectedData.length).toEqual(1);

      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("/transactions?portfolio=:portfolioId&owner=:userId should return status 200 and transactions array", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });
      await Promise.all([
        buildAssetTransaction({ owner: user.id, portfolio }),
        buildDepositCashTransaction({ owner: user.id }),
        buildAssetTransaction(),
        buildAssetTransaction()
      ]);

      const response = await request(app)
        .get(`/api/m2m/transactions?portfolio=${portfolio.id}&owner=${user.id}`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedData = await Transaction.find({ owner: user.id, portfolio: portfolio.id });

      expect(expectedData.length).toEqual(1);
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("/transactions?portfolio=:portfolioId&owner=:userId&populate=somethingthatdoesnotexist should return status 200 and transactions array", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });
      const transaction: AssetTransactionDocument = await buildAssetTransaction({ owner: user.id, portfolio });
      transaction.orders = [
        await buildOrder({
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
          side: "Sell",
          transaction: transaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          quantity: 2
        })
      ];
      await transaction.save();

      await Promise.all([
        buildDepositCashTransaction({ owner: user.id, portfolio }),
        buildAssetTransaction(),
        buildAssetTransaction()
      ]);

      const response = await request(app)
        .get(`/api/m2m/transactions?portfolio=${portfolio.id}&owner=${user.id}&populate=somethingthatdoesnotexist`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedData = await Transaction.find({ owner: user.id, portfolio: portfolio.id });

      expect(expectedData.length).toEqual(2);
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("/transactions?populate=orders for pending asset sell transaction should return status 200 and estimated displayed amount", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });
      const investmentProduct = await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
      const pendingAssetSellTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio,
        status: "Pending",
        portfolioTransactionCategory: "update",
        consideration: { currency: "GBP" }
      });

      pendingAssetSellTransaction.orders = [
        await buildOrder({
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
          side: "Sell",
          transaction: pendingAssetSellTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: undefined,
          quantity: 1
        })
      ];
      await pendingAssetSellTransaction.save();

      const response = await request(app)
        .get("/api/m2m/transactions?populate=orders")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const transactionsReceived: AssetTransactionDocument[] = JSON.parse(response.text);
      expect(transactionsReceived[0].displayAmount).toEqual(
        pendingAssetSellTransaction.orders[0].quantity *
          investmentProduct.currentTicker.pricePerCurrency["GBP"] *
          100
      );

      const order = transactionsReceived[0].orders[0];
      expect(order).toEqual(
        expect.objectContaining({
          displayAmount: order.quantity * investmentProduct.currentTicker.pricePerCurrency["GBP"] * 100
        })
      );
    });

    it("/transactions?populate=orders for settled sell transaction should return status 200 and displayed amount equal to consideration amount of order", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });
      const settledAssetSellTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio,
        status: "Settled",
        portfolioTransactionCategory: "update",
        consideration: { currency: "GBP" }
      });
      settledAssetSellTransaction.orders = [
        await buildOrder({
          status: "Matched",
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
          side: "Sell",
          transaction: settledAssetSellTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            amount: 983,
            currency: "GBP"
          },
          unitPrice: {
            amount: 1,
            currency: "GBP"
          },
          quantity: 1
        })
      ];
      await settledAssetSellTransaction.save();

      const response = await request(app)
        .get("/api/m2m/transactions?populate=orders")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const transactionsReceived: AssetTransactionDocument[] = JSON.parse(response.text);
      expect(transactionsReceived[0].displayAmount).toEqual(
        settledAssetSellTransaction.orders[0].consideration.amount
      );

      const order = transactionsReceived[0].orders[0];
      expect(order).toEqual(
        expect.objectContaining({
          displayAmount: order.consideration.amount,
          displayUnitPrice: {
            amount: order.consideration.amount / order.quantity / 100,
            currency: "GBP"
          }
        })
      );
    });

    it("/transactions?populate=orders for asset buy transaction with fees should return status 200 and displayed amount equal to original amount of order", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });
      const assetBuyTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio,
        status: "Settled",
        portfolioTransactionCategory: "update",
        consideration: { currency: "GBP" },
        fees: {
          fx: {
            amount: MINIMUM_FX_FEE,
            currency: "GBP"
          }
        }
      });

      assetBuyTransaction.orders = [
        await buildOrder({
          status: "Matched",
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
          side: "Buy",
          transaction: assetBuyTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            originalAmount: 1000,
            amountSubmitted: 983,
            amount: 983,
            currency: "GBP"
          },
          unitPrice: {
            amount: 1,
            currency: "GBP"
          },
          quantity: 1
        })
      ];

      await assetBuyTransaction.save();

      const response = await request(app)
        .get("/api/m2m/transactions?populate=orders")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const transactionsReceived: AssetTransactionDocument[] = JSON.parse(response.text);
      expect(transactionsReceived[0].displayAmount).toEqual(
        assetBuyTransaction.orders[0].consideration.originalAmount
      );

      const order = transactionsReceived[0].orders[0];
      expect(order).toEqual(
        expect.objectContaining({
          displayAmount: order.consideration.originalAmount,
          displayUnitPrice: {
            amount: order.consideration.originalAmount / order.quantity / 100,
            currency: "GBP"
          }
        })
      );
    });

    it("/transactions?populate=orders for asset buy transaction that was settled for less than original amount should return status 200 and displayed amount equal to submitted amount", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });
      const assetBuyTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio,
        status: "Settled",
        portfolioTransactionCategory: "update",
        consideration: { currency: "GBP" }
      });

      assetBuyTransaction.orders = [
        await buildOrder({
          status: "Matched",
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
          side: "Buy",
          transaction: assetBuyTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            amountSubmitted: 1000,
            amount: 999,
            currency: "GBP"
          },
          unitPrice: {
            amount: 1,
            currency: "GBP"
          },
          quantity: 1
        })
      ];

      await assetBuyTransaction.save();

      const response = await request(app)
        .get("/api/m2m/transactions?populate=orders")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const transactionsReceived: AssetTransactionDocument[] = JSON.parse(response.text);
      expect(transactionsReceived[0].displayAmount).toEqual(
        assetBuyTransaction.orders[0].consideration.amountSubmitted
      );

      const order = transactionsReceived[0].orders[0];
      expect(order).toEqual(
        expect.objectContaining({
          displayAmount: order.consideration.amountSubmitted,
          displayUnitPrice: {
            amount: order.consideration.amount / order.quantity / 100,
            currency: "GBP"
          }
        })
      );
    });

    it("/transactions?populate=orders for asset buy transaction that only has consideration amount should return status 200 and displayed amount equal to that consideration amount", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });
      const assetBuyTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio,
        status: "Settled",
        portfolioTransactionCategory: "update",
        consideration: { currency: "GBP" }
      });

      assetBuyTransaction.orders = [
        await buildOrder({
          status: "Matched",
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
          side: "Buy",
          transaction: assetBuyTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            amount: 1000,
            currency: "GBP"
          },
          unitPrice: {
            amount: 1,
            currency: "GBP"
          },
          quantity: 1
        })
      ];

      await assetBuyTransaction.save();

      const response = await request(app)
        .get("/api/m2m/transactions?populate=orders")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const transactionsReceived: AssetTransactionDocument[] = JSON.parse(response.text);
      expect(transactionsReceived[0].displayAmount).toEqual(assetBuyTransaction.orders[0].consideration.amount);

      const order = transactionsReceived[0].orders[0];
      expect(order).toEqual(
        expect.objectContaining({
          displayAmount: order.consideration.amount,
          displayUnitPrice: {
            amount: order.consideration.amount / order.quantity / 100,
            currency: "GBP"
          }
        })
      );
    });

    it("/transactions?populate=orders for pending portfolio sell transaction should return status 200 and displayed amount equal to original investment amount", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });
      const investmentProduct = await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
      const pendingPortfolioSellTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio,
        status: "Pending",
        portfolioTransactionCategory: "sell",
        originalInvestmentAmount: 1000,
        consideration: { currency: "GBP" }
      });

      pendingPortfolioSellTransaction.orders = [
        await buildOrder({
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
          side: "Sell",
          transaction: pendingPortfolioSellTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: undefined,
          quantity: 1
        })
      ];

      await pendingPortfolioSellTransaction.save();

      const response = await request(app)
        .get("/api/m2m/transactions?populate=orders")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const transactionsReceived: AssetTransactionDocument[] = JSON.parse(response.text);
      expect(transactionsReceived[0].displayAmount).toEqual(
        pendingPortfolioSellTransaction.originalInvestmentAmount
      );

      const order = transactionsReceived[0].orders[0];
      expect(order).toEqual(
        expect.objectContaining({
          displayAmount: investmentProduct.currentTicker.pricePerCurrency["GBP"] * order.quantity * 100
        })
      );
    });

    it("/transactions?populate=orders for settled portfolio sell transaction should return status 200 and displayed amount equal to consideration amount", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });
      const settledPortfolioSellTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio,
        status: "Settled",
        portfolioTransactionCategory: "sell",
        originalInvestmentAmount: 1000,
        consideration: { currency: "GBP", amount: 983 }
      });

      settledPortfolioSellTransaction.orders = [
        await buildOrder({
          status: "Matched",
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
          side: "Sell",
          transaction: settledPortfolioSellTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            amount: 983,
            currency: "GBP"
          },
          unitPrice: {
            amount: 1,
            currency: "GBP"
          },
          quantity: 1
        })
      ];

      await settledPortfolioSellTransaction.save();

      const response = await request(app)
        .get("/api/m2m/transactions?populate=orders")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const transactionsReceived: AssetTransactionDocument[] = JSON.parse(response.text);
      expect(transactionsReceived[0].displayAmount).toEqual(settledPortfolioSellTransaction.consideration.amount);

      const order = transactionsReceived[0].orders[0];
      expect(order).toEqual(
        expect.objectContaining({
          displayAmount: order.consideration.amount,
          displayUnitPrice: {
            amount: order.consideration.amount / order.quantity / 100,
            currency: "GBP"
          }
        })
      );
    });

    it("/transactions?populate=orders for cashback transaction should return status 200 and empty list if the cashback is linked to an incomplete 1-step investment", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });
      const incompleteDeposit = await buildDepositCashTransaction({
        owner: user.id,
        portfolio,
        consideration: { currency: "GBP", amount: 1000 },
        providers: { truelayer: { id: faker.string.uuid(), status: "authorization_required", version: "v3" } }
      });

      const pendingPortfolioBuyTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio,
        status: "Pending",
        pendingDeposit: incompleteDeposit.id,
        portfolioTransactionCategory: "buy",
        originalInvestmentAmount: 1000,
        consideration: { currency: "GBP", amount: 983 }
      });
      const settledPortfolioBuyTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio,
        status: "Settled",
        portfolioTransactionCategory: "buy",
        originalInvestmentAmount: 1000,
        consideration: { currency: "GBP", amount: 983 }
      });

      const cashbackToNotReturn = await buildCashbackTransaction({
        owner: user.id,
        portfolio,
        status: "Pending",
        linkedAssetTransaction: pendingPortfolioBuyTransaction.id,
        consideration: { currency: "GBP", amount: 2 }
      });
      const cashbackToReturn = await buildCashbackTransaction({
        owner: user.id,
        portfolio,
        status: "Pending",
        linkedAssetTransaction: settledPortfolioBuyTransaction.id,
        consideration: { currency: "GBP", amount: 2 }
      });

      const response = await request(app)
        .get("/api/m2m/transactions?populate=orders")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const transactionsReceived: AssetTransactionDocument[] = JSON.parse(response.text);
      expect(transactionsReceived).not.toContainEqual(expect.objectContaining({ id: cashbackToNotReturn.id }));
      expect(transactionsReceived).toContainEqual(expect.objectContaining({ id: cashbackToReturn.id }));
    });

    it("/transactions?populate=orders for portfolio buy transaction should return status 200 and displayed amount equal to original investment amount", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });
      const pendingPortfolioBuyTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio,
        status: "Pending",
        portfolioTransactionCategory: "buy",
        originalInvestmentAmount: 1000,
        consideration: { currency: "GBP", amount: 983 }
      });

      pendingPortfolioBuyTransaction.orders = [
        await buildOrder({
          status: "Matched",
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
          side: "Buy",
          transaction: pendingPortfolioBuyTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            originalAmount: 1000,
            amount: 983,
            currency: "GBP"
          },
          unitPrice: {
            amount: 1,
            currency: "GBP"
          },
          quantity: 1
        })
      ];

      await pendingPortfolioBuyTransaction.save();

      const response = await request(app)
        .get("/api/m2m/transactions?populate=orders")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const transactionsReceived: AssetTransactionDocument[] = JSON.parse(response.text);
      expect(transactionsReceived[0].displayAmount).toEqual(
        pendingPortfolioBuyTransaction.originalInvestmentAmount
      );

      const order = transactionsReceived[0].orders[0];
      expect(order).toEqual(
        expect.objectContaining({
          displayAmount: order.consideration.originalAmount,
          displayUnitPrice: {
            amount: order.consideration.originalAmount / order.quantity / 100,
            currency: "GBP"
          }
        })
      );
    });

    it("/transactions?populate=orders for rebalance transaction should return status 200 and no displayed amount", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });

      const rebalanceTransaction = await buildRebalanceTransaction({ owner: user.id, portfolio: portfolio.id });

      const [buyOrder, buyOrderWithFee, sellOrder] = await Promise.all([
        buildOrder({
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
          side: "Buy",
          transaction: rebalanceTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            originalAmount: 1000,
            amountSubmitted: 1000,
            amount: 999,
            currency: "GBP"
          },
          unitPrice: {
            amount: 1,
            currency: "GBP"
          },
          quantity: 1
        }),
        buildOrder({
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
          side: "Buy",
          transaction: rebalanceTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            originalAmount: 1000,
            amount: 983,
            currency: "GBP"
          },
          unitPrice: {
            amount: 1,
            currency: "GBP"
          },
          quantity: 1
        }),
        buildOrder({
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
          side: "Sell",
          transaction: rebalanceTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            amount: 1000,
            currency: "GBP"
          },
          unitPrice: {
            amount: 1,
            currency: "GBP"
          },
          quantity: 1
        })
      ]);

      const response = await request(app)
        .get("/api/m2m/transactions?populate=orders")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const transactionsReceived: RebalanceTransactionDocument[] = JSON.parse(response.text);
      expect(transactionsReceived[0].displayAmount).toBe(undefined);

      const orders = transactionsReceived[0].orders;
      expect(orders).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            displayAmount: buyOrder.consideration.amountSubmitted,
            displayUnitPrice: {
              amount: buyOrder.consideration.originalAmount / buyOrder.quantity / 100,
              currency: "GBP"
            }
          }),
          expect.objectContaining({
            displayAmount: buyOrderWithFee.consideration.originalAmount,
            displayUnitPrice: {
              amount: buyOrderWithFee.consideration.originalAmount / buyOrderWithFee.quantity / 100,
              currency: "GBP"
            }
          }),
          expect.objectContaining({
            displayAmount: sellOrder.consideration.amount,
            displayUnitPrice: {
              amount: sellOrder.consideration.amount / sellOrder.quantity / 100,
              currency: "GBP"
            }
          })
        ])
      );
    });

    it("/transactions?populate=orders for deposit, withdrawal and dividend transactions should return status 200 and displayed amounts equal to consideration amounts", async () => {
      const user = await buildUser();

      await Promise.all([
        buildDepositCashTransaction({ owner: user.id }),
        buildWithdrawalCashTransaction({ owner: user.id }),
        buildDividendTransaction({ owner: user.id })
      ]);

      const response = await request(app)
        .get("/api/m2m/transactions?populate=orders")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const transactionsReceived: TransactionDocument[] = JSON.parse(response.text);
      transactionsReceived.forEach((transaction) => {
        expect(transaction.displayAmount).toEqual(transaction.consideration.amount);
      });
    });

    it("/transactions?truelayerStatus=authorization_required&truelayerStatus=authorized should return status 200 and transactions array", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });
      await Promise.all([
        buildAssetTransaction({ owner: user.id, portfolio }),
        buildAssetTransaction(),
        buildDepositCashTransaction({
          owner: user.id,
          providers: { truelayer: { id: faker.string.uuid(), status: "authorization_required", version: "v3" } }
        }),
        buildDepositCashTransaction({
          owner: user.id,
          providers: { truelayer: { id: faker.string.uuid(), status: "failed", version: "v3" } }
        }),
        buildDepositCashTransaction({
          owner: user.id,
          providers: { truelayer: { id: faker.string.uuid(), status: "authorized", version: "v3" } }
        })
      ]);

      const response = await request(app)
        .get("/api/m2m/transactions?truelayerStatus=authorization_required&truelayerStatus=authorized")
        .set("external-user-id", user._id)
        .set("platform", "android")
        .set("version", "1.2.5")
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedData = await Transaction.find({
        owner: user.id,
        $or: [
          {
            "providers.truelayer.status": {
              $in: ["authorization_required", "authorized"]
            }
          },
          {
            "providers.truelayer.status": {
              $exists: false
            }
          }
        ]
      });
      expect(expectedData.length).toEqual(3);
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("/transactions?status=Settled&status=Pending&sort=_id should return status 200 with transactions array", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });
      await Promise.all([
        buildDepositCashTransaction({
          owner: user.id,
          portfolio,
          providers: {
            truelayer: { id: faker.string.uuid(), status: "executed", version: "v3" },
            wealthkernel: { id: faker.string.uuid(), status: "Created" }
          }
        }), // status Pending
        buildAssetTransaction({ owner: user.id }), // status Pending
        buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: { id: faker.string.uuid(), status: "failed", version: "v3" }
          },
          status: "Cancelled"
        }), // status Cancelled
        buildDepositCashTransaction({
          owner: user.id,
          providers: { truelayer: { id: faker.string.uuid(), status: "authorized", version: "v3" } }
        }), // status Pending
        buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: { id: faker.string.uuid(), status: "executed", version: "v3" }
          }
        }), //status Settled
        buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        }) // status Pending
      ]);

      const response = await request(app)
        .get("/api/m2m/transactions?status=Settled&status=Pending&sort=_id")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedData = await Transaction.find(
        {
          owner: user.id,
          $or: [
            {
              // resulting to status Pending
              "providers.truelayer.status": {
                $in: ["authorized", "executed", null]
              }
            },
            {
              // resulting to status Settled or Pending
              "providers.wealthkernel.status": {
                $in: ["Created", "Settled", "Active"]
              }
            }
          ]
        },
        null,
        {
          sort: { _id: 1 }
        }
      );

      const transactionsReceived: TransactionDocument[] = JSON.parse(response.text);
      const isArraySorted = (arr: any[]) => arr.slice(1).every((item, i) => arr[i] <= item); //asc order
      expect(isArraySorted(transactionsReceived.map((transaction) => transaction._id))).toEqual(true);

      expect(expectedData.length).toEqual(5); //  4 Pending + 1 Settled = 5
      expect(expectedData.length).toEqual(transactionsReceived.length);
      expect(transactionsReceived).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("/transactions?truelayerStatus=blah1^$ (invalid param truelayerStatus) should return status 400 and error message", async () => {
      const user = await buildUser();
      const response = await request(app)
        .get("/api/m2m/transactions?truelayerStatus=blah1^$")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: expect.stringContaining("Param 'truelayerStatus' has invalid value 'blah1^$', must be one of")
          }
        })
      );
    });

    it("/transactions?status=foo^$ (invalid param status) should return status 400 and error message", async () => {
      const user = await buildUser();
      const response = await request(app)
        .get("/api/m2m/transactions?status=foo^$")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: `Param 'status' has invalid value 'foo^$', must be one of [${TransactionStatusArray}]`
          }
        })
      );
    });

    it("/transactions?populate=pendingDeposit.bankAccount for portfolio buy transaction pending deposit should return status 200 with populated bank account", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });
      const deposit = await buildDepositCashTransaction(
        {
          createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            },
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          settledAt: new Date(),
          bankAccount: user.bankAccounts[0].id
        },
        user
      );

      const pendingPortfolioBuyTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolio,
        status: "Pending",
        portfolioTransactionCategory: "buy",
        originalInvestmentAmount: 1000,
        consideration: { currency: "GBP", amount: 983 },
        pendingDeposit: deposit.id
      });

      pendingPortfolioBuyTransaction.orders = [
        await buildOrder({
          status: "Matched",
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
          side: "Buy",
          transaction: pendingPortfolioBuyTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            originalAmount: 1000,
            amount: 983,
            currency: "GBP"
          },
          quantity: 1
        })
      ];

      await pendingPortfolioBuyTransaction.save();

      const response = await request(app)
        .get("/api/m2m/transactions?populate=pendingDeposit.bankAccount")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const transactionsReceived: AssetTransactionDocument[] = JSON.parse(response.text);
      expect(
        (
          transactionsReceived.find((transaction) => transaction._id === pendingPortfolioBuyTransaction.id)
            .pendingDeposit as DepositCashTransactionDocument
        ).bankAccount._id === user.bankAccounts[0]._id
      );
    });

    it("/transactions?category=WealthyhoodDividendTransaction&hasViewedAppModal=false should return status 200 and transactions array", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });
      await Promise.all([
        buildWealthyhoodDividendTransaction({ owner: user.id, portfolio, hasViewedAppModal: false }),
        buildWealthyhoodDividendTransaction({ owner: user.id, portfolio, hasViewedAppModal: true })
      ]);

      const response = await request(app)
        .get("/api/m2m/transactions?category=WealthyhoodDividendTransaction&hasViewedAppModal=false")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedData = await Transaction.find({
        category: "WealthyhoodDividendTransaction",
        hasViewedAppModal: false
      });
      expect(expectedData.length).toEqual(1);

      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("/transactions?category=AssetTransaction should return 200 with empty array when the transaction is linked to incomplete deposit", async () => {
      const user = await buildUser();
      const userBankAccount = await buildBankAccount({ owner: user.id });
      await user.populate("bankAccounts");
      const portfolio = await buildPortfolio({ owner: user.id });
      const incompleteDepositLinkedToAssetTransaction = await buildDepositCashTransaction({
        owner: user.id,
        providers: {
          truelayer: {
            id: faker.string.uuid(),
            version: "v3",
            status: "authorization_required"
          }
        },
        bankAccount: userBankAccount.id
      });

      await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "PendingDeposit",
        portfolioTransactionCategory: "buy",
        originalInvestmentAmount: 1000,
        consideration: { currency: "GBP", amount: 983 },
        pendingDeposit: incompleteDepositLinkedToAssetTransaction
      });

      const response = await request(app)
        .get("/api/m2m/transactions?category=AssetTransaction")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text)).toEqual([]);
    });

    it("/transactions?category=AssetTransaction should return 200 with empty array when the transaction has depositFailed status", async () => {
      const user = await buildUser();
      await user.populate("bankAccounts");
      const portfolio = await buildPortfolio({ owner: user.id });

      await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "DepositFailed",
        portfolioTransactionCategory: "buy",
        originalInvestmentAmount: 1000,
        consideration: { currency: "GBP", amount: 983 }
      });

      const response = await request(app)
        .get("/api/m2m/transactions?category=AssetTransaction")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text)).toEqual([]);
    });

    it("/transactions?category=CashbackTransaction should return 200 with empty array when the transaction is linked an asset Transaction that is then linked to incomplete deposit", async () => {
      const user = await buildUser();
      const userBankAccount = await buildBankAccount({ owner: user.id });
      await user.populate("bankAccounts");
      const portfolio = await buildPortfolio({ owner: user.id });
      const incompleteDepositLinkedToAssetTransaction = await buildDepositCashTransaction({
        owner: user.id,
        providers: {
          truelayer: {
            id: faker.string.uuid(),
            version: "v3",
            status: "authorization_required"
          }
        },
        bankAccount: userBankAccount.id
      });

      await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "PendingDeposit",
        portfolioTransactionCategory: "buy",
        originalInvestmentAmount: 1000,
        consideration: { currency: "GBP", amount: 983 },
        pendingDeposit: incompleteDepositLinkedToAssetTransaction
      });

      const response = await request(app)
        .get("/api/m2m/transactions?category=CashbackTransaction")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text)).toEqual([]);
    });
  });

  describe("GET /transactions/:id", () => {
    beforeEach(() => jest.clearAllMocks());
    afterEach(async () => await clearDb());

    it("GET /transactions/:id (with invalid id) should return status 400 with proper cause", async () => {
      const user = await buildUser();
      const response = await request(app)
        .get("/api/m2m/transactions/sdasda243%$")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid url",
            message: "Invalid url"
          }
        })
      );
    });

    it("GET /transactions/:id (with invalid id) should return status 400 with proper cause", async () => {
      const user = await buildUser();
      const response = await request(app)
        .get("/api/m2m/transactions/foo123")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Transaction does not exist"
          }
        })
      );
    });

    it("GET /transactions/:id (with valid id) should return status 200 with transaction object", async () => {
      const user = await buildUser();
      const transaction = await buildAssetTransaction({ owner: user.id });
      await Promise.all([
        buildDepositCashTransaction({ owner: user.id }),
        buildWithdrawalCashTransaction({ owner: user.id })
      ]);
      const response = await request(app)
        .get(`/api/m2m/transactions/${transaction.id}`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedTransaction = await Transaction.findOne({ _id: transaction._id, owner: user.id });
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedTransaction)));
    });
  });

  describe("GET /transactions/deposits", () => {
    let user: UserDocument;

    beforeEach(() => jest.clearAllMocks());
    beforeAll(async () => {
      user = await buildUser();
      await user.populate("bankAccounts");
      await buildPortfolio({ owner: user.id });
      await Promise.all([
        buildAssetTransaction({ owner: user.id }),
        buildDepositCashTransaction({ owner: user.id }),
        buildDepositCashTransaction({ owner: user.id, bankAccount: user.bankAccounts[0].id }),
        buildWithdrawalCashTransaction({ owner: user.id }),
        buildAssetTransaction(),
        buildAssetTransaction()
      ]);
    });

    it("should return status 200 with deposit cash transactions array", async () => {
      const response = await request(app)
        .get("/api/m2m/transactions/deposits")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedData = await DepositCashTransaction.find({ owner: user.id });

      expect(expectedData.length).toEqual(2);
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify({ data: expectedData })));
    });

    it("should return the bank accounts populated for populate=bankAccount query param", async () => {
      const response = await request(app)
        .get("/api/m2m/transactions/deposits?populate=bankAccount")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const expectedData = await DepositCashTransaction.find({
        owner: user.id,
        bankAccount: { $exists: true }
      }).populate("bankAccount");

      expect(expectedData.length).toEqual(1);
      expect(
        JSON.parse(response.text).data.filter((deposit: DepositCashTransactionDocument) =>
          Boolean(deposit.bankAccount)
        )
      ).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });
  });

  describe("GET /transactions/pending", () => {
    let user: UserDocument;
    let bankAccount: BankAccountDocument;
    beforeAll(async () => {
      jest.clearAllMocks();
    });
    beforeEach(async () => {
      await clearDb();
      user = await buildUser();
      await buildSubscription({ owner: user.id });
      bankAccount = await buildBankAccount({ owner: user.id });
      await user.populate("bankAccounts");
      await buildPortfolio({ owner: user.id });
    });

    describe("when user has no pending transactions", () => {
      it("should return an empty array", async () => {
        const response = await request(app)
          .get("/api/m2m/transactions/pending")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        const receivedTransactions = response.body;
        expect(response.status).toEqual(200);
        expect(receivedTransactions).toEqual([]);
      });
    });

    describe("when user has a pending deposit", () => {
      it("should return the pending deposit", async () => {
        const deposit = await buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            }
          },
          bankAccount: bankAccount.id
        });

        const response = await request(app)
          .get("/api/m2m/transactions/pending")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
        const expectedTransaction = await DepositCashTransaction.findOne({
          _id: deposit._id
        })
          .populate("bankAccount")
          .populate("linkedAssetTransaction");

        const receivedTransactions = response.body;
        expect(response.status).toEqual(200);
        expect(receivedTransactions).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });
  });

  describe("GET /transactions/pending/rebalances", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    beforeAll(async () => {
      jest.clearAllMocks();
    });
    beforeEach(async () => {
      await clearDb();
      user = await buildUser();
      await user.populate("bankAccounts");
      portfolio = await buildPortfolio({ owner: user.id });
    });

    describe("when user has a pending rebalance", () => {
      it("should return the pending rebalance", async () => {
        const rebalance = await buildRebalanceTransaction({
          owner: user.id,
          rebalanceStatus: "PendingSell",
          portfolio
        });
        const response = await request(app)
          .get("/api/m2m/transactions/pending/rebalances")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
        const expectedTransaction = await RebalanceTransaction.findOne({
          _id: rebalance._id
        });

        const receivedTransactions = response.body;
        expect(response.status).toEqual(200);
        expect(receivedTransactions).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has no rebalance transactions", () => {
      it("should return an empty array", async () => {
        const response = await request(app)
          .get("/api/m2m/transactions/pending/rebalances")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
        const receivedTransactions = response.body;
        expect(response.status).toEqual(200);
        expect(receivedTransactions).toEqual([]);
      });
    });
  });

  describe("GET /transactions/deposits/:id", () => {
    beforeEach(() => jest.clearAllMocks());

    it("should return status 400 and error message for invalid id", async () => {
      const user = await buildUser();
      const response = await request(app)
        .get("/api/m2m/transactions/deposits/sdasda243%$")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid url",
            message: "Invalid url"
          }
        })
      );
    });

    it("should return status 400 and error message for non-existing transaction", async () => {
      const user = await buildUser();
      const response = await request(app)
        .get("/api/m2m/transactions/deposits/foo123")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Transaction does not exist"
          }
        })
      );
    });

    it("should return status 200 with transaction object for valid id", async () => {
      const user = await buildUser();
      const transaction = await buildDepositCashTransaction({ owner: user.id });
      await Promise.all([
        buildAssetTransaction({ owner: user.id }),
        buildWithdrawalCashTransaction({ owner: user.id })
      ]);
      const response = await request(app)
        .get(`/api/m2m/transactions/deposits/${transaction.id}`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedTransaction = await DepositCashTransaction.findOne({
        _id: transaction._id,
        owner: user.id
      });
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedTransaction)));
    });
  });

  describe("GET /transactions/assets", () => {
    beforeEach(() => jest.clearAllMocks());

    it("GET /transactions/assets should return status 200 with asset transactions array", async () => {
      const user = await buildUser();
      const [assetTransactionWithOrders] = await Promise.all([
        buildAssetTransaction({ owner: user.id }),
        buildDepositCashTransaction({ owner: user.id }),
        buildDepositCashTransaction({ owner: user.id }),
        buildWithdrawalCashTransaction({ owner: user.id }),
        buildAssetTransaction({ owner: user.id }),
        buildAssetTransaction()
      ]);

      assetTransactionWithOrders.orders = [
        await buildOrder({
          status: "Matched",
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
          side: "Buy",
          transaction: assetTransactionWithOrders.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            amount: 10,
            currency: "GBP"
          }
        })
      ];
      await assetTransactionWithOrders.save();

      const response = await request(app)
        .get("/api/m2m/transactions/assets")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedData = await AssetTransaction.find({ owner: user.id });

      expect(expectedData.length).toEqual(2);
      expect(JSON.parse(response.text).data).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    describe("and asset transactions with invalid pending deposits exist", () => {
      let user: UserDocument;
      let response: supertest.Response;
      let assetTransactionToDisplay: AssetTransactionDocument;

      beforeEach(async () => {
        user = await buildUser();
        const invalidPendingDeposits = await Promise.all([
          await buildDepositCashTransaction({
            providers: {
              truelayer: { id: faker.string.uuid(), status: "authorization_required", version: "v3" }
            }
          }),
          await buildDepositCashTransaction({
            providers: { truelayer: { id: faker.string.uuid(), status: "failed", version: "v3" } }
          }),
          await buildDepositCashTransaction({
            providers: { truelayer: { id: faker.string.uuid(), status: "failed", version: "v3" } }
          })
        ]);
        const validPendingDeposit = await buildDepositCashTransaction({
          providers: { truelayer: { id: faker.string.uuid(), status: "executed", version: "v3" } }
        });
        const assetTransactions = await Promise.all([
          ...invalidPendingDeposits.map(({ id }) =>
            buildAssetTransaction({
              owner: user.id,
              pendingDeposit: id
            })
          ),
          buildAssetTransaction({ owner: user.id, pendingDeposit: validPendingDeposit.id })
        ]);
        assetTransactionToDisplay = assetTransactions.pop() as AssetTransactionDocument;

        response = await request(app)
          .get("/api/m2m/transactions/assets")
          .set("external-user-id", user._id)
          .set("Accept", "application/json");
      });
      afterEach(async () => await clearDb());

      it("should return status 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should return only 1 transaction (the valid one)", async () => {
        const expectedData = await AssetTransaction.findOne({ _id: assetTransactionToDisplay.id }).populate(
          "pendingDeposit"
        );
        const transactionsReceived: TransactionDocument[] = JSON.parse(response.text).data;
        expect(transactionsReceived.length).toEqual(1);
        expect(transactionsReceived[0]).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
      });
    });
  });

  describe("GET /transactions/assets/:id", () => {
    beforeEach(async () => {
      jest.clearAllMocks();
      await clearDb();
    });

    it("GET /transactions/assets/:id (with invalid id) should return status 400 with proper cause", async () => {
      const user = await buildUser();
      await buildSubscription({ owner: user.id });

      const response = await request(app)
        .get("/api/m2m/transactions/assets/sdasda243%$")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid url",
            message: "Invalid url"
          }
        })
      );
    });

    it("GET /transactions/assets/:id (with invalid id) should return status 400 with proper cause", async () => {
      const user = await buildUser();
      await buildSubscription({ owner: user.id });

      const response = await request(app)
        .get("/api/m2m/transactions/assets/foo123")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Transaction does not exist"
          }
        })
      );
    });

    it("GET /transactions/assets/:id?populateOrders=foo (with valid populateOrders param) should return status 200 with transaction object with unpopulated orders", async () => {
      const user = await buildUser();
      await buildSubscription({ owner: user.id });

      const transaction = await buildAssetTransaction({ owner: user.id });
      const response = await request(app)
        .get(`/api/m2m/transactions/assets/${transaction.id}?populateOrders=foo`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for param 'populateOrders' , should be boolean"
          }
        })
      );
    });

    it("GET /transactions/assets/:id (with valid id) should return status 200 with transaction object with unpopulated orders", async () => {
      const user = await buildUser();
      await buildSubscription({ owner: user.id });

      const transaction = await buildAssetTransaction({ owner: user.id });
      await Promise.all([
        buildDepositCashTransaction({ owner: user.id }),
        buildWithdrawalCashTransaction({ owner: user.id })
      ]);
      const response = await request(app)
        .get(`/api/m2m/transactions/assets/${transaction.id}`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedTransaction = await AssetTransaction.findOne({ _id: transaction._id, owner: user.id });
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedTransaction)));
    });

    it("GET /transactions/assets/:id?populateOrders=true (with valid id and populateOrdes param) should return status 200 with transaction object with populated orders", async () => {
      const user = await buildUser();
      await buildSubscription({ owner: user.id });
      const transaction = await buildAssetTransaction({ owner: user.id });
      await Promise.all([
        buildDepositCashTransaction({
          owner: user.id
        }),
        buildWithdrawalCashTransaction({
          owner: user.id
        })
      ]);
      const response = await request(app)
        .get(`/api/m2m/transactions/assets/${transaction.id}?populateOrders=true`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedTransaction = await AssetTransaction.findOne({
        _id: transaction._id,
        owner: user.id
      }).populate("orders");
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedTransaction)));
    });

    it("GET /transactions/assets/:id?populateOrders=true (with valid id and populateOrdes param and populateForeignCurrencyRates param) for non-GBP traded asset sell should return status 200 with transaction object with populated orders and foreignCurrencyRates field", async () => {
      // Current FX rate GBP <-> USD is 1.27
      const FX_RATES = {
        USD: {
          USD: 1,
          EUR: 0.9,
          GBP: 0.787402
        },
        EUR: {
          EUR: 1,
          GBP: 0.869565,
          USD: 1.1
        },
        GBP: {
          GBP: 1,
          EUR: 1.15,
          USD: 1.27
        }
      };
      await RedisClientService.Instance.set("fxRates", FX_RATES);

      const user = await buildUser();
      await buildSubscription({ owner: user.id });

      await buildInvestmentProduct(true, { assetId: "equities_apple", listed: true });
      const transaction = await buildAssetTransaction({
        owner: user,
        portfolioTransactionCategory: "update"
      });

      transaction.orders = [
        await buildOrder({
          status: "Matched",
          providers: {
            wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
          },
          side: "Sell",
          transaction: transaction.id,
          isin: investmentUniverseConfig?.ASSET_CONFIG["equities_apple"]?.isin,
          quantity: 1
        })
      ];
      await transaction.save();

      const response = await request(app)
        .get(
          `/api/m2m/transactions/assets/${transaction.id}?populateForeignCurrencyRates=true&populateOrders=true`
        )
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const expectedTransaction = await AssetTransaction.findOne({
        _id: transaction._id,
        owner: user.id
      }).populate("orders");
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedTransaction)));
      expect(response.body.foreignCurrencyRates).toEqual({ USD: 1.277 }); // £1 -> $1.27 initially, then 1.27 * (1 + 0.0055) for sell order with free plan spread
    });

    it("GET /transactions/assets/:id?populateOrders=true (with valid id and populateOrdes param and populateForeignCurrencyRates param) for non-GBP traded asset buy should return status 200 with transaction object with populated orders and foreignCurrencyRates field", async () => {
      // Current FX rate GBP <-> USD is 1.27
      const FX_RATES = {
        USD: {
          USD: 1,
          EUR: 0.9,
          GBP: 0.787402
        },
        EUR: {
          EUR: 1,
          GBP: 0.869565,
          USD: 1.1
        },
        GBP: {
          GBP: 1,
          EUR: 1.15,
          USD: 1.27
        }
      };
      await RedisClientService.Instance.set("fxRates", FX_RATES);

      const user = await buildUser();
      await buildSubscription({ owner: user.id });

      await buildInvestmentProduct(true, { assetId: "equities_apple", listed: true });
      const transaction = await buildAssetTransaction({
        owner: user,
        portfolioTransactionCategory: "update"
      });

      transaction.orders = [
        await buildOrder({
          status: "Matched",
          providers: {
            wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
          },
          side: "Buy",
          exchangeRate: 1.05,
          transaction: transaction.id,
          isin: investmentUniverseConfig?.ASSET_CONFIG["equities_apple"]?.isin,
          quantity: 1
        })
      ];
      await transaction.save();

      const response = await request(app)
        .get(
          `/api/m2m/transactions/assets/${transaction.id}?populateForeignCurrencyRates=true&populateOrders=true`
        )
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const expectedTransaction = await AssetTransaction.findOne({
        _id: transaction._id,
        owner: user.id
      }).populate("orders");
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedTransaction)));
      expect(response.body.foreignCurrencyRates).toEqual({ USD: 1.263 }); // £1 -> $1.27 initially, then 1.27 * (1 - 0.0055) for buy order with free plan spread
      expect(response.body.orders[0].displayExchangeRate).toEqual({ rate: 1.05 });
    });

    it("GET /transactions/assets/:id?populateOrders=true (with valid id and populateOrdes param and populateForeignCurrencyRates param) for GBP traded asset buy should return status 200 with transaction object with populated orders and no foreignCurrencyRates field", async () => {
      // Current FX rate GBP <-> USD is 5
      const FX_RATES = {
        USD: {
          USD: 1,
          EUR: 0.2,
          GBP: 0.2
        },
        EUR: {
          EUR: 1,
          GBP: 1,
          USD: 5
        },
        GBP: {
          GBP: 1,
          EUR: 1,
          USD: 5
        }
      };
      await RedisClientService.Instance.set("fxRates", FX_RATES);

      const user = await buildUser();
      await buildSubscription({ owner: user.id });

      await buildInvestmentProduct(true, { assetId: "equities_global", listed: true });
      const transaction = await buildAssetTransaction({
        owner: user,
        portfolioTransactionCategory: "update"
      });

      transaction.orders = [
        await buildOrder({
          status: "Matched",
          providers: {
            wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
          },
          side: "Buy",
          transaction: transaction.id,
          isin: investmentUniverseConfig?.ASSET_CONFIG["equities_global"]?.isin,
          quantity: 1
        })
      ];
      await transaction.save();

      const response = await request(app)
        .get(
          `/api/m2m/transactions/assets/${transaction.id}?populateForeignCurrencyRates=true&populateOrders=true`
        )
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const expectedTransaction = await AssetTransaction.findOne({
        _id: transaction._id,
        owner: user.id
      }).populate("orders");
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedTransaction)));
      expect(response.body.foreignCurrencyRates).toBeUndefined(); // £1 -> $5 initially, then updated with our FX spread
    });
  });

  describe("GET /transactions/me/cash-activity", () => {
    let user: UserDocument;
    let bankAccount: BankAccountDocument;
    let portfolio: PortfolioDocument;

    beforeAll(async () => {
      jest.clearAllMocks();
    });

    beforeEach(async () => {
      await clearDb();
      user = await buildUser();
      await buildSubscription({ owner: user.id });
      bankAccount = await buildBankAccount({ owner: user.id });
      await user.populate("bankAccounts");
      portfolio = await buildPortfolio({ owner: user.id });
    });

    it("should return 400 if the limit is not numeric", async () => {
      const response = await request(app)
        .get("/api/m2m/transactions/me/cash-activity?limit=bfae")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
    });

    it("should return 400 if the limit is negative", async () => {
      await buildDepositCashTransaction({
        owner: user.id,
        providers: {
          truelayer: {
            id: faker.string.uuid(),
            status: "executed"
          },
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Settled"
          }
        },
        bankAccount: bankAccount.id
      });

      const response = await request(app)
        .get("/api/m2m/transactions/me/cash-activity?limit=-40")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
    });

    describe("when user has no transactions and no limit", () => {
      it("should return an empty array", async () => {
        const response = await request(app)
          .get("/api/m2m/transactions/me/cash-activity")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        const receivedTransactions = response.body;
        expect(response.status).toEqual(200);
        expect(receivedTransactions).toEqual(expect.arrayContaining([]));
      });
    });

    describe("when user has a settled deposit and no limit", () => {
      it("should return the settled deposit", async () => {
        const deposit = await buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            },
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          bankAccount: bankAccount.id
        });

        const response = await request(app)
          .get("/api/m2m/transactions/me/cash-activity")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        const receivedTransactions = response.body;
        expect(response.status).toEqual(200);
        expect(receivedTransactions).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              type: "transaction",
              activityFilter: TransactionCashActivityFilterEnum.Deposit,
              cashFlowSign: 1,
              item: expect.objectContaining({
                _id: deposit.id
              })
            })
          ])
        );
      });
    });

    describe("when user has 2 settled deposits but the limit is set to 1", () => {
      it("should return only the newest deposit", async () => {
        await buildDepositCashTransaction({
          owner: user.id,
          createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            },
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          bankAccount: bankAccount.id
        });

        const deposit2 = await buildDepositCashTransaction({
          owner: user.id,
          createdAt: new Date(),
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            },
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          bankAccount: bankAccount.id
        });

        const response = await request(app)
          .get("/api/m2m/transactions/me/cash-activity?limit=1")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        const receivedTransactions = response.body;
        expect(response.status).toEqual(200);
        expect(receivedTransactions).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              type: "transaction",
              activityFilter: TransactionCashActivityFilterEnum.Deposit,
              cashFlowSign: 1,
              item: expect.objectContaining({
                _id: deposit2.id
              })
            })
          ])
        );
        expect(receivedTransactions).toHaveLength(1);
      });
    });

    describe("when user has a settled withdrawal and limit is set to 1", () => {
      it("should return status 200 and one withdrawal transaction with negative cashFlowSign", async () => {
        const withdrawal = await buildWithdrawalCashTransaction({
          owner: user.id,
          createdAt: new Date(),
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          status: "Settled",
          bankAccount: bankAccount.id
        });

        const response = await request(app)
          .get("/api/m2m/transactions/me/cash-activity?limit=1")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        const receivedTransactions = response.body;
        expect(response.status).toEqual(200);
        expect(receivedTransactions).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              type: "transaction",
              activityFilter: TransactionCashActivityFilterEnum.Withdraw,
              cashFlowSign: -1,
              item: expect.objectContaining({
                _id: withdrawal.id
              })
            })
          ])
        );
        expect(receivedTransactions).toHaveLength(1);
      });
    });

    describe("when a asset transaction exists and is linked to an incomplete deposit", () => {
      it("should not return the asset transaction", async () => {
        const incompleteDepositLinkedToAssetTransaction = await buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              version: "v3",
              status: "failed"
            }
          },
          bankAccount: bankAccount.id
        });

        const portfolioBuyWithIncompleteDeposit = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "PendingDeposit",
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          consideration: { currency: "GBP", amount: 983 },
          pendingDeposit: incompleteDepositLinkedToAssetTransaction.id
        });
        const response = await request(app)
          .get("/api/m2m/transactions/me/cash-activity")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        const receivedTransactions = response.body;
        expect(response.status).toEqual(200);
        expect(receivedTransactions).toEqual([]);
      });
    });

    describe("when a cashback transaction exists and is linked to an asset Transaction that is then linked to incomplete deposit", () => {
      it("should not return the cashback transaction", async () => {
        const incompleteDepositLinkedToAssetTransaction = await buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              version: "v3",
              status: "authorization_required"
            }
          },
          bankAccount: bankAccount.id
        });

        const portfolioBuyWithIncompleteDeposit = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "PendingDeposit",
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          consideration: { currency: "GBP", amount: 983 },
          pendingDeposit: incompleteDepositLinkedToAssetTransaction.id
        });

        const cashbackLinkedToPortfolioBuyWithIncompleteDeposit = await buildCashbackTransaction({
          owner: user.id,
          status: "Pending",
          portfolio: portfolio.id,
          linkedAssetTransaction: portfolioBuyWithIncompleteDeposit.id
        });
        const response = await request(app)
          .get("/api/m2m/transactions/me/cash-activity")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        const receivedTransactions = response.body;
        expect(response.status).toEqual(200);
        expect(receivedTransactions).toEqual([]);
      });
    });
  });

  describe("POST /transactions/deposits", () => {
    const truelayerClientSpy = jest.spyOn(TruelayerPaymentsClient.prototype, "createPayment");

    beforeEach(async () => {
      jest.clearAllMocks();
      jest.spyOn(TransactionService, "syncDepositTruelayerStatus");
    });
    afterEach(async () => await clearDb());

    describe("when the user hasn't passed KYC", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const user = await buildUser({ kycStatus: KycStatusEnum.PENDING });

        response = await request(app)
          .post("/api/m2m/transactions/deposits")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      it("should not create a truelayer payment", () => {
        expect(TruelayerPaymentsClient.prototype.createPayment).not.toHaveBeenCalled();
      });
      it("should not create a deposit db document", async () => {
        expect(await DepositCashTransaction.find({})).toHaveLength(0);
      });
      it("should return status 400 with proper message", () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "User has not passed kyc"
            }
          })
        );
      });
    });

    describe("when the user has passed KYC", () => {
      let response: supertest.Response;

      describe("and the user doesn't have a real portfolio", () => {
        beforeAll(async () => {
          const user: UserDocument = await buildUser({ kycStatus: KycStatusEnum.PASSED });
          response = await request(app)
            .post("/api/m2m/transactions/deposits")
            .send({
              paymentAmount: "50",
              bankAccountId: user.bankAccounts[0]._id.toString()
            })
            .set("external-user-id", user.id)
            .set("Accept", "application/json");
        });
        it("should not create a truelayer payment", () => {
          expect(TruelayerPaymentsClient.prototype.createPayment).not.toHaveBeenCalled();
        });
        it("should not create a deposit db document", () => {
          expect(TransactionService.syncDepositTruelayerStatus).not.toHaveBeenCalled();
        });
        it("should return 400", () => {
          expect(response.status).toEqual(400);
          expect(JSON.parse(response.text)).toMatchObject(
            expect.objectContaining({
              error: {
                description: "Operation failed",
                message: "User has not REAL portfolio"
              }
            })
          );
        });
      });

      describe("and the user has a real portfolio", () => {
        describe("and the payment amount is invalid", () => {
          beforeAll(async () => {
            const user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
            await buildPortfolio({ owner: user.id, mode: PortfolioModeEnum.REAL });
            await user.populate("portfolios");

            response = await request(app)
              .post("/api/m2m/transactions/deposits")
              .send({
                paymentAmount: "50aa543^*&",
                bankAccountId: user.bankAccounts[0]._id.toString()
              })
              .set("external-user-id", user.id)
              .set("Accept", "application/json");
          });
          it("should not create a truelayer payment", () => {
            expect(TruelayerPaymentsClient.prototype.createPayment).not.toHaveBeenCalled();
          });
          it("should not create a deposit db document", () => {
            expect(TransactionService.syncDepositTruelayerStatus).not.toHaveBeenCalled();
          });
          it("should return 400", () => {
            expect(response.status).toEqual(400);
            expect(JSON.parse(response.text)).toMatchObject(
              expect.objectContaining({
                error: {
                  description: "Invalid parameter",
                  message: "Invalid value for param 'paymentAmount' , should be numeric"
                }
              })
            );
          });
        });

        describe("and the payment amount is valid", () => {
          describe("and the user passes a bank account ID and they have a UK company entity", () => {
            describe("and the selected bank account can't be found", () => {
              beforeEach(async () => {
                const user = await buildUser({
                  kycStatus: KycStatusEnum.PASSED
                });
                response = await request(app)
                  .post("/api/m2m/transactions/deposits")
                  .send({
                    paymentAmount: "50",
                    bankAccountId: (await buildUser()).bankAccounts[0]._id.toString()
                  })
                  .set("external-user-id", user.id)
                  .set("Accept", "application/json");
              });
              afterEach(async () => await clearDb());

              it("should not create a truelayer payment", () => {
                expect(TruelayerPaymentsClient.prototype.createPayment).not.toHaveBeenCalled();
              });
              it("should not create a deposit db document", () => {
                expect(TransactionService.syncDepositTruelayerStatus).not.toHaveBeenCalled();
              });
              it("should log the error", () => {
                expect(logger.error).toHaveBeenCalled();
              });
              it("should return 400 with proper message", () => {
                expect(response.status).toEqual(400);
                expect(JSON.parse(response.text)).toMatchObject(
                  expect.objectContaining({
                    error: {
                      description: "Operation failed",
                      message: "Bank account does not belong to user"
                    }
                  })
                );
              });
            });

            describe("and the selected bank account is inactive", () => {
              beforeEach(async () => {
                const user = await buildUser({ kycStatus: KycStatusEnum.PASSED }, false);
                const bankAccount = await buildBankAccount({ owner: user.id, active: false });
                response = await request(app)
                  .post("/api/m2m/transactions/deposits")
                  .send({
                    paymentAmount: "50",
                    bankAccountId: bankAccount.id
                  })
                  .set("external-user-id", user.id)
                  .set("Accept", "application/json");
              });
              it("should not create a truelayer payment", () => {
                expect(TruelayerPaymentsClient.prototype.createPayment).not.toHaveBeenCalled();
              });
              it("should not create a deposit db document", () => {
                expect(TransactionService.syncDepositTruelayerStatus).not.toHaveBeenCalled();
              });
              it("should log the error", () => {
                expect(logger.error).toHaveBeenCalled();
              });
              it("should return 400 with proper message", () => {
                expect(response.status).toEqual(400);
                expect(JSON.parse(response.text)).toMatchObject(
                  expect.objectContaining({
                    error: {
                      description: "Operation failed",
                      message: "Bank account is not active"
                    }
                  })
                );
              });
            });

            describe("and the selected bank account can be found", () => {
              describe("and the user doesn't own the bank account", () => {
                beforeEach(async () => {
                  const user = await buildUser({
                    kycStatus: KycStatusEnum.PASSED
                  });
                  await buildPortfolio({ owner: user.id, mode: PortfolioModeEnum.REAL });
                  response = await request(app)
                    .post("/api/m2m/transactions/deposits")
                    .send({
                      paymentAmount: "50",
                      // here we create another bank account that does not belong to target user
                      bankAccountId: (await buildUser()).bankAccounts[0]._id.toString()
                    })
                    .set("external-user-id", user.id)
                    .set("Accept", "application/json");
                });
                afterEach(async () => await clearDb());
                it("should not create a truelayer payment", () => {
                  expect(TruelayerPaymentsClient.prototype.createPayment).not.toHaveBeenCalled();
                });
                it("should not create a deposit db document", () => {
                  expect(TransactionService.syncDepositTruelayerStatus).not.toHaveBeenCalled();
                });
                it("should log the error", () => {
                  expect(logger.error).toHaveBeenCalled();
                });
                it("should return 400 with message", () => {
                  expect(response.status).toEqual(400);
                  expect(JSON.parse(response.text)).toMatchObject(
                    expect.objectContaining({
                      error: {
                        description: "Operation failed",
                        message: "Bank account does not belong to user"
                      }
                    })
                  );
                });
              });

              describe("and the user owns the bank account", () => {
                describe("and the wealthkernel portfolio doesn't exist", () => {
                  let portfolio: PortfolioDocument;

                  beforeEach(async () => {
                    const user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
                    portfolio = await buildPortfolio({
                      owner: user.id,
                      mode: PortfolioModeEnum.REAL
                    });
                    await user.populate("portfolios bankAccounts");

                    response = await request(app)
                      .post("/api/m2m/transactions/deposits")
                      .send({
                        paymentAmount: "50",
                        bankAccountId: user.bankAccounts[0]._id.toString()
                      })
                      .set("external-user-id", user.id)
                      .set("Accept", "application/json");
                  });
                  afterEach(async () => await clearDb());

                  it("should not create a truelayer payment", () => {
                    expect(TruelayerPaymentsClient.prototype.createPayment).not.toHaveBeenCalled();
                  });
                  it("should not create a deposit db document", () => {
                    expect(TransactionService.syncDepositTruelayerStatus).not.toHaveBeenCalled();
                  });
                  it("should log the error", () => {
                    expect(logger.error).toHaveBeenCalled();
                  });
                  it("should return 400 with proper message", () => {
                    expect(response.status).toEqual(400);
                    expect(JSON.parse(response.text)).toMatchObject(
                      expect.objectContaining({
                        error: {
                          description: "Operation failed",
                          message: `User is not verified yet. Wealthkernel portfolio id is missing for portfolio '${portfolio._id}'.`
                        }
                      })
                    );
                  });
                });

                describe("and the wealthkernel portfolio exists", () => {
                  const paymentResult: { paymentId: string; paymentUri: string; status: PaymentStatusTypeV3 } = {
                    paymentId: faker.string.uuid(),
                    paymentUri: faker.internet.url(),
                    status: "authorized" as PaymentStatusTypeV3
                  };

                  beforeEach(async () => {
                    const user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
                    await buildPortfolio({
                      owner: user.id,
                      mode: PortfolioModeEnum.REAL,
                      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
                    });
                    await user.populate("portfolios bankAccounts");

                    truelayerClientSpy.mockRestore();
                    jest.spyOn(TruelayerPaymentsClient.prototype, "createPayment").mockImplementation(
                      async (): Promise<{
                        paymentId: string;
                        paymentUri: string;
                        status: PaymentStatusTypeV3;
                      }> => paymentResult
                    );

                    response = await request(app)
                      .post("/api/m2m/transactions/deposits")
                      .send({
                        paymentAmount: "50",
                        bankAccountId: user.bankAccounts[0]._id.toString()
                      })
                      .set("external-user-id", user.id)
                      .set("Accept", "application/json");
                  });
                  afterEach(async () => await clearDb());

                  it("should create a truelayer payment", () => {
                    expect(TruelayerPaymentsClient.prototype.createPayment).toBeCalledTimes(1);
                  });

                  it("should create a new deposit transaction", async () => {
                    const deposits = await DepositCashTransaction.find({
                      "providers.truelayer.id": paymentResult.paymentId
                    });
                    expect(deposits).toHaveLength(1);

                    const deposit = deposits[0];
                    expect(deposit.activeProviders.length).toBe(2);
                    expect(deposit).toEqual(
                      expect.objectContaining({
                        depositAction: DepositActionEnum.JUST_PAY,
                        activeProviders: expect.arrayContaining([
                          ProviderEnum.WEALTHKERNEL,
                          ProviderEnum.TRUELAYER
                        ]),
                        providers: expect.objectContaining({
                          truelayer: {
                            id: paymentResult.paymentId,
                            status: paymentResult.status,
                            version: "v3"
                          }
                        }),
                        status: "Pending"
                      })
                    );
                  });
                });
              });
            });
          });

          describe("and the user passes a bank account ID and they have an EU company entity", () => {
            describe("and the selected bank account is valid", () => {
              let user: UserDocument;

              const REDIRECT_URL = "https://saltedge.com/some-redirect-uri";

              beforeEach(async () => {
                jest.spyOn(SaltedgeService.Instance, "createPaymentSession").mockResolvedValue({
                  data: {
                    connect_url: REDIRECT_URL
                  }
                });

                user = await buildUser(
                  {
                    companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
                    residencyCountry: "GR",
                    kycStatus: KycStatusEnum.PASSED,
                    providers: {
                      saltedge: {
                        id: faker.string.uuid()
                      }
                    }
                  },
                  false
                );
                await buildBankAccount({
                  owner: user.id,
                  bankId: "revolut"
                });
                await buildPortfolio({
                  owner: user.id,
                  mode: PortfolioModeEnum.REAL,
                  providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
                });
                await user.populate("portfolios bankAccounts");

                response = await request(app)
                  .post("/api/m2m/transactions/deposits")
                  .send({
                    paymentAmount: "50",
                    bankAccountId: user.bankAccounts[0]._id.toString()
                  })
                  .set("external-user-id", user.id)
                  .set("Accept", "application/json");
              });
              afterEach(async () => await clearDb());

              it("should create a Saltedge payment", async () => {
                expect(SaltedgeService.Instance.createPaymentSession).toHaveBeenCalledWith(
                  {
                    customerId: user.providers.saltedge.id,
                    reference: expect.anything(),
                    amount: "50",
                    fullName: `${user.firstName} ${user.lastName}`,
                    returnTo: expect.stringContaining(
                      "https://localhost:3000/investor/saltedge-pay-callback/just-pay?customId="
                    )
                  },
                  "revolut",
                  expect.anything()
                );
              });

              it("should create a new deposit transaction without a bank account ID", async () => {
                const deposits = await DepositCashTransaction.find({
                  owner: user.id
                });
                expect(deposits).toHaveLength(1);

                const deposit = deposits[0];

                expect(deposit.activeProviders.length).toBe(2);
                expect(deposit.toObject()).toEqual(
                  expect.objectContaining({
                    depositAction: DepositActionEnum.JUST_PAY,
                    activeProviders: expect.arrayContaining([ProviderEnum.WEALTHKERNEL, ProviderEnum.SALTEDGE]),
                    providers: expect.objectContaining({
                      saltedge: {
                        customId: expect.anything()
                      }
                    }),
                    isPaymentAuthorised: false,
                    depositMethod: DepositMethodEnum.OPEN_BANKING,
                    status: "Pending"
                  })
                );
                expect(deposit.toObject()).toEqual(
                  expect.not.objectContaining({
                    bankAccount: expect.anything()
                  })
                );
              });

              it("should return a 200 with the Saltedge redirect URL", async () => {
                const deposit = await DepositCashTransaction.findOne({
                  owner: user.id
                });

                expect(response.status).toEqual(200);
                expect(JSON.parse(response.text)).toEqual({
                  data: {
                    paymentUri: REDIRECT_URL,
                    depositId: deposit.id
                  }
                });
              });
            });
          });

          describe("and the user passes a bank ID", () => {
            describe("and the selected bank can be found", () => {
              describe("and the wealthkernel portfolio exists", () => {
                let user: UserDocument;

                const REDIRECT_URL = "https://saltedge.com/some-redirect-uri";

                beforeEach(async () => {
                  jest.spyOn(SaltedgeService.Instance, "createPaymentSession").mockResolvedValue({
                    data: {
                      connect_url: REDIRECT_URL
                    }
                  });

                  user = await buildUser({
                    companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
                    residencyCountry: "GR",
                    kycStatus: KycStatusEnum.PASSED,
                    providers: {
                      saltedge: {
                        id: faker.string.uuid()
                      }
                    }
                  });
                  await buildPortfolio({
                    owner: user.id,
                    mode: PortfolioModeEnum.REAL,
                    providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
                  });
                  await user.populate("portfolios bankAccounts");

                  response = await request(app)
                    .post("/api/m2m/transactions/deposits")
                    .send({
                      paymentAmount: "50",
                      bankId: "revolut"
                    })
                    .set("external-user-id", user.id)
                    .set("Accept", "application/json");
                });
                afterEach(async () => await clearDb());

                it("should create a Saltedge payment", async () => {
                  expect(SaltedgeService.Instance.createPaymentSession).toHaveBeenCalledWith(
                    {
                      customerId: user.providers.saltedge.id,
                      reference: expect.anything(),
                      amount: "50",
                      fullName: `${user.firstName} ${user.lastName}`,
                      returnTo: expect.stringContaining(
                        "https://localhost:3000/investor/saltedge-pay-callback/just-pay?customId="
                      )
                    },
                    "revolut",
                    expect.anything()
                  );
                });

                it("should create a new deposit transaction", async () => {
                  const deposits = await DepositCashTransaction.find({
                    owner: user.id
                  });
                  expect(deposits).toHaveLength(1);

                  const deposit = deposits[0];

                  expect(deposit.activeProviders.length).toBe(2);
                  expect(deposit.toObject()).toEqual(
                    expect.objectContaining({
                      depositAction: DepositActionEnum.JUST_PAY,
                      activeProviders: expect.arrayContaining([ProviderEnum.WEALTHKERNEL, ProviderEnum.SALTEDGE]),
                      providers: expect.objectContaining({
                        saltedge: {
                          customId: expect.anything()
                        }
                      }),
                      isPaymentAuthorised: false,
                      depositMethod: DepositMethodEnum.OPEN_BANKING,
                      status: "Pending"
                    })
                  );
                });

                it("should return a 200 with the Saltedge redirect URL", async () => {
                  const deposit = await DepositCashTransaction.findOne({
                    owner: user.id
                  });

                  expect(response.status).toEqual(200);
                  expect(JSON.parse(response.text)).toEqual({
                    data: {
                      paymentUri: REDIRECT_URL,
                      depositId: deposit.id
                    }
                  });
                });
              });
            });
          });
        });
      });

      describe("and the user has two real portfolios (GIA and ISA) and do not specify which portfolio they want to deposit in", () => {
        let generalInvestmentPortfolio: PortfolioDocument;

        const paymentResult: { paymentId: string; paymentUri: string; status: PaymentStatusTypeV3 } = {
          paymentId: faker.string.uuid(),
          paymentUri: faker.internet.url(),
          status: "authorized" as PaymentStatusTypeV3
        };

        beforeEach(async () => {
          const user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
          const generalInvestmentAccount = await buildAccount({
            owner: user.id,
            wrapperType: PortfolioWrapperTypeEnum.GIA
          });
          const investmentSavingsAccount = await buildAccount({
            owner: user.id,
            wrapperType: PortfolioWrapperTypeEnum.GIA
          });

          // User has both GIA nad ISA portfolios
          generalInvestmentPortfolio = await buildPortfolio({
            owner: user.id,
            mode: PortfolioModeEnum.REAL,
            account: generalInvestmentAccount,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });
          await buildPortfolio({
            owner: user.id,
            mode: PortfolioModeEnum.REAL,
            account: investmentSavingsAccount,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });
          await user.populate("portfolios bankAccounts");

          truelayerClientSpy.mockRestore();
          jest
            .spyOn(TruelayerPaymentsClient.prototype, "createPayment")
            .mockImplementation(
              async (): Promise<{ paymentId: string; paymentUri: string; status: PaymentStatusTypeV3 }> => {
                return paymentResult;
              }
            );

          response = await request(app)
            .post("/api/m2m/transactions/deposits")
            .send({
              paymentAmount: "50",
              bankAccountId: user.bankAccounts[0]._id.toString()
            })
            .set("external-user-id", user.id)
            .set("Accept", "application/json");
        });
        afterEach(async () => await clearDb());

        it("should create a truelayer payment and deposit transaction document", async () => {
          expect(TruelayerPaymentsClient.prototype.createPayment).toBeCalledTimes(1);

          const depositTransactionDocuments = await DepositCashTransaction.find({
            "providers.truelayer.id": paymentResult.paymentId
          });
          expect(depositTransactionDocuments).toHaveLength(1);
          expect(depositTransactionDocuments).toEqual(
            expect.arrayContaining([
              expect.objectContaining({
                portfolio: generalInvestmentPortfolio._id,
                status: "Pending"
              })
            ])
          );
        });
      });

      describe("and the user has two real portfolios (GIA and ISA) and want to deposit to their ISA portfolio", () => {
        let investmentSavingsPortfolio: PortfolioDocument;

        const paymentResult: { paymentId: string; paymentUri: string; status: PaymentStatusTypeV3 } = {
          paymentId: faker.string.uuid(),
          paymentUri: faker.internet.url(),
          status: "authorized" as PaymentStatusTypeV3
        };

        beforeEach(async () => {
          const user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
          const generalInvestmentAccount = await buildAccount({
            owner: user.id,
            wrapperType: PortfolioWrapperTypeEnum.GIA
          });
          const investmentSavingsAccount = await buildAccount({
            owner: user.id,
            wrapperType: PortfolioWrapperTypeEnum.GIA
          });
          await buildPortfolio({
            owner: user.id,
            mode: PortfolioModeEnum.REAL,
            account: generalInvestmentAccount,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });
          // User also has an ISA portfolio
          investmentSavingsPortfolio = await buildPortfolio({
            owner: user.id,
            mode: PortfolioModeEnum.REAL,
            account: investmentSavingsAccount,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });
          await user.populate("portfolios bankAccounts");

          truelayerClientSpy.mockRestore();
          jest
            .spyOn(TruelayerPaymentsClient.prototype, "createPayment")
            .mockImplementation(
              async (): Promise<{ paymentId: string; paymentUri: string; status: PaymentStatusTypeV3 }> => {
                return paymentResult;
              }
            );

          response = await request(app)
            .post("/api/m2m/transactions/deposits")
            .send({
              paymentAmount: "50",
              bankAccountId: user.bankAccounts[0]._id.toString(),
              portfolioId: investmentSavingsPortfolio.id
            })
            .set("external-user-id", user.id)
            .set("Accept", "application/json");
        });
        afterEach(async () => await clearDb());

        it("should create a truelayer payment and deposit transaction document", async () => {
          expect(TruelayerPaymentsClient.prototype.createPayment).toBeCalledTimes(1);

          const depositTransactionDocuments = await DepositCashTransaction.find({
            "providers.truelayer.id": paymentResult.paymentId
          });
          expect(depositTransactionDocuments).toHaveLength(1);
          expect(depositTransactionDocuments).toEqual(
            expect.arrayContaining([
              expect.objectContaining({
                portfolio: investmentSavingsPortfolio._id,
                status: "Pending"
              })
            ])
          );
        });
      });
    });

    describe("when an eligible user creates a payment with depositAndInvest flag", () => {
      const paymentResult: { paymentId: string; paymentUri: string; status: PaymentStatusTypeV3 } = {
        paymentId: faker.string.uuid(),
        paymentUri: faker.internet.url(),
        status: "authorized" as PaymentStatusTypeV3
      };

      beforeEach(async () => {
        const user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        await user.populate("portfolios bankAccounts");

        truelayerClientSpy.mockRestore();
        jest.spyOn(TruelayerPaymentsClient.prototype, "createPayment").mockImplementation(
          async (): Promise<{
            paymentId: string;
            paymentUri: string;
            status: PaymentStatusTypeV3;
          }> => paymentResult
        );

        await request(app)
          .post("/api/m2m/transactions/deposits")
          .send({
            paymentAmount: "50",
            bankAccountId: user.bankAccounts[0]._id.toString(),
            depositAndInvest: true
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterEach(async () => await clearDb());

      it("should create a truelayer payment", () => {
        expect(TruelayerPaymentsClient.prototype.createPayment).toBeCalledTimes(1);
      });

      it("should create a new deposit transaction", async () => {
        const deposits = await DepositCashTransaction.find({
          "providers.truelayer.id": paymentResult.paymentId
        });
        expect(deposits).toHaveLength(1);

        const deposit = deposits[0];
        expect(deposit.activeProviders.length).toBe(2);
        expect(deposit).toEqual(
          expect.objectContaining({
            depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
            activeProviders: expect.arrayContaining([ProviderEnum.WEALTHKERNEL, ProviderEnum.TRUELAYER]),
            providers: expect.objectContaining({
              truelayer: {
                id: paymentResult.paymentId,
                status: paymentResult.status,
                version: "v3"
              }
            }),
            status: "Pending"
          })
        );
      });
    });
  });

  describe("POST /transactions/charges/lifetime", () => {
    const truelayerClientSpy = jest.spyOn(TruelayerPaymentsClient.prototype, "createPayment");

    describe("when the user hasn't passed KYC", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const user = await buildUser({ kycStatus: KycStatusEnum.PENDING });

        truelayerClientSpy.mockRestore();
        jest.spyOn(TruelayerPaymentsClient.prototype, "createPayment").mockResolvedValue({
          paymentId: faker.string.uuid(),
          paymentUri: faker.internet.url(),
          status: "authorized" as PaymentStatusTypeV3
        });

        response = await request(app)
          .post("/api/m2m/transactions/charges/lifetime")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should not create a truelayer payment", () => {
        expect(TruelayerPaymentsClient.prototype.createPayment).not.toHaveBeenCalled();
      });

      it("should not create a charge document", async () => {
        expect(await ChargeTransaction.find({})).toHaveLength(0);
      });

      it("should return status 400 with proper message", () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "User has not passed kyc"
            }
          })
        );
      });
    });

    describe("when the request does not have a price", () => {
      let response: request.Response;

      const PAYMENT_ID = faker.string.uuid();

      beforeAll(async () => {
        const user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });

        truelayerClientSpy.mockRestore();
        jest.spyOn(TruelayerPaymentsClient.prototype, "createPayment").mockResolvedValue({
          paymentId: PAYMENT_ID,
          paymentUri: faker.internet.url(),
          status: "authorized" as PaymentStatusTypeV3
        });

        response = await request(app)
          .post("/api/m2m/transactions/charges/lifetime")
          .send({
            bankAccountId: user.bankAccounts[0]._id.toString()
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should not create a truelayer payment", () => {
        expect(TruelayerPaymentsClient.prototype.createPayment).not.toHaveBeenCalled();
      });

      it("should not create a new charge transaction", async () => {
        const charges = await ChargeTransaction.find({
          "providers.truelayer.id": PAYMENT_ID
        });
        expect(charges).toHaveLength(0);
      });

      it("should return status 400 with proper message", () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "Missing field 'price'"
            }
          })
        );
      });
    });

    describe("when the user does not have a subscription", () => {
      let response: request.Response;

      const PAYMENT_ID = faker.string.uuid();
      const PAYMENT_URI = faker.internet.url();

      beforeAll(async () => {
        const user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });

        truelayerClientSpy.mockRestore();
        jest.spyOn(TruelayerPaymentsClient.prototype, "createPayment").mockResolvedValue({
          paymentId: PAYMENT_ID,
          paymentUri: PAYMENT_URI,
          status: "authorized" as PaymentStatusTypeV3
        });

        response = await request(app)
          .post("/api/m2m/transactions/charges/lifetime")
          .send({
            price: "paid_mid_lifetime_blackfriday_2023",
            bankAccountId: user.bankAccounts[0]._id.toString()
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should not create a truelayer payment", () => {
        expect(TruelayerPaymentsClient.prototype.createPayment).not.toHaveBeenCalled();
      });

      it("should not create a new charge transaction", async () => {
        const charges = await ChargeTransaction.find({
          "providers.truelayer.id": PAYMENT_ID
        });
        expect(charges).toHaveLength(0);
      });

      it("should return status 500", () => {
        expect(response.status).toEqual(500);
      });
    });

    describe("when the user has already a subscription with a different lifetime plan", () => {
      let response: request.Response;

      const PAYMENT_ID = faker.string.uuid();
      const PAYMENT_URI = faker.internet.url();

      beforeAll(async () => {
        const user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });

        await buildSubscription({ owner: user.id, active: true, price: "paid_low_lifetime_blackfriday_2023" });
        await buildChargeTransaction({
          owner: user.id,
          chargeType: "subscription",
          chargeMethod: "lifetime-payment"
        });
        truelayerClientSpy.mockRestore();
        jest.spyOn(TruelayerPaymentsClient.prototype, "createPayment").mockResolvedValue({
          paymentId: PAYMENT_ID,
          paymentUri: PAYMENT_URI,
          status: "authorized" as PaymentStatusTypeV3
        });

        response = await request(app)
          .post("/api/m2m/transactions/charges/lifetime")
          .send({
            price: "paid_mid_lifetime_blackfriday_2023",
            bankAccountId: user.bankAccounts[0]._id.toString()
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should create a truelayer payment", () => {
        expect(TruelayerPaymentsClient.prototype.createPayment).toBeCalledTimes(1);
        expect(TruelayerPaymentsClient.prototype.createPayment).toBeCalledWith(
          expect.objectContaining({
            redirectUri: expect.stringContaining("/investor/truelayer-pay-callback/pay-lifetime-subscription")
          })
        );
      });

      it("should create a new charge transaction", async () => {
        const charges = await ChargeTransaction.find({
          "providers.truelayer.id": PAYMENT_ID
        });
        expect(charges).toHaveLength(1);

        const charge = charges[0];
        expect(charge).toEqual(
          expect.objectContaining({
            activeProviders: [ProviderEnum.TRUELAYER],
            providers: expect.objectContaining({
              truelayer: {
                id: PAYMENT_ID,
                status: "authorized"
              }
            })
          })
        );
      });

      it("should return a redirect URI", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toEqual(
          expect.objectContaining({
            data: {
              paymentUri: PAYMENT_URI
            }
          })
        );
      });
    });

    describe("when the user has already a subscription with the same lifetime plan", () => {
      let response: request.Response;

      const PAYMENT_ID = faker.string.uuid();
      const PAYMENT_URI = faker.internet.url();

      beforeAll(async () => {
        const user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });

        await buildSubscription({ owner: user.id, active: true, price: "paid_mid_lifetime_sweatcoin_1" });
        await buildChargeTransaction({
          owner: user.id,
          chargeType: "subscription",
          chargeMethod: "lifetime-payment"
        });
        truelayerClientSpy.mockRestore();
        jest.spyOn(TruelayerPaymentsClient.prototype, "createPayment").mockResolvedValue({
          paymentId: PAYMENT_ID,
          paymentUri: PAYMENT_URI,
          status: "authorized" as PaymentStatusTypeV3
        });

        response = await request(app)
          .post("/api/m2m/transactions/charges/lifetime")
          .send({
            price: "paid_mid_lifetime_sweatcoin_1",
            bankAccountId: user.bankAccounts[0]._id.toString()
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should return status 500", () => {
        expect(response.status).toEqual(500);
      });

      it("should not create a truelayer payment", () => {
        expect(TruelayerPaymentsClient.prototype.createPayment).not.toHaveBeenCalled();
      });

      it("should not create a new charge transaction", async () => {
        const charges = await ChargeTransaction.find({
          "providers.truelayer.id": PAYMENT_ID
        });
        expect(charges).toHaveLength(0);
      });
    });

    describe("when the request is valid", () => {
      let response: request.Response;

      const PAYMENT_ID = faker.string.uuid();
      const PAYMENT_URI = faker.internet.url();

      beforeAll(async () => {
        const user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        await buildSubscription({ owner: user.id, active: true, price: "free_monthly" });

        truelayerClientSpy.mockRestore();
        jest.spyOn(TruelayerPaymentsClient.prototype, "createPayment").mockResolvedValue({
          paymentId: PAYMENT_ID,
          paymentUri: PAYMENT_URI,
          status: "authorized" as PaymentStatusTypeV3
        });

        response = await request(app)
          .post("/api/m2m/transactions/charges/lifetime")
          .send({
            price: "paid_mid_lifetime_blackfriday_2023",
            bankAccountId: user.bankAccounts[0]._id.toString()
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should create a truelayer payment", () => {
        expect(TruelayerPaymentsClient.prototype.createPayment).toBeCalledTimes(1);
        expect(TruelayerPaymentsClient.prototype.createPayment).toBeCalledWith(
          expect.objectContaining({
            redirectUri: expect.stringContaining("/investor/truelayer-pay-callback/pay-lifetime-subscription")
          })
        );
      });

      it("should create a new charge transaction", async () => {
        const charges = await ChargeTransaction.find({
          "providers.truelayer.id": PAYMENT_ID
        });
        expect(charges).toHaveLength(1);

        const charge = charges[0];
        expect(charge).toEqual(
          expect.objectContaining({
            activeProviders: [ProviderEnum.TRUELAYER],
            providers: expect.objectContaining({
              truelayer: {
                id: PAYMENT_ID,
                status: "authorized"
              }
            })
          })
        );
      });

      it("should return a redirect URI", async () => {
        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toEqual(
          expect.objectContaining({
            data: {
              paymentUri: PAYMENT_URI
            }
          })
        );
      });
    });
  });

  describe("POST /transactions/savings/deposit", () => {
    const truelayerClientSpy = jest.spyOn(TruelayerPaymentsClient.prototype, "createPayment");

    let response: supertest.Response;

    beforeEach(() => {
      jest.clearAllMocks();
    });

    describe("when the user hasn't passed KYC", () => {
      beforeAll(async () => {
        const user = await buildUser({ kycStatus: KycStatusEnum.PENDING });

        truelayerClientSpy.mockResolvedValue({
          paymentId: faker.string.uuid(),
          paymentUri: faker.internet.url(),
          status: "authorized" as PaymentStatusTypeV3
        });

        response = await request(app)
          .post("/api/m2m/transactions/savings/deposit")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => await clearDb());

      it("should not create a truelayer payment", () => {
        expect(TruelayerPaymentsClient.prototype.createPayment).not.toHaveBeenCalled();
      });

      it("should not create a deposit db document", async () => {
        expect(await DepositCashTransaction.countDocuments({})).toEqual(0);
      });

      it("should return status 400 with proper message", () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "User has not passed kyc"
            }
          })
        );
      });
    });

    describe("and the user doesn't have a real portfolio", () => {
      beforeAll(async () => {
        const user: UserDocument = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        response = await request(app)
          .post("/api/m2m/transactions/savings/deposit")
          .send({
            paymentAmount: "50",
            bankAccountId: user.bankAccounts[0]._id.toString()
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      it("should not create a truelayer payment", () => {
        expect(truelayerClientSpy).not.toHaveBeenCalled();
      });

      it("should not create a deposit db document", async () => {
        expect(await DepositCashTransaction.countDocuments({})).toEqual(0);
      });

      it("should return 400", () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "User has not REAL portfolio"
            }
          })
        );
      });
    });

    describe("and the payment amount is invalid", () => {
      beforeAll(async () => {
        const user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        await buildPortfolio({ owner: user.id, mode: PortfolioModeEnum.REAL });
        await user.populate("portfolios");

        response = await request(app)
          .post("/api/m2m/transactions/savings/deposit")
          .send({
            paymentAmount: "50aa543^*&",
            bankAccountId: user.bankAccounts[0]._id.toString()
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      it("should not create a truelayer payment", () => {
        expect(truelayerClientSpy).not.toHaveBeenCalled();
      });

      it("should not create a deposit db document", async () => {
        expect(await DepositCashTransaction.countDocuments({})).toEqual(0);
      });

      it("should return 400", () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Invalid parameter",
              message: "Invalid value for param 'paymentAmount' , should be numeric"
            }
          })
        );
      });
    });

    describe("and the user passes a bank account ID", () => {
      describe("and the selected bank account can't be found", () => {
        beforeEach(async () => {
          const user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
          response = await request(app)
            .post("/api/m2m/transactions/savings/deposit")
            .send({
              paymentAmount: "50",
              bankAccountId: (await buildUser()).bankAccounts[0]._id.toString()
            })
            .set("external-user-id", user.id)
            .set("Accept", "application/json");
        });
        afterEach(async () => await clearDb());

        it("should not create a truelayer payment", () => {
          expect(truelayerClientSpy).not.toHaveBeenCalled();
        });

        it("should not create a deposit db document", async () => {
          expect(await DepositCashTransaction.countDocuments({})).toEqual(0);
        });

        it("should log the error", () => {
          expect(logger.error).toHaveBeenCalled();
        });

        it("should return 400 with proper message", () => {
          expect(response.status).toEqual(400);
          expect(JSON.parse(response.text)).toMatchObject(
            expect.objectContaining({
              error: {
                description: "Operation failed",
                message: "Bank account does not belong to user"
              }
            })
          );
        });
      });

      describe("and the selected bank account is inactive", () => {
        beforeEach(async () => {
          const user = await buildUser({ kycStatus: KycStatusEnum.PASSED }, false);
          const bankAccount = await buildBankAccount({ owner: user.id, active: false });
          response = await request(app)
            .post("/api/m2m/transactions/savings/deposit")
            .send({
              paymentAmount: "50",
              bankAccountId: bankAccount.id
            })
            .set("external-user-id", user.id)
            .set("Accept", "application/json");
        });

        it("should not create a truelayer payment", () => {
          expect(truelayerClientSpy).not.toHaveBeenCalled();
        });

        it("should not create a deposit db document", async () => {
          expect(await DepositCashTransaction.countDocuments({})).toEqual(0);
        });

        it("should log the error", () => {
          expect(logger.error).toHaveBeenCalled();
        });

        it("should return 400 with proper message", () => {
          expect(response.status).toEqual(400);
          expect(JSON.parse(response.text)).toMatchObject(
            expect.objectContaining({
              error: {
                description: "Operation failed",
                message: "Bank account is not active"
              }
            })
          );
        });
      });

      describe("and the user doesn't own the bank account", () => {
        beforeEach(async () => {
          const user = await buildUser({
            kycStatus: KycStatusEnum.PASSED
          });
          await buildPortfolio({ owner: user.id, mode: PortfolioModeEnum.REAL });
          response = await request(app)
            .post("/api/m2m/transactions/savings/deposit")
            .send({
              paymentAmount: "50",
              // here we create another bank account that does not belong to target user
              bankAccountId: (await buildUser()).bankAccounts[0]._id.toString()
            })
            .set("external-user-id", user.id)
            .set("Accept", "application/json");
        });
        afterEach(async () => await clearDb());

        it("should not create a truelayer payment", () => {
          expect(truelayerClientSpy).not.toHaveBeenCalled();
        });

        it("should not create a deposit db document", async () => {
          expect(await DepositCashTransaction.countDocuments({})).toEqual(0);
        });

        it("should log the error", () => {
          expect(logger.error).toHaveBeenCalled();
        });

        it("should return 400 with message", () => {
          expect(response.status).toEqual(400);
          expect(JSON.parse(response.text)).toMatchObject(
            expect.objectContaining({
              error: {
                description: "Operation failed",
                message: "Bank account does not belong to user"
              }
            })
          );
        });
      });

      describe("and the user owns the bank account", () => {
        const paymentResult: { paymentId: string; paymentUri: string; status: PaymentStatusTypeV3 } = {
          paymentId: faker.string.uuid(),
          paymentUri: faker.internet.url(),
          status: "authorized" as PaymentStatusTypeV3
        };

        beforeEach(async () => {
          const user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
          await buildPortfolio({
            owner: user.id,
            mode: PortfolioModeEnum.REAL,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          });
          await user.populate("portfolios bankAccounts");

          truelayerClientSpy.mockRestore();
          jest.spyOn(TruelayerPaymentsClient.prototype, "createPayment").mockResolvedValue(paymentResult);

          response = await request(app)
            .post("/api/m2m/transactions/savings/deposit")
            .send({
              paymentAmount: "50",
              bankAccountId: user.bankAccounts[0]._id.toString()
            })
            .set("external-user-id", user.id)
            .set("Accept", "application/json")
            .expect(200);
        });
        afterEach(async () => await clearDb());

        it("should create a truelayer payment", () => {
          expect(TruelayerPaymentsClient.prototype.createPayment).toBeCalledTimes(1);
        });

        it("should create a new deposit transaction", async () => {
          const deposits = await DepositCashTransaction.find({
            "providers.truelayer.id": paymentResult.paymentId
          });
          expect(deposits).toHaveLength(1);

          const deposit = deposits[0];
          expect(deposit.activeProviders.length).toBe(2);
          expect(deposit).toEqual(
            expect.objectContaining({
              depositAction: DepositActionEnum.DEPOSIT_AND_SAVE,
              activeProviders: expect.arrayContaining([ProviderEnum.WEALTHKERNEL, ProviderEnum.TRUELAYER]),
              providers: expect.objectContaining({
                truelayer: {
                  id: paymentResult.paymentId,
                  status: paymentResult.status,
                  version: "v3"
                }
              }),
              status: "Pending"
            })
          );
        });
      });
    });

    describe("when the request is valid", () => {
      const paymentResult: { paymentId: string; paymentUri: string; status: PaymentStatusTypeV3 } = {
        paymentId: faker.string.uuid(),
        paymentUri: faker.internet.url(),
        status: "authorized" as PaymentStatusTypeV3
      };

      beforeEach(async () => {
        const user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        await user.populate("portfolios bankAccounts");

        truelayerClientSpy.mockRestore();
        jest.spyOn(TruelayerPaymentsClient.prototype, "createPayment").mockResolvedValue(paymentResult);

        response = await request(app)
          .post("/api/m2m/transactions/savings/deposit")
          .send({
            paymentAmount: "50",
            bankAccountId: user.bankAccounts[0]._id.toString()
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json")
          .expect(200);
      });
      afterEach(async () => await clearDb());

      it("should create a truelayer payment", () => {
        expect(TruelayerPaymentsClient.prototype.createPayment).toBeCalledTimes(1);
      });

      it("should create a new deposit transaction", async () => {
        const deposits = await DepositCashTransaction.find({
          "providers.truelayer.id": paymentResult.paymentId
        });
        expect(deposits).toHaveLength(1);

        const deposit = deposits[0];
        expect(deposit.activeProviders.length).toBe(2);
        expect(deposit).toEqual(
          expect.objectContaining({
            depositAction: DepositActionEnum.DEPOSIT_AND_SAVE,
            activeProviders: expect.arrayContaining([ProviderEnum.WEALTHKERNEL, ProviderEnum.TRUELAYER]),
            providers: expect.objectContaining({
              truelayer: {
                id: paymentResult.paymentId,
                status: paymentResult.status,
                version: "v3"
              }
            }),
            status: "Pending"
          })
        );
      });

      it("should return a redirect URI", async () => {
        const deposit = await DepositCashTransaction.findOne({
          "providers.truelayer.id": paymentResult.paymentId
        });

        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toEqual(
          expect.objectContaining({
            data: {
              paymentUri: paymentResult.paymentUri,
              depositId: deposit.id
            }
          })
        );
      });
    });
  });

  describe("GET /transactions/assets/pending-deposit/:id", () => {
    beforeEach(() => jest.clearAllMocks());

    it("should return status 400 and error message for invalid id", async () => {
      const user = await buildUser();
      const response = await request(app)
        .get("/api/m2m/transactions/assets/pending-deposit/sdasda243%$")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid url",
            message: "Invalid url"
          }
        })
      );
    });

    it("should return status 400 and error message for non-existing transaction", async () => {
      const user = await buildUser();
      const response = await request(app)
        .get("/api/m2m/transactions/assets/pending-deposit/foo123")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Transaction does not exist"
          }
        })
      );
    });

    it("should return status 200 with transaction object with populated orders and cashback for valid id", async () => {
      const user = await buildUser();
      const depositTransaction = await buildDepositCashTransaction({ owner: user.id });
      const assetTransaction = await buildAssetTransaction({
        owner: user.id,
        pendingDeposit: depositTransaction.id
      });
      await buildCashbackTransaction({
        status: "Pending",
        owner: user.id,
        linkedAssetTransaction: assetTransaction.id
      });

      const response = await request(app)
        .get(`/api/m2m/transactions/assets/pending-deposit/${depositTransaction.id}`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedTransaction = await AssetTransaction.findOne({
        _id: assetTransaction._id,
        owner: user.id
      })
        .populate("orders")
        .populate("cashback");
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedTransaction)));
    });
  });

  describe("POST /transactions/preview", () => {
    const WK_PORTFOLIO_ID = "WK_PORTFOLIO_ID";

    afterAll(async () => {
      jest.restoreAllMocks();
      await clearDb();
    });

    describe("when a query parameter category is not passed", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });
        response = await request(app)
          .post(`/api/m2m/transactions/preview?portfolioId=${portfolio.id}`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 400 with proper message", () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Invalid parameter",
              message: expect.stringContaining("Param 'category' is required")
            }
          })
        );
      });
    });

    describe("when a query parameter portfolioId is not passed", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const user: UserDocument = await buildUser();
        response = await request(app)
          .post(
            "/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=buy&allocationMethod=holdings"
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 400 with proper message", () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Invalid parameter",
              message: "Param 'portfolioId' is required"
            }
          })
        );
      });
    });

    describe("when a query parameter allocationMethod is not passed", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });
        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=buy&portfolioId=${portfolio.id}`
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 400 with proper message", () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Invalid parameter",
              message: "Param 'allocationMethod' is required, must be one of [targetAllocation,holdings]"
            }
          })
        );
      });
    });

    describe("when a query parameter type is not passed and category is AssetTransaction", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });
        response = await request(app)
          .post(`/api/m2m/transactions/preview?category=AssetTransaction&portfolioId=${portfolio.id}`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 400 with proper message", () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "Preview cannot be given for asset transaction type undefined"
            }
          })
        );
      });
    });

    describe("when a query parameter category is AssetTransaction and type is update but no order is in the body", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });
        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=update&portfolioId=${portfolio.id}`
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 400 with proper message", () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "Update portfolio operation preview required pendingOrders in body"
            }
          })
        );
      });
    });

    describe("when request is made for a portfolio sell operation", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 1.27
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.787402
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 1.27
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 1.27
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
        }[] = [
          { assetId: "real_estate_us", quantity: 1, price: 10 },
          { assetId: "equities_eu", quantity: 1, price: 10 }
        ];

        const user = await buildUser();
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((asset) =>
            buildHoldingDTO(true, asset.assetId, asset.quantity, {
              pricePerCurrency: { EUR: asset.price, GBP: asset.price, USD: asset.price }
            })
          )
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });

        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=sell&portfolioId=${portfolio.id}&orderAmount=10`
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 400 with proper message", () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "Preview cannot be given for asset transaction type sell"
            }
          })
        );
      });
    });

    describe("when request is made for a portfolio update operation with two orders", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = ["real_estate_us"];

        const user = await buildUser();
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });
        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=update&portfolioId=${portfolio.id}`
          )
          .send({
            pendingOrders: {
              real_estate_us: {
                side: "buy",
                money: 10
              },
              government_bonds_uk: {
                side: "buy",
                money: 10
              }
            }
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 400 with proper message", () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "Portfolio update only allows a single order in the request body"
            }
          })
        );
      });
    });

    describe("when request is made for a portfolio update operation with a ETF buy order for a UK user", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);
        // Current FX rate GBP <-> USD is 1.27
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.787402
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 1.27
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 1.27
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = ["real_estate_us"];

        const user = await buildUser();
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });
        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=update&portfolioId=${portfolio.id}`
          )
          .send({
            pendingOrders: {
              real_estate_us: {
                side: "buy",
                money: 11
              }
            }
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview of the transaction", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            executionWindow: {
              smart: {
                etfs: {
                  start: "2022-07-18T12:00:00.000Z",
                  end: "2022-07-18T17:00:00.000Z",
                  executionType: "MARKET_HOURS"
                }
              }
            },
            fees: {
              smart: {
                fx: {
                  amount: 0.02, // 0.15% of £10 (£11 - £1 for real time fee), rounded to 2 decimal places
                  currency: "GBP"
                },
                commission: {
                  amount: 0, // 0%
                  currency: "GBP"
                },
                executionSpread: {
                  amount: 0, // 0% of £10
                  currency: "GBP"
                },
                realtimeExecution: {
                  amount: 0, // 0%
                  currency: "GBP"
                }
              }
            },
            orders: {
              smart: {
                real_estate_us: {
                  side: "buy",
                  quantity: 0.22,
                  money: 11
                }
              }
            },
            foreignCurrencyRates: {
              USD: 1.263 // £1 -> $1.27 initially, then 1.27 * (1 - 0.0055) for buy order with free plan spread
            }
          })
        );
      });
    });

    describe("when request is made for a portfolio update operation with a ETF buy order for an EU user within market hours", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-18T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 1.27
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.787402
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 1.27
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 1.27
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = ["real_estate_us"];

        const user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE });
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });
        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=update&portfolioId=${portfolio.id}`
          )
          .send({
            pendingOrders: {
              real_estate_us: {
                side: "buy",
                money: 11
              }
            }
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview of the transaction including realtime execution fee", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            executionWindow: {
              express: {
                etfs: {
                  executionType: "REALTIME"
                }
              },
              smart: {
                etfs: {
                  start: "2022-07-18T12:00:00.000Z",
                  end: "2022-07-18T17:00:00.000Z",
                  executionType: "MARKET_HOURS"
                }
              }
            },
            fees: {
              express: {
                fx: {
                  amount: 0.02, // 0.15% of £10 (£11 - £1 for real time fee) , rounded to 2 decimal places
                  currency: "GBP"
                },
                commission: {
                  amount: 0, // 0%
                  currency: "GBP"
                },
                executionSpread: {
                  amount: 0, // 0% of £10
                  currency: "GBP"
                },
                realtimeExecution: {
                  amount: 1,
                  currency: "GBP"
                }
              },
              smart: {
                fx: {
                  amount: 0.02, // 0.15% of £10, rounded to 2 decimal places
                  currency: "GBP"
                },
                commission: {
                  amount: 0, // 0%
                  currency: "GBP"
                },
                executionSpread: {
                  amount: 0, // 0% of £10
                  currency: "GBP"
                },
                realtimeExecution: {
                  amount: 0,
                  currency: "GBP"
                }
              }
            },
            orders: {
              smart: {
                real_estate_us: {
                  side: "buy",
                  quantity: 0.22,
                  money: 11
                }
              },
              express: {
                real_estate_us: {
                  side: "buy",
                  quantity: 0.2,
                  money: 11
                }
              }
            },
            foreignCurrencyRates: {
              USD: 1.263 // £1 -> $1.27 initially, then 1.27 * (1 - 0.0055) for buy order with free plan spread
            }
          })
        );
      });
    });

    describe("when request is made for a portfolio update operation with a ETF buy order for an EU user outside of market hours", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-18T06:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 1.27
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.787402
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 1.27
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 1.27
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = ["real_estate_us"];

        const user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE });
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });
        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=update&portfolioId=${portfolio.id}&executeEtfOrdersInRealtime=true`
          )
          .send({
            pendingOrders: {
              real_estate_us: {
                side: "buy",
                money: 11
              }
            }
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview of the transaction", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            executionWindow: {
              smart: {
                etfs: {
                  start: "2022-07-18T12:00:00.000Z",
                  end: "2022-07-18T17:00:00.000Z",
                  executionType: "MARKET_HOURS"
                }
              },
              express: {
                etfs: {
                  start: "2022-07-18T07:00:00.000Z",
                  end: "2022-07-18T15:30:00.000Z",
                  executionType: "MARKET_HOURS"
                }
              }
            },
            fees: {
              smart: {
                fx: {
                  amount: 0.02, // 0.15% of £10 (£11 - £1 for real time fee), rounded to 2 decimal places
                  currency: "GBP"
                },
                commission: {
                  amount: 0, // 0%
                  currency: "GBP"
                },
                executionSpread: {
                  amount: 0, // 0% of £10
                  currency: "GBP"
                },
                realtimeExecution: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              express: {
                fx: {
                  amount: 0.02, // 0.15% of £10, rounded to 2 decimal places
                  currency: "GBP"
                },
                commission: {
                  amount: 0, // 0%
                  currency: "GBP"
                },
                executionSpread: {
                  amount: 0, // 0% of £10
                  currency: "GBP"
                },
                realtimeExecution: {
                  amount: 1,
                  currency: "GBP"
                }
              }
            },
            orders: {
              smart: {
                real_estate_us: {
                  side: "buy",
                  quantity: 0.22,
                  money: 11
                }
              },
              express: {
                real_estate_us: {
                  side: "buy",
                  quantity: 0.2,
                  money: 11
                }
              }
            },
            foreignCurrencyRates: {
              USD: 1.263 // £1 -> $1.27 initially, then 1.27 * (1 - 0.0055) for buy order with free plan spread
            }
          })
        );
      });
    });

    describe("when request is made for a portfolio update operation with a Stock buy order and we are outside realtime market hours", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T21:30:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 1.27
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.787402
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 1.27
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 1.27
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = ["equities_apple"];

        const user = await buildUser();
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });
        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=update&portfolioId=${portfolio.id}`
          )
          .send({
            pendingOrders: {
              equities_apple: {
                side: "buy",
                money: 10
              }
            }
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview of the transaction", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            executionWindow: {
              express: {
                stocks: {
                  start: "2022-07-18T13:30:00.000Z",
                  end: "2022-07-18T20:00:00.000Z",
                  executionType: "MARKET_HOURS"
                }
              }
            },
            fees: {
              express: {
                fx: {
                  amount: 0.02, // 0.15% of £10, rounded to 2 decimal places
                  currency: "GBP"
                },
                commission: {
                  amount: 0, // 0%
                  currency: "GBP"
                },
                executionSpread: {
                  amount: 0, // 0% of £10
                  currency: "GBP"
                },
                realtimeExecution: {
                  amount: 0,
                  currency: "GBP"
                }
              }
            },
            orders: {
              express: {
                equities_apple: {
                  side: "buy",
                  quantity: 0.2,
                  money: 10
                }
              }
            },
            foreignCurrencyRates: {
              USD: 1.263 // £1 -> $1.27 initially, then 1.27 * (1 - 0.0055) for buy order with free plan spread
            }
          })
        );
      });
    });

    describe("when request is made for a portfolio update operation with a Stock buy order and we are within realtime market hours", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-18T15:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 1.27
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.787402
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 1.27
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 1.27
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = ["equities_apple"];

        const user = await buildUser();
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });
        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=update&portfolioId=${portfolio.id}`
          )
          .send({
            pendingOrders: {
              equities_apple: {
                side: "buy",
                money: 10
              }
            }
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview of the transaction", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            executionWindow: {
              express: {
                stocks: {
                  executionType: "REALTIME"
                }
              }
            },
            fees: {
              express: {
                fx: {
                  amount: 0.02, // 0.15% of £10, rounded to 2 decimal places
                  currency: "GBP"
                },
                commission: {
                  amount: 0, // 0%
                  currency: "GBP"
                },
                executionSpread: {
                  amount: 0, // 0% of £10
                  currency: "GBP"
                },
                realtimeExecution: {
                  amount: 0,
                  currency: "GBP"
                }
              }
            },
            orders: {
              express: {
                equities_apple: {
                  side: "buy",
                  quantity: 0.2,
                  money: 10
                }
              }
            },
            foreignCurrencyRates: {
              USD: 1.263 // £1 -> $1.27 initially, then 1.27 * (1 - 0.0055) for buy order with free plan spread
            }
          })
        );
      });
    });

    describe("when request is made for a portfolio update operation with a ETF sell order for a UK user", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 1.27
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.787402
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 1.27
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 1.27
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = ["real_estate_us"];

        const user = await buildUser();
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });

        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=update&portfolioId=${portfolio.id}`
          )
          .send({
            pendingOrders: {
              real_estate_us: {
                side: "sell",
                quantity: 1
              }
            }
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview of the transaction", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            executionWindow: {
              smart: {
                etfs: {
                  start: "2022-07-18T12:00:00.000Z",
                  end: "2022-07-18T17:00:00.000Z",
                  executionType: "MARKET_HOURS"
                }
              }
            },
            fees: {
              smart: {
                fx: {
                  amount: 0.08, // 0.15% of £50 (sell order amount), rounded to 2 decimal places
                  currency: "GBP"
                },
                commission: {
                  amount: 0, // 0%
                  currency: "GBP"
                },
                executionSpread: {
                  amount: 0, // 0% of £50
                  currency: "GBP"
                },
                realtimeExecution: {
                  amount: 0,
                  currency: "GBP"
                }
              }
            },
            foreignCurrencyRates: {
              USD: 1.277 // £1 -> $1.27 initially, then 1.27 * (1 + 0.0055) for sell order with free plan spread
            },
            orders: {
              smart: {
                real_estate_us: {
                  side: "sell",
                  quantity: 1,
                  money: 50 // Quantity (1) * Current Price (50)
                }
              }
            }
          })
        );
      });
    });

    describe("when request is made for a portfolio update operation with a ETF sell order for an EU user within market hours", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-18T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 1.27
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.787402
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 1.27
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 1.27
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = ["real_estate_us"];

        const user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE });
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 51 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });

        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=update&portfolioId=${portfolio.id}`
          )
          .send({
            pendingOrders: {
              real_estate_us: {
                side: "sell",
                quantity: 1
              }
            }
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview of the transaction", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            executionWindow: {
              express: {
                etfs: {
                  executionType: "REALTIME"
                }
              }
            },
            fees: {
              express: {
                fx: {
                  amount: 0.08, // 0.15% of £50 (£51 - £1 for real time fee) (sell order amount), rounded to 2 decimal places
                  currency: "GBP"
                },
                commission: {
                  amount: 0, // 0%
                  currency: "GBP"
                },
                executionSpread: {
                  amount: 0, // 0% of £50
                  currency: "GBP"
                },
                realtimeExecution: {
                  amount: 1,
                  currency: "GBP"
                }
              }
            },
            foreignCurrencyRates: {
              USD: 1.277 // £1 -> $1.27 initially, then 1.27 * (1 + 0.0055) for sell order with free plan spread
            },
            orders: {
              express: {
                real_estate_us: {
                  side: "sell",
                  quantity: 1,
                  money: 50 // Quantity (1) * Current Price (51) - realtime commission fee
                }
              }
            }
          })
        );
      });
    });

    describe("when request is made for a portfolio update operation with a ETF sell order for an EU user outside market hours", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 1.27
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.787402
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 1.27
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 1.27
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = ["real_estate_us"];

        const user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE });
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 51 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });

        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=update&portfolioId=${portfolio.id}`
          )
          .send({
            pendingOrders: {
              real_estate_us: {
                side: "sell",
                quantity: 1
              }
            }
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview of the transaction", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            executionWindow: {
              express: {
                etfs: {
                  start: "2022-07-18T07:00:00.000Z",
                  end: "2022-07-18T15:30:00.000Z",
                  executionType: "MARKET_HOURS"
                }
              }
            },
            fees: {
              express: {
                fx: {
                  amount: 0.08, // 0.15% of £50 (£51 - £1 for real time fee) (sell order amount), rounded to 2 decimal places
                  currency: "GBP"
                },
                commission: {
                  amount: 0, // 0%
                  currency: "GBP"
                },
                executionSpread: {
                  amount: 0, // 0% of £50
                  currency: "GBP"
                },
                realtimeExecution: {
                  amount: 1,
                  currency: "GBP"
                }
              }
            },
            foreignCurrencyRates: {
              USD: 1.277 // £1 -> $1.27 initially, then 1.27 * (1 + 0.0055) for sell order with free plan spread
            },
            orders: {
              express: {
                real_estate_us: {
                  side: "sell",
                  quantity: 1,
                  money: 50 // Quantity (1) * Current Price (51) - realtime commission fee
                }
              }
            }
          })
        );
      });
    });

    describe("when request is made for a portfolio update operation with a ETF sell order for an EU user and resulting quantity would be less than £1", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 1.27
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.787402
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 1.27
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 1.27
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = ["real_estate_us"];

        const user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE });
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 1.5, { price: 1 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });

        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=update&portfolioId=${portfolio.id}`
          )
          .send({
            pendingOrders: {
              real_estate_us: {
                side: "sell",
                quantity: 1
              }
            }
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview including willResultInLowQuantityHolding", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            willResultInLowQuantityHolding: true
          })
        );
      });
    });

    describe("when request is made for a portfolio update operation with a Stock sell order for an EU user and resulting quantity would be less than £1", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 1.27
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.787402
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 1.27
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 1.27
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = ["equities_microsoft"];

        const user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE });
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 1.5, { price: 1 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });

        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=update&portfolioId=${portfolio.id}`
          )
          .send({
            pendingOrders: {
              equities_microsoft: {
                side: "sell",
                quantity: 1
              }
            }
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview NOT including willResultInLowQuantityHolding", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.not.objectContaining({
            willResultInLowQuantityHolding: true
          })
        );
      });
    });

    describe("when request is made for a portfolio update operation with a Stock sell order and we are within realtime market hours", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-18T15:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 1.27
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.787402
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 1.27
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 1.27
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = ["equities_apple"];

        const user = await buildUser();
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });
        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=update&portfolioId=${portfolio.id}`
          )
          .send({
            pendingOrders: {
              equities_apple: {
                side: "sell",
                quantity: 1
              }
            }
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview of the transaction", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            executionWindow: {
              express: {
                stocks: {
                  executionType: "REALTIME"
                }
              }
            },
            fees: {
              express: {
                fx: {
                  amount: 0.08, // 0.15% of £50, rounded to 2 decimal places
                  currency: "GBP"
                },
                commission: {
                  amount: 0, // 0%
                  currency: "GBP"
                },
                executionSpread: {
                  amount: 0, // 0% of £50
                  currency: "GBP"
                },
                realtimeExecution: {
                  amount: 0,
                  currency: "GBP"
                }
              }
            },
            orders: {
              express: {
                equities_apple: {
                  side: "sell",
                  quantity: 1,
                  money: 50
                }
              }
            },
            foreignCurrencyRates: {
              USD: 1.277 // £1 -> $1.27 initially, then 1.27 * (1 + 0.0055) for sell order with free plan spread
            }
          })
        );
      });
    });

    describe("when request is made for a portfolio update operation with a Stock sell order and we are within realtime market hours BUT the stock order is close to our minimum asset investment", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-18T15:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 1.27
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.787402
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 1.27
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 1.27
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = ["equities_apple"];

        const user = await buildUser();
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 1.01 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });
        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=update&portfolioId=${portfolio.id}`
          )
          .send({
            pendingOrders: {
              equities_apple: {
                side: "sell",
                quantity: 1 // We are selling one share of Apple which costs 1.01 -> the estimated order amount is £1.01 which is under our limit for realtime execution
              }
            }
          })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview of the transaction", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            executionWindow: {
              express: {
                stocks: {
                  start: "2022-07-19T13:30:00.000Z",
                  end: "2022-07-19T20:00:00.000Z",
                  executionType: "MARKET_HOURS"
                }
              }
            },
            fees: {
              express: {
                fx: {
                  amount: 0.0, // 0.15% of £1.01 = £0.002 ≈ £0.00, rounded to 2 decimal places
                  currency: "GBP"
                },
                commission: {
                  amount: 0, // 0%
                  currency: "GBP"
                },
                executionSpread: {
                  amount: 0,
                  currency: "GBP"
                },
                realtimeExecution: {
                  amount: 0,
                  currency: "GBP"
                }
              }
            },
            orders: {
              express: {
                equities_apple: {
                  side: "sell",
                  quantity: 1,
                  money: 1.01
                }
              }
            },
            foreignCurrencyRates: {
              USD: 1.277 // £1 -> $1.27 initially, then 1.27 * (1 + 0.0055) for sell order with free plan spread
            }
          })
        );
      });
    });

    describe("when request is made for a portfolio buy operation with only ETF orders for a UK user in free", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 1.27
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.787402
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 1.27
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 1.27
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = [
          "real_estate_us",
          "equities_global"
        ];

        const user = await buildUser();
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });

        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=buy&allocationMethod=holdings&portfolioId=${portfolio.id}&orderAmount=10`
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview of the transaction", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            executionWindow: {
              smart: {
                etfs: {
                  start: "2022-07-18T12:00:00.000Z",
                  end: "2022-07-18T17:00:00.000Z",
                  executionType: "MARKET_HOURS"
                }
              }
            },
            fees: {
              smart: {
                fx: {
                  amount: 0.01, // government_bonds_us (USD): £5 × 0.15% = £0.0075 → £0.01, equities_global (GBP): £0 (same currency), Total: £0.01
                  currency: "GBP"
                },
                commission: {
                  amount: 0, // 0%
                  currency: "GBP"
                },
                executionSpread: {
                  amount: 0, // 0% of £10
                  currency: "GBP"
                },
                realtimeExecution: {
                  amount: 0,
                  currency: "GBP"
                }
              }
            },
            orders: {
              smart: {
                real_estate_us: {
                  side: "buy",
                  quantity: 0.1,
                  money: 5
                },
                equities_global: {
                  side: "buy",
                  quantity: 0.1,
                  money: 5
                }
              }
            },
            foreignCurrencyRates: {
              USD: 1.263 // £1 -> $1.27 initially, then 1.27 * (1 - 0.0055) for buy order with free plan spread
            },
            willSkipOrders: false,
            hasETFOrders: true
          })
        );
      });
    });

    describe("when request is made for a portfolio buy operation with only stock orders for a UK user in free", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 5
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.2
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 5
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 5
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = [
          "equities_microsoft",
          "equities_apple"
        ];

        const user = await buildUser();
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });

        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=buy&allocationMethod=holdings&portfolioId=${portfolio.id}&orderAmount=10`
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a hasETFOrders set to false", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject({ hasETFOrders: false });
      });
    });

    describe("when request is made for a portfolio buy operation with both ETF and stock orders for a UK user in free and we are outside both ETF/stock realtime market hours", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 1.27
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.787402
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 1.27
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 1.27
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = [
          "real_estate_us",
          "equities_microsoft"
        ];

        const user = await buildUser();
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });

        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=buy&allocationMethod=holdings&portfolioId=${portfolio.id}&orderAmount=10`
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview of the transaction", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            executionWindow: {
              smart: {
                etfs: {
                  start: "2022-07-18T12:00:00.000Z",
                  end: "2022-07-18T17:00:00.000Z",
                  executionType: "MARKET_HOURS"
                },
                stocks: {
                  start: "2022-07-18T13:30:00.000Z",
                  end: "2022-07-18T20:00:00.000Z",
                  executionType: "MARKET_HOURS"
                }
              }
            },
            fees: {
              smart: {
                fx: {
                  amount: 0.02, // 0.15% of £10 (USD traded asset order amount), rounded to 2 decimal places
                  currency: "GBP"
                },
                commission: {
                  amount: 0, // 0%
                  currency: "GBP"
                },
                executionSpread: {
                  amount: 0, // 0% of £10
                  currency: "GBP"
                },
                realtimeExecution: {
                  amount: 0,
                  currency: "GBP"
                }
              }
            },
            orders: {
              smart: {
                real_estate_us: {
                  side: "buy",
                  quantity: 0.1,
                  money: 5
                },
                equities_microsoft: {
                  side: "buy",
                  quantity: 0.1,
                  money: 5
                }
              }
            },
            foreignCurrencyRates: {
              USD: 1.263 // £1 -> $1.27 initially, then 1.27 * (1 - 0.0055) for buy order with free plan spread
            },
            willSkipOrders: false,
            hasETFOrders: true
          })
        );
      });
    });

    describe("when request is made for a portfolio buy operation with only ETF orders for an EU user in free", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 1.27
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.787402
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 1.27
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 1.27
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = [
          "real_estate_us",
          "equities_global"
        ];

        const user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE });
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });

        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=buy&allocationMethod=holdings&portfolioId=${portfolio.id}&orderAmount=10`
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview of the transaction", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            executionWindow: {
              smart: {
                etfs: {
                  start: "2022-07-18T12:00:00.000Z",
                  end: "2022-07-18T17:00:00.000Z",
                  executionType: "MARKET_HOURS"
                }
              },
              express: {
                etfs: {
                  start: "2022-07-18T07:00:00.000Z",
                  end: "2022-07-18T15:30:00.000Z",
                  executionType: "MARKET_HOURS"
                }
              }
            },
            fees: {
              smart: {
                fx: {
                  amount: 0.01, // 0.15% of £5 (USD traded asset order amount), rounded to 2 decimal places
                  currency: "GBP"
                },
                commission: {
                  amount: 0, // 0%
                  currency: "GBP"
                },
                executionSpread: {
                  amount: 0, // 0% of £10
                  currency: "GBP"
                },
                realtimeExecution: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              express: {
                fx: {
                  amount: 0.01, // 0.15% of £4 (£5 - £1 for real time fee) (USD traded asset order amount), rounded to 2 decimal places
                  currency: "GBP"
                },
                commission: {
                  amount: 0, // 0%
                  currency: "GBP"
                },
                executionSpread: {
                  amount: 0, // 0% of £10
                  currency: "GBP"
                },
                realtimeExecution: {
                  amount: 2, // £1 for each order
                  currency: "GBP"
                }
              }
            },
            orders: {
              smart: {
                real_estate_us: {
                  side: "buy",
                  quantity: 0.1,
                  money: 5
                },
                equities_global: {
                  side: "buy",
                  quantity: 0.1,
                  money: 5
                }
              },
              express: {
                real_estate_us: {
                  side: "buy",
                  quantity: 0.06,
                  money: 5
                },
                equities_global: {
                  side: "buy",
                  quantity: 0.06,
                  money: 5
                }
              }
            },
            foreignCurrencyRates: {
              USD: 1.263 // £1 -> $1.27 initially, then 1.27 * (1 - 0.0055) for buy order with free plan spread
            },
            willSkipOrders: false,
            hasETFOrders: true
          })
        );
      });
    });

    describe("when request is made for a portfolio buy operation with both ETF and stock orders for an EU user in free and we are outside both ETF/stock realtime market hours", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 1.27
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.787402
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 1.27
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 1.27
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = [
          "real_estate_us",
          "equities_microsoft"
        ];

        const user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE });
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });

        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=buy&allocationMethod=holdings&portfolioId=${portfolio.id}&orderAmount=10`
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview of the transaction", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            executionWindow: {
              smart: {
                etfs: {
                  start: "2022-07-18T12:00:00.000Z",
                  end: "2022-07-18T17:00:00.000Z",
                  executionType: "MARKET_HOURS"
                },
                stocks: {
                  start: "2022-07-18T13:30:00.000Z",
                  end: "2022-07-18T20:00:00.000Z",
                  executionType: "MARKET_HOURS"
                }
              },
              express: {
                etfs: {
                  start: "2022-07-18T07:00:00.000Z",
                  end: "2022-07-18T15:30:00.000Z",
                  executionType: "MARKET_HOURS"
                },
                stocks: {
                  start: "2022-07-18T13:30:00.000Z",
                  end: "2022-07-18T20:00:00.000Z",
                  executionType: "MARKET_HOURS"
                }
              }
            },
            fees: {
              smart: {
                fx: {
                  amount: 0.02, // 0.15% of £10 (USD traded asset order amount), rounded to 2 decimal places
                  currency: "GBP"
                },
                commission: {
                  amount: 0, // 0%
                  currency: "GBP"
                },
                executionSpread: {
                  amount: 0, // 0% of £10
                  currency: "GBP"
                },
                realtimeExecution: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              express: {
                fx: {
                  amount: 0.02, // ETF order: £4 × 0.15% = £0.006 → £0.01, Stock order: £5 × 0.15% = £0.0075 → £0.01, Total: £0.02
                  currency: "GBP"
                },
                commission: {
                  amount: 0, // 0%
                  currency: "GBP"
                },
                executionSpread: {
                  amount: 0, // 0% of £9
                  currency: "GBP"
                },
                realtimeExecution: {
                  amount: 1, // £1 for each ETF order
                  currency: "GBP"
                }
              }
            },
            orders: {
              smart: {
                real_estate_us: {
                  side: "buy",
                  quantity: 0.1,
                  money: 5
                },
                equities_microsoft: {
                  side: "buy",
                  quantity: 0.1,
                  money: 5
                }
              },
              express: {
                real_estate_us: {
                  side: "buy",
                  quantity: 0.08,
                  money: 5
                },
                equities_microsoft: {
                  side: "buy",
                  quantity: 0.08,
                  money: 5
                }
              }
            },
            foreignCurrencyRates: {
              USD: 1.263 // £1 -> $1.27 initially, then 1.27 * (1 - 0.0055) for buy order with free plan spread
            },
            willSkipOrders: false,
            hasETFOrders: true
          })
        );
      });
    });

    describe("when request is made for a portfolio buy operation with both ETF and stock orders for an EU user in free and we are within both ETF/stock realtime market hours", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-18T15:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 1.27
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.787402
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 1.27
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 1.27
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = [
          "real_estate_us",
          "equities_microsoft"
        ];

        const user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE });
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });

        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=buy&allocationMethod=holdings&portfolioId=${portfolio.id}&orderAmount=10`
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview of the transaction", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            executionWindow: {
              smart: {
                etfs: {
                  start: "2022-07-19T12:00:00.000Z",
                  end: "2022-07-19T17:00:00.000Z",
                  executionType: "MARKET_HOURS"
                },
                stocks: {
                  executionType: "REALTIME"
                }
              },
              express: {
                etfs: {
                  executionType: "REALTIME"
                },
                stocks: {
                  executionType: "REALTIME"
                }
              }
            },
            fees: {
              smart: {
                fx: {
                  amount: 0.02, // 0.15% of £10 (USD traded asset order amount), rounded to 2 decimal places
                  currency: "GBP"
                },
                commission: {
                  amount: 0, // 0%
                  currency: "GBP"
                },
                executionSpread: {
                  amount: 0, // 0% of £10
                  currency: "GBP"
                },
                realtimeExecution: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              express: {
                fx: {
                  amount: 0.02, // ETF order: £4 × 0.15% = £0.006 → £0.01, Stock order: £5 × 0.15% = £0.0075 → £0.01, Total: £0.02
                  currency: "GBP"
                },
                commission: {
                  amount: 0, // 0%
                  currency: "GBP"
                },
                executionSpread: {
                  amount: 0, // 0% of £10
                  currency: "GBP"
                },
                realtimeExecution: {
                  amount: 1, // £1 for each ETF order
                  currency: "GBP"
                }
              }
            },
            orders: {
              smart: {
                real_estate_us: {
                  side: "buy",
                  quantity: 0.1,
                  money: 5
                },
                equities_microsoft: {
                  side: "buy",
                  quantity: 0.1,
                  money: 5
                }
              },
              express: {
                real_estate_us: {
                  side: "buy",
                  quantity: 0.08,
                  money: 5
                },
                equities_microsoft: {
                  side: "buy",
                  quantity: 0.08,
                  money: 5
                }
              }
            },
            foreignCurrencyRates: {
              USD: 1.263 // £1 -> $1.27 initially, then 1.27 * (1 - 0.0055) for buy order with free plan spread
            },
            willSkipOrders: false
          })
        );
      });
    });

    describe("when request is made for a portfolio buy operation for a UK user in paid_low but order is less than minimum required for cashback", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 5
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.2
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 5
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 5
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = [
          "real_estate_us",
          "equities_global"
        ];

        const user = await buildUser();
        await buildSubscription({ owner: user.id, price: "paid_low_monthly" });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });

        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=buy&allocationMethod=holdings&portfolioId=${portfolio.id}&orderAmount=10`
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview of the transaction", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            cashback: 0
          })
        );
      });
    });

    describe("when request is made for a portfolio buy operation for a UK user with orders in multiple foreign currencies", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 1.27 and GBP <-> EUR is 1.15
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.9,
            GBP: 0.787402
          },
          EUR: {
            EUR: 1,
            GBP: 0.869565,
            USD: 1.1
          },
          GBP: {
            GBP: 1,
            EUR: 1.15,
            USD: 1.27
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = [
          "real_estate_us",
          "corporate_bonds_eu"
        ];

        const user = await buildUser();
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });

        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=buy&allocationMethod=holdings&portfolioId=${portfolio.id}&orderAmount=10`
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview of the transaction", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            foreignCurrencyRates: {
              USD: 1.263, // £1 -> $1.27 initially, then 1.27 * (1 - 0.0055) for buy order with free plan spread
              EUR: 1.144 // £1 -> €1.15 initially, then 1.15 * (1 - 0.0055) for buy order with free plan spread
            }
          })
        );
      });
    });

    describe("when request is made for a portfolio buy operation for a UK user who does not have holdings", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "equities_uk", price: 1, percentage: 50 },
          { assetId: "equities_eu", price: 1, percentage: 50 }
        ];

        await Promise.all(
          ASSET_COMMON_IDS_CONFIG.map(({ assetId, price }) =>
            buildInvestmentProduct(
              true,
              { assetId, listed: true },
              { pricePerCurrency: { EUR: price, GBP: price, USD: price } }
            )
          )
        );

        const user = await buildUser();
        await buildSubscription({ owner: user.id });
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: [],
          initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=buy&allocationMethod=targetAllocation&portfolioId=${portfolio.id}&orderAmount=10`
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview of the transaction", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            executionWindow: {
              smart: {
                etfs: {
                  start: "2022-07-18T12:00:00.000Z",
                  end: "2022-07-18T17:00:00.000Z",
                  executionType: "MARKET_HOURS"
                }
              }
            },
            orders: {
              smart: {
                equities_uk: {
                  side: "buy",
                  quantity: 5,
                  money: 5
                },
                equities_eu: {
                  side: "buy",
                  quantity: 5,
                  money: 5
                }
              }
            },
            willSkipOrders: false
          })
        );
      });
    });

    describe("when request is made for a portfolio buy operation for a UK user who has holdings, but no target allocation and allocation method is target allocation", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "equities_uk", price: 1, percentage: 50 },
          { assetId: "equities_eu", price: 1, percentage: 50 }
        ];

        const holdings = await Promise.all([
          ...ASSET_COMMON_IDS_CONFIG.map(({ assetId }) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
        ]);

        const user = await buildUser();
        await buildSubscription({ owner: user.id });
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings,
          initialHoldingsAllocation: []
        });

        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=buy&allocationMethod=targetAllocation&portfolioId=${portfolio.id}&orderAmount=10`
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 400 with proper message", () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "Target allocation is not set up for this portfolio"
            }
          })
        );
      });
    });

    describe("when request is made for a portfolio buy operation that will result in skipped orders", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 5
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.2
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 5
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 5
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = [
          "real_estate_us",
          "equities_global",
          "equities_eu",
          "equities_china",
          "equities_global_ai",
          "equities_global_clean_energy",
          "equities_global_cloud_computing",
          "equities_global_consumer_discretionary_broad",
          "equities_global_consumer_staples_broad",
          "equities_global_digitalisation",
          "equities_global_energy_broad"
        ];

        const user = await buildUser();
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 0.1, { price: 1 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });

        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=buy&allocationMethod=holdings&portfolioId=${portfolio.id}&orderAmount=1`
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a preview of the transaction and a flag that will skip orders", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            willSkipOrders: true
          })
        );
      });
    });

    describe("when request is made for a portfolio buy operation which have more holdings than initialHoldingsAllocation and allocation method is target allocation", () => {
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        // Current FX rate GBP <-> USD is 5
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.2,
            GBP: 0.2
          },
          EUR: {
            EUR: 1,
            GBP: 1,
            USD: 5
          },
          GBP: {
            GBP: 1,
            EUR: 1,
            USD: 5
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const ELIGIBLE_ASSET_COMMON_IDS: investmentUniverseConfig.AssetType[] = [
          "real_estate_us",
          "equities_global"
        ];

        const user = await buildUser();
        await buildSubscription({ owner: user.id });
        const holdings = await Promise.all([
          ...ELIGIBLE_ASSET_COMMON_IDS.map((assetId) => buildHoldingDTO(true, assetId, 10, { price: 50 }))
        ]);
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings,
          initialHoldingsAllocation: [
            {
              assetCommonId: ELIGIBLE_ASSET_COMMON_IDS[0],
              percentage: 100
            }
          ]
        });

        response = await request(app)
          .post(
            `/api/m2m/transactions/preview?category=AssetTransaction&portfolioTransactionType=buy&allocationMethod=targetAllocation&portfolioId=${portfolio.id}&orderAmount=10`
          )
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 with a false 'willSkipOrders' flag", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            willSkipOrders: false
          })
        );
      });
    });
  });

  describe("POST /transactions/wealthyhood-dividends/:id", () => {
    describe("when the request body is empty", () => {
      let wealthyhoodDividend: WealthyhoodDividendTransactionDocument;
      let user: UserDocument;

      beforeAll(async () => {
        user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        wealthyhoodDividend = await buildWealthyhoodDividendTransaction({
          owner: user.id
        });

        expect(wealthyhoodDividend.hasViewedAppModal).toBe(false);
      });
      afterAll(async () => await clearDb());

      it("should return 400 if the request body is empty", async () => {
        const response = await request(app)
          .post(`/api/m2m/transactions/wealthyhood-dividends/${wealthyhoodDividend.id}`)
          .send({})
          .set("external-user-id", user._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(400);
      });
    });

    describe("when the request body has hasViewedAppModal set to true", () => {
      let wealthyhoodDividend: WealthyhoodDividendTransactionDocument;
      let user: UserDocument;

      beforeAll(async () => {
        user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        wealthyhoodDividend = await buildWealthyhoodDividendTransaction({
          owner: user.id
        });

        expect(wealthyhoodDividend.hasViewedAppModal).toBe(false);
      });
      afterAll(async () => await clearDb());

      it("should return 204 and set hasViewedAppModal of the document to true", async () => {
        const response = await request(app)
          .post(`/api/m2m/transactions/wealthyhood-dividends/${wealthyhoodDividend.id}`)
          .send({
            hasViewedAppModal: true
          })
          .set("external-user-id", user._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(204);

        const updatedDocument: WealthyhoodDividendTransactionDocument =
          await WealthyhoodDividendTransaction.findById(wealthyhoodDividend.id);
        expect(updatedDocument.hasViewedAppModal).toBe(true);
      });
    });
  });

  describe("POST /transactions/deposits/sync-truelayer", () => {
    let user: UserDocument;
    const TODAY = new Date("2022-07-17T11:00:00Z");

    beforeAll(async () => {
      await clearDb();
      jest.restoreAllMocks();
      jest.resetAllMocks();
      DateTime.now = jest.fn(() => DateTime.fromJSDate(TODAY));
      Date.now = jest.fn(() => TODAY.valueOf());
      user = await buildUser({ portfolioConversionStatus: "completed" });
      await buildPortfolio({ owner: user.id });
    });
    afterAll(async () => {
      jest.restoreAllMocks();
      await clearDb();
    });

    describe("when truelayerId param is missing", () => {
      it("should return status 400 with proper message, ", async () => {
        const response = await request(app)
          .post("/api/m2m/transactions/deposits/sync-truelayer")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Invalid parameter",
              message: expect.stringContaining("Param 'truelayerId' is required")
            }
          })
        );
      });
    });

    describe("when no deposit cash transaction exists for given existing truelayer id", () => {
      const VALID_PAYMENT_ID = "8asd9-asd32-123as-f4238";

      let response: supertest.Response;

      beforeEach(async () => {
        DateTime.now = jest.fn(() => DateTime.fromJSDate(TODAY));
        Date.now = jest.fn(() => TODAY.valueOf());
        jest
          .spyOn(TruelayerPaymentsClient.prototype, "getPayment")
          .mockResolvedValue(await buildPaymentType({ status: "executed" }));
        response = await request(app)
          .post(`/api/m2m/transactions/deposits/sync-truelayer?truelayerId=${VALID_PAYMENT_ID}`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });

      it("should return status 400 with proper message", async () => {
        expect(response.status).toEqual(403);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Inaccessible resource",
              message: expect.stringContaining("Transaction does not belong to user")
            }
          })
        );
      });
    });

    describe("when a deposit cash transaction exists for given truelayer id", () => {
      const VALID_PAYMENT_ID = "8asd9-asd32-123as-f4234";
      let assetTransaction: AssetTransactionDocument;

      describe("when truelayer id found in truelayer with status executed", () => {
        let response: supertest.Response;

        beforeEach(async () => {
          await clearDb();
          jest.resetAllMocks();
          DateTime.now = jest.fn(() => DateTime.fromJSDate(TODAY));
          Date.now = jest.fn(() => TODAY.valueOf());
          user = await buildUser({ portfolioConversionStatus: "completed" });
          await buildPortfolio({ owner: user.id });
          const deposit = await buildDepositCashTransaction({
            owner: user.id,
            providers: {
              truelayer: {
                id: VALID_PAYMENT_ID,
                status: "authorizing",
                version: "v3"
              }
            }
          });

          assetTransaction = await buildAssetTransaction({
            owner: user,
            status: "PendingDeposit",
            pendingDeposit: deposit._id
          });

          assetTransaction.orders = [
            await buildOrder({
              status: "Pending",
              side: "Buy",
              transaction: assetTransaction.id,
              isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
              consideration: {
                originalAmount: 2000,
                amount: 2000,
                currency: "GBP"
              }
            }),
            await buildOrder({
              status: "Pending",
              side: "Buy",
              transaction: assetTransaction.id,
              isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
              consideration: {
                originalAmount: 2000,
                amountSubmitted: 2000,
                amount: 2000,
                currency: "GBP"
              }
            })
          ];

          await assetTransaction.save();

          jest
            .spyOn(TruelayerPaymentsClient.prototype, "getPayment")
            .mockResolvedValue(buildPaymentType({ status: "executed" }));
          response = await request(app)
            .post(`/api/m2m/transactions/deposits/sync-truelayer?truelayerId=${VALID_PAYMENT_ID}`)
            .set("external-user-id", user.id)
            .set("Accept", "application/json");
        });

        it("should succeed with status 200", () => {
          expect(response.status).toEqual(200);
        });

        it("should respond with deposit document", async () => {
          const updatedDeposit = (await DepositCashTransaction.findOne({
            "providers.truelayer.id": VALID_PAYMENT_ID
          })) as DepositCashTransactionDocument;

          expect(JSON.parse(response.text)).toMatchObject(
            expect.objectContaining({
              _id: updatedDeposit.id.toString(),
              status: "Pending"
            })
          );
        });
      });
    });
  });

  describe("POST /transactions/charges/lifetime/sync-truelayer", () => {
    let user: UserDocument;

    beforeAll(async () => {
      user = await buildUser({ portfolioConversionStatus: "completed" });
      await buildPortfolio({ owner: user.id });
    });
    afterAll(async () => {
      jest.restoreAllMocks();
      await clearDb();
    });

    it("should return status 400 with proper message, if truelayerId param is missing", async () => {
      const response = await request(app)
        .post("/api/m2m/transactions/charges/lifetime/sync-truelayer")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: expect.stringContaining("Param 'truelayerId' is required")
          }
        })
      );
    });

    it("should return status 403 with proper message, if truelayerId param does not exist", async () => {
      const invalidPaymentId = "AnIdThatDoesNotExist";
      const response = await request(app)
        .post(`/api/m2m/transactions/charges/lifetime/sync-truelayer?truelayerId=${invalidPaymentId}`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(403);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Inaccessible resource",
            message: expect.stringContaining("Transaction does not belong to user")
          }
        })
      );
    });

    it("should return status 400 with proper message, if no charge transaction exists for given truelayer id", async () => {
      jest
        .spyOn(TruelayerPaymentsClient.prototype, "getPayment")
        .mockResolvedValue(await buildPaymentType({ status: "executed" }));
      const response = await request(app)
        .post("/api/m2m/transactions/charges/lifetime/sync-truelayer?truelayerId=8asd9-asd32-123as-f4238")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(403);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Inaccessible resource",
            message: expect.stringContaining("Transaction does not belong to user")
          }
        })
      );
    });

    it("should return status 200 and not update the subscription if truelayer ID is found in Truelayer and is failed", async () => {
      const VALID_PAYMENT_ID = faker.string.uuid();

      jest
        .spyOn(TruelayerPaymentsClient.prototype, "getPayment")
        .mockResolvedValue(await buildPaymentType({ status: "failed" }));

      const subscription = await buildSubscription({
        owner: user.id,
        category: "FeeBasedSubscription",
        price: "free_monthly"
      });
      await buildChargeTransaction({
        owner: user.id,
        subscription: subscription.id,
        price: "paid_mid_lifetime_blackfriday_2023",
        providers: {
          truelayer: {
            id: VALID_PAYMENT_ID,
            status: "authorization_required"
          }
        }
      });

      const response = await request(app)
        .post(`/api/m2m/transactions/charges/lifetime/sync-truelayer?truelayerId=${VALID_PAYMENT_ID}`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const updatedSubscription = await Subscription.findById(subscription.id);
      expect(updatedSubscription).toEqual(
        expect.objectContaining({
          category: "FeeBasedSubscription",
          price: "free_monthly"
        })
      );

      const updatedChargeTransaction = await ChargeTransaction.findOne({ subscription: subscription.id });
      expect(updatedChargeTransaction).toEqual(
        expect.objectContaining({
          status: "Rejected",
          providers: expect.objectContaining({
            truelayer: expect.objectContaining({
              id: VALID_PAYMENT_ID,
              status: "failed"
            })
          })
        })
      );
    });

    it("should return status 200 and not update the subscription if truelayer ID is found in Truelayer and is authorizing", async () => {
      const VALID_PAYMENT_ID = faker.string.uuid();

      jest
        .spyOn(TruelayerPaymentsClient.prototype, "getPayment")
        .mockResolvedValue(await buildPaymentType({ status: "authorizing" }));

      const subscription = await buildSubscription({
        owner: user.id,
        category: "FeeBasedSubscription",
        price: "free_monthly"
      });
      await buildChargeTransaction({
        owner: user.id,
        subscription: subscription.id,
        price: "paid_mid_lifetime_blackfriday_2023",
        providers: {
          truelayer: {
            id: VALID_PAYMENT_ID,
            status: "authorization_required"
          }
        }
      });

      const response = await request(app)
        .post(`/api/m2m/transactions/charges/lifetime/sync-truelayer?truelayerId=${VALID_PAYMENT_ID}`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const updatedSubscription = await Subscription.findById(subscription.id);
      expect(updatedSubscription).toEqual(
        expect.objectContaining({
          category: "FeeBasedSubscription",
          price: "free_monthly"
        })
      );

      const updatedChargeTransaction = await ChargeTransaction.findOne({ subscription: subscription.id });
      expect(updatedChargeTransaction).toEqual(
        expect.objectContaining({
          status: "Pending",
          providers: expect.objectContaining({
            truelayer: expect.objectContaining({
              id: VALID_PAYMENT_ID,
              status: "authorizing"
            })
          })
        })
      );
    });

    it("should return status 200 and cancel the Stripe subscription if truelayer ID is found and is authorized, and user had card-based subscription before", async () => {
      const VALID_PAYMENT_ID = faker.string.uuid();
      const STRIPE_SUBSCRIPTION_ID = faker.string.uuid();

      jest.spyOn(StripeService.Instance, "cancelSubscription");
      jest
        .spyOn(TruelayerPaymentsClient.prototype, "getPayment")
        .mockResolvedValue(await buildPaymentType({ status: "authorized" }));

      const subscription = await buildSubscription({
        owner: user.id,
        category: "CardPaymentSubscription",
        price: "paid_low_monthly",
        providers: {
          stripe: {
            id: STRIPE_SUBSCRIPTION_ID,
            status: "active"
          }
        }
      });
      await buildChargeTransaction({
        owner: user.id,
        subscription: subscription.id,
        price: "paid_mid_lifetime_blackfriday_2023",
        providers: {
          truelayer: {
            id: VALID_PAYMENT_ID,
            status: "authorizing"
          }
        }
      });

      const response = await request(app)
        .post(`/api/m2m/transactions/charges/lifetime/sync-truelayer?truelayerId=${VALID_PAYMENT_ID}`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      expect(StripeService.Instance.cancelSubscription).toHaveBeenCalledWith(STRIPE_SUBSCRIPTION_ID);

      const updatedSubscription = await Subscription.findById(subscription.id);
      expect(updatedSubscription).toEqual(
        expect.objectContaining({
          category: "SinglePaymentSubscription",
          price: "paid_mid_lifetime_blackfriday_2023"
        })
      );

      const updatedChargeTransaction = await ChargeTransaction.findOne({ subscription: subscription.id });
      expect(updatedChargeTransaction).toEqual(
        expect.objectContaining({
          status: "Pending",
          providers: expect.objectContaining({
            truelayer: expect.objectContaining({
              id: VALID_PAYMENT_ID,
              status: "authorized"
            })
          })
        })
      );
    });

    it("should return status 200 and update the subscription if truelayer ID is found in Truelayer and is authorized", async () => {
      const VALID_PAYMENT_ID = faker.string.uuid();

      jest
        .spyOn(TruelayerPaymentsClient.prototype, "getPayment")
        .mockResolvedValue(await buildPaymentType({ status: "authorized" }));

      const subscription = await buildSubscription({
        owner: user.id,
        category: "FeeBasedSubscription",
        price: "free_monthly"
      });
      await buildChargeTransaction({
        owner: user.id,
        subscription: subscription.id,
        price: "paid_mid_lifetime_blackfriday_2023",
        providers: {
          truelayer: {
            id: VALID_PAYMENT_ID,
            status: "authorizing"
          }
        }
      });

      const response = await request(app)
        .post(`/api/m2m/transactions/charges/lifetime/sync-truelayer?truelayerId=${VALID_PAYMENT_ID}`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const updatedSubscription = await Subscription.findById(subscription.id);
      expect(updatedSubscription).toEqual(
        expect.objectContaining({
          category: "SinglePaymentSubscription",
          price: "paid_mid_lifetime_blackfriday_2023"
        })
      );

      const updatedChargeTransaction = await ChargeTransaction.findOne({ subscription: subscription.id });
      expect(updatedChargeTransaction).toEqual(
        expect.objectContaining({
          status: "Pending",
          providers: expect.objectContaining({
            truelayer: expect.objectContaining({
              id: VALID_PAYMENT_ID,
              status: "authorized"
            })
          })
        })
      );
    });

    it("should return status 200 and update the subscription if truelayer ID is found in Truelayer and is executed", async () => {
      const VALID_PAYMENT_ID = faker.string.uuid();

      jest
        .spyOn(TruelayerPaymentsClient.prototype, "getPayment")
        .mockResolvedValue(await buildPaymentType({ status: "executed" }));

      const subscription = await buildSubscription({
        owner: user.id,
        category: "FeeBasedSubscription",
        price: "free_monthly"
      });
      await buildChargeTransaction({
        owner: user.id,
        subscription: subscription.id,
        price: "paid_mid_lifetime_blackfriday_2023",
        providers: {
          truelayer: {
            id: VALID_PAYMENT_ID,
            status: "authorizing"
          }
        }
      });

      const response = await request(app)
        .post(`/api/m2m/transactions/charges/lifetime/sync-truelayer?truelayerId=${VALID_PAYMENT_ID}`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const updatedSubscription = await Subscription.findById(subscription.id);
      expect(updatedSubscription).toEqual(
        expect.objectContaining({
          category: "SinglePaymentSubscription",
          price: "paid_mid_lifetime_blackfriday_2023"
        })
      );

      const updatedChargeTransaction = await ChargeTransaction.findOne({ subscription: subscription.id });
      expect(updatedChargeTransaction).toEqual(
        expect.objectContaining({
          status: "Settled",
          providers: expect.objectContaining({
            truelayer: expect.objectContaining({
              id: VALID_PAYMENT_ID,
              status: "executed"
            })
          })
        })
      );
    });
  });

  describe("POST /transactions/:id/cancel", () => {
    describe("when a settled transaction ID is passed", () => {
      let response: supertest.Response;
      let transaction: TransactionDocument;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        DateTime.now = jest.fn(() => DateTime.fromJSDate(TODAY));
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });

        transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        });

        response = await request(app)
          .post(`/api/m2m/transactions/${transaction.id}/cancel`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 400 with proper message", () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: expect.stringContaining(`Transaction ${transaction.id} is not cancellable`)
            }
          })
        );
      });
    });

    describe("when a non-asset/rebalance transaction ID is passed", () => {
      let response: supertest.Response;
      let transaction: TransactionDocument;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        DateTime.now = jest.fn(() => DateTime.fromJSDate(TODAY));
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });

        transaction = await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id
        });

        response = await request(app)
          .post(`/api/m2m/transactions/${transaction.id}/cancel`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 400 with proper message", () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: expect.stringContaining(`Transaction ${transaction.id} is not cancellable`)
            }
          })
        );
      });
    });

    describe("when an asset transaction ID is passed but it belongs to another user", () => {
      let response: supertest.Response;
      let transaction: AssetTransactionDocument;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        DateTime.now = jest.fn(() => DateTime.fromJSDate(TODAY));
        Date.now = jest.fn(() => TODAY.valueOf());

        jest.spyOn(eventEmitter, "emit");

        const user = await buildUser();
        const anotherUser = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          cash: { GBP: { available: 0, reserved: 10, settled: 0 } }
        });

        transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            amount: 1000
          },
          executionWindow: {
            etfs: {
              start: new Date("2024-10-06"),
              end: new Date("2024-10-07"),
              executionType: "MARKET_HOURS"
            }
          }
        });

        transaction.orders = [
          await buildOrder({
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
            consideration: {
              amount: 500,
              currency: "GBP"
            }
          }),
          await buildOrder({
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
            consideration: {
              amount: 500,
              currency: "GBP"
            }
          })
        ];
        await transaction.save();

        response = await request(app)
          .post(`/api/m2m/transactions/${transaction.id}/cancel`)
          .set("external-user-id", anotherUser.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 403 with proper message", () => {
        expect(response.status).toEqual(403);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Inaccessible resource",
              message: expect.stringContaining("Transaction does not belong to user")
            }
          })
        );
      });

      it("should not emit a transaction cancelled event", () => {
        expect(eventEmitter.emit).not.toBeCalledWith(
          events.transaction.transactionCancellation.eventId,
          expect.objectContaining({ id: transaction.owner })
        );
      });
    });

    describe("when a valid rebalance transaction ID is passed but orders have been submitted to WK", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;
      let user: UserDocument;
      let response: supertest.Response;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        DateTime.now = jest.fn(() => DateTime.fromJSDate(TODAY));
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();
        await Promise.all([
          buildPortfolio({
            owner: user.id,
            holdings: []
          }),
          buildHoldingDTO(true, "equities_china", 1),
          buildHoldingDTO(true, "equities_eu", 1)
        ]);

        await buildSubscription({ owner: user.id });

        rebalanceTransaction = await buildRebalanceTransaction({
          owner: user,
          rebalanceStatus: "PendingSell",
          targetAllocation: [
            {
              assetCommonId: "equities_china",
              percentage: 50
            },
            {
              assetCommonId: "equities_eu",
              percentage: 50
            }
          ],
          sellExecutionWindow: {
            start: new Date("2024-10-06"),
            end: new Date("2024-10-07"),
            executionType: "MARKET_HOURS"
          },
          buyExecutionWindow: {
            start: new Date("2025-10-06"),
            end: new Date("2025-10-07"),
            executionType: "MARKET_HOURS"
          }
        });

        await buildOrder({
          transaction: rebalanceTransaction._id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_china"]?.isin,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
          quantity: 1,
          side: "Sell"
        });

        response = await request(app)
          .post(`/api/m2m/transactions/${rebalanceTransaction.id}/cancel`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 400 since transaction is not cancellable", () => {
        expect(response.status).toEqual(400);
      });
    });

    describe("when a valid portfolio buy transaction ID is passed", () => {
      let response: supertest.Response;
      let transaction: AssetTransactionDocument;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        DateTime.now = jest.fn(() => DateTime.fromJSDate(TODAY));
        Date.now = jest.fn(() => TODAY.valueOf());

        jest.spyOn(eventEmitter, "emit");

        const user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          cash: { GBP: { available: 0, reserved: 10, settled: 0 } }
        });

        await buildSubscription({ owner: user.id });

        transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            amount: 1000
          },
          executionWindow: {
            etfs: {
              start: new Date("2024-10-06"),
              end: new Date("2024-10-07"),
              executionType: "MARKET_HOURS"
            }
          }
        });

        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_uk" }),
          buildInvestmentProduct(true, { assetId: "equities_us" })
        ]);

        transaction.orders = [
          await buildOrder({
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
            consideration: {
              originalAmount: 500,
              amount: 500,
              currency: "GBP"
            }
          }),
          await buildOrder({
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
            consideration: {
              originalAmount: 500,
              amount: 500,
              currency: "GBP"
            }
          })
        ];
        await transaction.save();

        response = await request(app)
          .post(`/api/m2m/transactions/${transaction.id}/cancel`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 and the cancelled transaction in the response", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            status: "Cancelled",
            isCancellable: false,
            orders: expect.arrayContaining([
              expect.objectContaining({
                status: "Cancelled",
                isCancellable: false
              })
            ])
          })
        );
      });

      it("should set transaction status to cancelled and not update its consideration amount", async () => {
        const updatedTransaction = await Transaction.findById(transaction.id);
        expect(updatedTransaction).toEqual(
          expect.objectContaining({ status: "Cancelled", consideration: transaction.consideration })
        );
      });

      it("should have valid values for available and settled cash", async () => {
        const updatedPortfolio = await Portfolio.findById(transaction.portfolio);
        expect(updatedPortfolio.toObject()).toEqual(
          expect.objectContaining({
            cash: expect.objectContaining({
              GBP: expect.objectContaining({ available: 10, settled: 10 })
            })
          })
        );
      });
    });

    describe("when a valid portfolio buy transaction ID is passed and some orders are already matched", () => {
      let response: supertest.Response;
      let transaction: AssetTransactionDocument;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        DateTime.now = jest.fn(() => DateTime.fromJSDate(TODAY));
        Date.now = jest.fn(() => TODAY.valueOf());

        jest.spyOn(eventEmitter, "emit");

        const user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          cash: { GBP: { available: 0, reserved: 5, settled: 0 } }
        });

        await buildSubscription({ owner: user.id });

        transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            amount: 1000
          },
          executionWindow: {
            etfs: {
              start: new Date("2024-10-06"),
              end: new Date("2024-10-07"),
              executionType: "MARKET_HOURS"
            }
          }
        });

        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_uk" }),
          buildInvestmentProduct(true, { assetId: "equities_us" })
        ]);

        transaction.orders = [
          await buildOrder({
            side: "Buy",
            status: "Pending",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
            consideration: {
              originalAmount: 500,
              amount: 500,
              currency: "GBP"
            }
          }),
          await buildOrder({
            side: "Buy",
            status: "Matched",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
            consideration: {
              originalAmount: 500,
              amount: 500,
              currency: "GBP"
            }
          })
        ];
        await transaction.save();

        response = await request(app)
          .post(`/api/m2m/transactions/${transaction.id}/cancel`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200 and the settled transaction in the response", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            status: "Settled",
            isCancellable: false,
            orders: expect.arrayContaining([
              expect.objectContaining({
                status: "Cancelled",
                isCancellable: false
              }),
              expect.objectContaining({
                status: "Matched",
                isCancellable: false
              })
            ])
          })
        );
      });

      it("should set transaction status to settled", async () => {
        const updatedTransaction = await Transaction.findById(transaction.id);
        expect(updatedTransaction.status).toEqual("Settled");
      });

      it("should have valid values for available and settled cash", async () => {
        const updatedPortfolio = await Portfolio.findById(transaction.portfolio);
        expect(updatedPortfolio.toObject()).toEqual(
          expect.objectContaining({
            cash: expect.objectContaining({
              GBP: expect.objectContaining({ available: 5, settled: 5 })
            })
          })
        );
      });
    });

    describe("when a valid portfolio buy transaction ID is passed and user conversion status is inProgress and user has no other pending orders", () => {
      let response: supertest.Response;
      let transaction: AssetTransactionDocument;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        DateTime.now = jest.fn(() => DateTime.fromJSDate(TODAY));
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser({ portfolioConversionStatus: "inProgress" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          cash: { GBP: { available: 0, reserved: 10, settled: 0 } }
        });

        await buildSubscription({ owner: user.id });

        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_uk" }),
          buildInvestmentProduct(true, { assetId: "equities_us" })
        ]);

        transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            amount: 1000
          },
          executionWindow: {
            etfs: {
              start: new Date("2024-10-06"),
              end: new Date("2024-10-07"),
              executionType: "MARKET_HOURS"
            }
          }
        });

        transaction.orders = [
          await buildOrder({
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
            consideration: {
              originalAmount: 500,
              amount: 500,
              currency: "GBP"
            }
          }),
          await buildOrder({
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
            consideration: {
              originalAmount: 500,
              amount: 500,
              currency: "GBP"
            }
          })
        ];
        await transaction.save();

        response = await request(app)
          .post(`/api/m2m/transactions/${transaction.id}/cancel`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200", () => {
        expect(response.status).toEqual(200);
      });

      it("should set transaction status to cancelled", async () => {
        const updatedTransaction = await Transaction.findById(transaction.id);
        expect(updatedTransaction.status).toEqual("Cancelled");
      });

      it("should have valid values for available and settled cash", async () => {
        const updatedPortfolio = await Portfolio.findById(transaction.portfolio);
        expect(updatedPortfolio).toEqual(
          expect.objectContaining({
            cash: expect.objectContaining({
              GBP: expect.objectContaining({ available: 10, settled: 10 })
            })
          })
        );
      });

      it("should move the user back to notStarted", async () => {
        const updatedUser = await User.findById(transaction.owner);
        expect(updatedUser).toEqual(
          expect.objectContaining({
            portfolioConversionStatus: "notStarted"
          })
        );
      });
    });

    describe("when a valid asset buy transaction ID is passed", () => {
      let response: supertest.Response;
      let transaction: AssetTransactionDocument;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        DateTime.now = jest.fn(() => DateTime.fromJSDate(TODAY));
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          cash: { GBP: { available: 0, reserved: 10, settled: 0 } }
        });

        await buildSubscription({ owner: user.id });
        await buildInvestmentProduct(true, { assetId: "equities_uk" });

        // The asset buy transaction does not have a consideration amount
        transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          portfolioTransactionCategory: "update",
          consideration: {},
          executionWindow: {
            etfs: {
              start: new Date("2024-10-06"),
              end: new Date("2024-10-07"),
              executionType: "MARKET_HOURS"
            }
          }
        });

        transaction.orders = [
          await buildOrder({
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
            consideration: {
              originalAmount: 1000,
              amount: 1000,
              currency: "GBP"
            }
          })
        ];
        await transaction.save();

        response = await request(app)
          .post(`/api/m2m/transactions/${transaction.id}/cancel`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            status: "Cancelled",
            isCancellable: false,
            orders: expect.arrayContaining([
              expect.objectContaining({
                status: "Cancelled",
                isCancellable: false
              })
            ])
          })
        );
      });

      it("should set transaction status to cancelled", async () => {
        const updatedTransaction = await Transaction.findById(transaction.id);
        expect(updatedTransaction).toEqual(
          expect.objectContaining({ status: "Cancelled", consideration: transaction.consideration })
        );
      });

      it("should have valid values for available and settled cash", async () => {
        const updatedPortfolio = await Portfolio.findById(transaction.portfolio);
        expect(updatedPortfolio).toEqual(
          expect.objectContaining({
            cash: expect.objectContaining({
              GBP: expect.objectContaining({ available: 10, settled: 10 })
            })
          })
        );
      });
    });

    describe("when a valid asset buy transaction ID is passed and it has a linked cashback transaction", () => {
      let response: supertest.Response;
      let transaction: AssetTransactionDocument;
      let cashback: CashbackTransactionDocument;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        DateTime.now = jest.fn(() => DateTime.fromJSDate(TODAY));
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          cash: { GBP: { available: 0, reserved: 10, settled: 0 } }
        });

        await buildSubscription({ owner: user.id });
        await buildInvestmentProduct(true, { assetId: "equities_uk" });

        // The asset buy transaction does not have a consideration amount
        transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          portfolioTransactionCategory: "update",
          consideration: {},
          executionWindow: {
            etfs: {
              start: new Date("2024-10-06"),
              end: new Date("2024-10-07"),
              executionType: "MARKET_HOURS"
            }
          }
        });

        transaction.orders = [
          await buildOrder({
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
            consideration: {
              originalAmount: 1000,
              amount: 1000,
              currency: "GBP"
            }
          })
        ];
        await transaction.save();

        cashback = await buildCashbackTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAssetTransaction: transaction.id
        });

        response = await request(app)
          .post(`/api/m2m/transactions/${transaction.id}/cancel`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200", () => {
        expect(response.status).toEqual(200);
      });

      it("should set transaction status to cancelled", async () => {
        const updatedTransaction = await Transaction.findById(transaction.id);
        expect(updatedTransaction.status).toEqual("Cancelled");
      });

      it("should set cashback transaction status to cancelled", async () => {
        const updatedCashbackTransaction = await Transaction.findById(cashback.id);
        expect(updatedCashbackTransaction.status).toEqual("Cancelled");
      });

      it("should have valid values for available and settled cash", async () => {
        const updatedPortfolio = await Portfolio.findById(transaction.portfolio);
        expect(updatedPortfolio).toEqual(
          expect.objectContaining({
            cash: expect.objectContaining({
              GBP: expect.objectContaining({ available: 10, settled: 10 })
            })
          })
        );
      });
    });

    describe("hen a valid asset buy transaction ID is passed, asset transaction has status PendingDeposit with a completed Truelayer flow and it has a linked cashback transaction", () => {
      let response: supertest.Response;
      let transaction: AssetTransactionDocument;
      let cashback: CashbackTransactionDocument;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        DateTime.now = jest.fn(() => DateTime.fromJSDate(TODAY));
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildSubscription({ owner: user.id });
        await buildInvestmentProduct(true, { assetId: "equities_uk" });

        const deposit = await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              version: "v3",
              status: "authorized"
            }
          }
        });

        // The asset buy transaction does not have a consideration amount
        transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "PendingDeposit",
          portfolioTransactionCategory: "update",
          consideration: {},
          pendingDeposit: deposit.id,
          executionWindow: {
            etfs: {
              start: new Date("2024-10-06"),
              end: new Date("2024-10-07"),
              executionType: "MARKET_HOURS"
            }
          }
        });

        transaction.orders = [
          await buildOrder({
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
            consideration: {
              originalAmount: 1000,
              amount: 1000,
              currency: "GBP"
            }
          })
        ];
        await transaction.save();

        cashback = await buildCashbackTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAssetTransaction: transaction.id
        });

        response = await request(app)
          .post(`/api/m2m/transactions/${transaction.id}/cancel`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200", () => {
        expect(response.status).toEqual(200);
      });

      it("should set transaction status to cancelled", async () => {
        const updatedTransaction = (await Transaction.findById(transaction.id)) as TransactionDocument;
        expect(updatedTransaction.status).toEqual("Cancelled");
      });

      it("should set cashback transaction status to cancelled", async () => {
        const updatedCashbackTransaction = (await Transaction.findById(cashback.id)) as TransactionDocument;
        expect(updatedCashbackTransaction.status).toEqual("Cancelled");
      });

      it("should NOT return the reserved cash to the user", async () => {
        const updatedPortfolio = await Portfolio.findById(transaction.portfolio);
        expect(updatedPortfolio.toObject()).toEqual(
          expect.objectContaining({
            cash: expect.objectContaining({
              GBP: expect.objectContaining({ available: 0, reserved: 0, settled: 0 })
            })
          })
        );
      });
    });

    describe("when a valid asset buy transaction ID is passed, asset transaction has status PendingDeposit with a non-completed Truelayer flow and it has a linked cashback transaction", () => {
      let response: supertest.Response;
      let transaction: AssetTransactionDocument;
      let cashback: CashbackTransactionDocument;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        DateTime.now = jest.fn(() => DateTime.fromJSDate(TODAY));
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildSubscription({ owner: user.id });
        await buildInvestmentProduct(true, { assetId: "equities_uk" });

        const deposit = await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              version: "v3",
              status: "authorization_required"
            }
          }
        });

        // The asset buy transaction does not have a consideration amount
        transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "PendingDeposit",
          pendingDeposit: deposit.id,
          portfolioTransactionCategory: "update",
          consideration: {},
          executionWindow: {
            etfs: {
              start: new Date("2024-10-06"),
              end: new Date("2024-10-07"),
              executionType: "MARKET_HOURS"
            }
          }
        });

        transaction.orders = [
          await buildOrder({
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
            consideration: {
              originalAmount: 1000,
              amount: 1000,
              currency: "GBP"
            }
          })
        ];
        await transaction.save();

        cashback = await buildCashbackTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAssetTransaction: transaction.id
        });

        response = await request(app)
          .post(`/api/m2m/transactions/${transaction.id}/cancel`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200", () => {
        expect(response.status).toEqual(200);
      });

      it("should set transaction status to cancelled", async () => {
        const updatedTransaction = (await Transaction.findById(transaction.id)) as TransactionDocument;
        expect(updatedTransaction.status).toEqual("Cancelled");
      });

      it("should set cashback transaction status to cancelled", async () => {
        const updatedCashbackTransaction = (await Transaction.findById(cashback.id)) as TransactionDocument;
        expect(updatedCashbackTransaction.status).toEqual("Cancelled");
      });

      it("should not return the cash to the user", async () => {
        const updatedPortfolio = await Portfolio.findById(transaction.portfolio);
        expect(updatedPortfolio).toEqual(
          expect.objectContaining({
            cash: expect.objectContaining({
              GBP: expect.objectContaining({ available: 0, reserved: 0, settled: 0 })
            })
          })
        );
      });
    });

    describe("when a valid portfolio buy transaction ID is passed, asset transaction has status PendingDeposit and it has a linked cashback transaction", () => {
      let response: supertest.Response;
      let transaction: AssetTransactionDocument;
      let cashback: CashbackTransactionDocument;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        DateTime.now = jest.fn(() => DateTime.fromJSDate(TODAY));
        Date.now = jest.fn(() => TODAY.valueOf());

        jest.spyOn(eventEmitter, "emit");

        const user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildSubscription({ owner: user.id });

        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_uk" }),
          buildInvestmentProduct(true, { assetId: "equities_us" })
        ]);

        transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "PendingDeposit",
          consideration: {
            amount: 1000
          },
          executionWindow: {
            etfs: {
              start: new Date("2024-10-06"),
              end: new Date("2024-10-07"),
              executionType: "MARKET_HOURS"
            }
          }
        });

        transaction.orders = [
          await buildOrder({
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
            consideration: {
              originalAmount: 500,
              amount: 500,
              currency: "GBP"
            }
          }),
          await buildOrder({
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
            consideration: {
              originalAmount: 500,
              amount: 500,
              currency: "GBP"
            }
          })
        ];
        await transaction.save();

        cashback = await buildCashbackTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAssetTransaction: transaction.id
        });

        response = await request(app)
          .post(`/api/m2m/transactions/${transaction.id}/cancel`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200", () => {
        expect(response.status).toEqual(200);
      });

      it("should set transaction status to cancelled", async () => {
        const updatedTransaction = (await Transaction.findById(transaction.id)) as TransactionDocument;
        expect(updatedTransaction.status).toEqual("Cancelled");
      });

      it("should return not update the reserved cash to the user", async () => {
        const updatedPortfolio = await Portfolio.findById(transaction.portfolio);
        expect(updatedPortfolio).toEqual(
          expect.objectContaining({
            cash: expect.objectContaining({
              GBP: expect.objectContaining({ available: 0, reserved: 0, settled: 0 })
            })
          })
        );
      });

      it("should set cashback transaction status to cancelled", async () => {
        const updatedCashbackTransaction = (await Transaction.findById(cashback.id)) as TransactionDocument;
        expect(updatedCashbackTransaction.status).toEqual("Cancelled");
      });
    });

    describe("when a valid portfolio buy pending deposit transaction ID is passed and deposit is not yet settled but payment is authorised", () => {
      let response: supertest.Response;
      let transaction: AssetTransactionDocument;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        DateTime.now = jest.fn(() => DateTime.fromJSDate(TODAY));
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser();

        // The cash from the deposit has not yet been added to the user's cash as the deposit is not settled
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildSubscription({ owner: user.id });

        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_uk" }),
          buildInvestmentProduct(true, { assetId: "equities_us" })
        ]);

        const deposit = await buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              version: "v3",
              status: "executed"
            },
            wealthkernel: {
              id: "some-wk-id",
              status: "Created"
            }
          }
        });

        // The transaction still has a PendingDeposit status
        transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "PendingDeposit",
          consideration: {
            amount: 1000
          },
          executionWindow: {
            etfs: {
              start: new Date("2024-10-06"),
              end: new Date("2024-10-07"),
              executionType: "MARKET_HOURS"
            }
          },
          pendingDeposit: deposit.id
        });

        transaction.orders = await Promise.all([
          buildOrder({
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
            consideration: {
              originalAmount: 500,
              amount: 500,
              currency: "GBP"
            }
          }),
          buildOrder({
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
            consideration: {
              originalAmount: 500,
              amount: 500,
              currency: "GBP"
            }
          })
        ]);
        await transaction.save();

        response = await request(app)
          .post(`/api/m2m/transactions/${transaction.id}/cancel`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200", () => {
        expect(response.status).toEqual(200);
      });

      it("should set transaction status to cancelled", async () => {
        const updatedTransaction = await Transaction.findById(transaction.id);
        expect(updatedTransaction.status).toEqual("Cancelled");
      });

      it("should NOT return the cash to the user", async () => {
        const updatedPortfolio = await Portfolio.findById(transaction.portfolio);
        expect(updatedPortfolio.cash.GBP.available).toBe(0);
      });
    });

    describe("when a valid portfolio buy pending gift transaction ID is passed and gift is not yet settled", () => {
      let response: supertest.Response;
      let transaction: AssetTransactionDocument;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        DateTime.now = jest.fn(() => DateTime.fromJSDate(TODAY));
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser();

        // The cash from the deposit has not yet been added to the user's cash as the deposit is not settled
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildSubscription({ owner: user.id });

        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_uk" }),
          buildInvestmentProduct(true, { assetId: "equities_us" })
        ]);

        const gift = await buildGift({
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: "some-wk-id",
                status: "Created"
              }
            }
          }
        });

        // The transaction still has a PendingGift status
        transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "PendingGift",
          consideration: {
            amount: 1000
          },
          executionWindow: {
            etfs: {
              start: new Date("2024-10-06"),
              end: new Date("2024-10-07"),
              executionType: "MARKET_HOURS"
            }
          },
          pendingGift: gift.id
        });

        transaction.orders = [
          await buildOrder({
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
            consideration: {
              originalAmount: 5,
              amount: 5,
              currency: "GBP"
            }
          }),
          await buildOrder({
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
            consideration: {
              originalAmount: 5,
              amount: 5,
              currency: "GBP"
            }
          })
        ];
        await transaction.save();

        response = await request(app)
          .post(`/api/m2m/transactions/${transaction.id}/cancel`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200", () => {
        expect(response.status).toEqual(200);
      });

      it("should set transaction status to cancelled", async () => {
        const updatedTransaction = await Transaction.findById(transaction.id);
        expect(updatedTransaction.status).toEqual("Cancelled");
      });

      it("should not return the reserved cash to the user", async () => {
        const updatedPortfolio = await Portfolio.findById(transaction.portfolio);
        expect(updatedPortfolio).toEqual(
          expect.objectContaining({
            cash: expect.objectContaining({
              GBP: expect.objectContaining({ available: 0, reserved: 0, settled: 0 })
            })
          })
        );
      });
    });

    describe("when a valid portfolio sell transaction ID is passed", () => {
      let response: supertest.Response;
      let transaction: AssetTransactionDocument;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        DateTime.now = jest.fn(() => DateTime.fromJSDate(TODAY));
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          cash: { GBP: { available: 0, reserved: 10, settled: 0 } }
        });

        await buildSubscription({ owner: user.id });

        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_uk" }),
          buildInvestmentProduct(true, { assetId: "equities_us" })
        ]);

        // The asset buy transaction does not have a consideration amount
        transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          portfolioTransactionCategory: "sell",
          consideration: {
            amount: 1000,
            currency: "GBP"
          },
          executionWindow: {
            etfs: {
              start: new Date("2024-10-06"),
              end: new Date("2024-10-07"),
              executionType: "MARKET_HOURS"
            }
          }
        });

        transaction.orders = [
          await buildOrder({
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
            consideration: null,
            quantity: 1
          }),
          await buildOrder({
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
            consideration: null,
            quantity: 1
          })
        ];
        await transaction.save();

        response = await request(app)
          .post(`/api/m2m/transactions/${transaction.id}/cancel`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            status: "Cancelled",
            isCancellable: false,
            orders: expect.arrayContaining([
              expect.objectContaining({
                status: "Cancelled",
                isCancellable: false
              })
            ])
          })
        );
      });

      it("should set transaction status to cancelled", async () => {
        const updatedTransaction = await Transaction.findById(transaction.id);
        expect(updatedTransaction).toEqual(
          expect.objectContaining({ status: "Cancelled", consideration: transaction.consideration })
        );
      });
    });

    describe("when a valid asset sell transaction ID is passed", () => {
      let response: supertest.Response;
      let transaction: AssetTransactionDocument;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        DateTime.now = jest.fn(() => DateTime.fromJSDate(TODAY));
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          cash: { GBP: { available: 0, reserved: 10, settled: 0 } }
        });

        await buildSubscription({ owner: user.id });

        await buildInvestmentProduct(true, { assetId: "equities_uk" });

        // The asset buy transaction does not have a consideration amount
        transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          portfolioTransactionCategory: "update",
          executionWindow: {
            etfs: {
              start: new Date("2024-10-06"),
              end: new Date("2024-10-07"),
              executionType: "MARKET_HOURS"
            }
          }
        });

        transaction.orders = [
          await buildOrder({
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
            quantity: 1
          })
        ];
        await transaction.save();

        response = await request(app)
          .post(`/api/m2m/transactions/${transaction.id}/cancel`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200", () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            status: "Cancelled",
            isCancellable: false,
            orders: expect.arrayContaining([
              expect.objectContaining({
                status: "Cancelled",
                isCancellable: false
              })
            ])
          })
        );
      });

      it("should set transaction status to cancelled", async () => {
        const updatedTransaction = await Transaction.findById(transaction.id);
        expect(updatedTransaction).toEqual(
          expect.objectContaining({ status: "Cancelled", consideration: transaction.consideration })
        );
      });
    });

    describe("when a valid rebalance transaction ID is passed", () => {
      let response: supertest.Response;
      let transaction: RebalanceTransactionDocument;

      beforeAll(async () => {
        const TODAY = new Date("2022-07-17T11:00:00Z");
        DateTime.now = jest.fn(() => DateTime.fromJSDate(TODAY));
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          cash: { GBP: { available: 0, reserved: 10, settled: 0 } }
        });

        await buildSubscription({ owner: user.id });

        transaction = await buildRebalanceTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          sellExecutionWindow: {
            start: new Date("2024-10-06"),
            end: new Date("2024-10-07"),
            executionType: "MARKET_HOURS"
          },
          buyExecutionWindow: {
            start: new Date("2025-10-06"),
            end: new Date("2025-10-07"),
            executionType: "MARKET_HOURS"
          }
        });

        response = await request(app)
          .post(`/api/m2m/transactions/${transaction.id}/cancel`)
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(async () => {
        jest.restoreAllMocks();
        await clearDb();
      });

      it("should return status 200", () => {
        expect(response.status).toEqual(200);
      });

      it("should set transaction status to cancelled", async () => {
        const updatedTransaction: RebalanceTransactionDocument = await RebalanceTransaction.findById(
          transaction.id
        );
        expect(updatedTransaction.rebalanceStatus).toEqual("Cancelled");
      });
    });
  });

  describe("GET /transactions/billing", () => {
    let user: UserDocument;
    beforeAll(async () => {
      jest.clearAllMocks();
    });
    beforeEach(async () => {
      await clearDb();
      user = await buildUser();
      await Promise.all([
        await buildBankAccount({ owner: user.id }),
        await user.populate("bankAccounts"),
        await buildPortfolio({ owner: user.id })
      ]);
    });

    it("should return 400 if the limit is not numeric", async () => {
      const response = await request(app)
        .get("/api/m2m/transactions/billing?limit=bfae")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
    });

    it("should return 400 if the limit is negative", async () => {
      await buildChargeTransaction({
        owner: user.id,
        chargeType: "custody"
      });

      const response = await request(app)
        .get("/api/m2m/transactions/billing?limit=-40")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
    });

    describe("when user has no charges and no limit", () => {
      it("should return an empty array", async () => {
        const response = await request(app)
          .get("/api/m2m/transactions/billing")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        const receivedTransactions = response.body;
        expect(response.status).toEqual(200);
        expect(receivedTransactions).toEqual(expect.arrayContaining([]));
      });
    });

    describe("when user has no charges and a limit", () => {
      it("should return an empty array", async () => {
        const response = await request(app)
          .get("/api/m2m/transactions/billing?limit=5")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        const receivedTransactions = response.body;
        expect(response.status).toEqual(200);
        expect(receivedTransactions).toEqual(expect.arrayContaining([]));
      });
    });

    describe("when user has a pending custody charge and no limit", () => {
      it("should return a custody charge transaction", async () => {
        const custody = await buildChargeTransaction({
          owner: user.id,
          chargeType: "custody",
          status: "Pending"
        });

        const response = await request(app)
          .get("/api/m2m/transactions/billing")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        const expectedTransaction = await ChargeTransaction.findOne({
          _id: custody._id
        }).populate("bankAccount");
        const receivedTransactions = response.body;
        expect(response.status).toEqual(200);
        expect(receivedTransactions).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has charges and a limit", () => {
      it("should return a custody charge transaction", async () => {
        await buildChargeTransaction({
          owner: user.id,
          chargeType: "custody",
          status: "Pending"
        });

        await buildChargeTransaction({
          owner: user.id,
          chargeType: "subscription",
          status: "Settled"
        });

        const response = await request(app)
          .get("/api/m2m/transactions/billing?limit=5")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        const expectedTransactions = await ChargeTransaction.find({
          owner: user.id
        }).populate("bankAccount subscription");
        const receivedTransactions = response.body;
        expect(response.status).toEqual(200);
        expect(receivedTransactions).toEqual(
          expect.arrayContaining([
            expect.objectContaining(JSON.parse(JSON.stringify(expectedTransactions[0]))),
            expect.objectContaining(JSON.parse(JSON.stringify(expectedTransactions[1])))
          ])
        );
      });
    });
  });
});
