import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import RewardController from "../controllers/rewardController";
import RewardMiddleware from "../middlewares/rewardMiddleware";

const router = express.Router();

/**
 * GET REQUESTS
 */
router.get("/", ErrorMiddleware.catchAsyncErrors(RewardController.getRewards));

/**
 * POST REQUESTS
 */
router.post("/:id", ErrorMiddleware.catchAsyncErrors(RewardController.updateReward));
router.post(
  "/:id/trade-confirmations/generate",
  ErrorMiddleware.catchAsyncErrors(RewardMiddleware.loadReward()),
  ErrorMiddleware.catchAsyncErrors(RewardController.generateTradeConfirmation)
);

export default router;
