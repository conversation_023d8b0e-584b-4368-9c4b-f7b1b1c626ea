import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import { AdminPortfolioController } from "../controllers/adminPortfolioController";
import AuthMiddleware from "../middlewares/authMiddleware";

const router = express.Router();

/**
 * GET REQUESTS
 */
router.get("/", ErrorMiddleware.catchAsyncErrors(AdminPortfolioController.getPortfolios));

/**
 * POST REQUESTS
 */
router.post(
  "/add-bonus-deposit",
  AuthMiddleware.findExternalUserById, // to have access to which admin sends the request
  ErrorMiddleware.catchAsyncErrors(AdminPortfolioController.addBonusDepositToUser)
);

export default router;
