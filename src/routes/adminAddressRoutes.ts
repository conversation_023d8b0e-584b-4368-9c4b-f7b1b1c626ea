import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import AdminAddressController from "../controllers/adminAddressController";

const router = express.Router();

router.post("/", ErrorMiddleware.catchAsyncErrors(AdminAddressController.createOrUpdateAddress));
router.post("/create-wealthkernel", ErrorMiddleware.catchAsyncErrors(AdminAddressController.createAllWkAddresses));

export default router;
