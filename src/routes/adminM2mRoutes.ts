import express from "express";
import adminPortfolioRoutes from "./adminPortfolioRoutes";
import adminTransactionRoutes from "./adminTransactionRoutes";
import adminUsersRoutes from "./adminUserRoutes";
import adminBankAccountRoutes from "./adminBankAccountRoutes";
import adminAddressRoutes from "./adminAddressRoutes";
import adminRewardRoutes from "./adminRewardRoutes";
import AuthMiddleware from "../middlewares/authMiddleware";
import adminOrderRoutes from "./adminOrderRoutes";
import adminInvestmentProductRoutes from "./adminInvestmentProductRoutes";
import adminParticipantRoutes from "./adminParticipantRoutes";
import adminUserDataRequestRoutes from "./adminUserDataRequestRoutes";
import adminGiftRoutes from "./adminGiftRoutes";

const adminM2mRoutes = express.Router();
adminM2mRoutes.use("/transactions", adminTransactionRoutes);
adminM2mRoutes.use("/investment-products", adminInvestmentProductRoutes);
adminM2mRoutes.use("/portfolios", adminPortfolioRoutes);
adminM2mRoutes.use("/bank-accounts", adminBankAccountRoutes);
adminM2mRoutes.use("/addresses", adminAddressRoutes);
adminM2mRoutes.use("/users", adminUsersRoutes);
adminM2mRoutes.use("/participants", adminParticipantRoutes);
adminM2mRoutes.use("/rewards", adminRewardRoutes);
adminM2mRoutes.use("/orders", adminOrderRoutes);
adminM2mRoutes.use("/user-data-requests", adminUserDataRequestRoutes);
adminM2mRoutes.use("/gifts", adminGiftRoutes);

const adminM2mRouter = express.Router();
adminM2mRouter.use(
  AuthMiddleware.validateAccessToken,
  // temporarily use scope 'read:users'
  AuthMiddleware.canAccessScope("read:users"),
  AuthMiddleware.handleErrors,
  adminM2mRoutes
);

export default adminM2mRouter;
