import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import PortfolioMiddleware from "../middlewares/portfolioMiddleware";
import { DailyTickerController } from "../controllers/dailyTickerController";

const router = express.Router();

router.get(
  "/by-tenor",
  ErrorMiddleware.catchAsyncErrors(PortfolioMiddleware.portfolioQueryParamBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(DailyTickerController.getPortfolioTickerPerformanceByTenor)
);

export default router;
