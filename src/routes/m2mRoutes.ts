import express from "express";
import addressRoutes from "./addressRoutes";
import investmentProductRoutes from "./investmentProductRoutes";
import portfolioRoutes from "./portfolioRoutes";
import transactionRoutes from "./transactionRoutes";
import dailyPortfolioTickerRoutes from "./dailyPortfolioTickerRoutes";
import rewardRoutes from "./rewardRoutes";
import userRoutes from "./userRoutes";
import AuthMiddleware from "../middlewares/authMiddleware";
import bankAccountRoutes from "./bankAccountRoutes";
import statisticsRoutes from "./statisticsRoutes";
import subscriptionRoutes from "./subscriptionRoutes";
import userDataRequestRoutes from "./userDataRequestRoutes";
import giftRoutes from "./giftRoutes";
import mandateRoutes from "./mandateRoutes";
import automationRoutes from "./automationRoutes";
import rewardInvitationRoutes from "./rewardInvitationRoutes";
import referralCodeRoutes from "./referralCodeRoutes";
import wealthyhubRoutes from "./wealthyhubRoutes";
import paymentMethodRoutes from "./paymentMethodRoutes";
import kycOperationRoutes from "./kycOperationRoutes";
import orderRoutes from "./orderRoutes";
import savingsProductRoutes from "./savingsProductRoutes";
import assetNewsRoutes from "./assetNewsRoutes";
import appRatingRoutes from "./appRatingRoutes";
import notificationSettingsRoutes from "./notificationSettingsRoutes";

const m2mRoutes = express.Router();
m2mRoutes.use("/portfolios", portfolioRoutes);
m2mRoutes.use("/investment-products", investmentProductRoutes);
m2mRoutes.use("/rewards", rewardRoutes);
m2mRoutes.use("/reward-invitations", rewardInvitationRoutes);
m2mRoutes.use("/gifts", giftRoutes);
m2mRoutes.use("/payment-methods", paymentMethodRoutes);
m2mRoutes.use("/transactions", transactionRoutes);
m2mRoutes.use("/daily-portfolio-tickers", dailyPortfolioTickerRoutes);
m2mRoutes.use("/addresses", addressRoutes);
m2mRoutes.use("/subscriptions", subscriptionRoutes);
m2mRoutes.use("/users", userRoutes);
m2mRoutes.use("/user-data-requests", userDataRequestRoutes);
m2mRoutes.use("/bank-accounts", bankAccountRoutes);
m2mRoutes.use("/statistics", statisticsRoutes);
m2mRoutes.use("/mandates", mandateRoutes);
m2mRoutes.use("/orders", orderRoutes);
m2mRoutes.use("/automations", automationRoutes);
m2mRoutes.use("/referral-codes", referralCodeRoutes);
m2mRoutes.use("/wealthyhub", wealthyhubRoutes);
m2mRoutes.use("/kyc-operations", kycOperationRoutes);
m2mRoutes.use("/savings-products", savingsProductRoutes);
m2mRoutes.use("/asset-news", assetNewsRoutes);
m2mRoutes.use("/app-ratings", appRatingRoutes);
m2mRoutes.use("/notification-settings", notificationSettingsRoutes);

const m2mRouter = express.Router();
m2mRouter.use(
  AuthMiddleware.validateAccessToken,
  // temporarily use scope 'read:users'
  AuthMiddleware.canAccessScope("read:users"),
  AuthMiddleware.findExternalUserById,
  AuthMiddleware.handleErrors,
  m2mRoutes
);
export default m2mRouter;
