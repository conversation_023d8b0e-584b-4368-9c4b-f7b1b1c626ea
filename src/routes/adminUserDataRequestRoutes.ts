import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import AdminUserDataRequestController from "../controllers/adminUserDataRequestController";

const router = express.Router();

router.get("/", ErrorMiddleware.catchAsyncErrors(AdminUserDataRequestController.getUserDataRequests));

router.post("/", ErrorMiddleware.catchAsyncErrors(AdminUserDataRequestController.createUserDataRequest));

export default router;
