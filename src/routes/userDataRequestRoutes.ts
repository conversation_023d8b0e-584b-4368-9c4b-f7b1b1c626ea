import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import UserDataRequestController from "../controllers/userDataRequestController";

const router = express.Router();

router.get("/", ErrorMiddleware.catchAsyncErrors(UserDataRequestController.getUserDataRequests));

router.post("/", ErrorMiddleware.catchAsyncErrors(UserDataRequestController.createUserDataRequest));

export default router;
