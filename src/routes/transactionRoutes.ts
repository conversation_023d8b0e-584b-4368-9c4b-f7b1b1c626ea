import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import TransactionController from "../controllers/transactionController";
import TransactionMiddleware from "../middlewares/transactionMiddleware";
import UserMiddleware from "../middlewares/userMiddleware";

const router = express.Router();

/**
 * GET Requests
 */

router.get("/", ErrorMiddleware.catchAsyncErrors(TransactionController.getTransactions));
router.get("/deposits", ErrorMiddleware.catchAsyncErrors(TransactionController.getDepositCashTransactions));
router.get("/billing", ErrorMiddleware.catchAsyncErrors(TransactionController.getBilling));
router.get(
  "/deposits/:id",
  ErrorMiddleware.catchAsyncErrors(TransactionMiddleware.transactionBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(TransactionController.getDepositCashTransaction)
);
router.get("/assets", ErrorMiddleware.catchAsyncErrors(TransactionController.getAssetTransactions));
router.get(
  "/assets/:id",
  ErrorMiddleware.catchAsyncErrors(TransactionMiddleware.transactionBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(TransactionController.getAssetTransaction)
);
router.get(
  "/assets/pending-deposit/:id",
  ErrorMiddleware.catchAsyncErrors(TransactionMiddleware.transactionBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(TransactionController.getAssetTransactionLinkedToDeposit)
);
router.get("/pending", ErrorMiddleware.catchAsyncErrors(TransactionController.getPendingTransactions));
router.get("/pending/rebalances", ErrorMiddleware.catchAsyncErrors(TransactionController.getPendingRebalances));
router.get("/me/cash-activity", ErrorMiddleware.catchAsyncErrors(TransactionController.getCashActivity));
router.get(
  "/:id",
  ErrorMiddleware.catchAsyncErrors(TransactionMiddleware.transactionBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(TransactionController.getTransaction)
);

/**
 * POST Requests
 */
router.post(
  "/deposits",
  UserMiddleware.userHasPassedKyc,
  ErrorMiddleware.catchAsyncErrors(TransactionController.createPayment)
);
router.post(
  "/charges/lifetime",
  UserMiddleware.userHasPassedKyc,
  ErrorMiddleware.catchAsyncErrors(TransactionController.createLifetimeSubscriptionCharge)
);
router.post(
  "/savings/deposit",
  UserMiddleware.userHasPassedKyc,
  ErrorMiddleware.catchAsyncErrors(TransactionController.createSavingsDepositPayment)
);
router.post(
  "/wealthyhood-dividends/:id",
  ErrorMiddleware.catchAsyncErrors(TransactionMiddleware.transactionBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(TransactionMiddleware.transactionIsWealthyhoodDividend),
  ErrorMiddleware.catchAsyncErrors(TransactionController.updateWealthyhoodDividendTransaction)
);
router.post(
  "/deposits/sync-truelayer",
  ErrorMiddleware.catchAsyncErrors(TransactionMiddleware.paymentBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(TransactionController.syncDepositTruelayerStatus)
);
router.post(
  "/charges/lifetime/sync-truelayer",
  ErrorMiddleware.catchAsyncErrors(TransactionMiddleware.paymentBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(TransactionController.syncLifetimeChargeTruelayerStatus)
);
router.post("/preview", ErrorMiddleware.catchAsyncErrors(TransactionController.getTransactionPreview));
router.post(
  "/:id/cancel",
  ErrorMiddleware.catchAsyncErrors(TransactionMiddleware.transactionBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(TransactionController.cancelTransaction)
);

export default router;
