{"eslint.autoFixOnSave": false, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "editor.formatOnSave": true, "files.eol": "\n", "[javascript]": {"editor.formatOnSave": true, "editor.tabSize": 2}, "[typescript]": {"editor.formatOnSave": true, "editor.tabSize": 2}, "[typescriptreact]": {"editor.formatOnSave": true, "editor.tabSize": 2}, "[json]": {"editor.formatOnSave": true, "editor.tabSize": 2}, "[markdown]": {"editor.formatOnSave": false}, "[handlebars]": {"editor.formatOnSave": false}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/coverage": true}, "typescript.referencesCodeLens.enabled": true, "files.associations": {"*.jsx": "javascriptreact", "*.tsx": "typescriptreact"}, "typescript.tsdk": "node_modules/typescript/lib"}