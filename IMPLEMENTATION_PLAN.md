## FX Spread Revamp – Implementation Plan

### 0. Objective
Align the backend FX calculation & charging logic with the new rules defined in `PRD.md` and the per-plan walk-throughs (`PRD_EXAMPLE_*`).  
Key points:
1. Show the client a target FX rate (m_target) that results in the perceived spreads: 32.5 bp (Gold), 40 bp (Plus), 55 bp (Basic).
2. Collect an **extra Wealthyhood FX fee** (m_hide) only where we make a profit: 0 bp (Gold), 5 bp (Plus), 15 bp (Basic).
3. Never subsidise the transaction – our remit to WK remains the fixed 40 bp.
4. Correct sign handling for BUY (rate gets *worse* → mid × (1 – spread)) and SELL (rate gets *worse* → mid × (1 + spread)).
5. Correct unit price display handling to "inflate" the price where needed with the correct fx display

### 1. Terminology & constants
| Plan | m_target (display spread) | m_hide (extra fee) |
|------|--------------------------|--------------------|
| Gold | 0.00325                  | 0                  |
| Plus | 0.00400                  | 0.00050            |
| Basic| 0.00550                  | 0.00150            |

Two new constant maps will be introduced
```ts
export const FX_DISPLAY_SPREADS: Record<plansConfig.PlanType, number> = {
  gold: 0.00325,
  plus: 0.004,   // 40 bp
  basic: 0.0055  // 55 bp
};

export const FX_FEE_SPREADS_WH: Record<plansConfig.PlanType, number> = {
  gold: 0,
  plus: 0.0005,  // 5 bp
  basic: 0.0015  // 15 bp
};
```

Do not change anything inside `@wealthyhood/shared-configs/src/fees.ts` because this is an external package. Just overwrite what's needed.

`FX_DISPLAY_SPREADS` replaces former `FX_RATES` for **rate display / storage**, while `FX_FEE_SPREADS_WH` drives the FX fee that reduces the customer's consideration amount.
For backwards compatibility `FX_RATES` will remain wherever needed.

### 2. Code-level changes
1. **shared-configs / fees.ts**  
1. **in an fx config**
   • Add constant maps `FX_DISPLAY_SPREADS`, `FX_FEE_SPREADS_WH` as shown.  
   • Deprecate wherever possible old `FX_RATES`; instead use `FX_FEE_SPREADS_WH`.

2. **CurrencyUtil.isForeignCurrency** (or equivalent helper)  
   • Verify it returns `true` whenever `userCurrency !== tradedCurrency` (covers GBP/USD, EUR/USD, GBP/EUR, etc.).  
   • Add unit tests for the four pairs mentioned.

3. **TransactionService.calculateFXRateWithSpread**  
   • Replace reference to `FX_RATES[plan]` with `FX_DISPLAY_SPREADS[plan]` for BUY/SELL rate adjustment.  
   • Logic remains `(1 – spread)` for BUY, `(1 + spread)` for SELL.

4. **OrderService.calculateFeesForSingleOrder**  
   • Remove the minimum fx fee floor – use purely the percentage from `FX_FEE_SPREADS_WH[plan]`.  
   ```ts
   // BEFORE
   fxFee = Decimal.max(MINIMUM_FX_FEE, Decimal.mul(amountAfterRealTimeFee, FX_RATES[plan]).toDecimalPlaces(2));
   // AFTER
   fxFee = Decimal.mul(amountAfterRealTimeFee, FX_FEE_SPREADS_WH[plan]).toDecimalPlaces(2);
   ```  
   • Keep execution spread block untouched – with the config set to 0 no fee will be added.

5. **OrderService.applyFeesToOrders** & downstream logic  
   • Behaviour unchanged but verify that for Gold plan FX fee is truly **0** when currencies match and **0** when they differ (percentage = 0).  

6. **Tests**  
   a. Unit tests for `calculateFXRateWithSpread` (BUY/SELL) per plan.  
   b. Unit tests for `calculateFeesForSingleOrder` ensuring:  
      • Gold → fxFee = 0;  
      • Plus → 5 bp;  
      • Basic → 15 bp.  
   c. Tests for `CurrencyUtil.isForeignCurrency` covering GBP/USD, EUR/USD, GBP/EUR, EUR/EUR (false).

### 3. Order Service adjustments  

File: `src/services/orderService.ts`

1. **applyFeesToOrders**  
   • Replace old `FX_RATES` logic with new `FX_FEE_SPREADS_WH` as already noted.  
   • After fees are calculated, update **buy-side** orders so that
     `consideration.amountSubmitted = originalAmount – realtimeExecutionFee – fxFee – commission – executionSpread` (FX fee now percentage-based, no min).  
   • For **sell-side** orders, continue to calculate fees *after* we estimate sell proceeds. FX fee is still deducted from what the user receives.  
   • Ensure Gold plan path results in `fxFee = 0` (percentage zero).

2. **calculateFeesForSingleOrder**  
   • Already covered – use `FX_FEE_SPREADS_WH`, ignore `MINIMUM_FX_FEE`.

3. **fillOrderClientDisplayFields / display helpers**  
   • Pass the new display FX rates (`FX_DISPLAY_SPREADS`) when calling `order.getDisplayExchangeRate`.

4. **Unit tests**  
   • Add regression tests to confirm `applyFeesToOrders` produces the correct `consideration.amountSubmitted` for representative BUY and SELL orders per plan.

### 4. Order model updates  

File: `src/models/Order.ts`

Key virtuals to revise:

1. **displayUnitPrice**  
   Current formula: `(amountPaid – realtimeExecutionFee) / qty × FX_display`.  
   • Ensure `FX_display` comes from `exchangeRate` *if matched*, else from live `FX_DISPLAY_SPREADS`.  
   • For BUY orders we still subtract realtime fee; for SELL we add it back (logic unchanged).

2. **getDisplayExchangeRate**  
   • When order is **Pending** return `FX_DISPLAY_SPREADS`-adjusted live rate (already fetched via `TransactionService.getAllForeignCurrencyRatesWithSpread`).  
   • When **Matched** return stored `exchangeRate` rounded to 3 dp (unchanged) – ensure that stored value was computed with display spreads (handled earlier).

3. **getDisplayAmount**  
   • For BUY: keep showing *original intent* (£ paid) – already unaffected.  
   • For SELL: ensure pending-state estimation multiplies `qty × currentTicker × FX_display` (was already using spread but confirm after refactor).

4. **amountForReturnsAndUpBy**  
   • Since FX fee is now included in `amountSubmitted` for BUYs, logic remains; just ensure no hidden min fee is lurking.

### 5. Tests for Order model  
• Add unit tests validating `displayUnitPrice`, `getDisplayExchangeRate`, `getDisplayAmount` for a pending BUY and pending SELL under each plan, given a mock mid-rate of 1.25.  
• Matched order tests: stored `exchangeRate` equals calculated display rate within rounding tolerance.

### 6. Schema / data migration
_No change._
