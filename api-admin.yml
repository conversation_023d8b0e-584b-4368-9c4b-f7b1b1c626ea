openapi: "3.0.2"
info:
  title: Wealthyhood API
  version: "1.0"
  description: |
    Wealthyhood admin api is protected and requires a bearer jwt token (retrieved with client_credentials flow) to be accessed.


    The following scopes are required in order to access api


    - **api:user** (example)

    - **api:admin** (example)


    ## How to retrieve access tokens
    ### client credentials m2m flow
    ```
    curl --location --request POST 'https://dev-inrhgw7y.eu.auth0.com/oauth/token' \
    --header 'Content-Type: application/x-www-form-urlencoded' \
    --data-urlencode 'grant_type=client_credentials' \
    --data-urlencode 'client_id=<client_id>' \
    --data-urlencode 'client_secret=<client_secret>' \
    --data-urlencode 'audience=https://dev-inrhgw7y.eu.auth0.com/api/v2/'
    ```

servers:
  - url: http://localhost:2000/api/admin/m2m/
tags:
  - name: "admin-portfolios"
    description: "Operations regarding portfolios for admins."
  - name: "admin-transactions"
    description: "Operations regarding transactions for admins."
  - name: "admin-users"
    description: "Operations regarding users for admins."
  - name: "admin-rewards"
    description: "Operations regarding rewards for admins"
security:
  - bearerAuth: [ ]

paths:
  /portfolios:
    get:
      description: "Retrieves authenticated user's portfolios"
      tags:
        - admin-portfolios
      parameters:
        - in: query
          name: owner
          schema:
            type: string
            format: hash
          required: false
          description: portfolios owner
        - in: query
          name: mode
          schema:
            $ref: "#/components/schemas/PortfolioModeEnum"
          required: false
          description: portfolios mode
        - in: query
          name: wealthkernelExists
          schema:
            type: boolean
          required: false
          description: determines if portfolios should have wealthkernel entry
        - in: query
          name: sort
          schema:
            type: string
          required: false
          description: projecting field to be used for sorting starting with '-' for desc/ '+' for asc ordering
          example: sort=-createdAt (desc order)
        - in: query
          name: populateTicker
          schema:
            type: boolean
          required: false
          description: determines if portfolio ticker, holdings should be populated
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Portfolio"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /portfolios/sync-wealthkernel:
    post:
      description: "Syncs all users' portofolios with wealthkernel"
      tags:
        - admin-portfolios
      responses:
        204:
          description: OK
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
  /transactions/{id}:
    get:
      description: "Retrieves a transaction by id"
      tags:
        - admin-transactions
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                anyOf:
                  - $ref: "#/components/schemas/Transaction"
                  - $ref: "#/components/schemas/AssetTransaction"
                  - $ref: "#/components/schemas/DepositCashTransaction"
                  - $ref: "#/components/schemas/WithdrawalCashTransaction"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /transactions:
    get:
      description: "Retrieves transactions"
      tags:
        - admin-transactions
      parameters:
        - in: query
          name: portfolio
          schema:
            type: string
            format: hash
          required: false
          description: portfolio's id
        - in: query
          name: owner
          schema:
            type: string
            format: hash
          required: false
          description: owner's id
        - in: query
          name: sort
          schema:
            type: string
          required: false
          description: projecting field to be used for sorting starting with '-' for desc/ '+' for asc ordering
          example: sort=-createdAt (desc order)
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  anyOf:
                    - $ref: "#/components/schemas/Transaction"
                    - $ref: "#/components/schemas/AssetTransaction"
                    - $ref: "#/components/schemas/DepositCashTransaction"
                    - $ref: "#/components/schemas/WithdrawalCashTransaction"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /transactions/{id}/sync:
    post:
      description: "Syncs transaction with wealthkernel"
      tags:
        - admin-transactions
      parameters:
        - name: id
          in: path
          description: "Transaction id"
          required: true
          schema:
            type: string
            format: hash
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Transaction"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /transactions/assets/{id}:
    get:
      description: "Retrieves an asset transaction by id"
      tags:
        - admin-transactions
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
        - in: query
          name: populateOrders
          schema:
            type: boolean
          required: false
          description: determines if orders of transaction should be populated
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AssetTransaction"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /transactions/assets:
    get:
      description: "Retrieves asset transactions. Pagination can be applied to retrieved data by using parameters pageSize, page"
      tags:
        - admin-transactions
      parameters:
        - in: query
          name: pageSize
          schema:
            type: number
            format: integer
          required: true
          description: determines pagination's page size
        - in: query
          name: page
          schema:
            type: number
            format: integer
          required: true
          description: determines which page to retrieve (starting from 1) based on page size
        - in: query
          name: populateOwner
          schema:
            type: boolean
          required: false
          description: determines if owners should be populated
        - in: query
          name: sort
          schema:
            type: string
          required: false
          description: projecting field to be used for sorting starting with '-' for desc/ '+' for asc ordering
          example: sort=-createdAt (desc order)
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  pagination:
                    $ref: "#/components/schemas/Pagination"
                  transactions:
                    type: array
                    items:
                      $ref: "#/components/schemas/AssetTransaction"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /transactions/assets/sync-wealthkernel:
    post:
      description: "Syncs all asset transactions with wealthkernel"
      tags:
        - admin-transactions
      responses:
        204:
          description: OK
        401:
          $ref: "#/components/responses/Unauthorized"
  /transactions/assets/sync-pending-deposit:
    post:
      description: "Syncs all asset transactions with settled pending deposits"
      tags:
        - admin-transactions
      responses:
        204:
          description: OK
        401:
          $ref: "#/components/responses/Unauthorized"
  /transactions/withdrawals/convert-users-with-pending-deposits:
    post:
      description: "Changes portfolio convertion status to 'inProgress' for users that have transactions with settled pending deposits"
      tags:
        - admin-transactions
      responses:
        204:
          description: OK
        401:
          $ref: "#/components/responses/Unauthorized"
  /transactions/deposits/{id}:
    get:
      description: "Retrieves a deposit transaction by id"
      tags:
        - admin-transactions
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DepositCashTransaction"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /transactions/deposits:
    get:
      description: "Retrieves deposit cash transactions. Pagination can be applied to retrieved data by using parameters pageSize, page"
      tags:
        - admin-transactions
      parameters:
        - in: query
          name: pageSize
          schema:
            type: number
            format: integer
          required: true
          description: determines pagination's page size
        - in: query
          name: page
          schema:
            type: number
            format: integer
          required: true
          description: determines which page to retrieve (starting from 1) based on page size
        - in: query
          name: populateOwner
          schema:
            type: boolean
          required: false
          description: determines if owners should be populated
        - in: query
          name: sort
          schema:
            type: string
          required: false
          description: projecting field to be used for sorting starting with '-' for desc/ '+' for asc ordering
          example: sort=-createdAt (desc order)
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  pagination:
                    $ref: "#/components/schemas/Pagination"
                  transactions:
                    type: array
                    items:
                      $ref: "#/components/schemas/DepositCashTransaction"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /transactions/deposits/sync-truelayer:
    post:
      description: "Sync all deposit with non terminal truelayer state with truelayer"
      tags:
        - admin-transactions
      responses:
        204:
          description: OK
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
  /transactions/deposits/create-wealthkernel:
    post:
      description: "Creates wealthkernel expectations for all eligible deposits"
      tags:
        - admin-transactions
      responses:
        204:
          description: OK
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
  /transactions/deposits/sync-wealthkernel:
    post:
      description: "Sync all deposit with non terminal truelayer state with wealthkernel"
      tags:
        - admin-transactions
      responses:
        204:
          description: OK
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
  /transactions/withdrawals/{id}:
    get:
      description: "Retrieves a withdrawal cash transaction by id"
      tags:
        - admin-transactions
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WithdrawalCashTransaction"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /transactions/withdrawals:
    get:
      description: "Retrieves withdrawal cash transactions. Pagination can be applied to retrieved data by using parameters pageSize, page"
      tags:
        - admin-transactions
      parameters:
        - in: query
          name: pageSize
          schema:
            type: number
            format: integer
          required: true
          description: determines pagination's page size
        - in: query
          name: page
          schema:
            type: number
            format: integer
          required: true
          description: determines which page to retrieve (starting from 1)  based on page size
        - in: query
          name: populateOwner
          schema:
            type: boolean
          required: false
          description: determines if owners should be populated
        - in: query
          name: sort
          schema:
            type: string
          required: false
          description: projecting field to be used for sorting starting with '-' for desc/ '+' for asc ordering
          example: sort=-createdAt (desc order)
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  pagination:
                    $ref: "#/components/schemas/Pagination"
                  transactions:
                    type: array
                    items:
                      $ref: "#/components/schemas/WithdrawalCashTransaction"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /transactions/withdrawals/sync-wealthkernel:
    post:
      description: "Syncs all pending withdrawals with wealthkernel"
      tags:
        - admin-transactions
      responses:
        204:
          description: OK
        401:
          $ref: "#/components/responses/Unauthorized"
  /transactions/withdrawals/create-wealthkernel:
    post:
      description: "Creates withdrawals with wealthkernel"
      tags:
        - admin-transactions
      responses:
        204:
          description: OK
        401:
          $ref: "#/components/responses/Unauthorized"
  /daily-portfolio-tickers/create-daily:
    post:
      description: "Updates daily portfolio tickers. Returns 204 when updating has been completed successfully"
      tags:
        - admin-daily-tickers
      responses:
        204:
          description: OK
        401:
          $ref: "#/components/responses/Unauthorized"
  /daily-asset-tickers/create-daily:
    post:
      description: "Updates daily investment asset tickers. Returns 204 when updating has been completed successfully"
      tags:
        - admin-daily-tickers
      responses:
        204:
          description: OK
        401:
          $ref: "#/components/responses/Unauthorized"
  /addresses:
    post:
      description: "Creates or updates address of given ownerId"
      tags:
        - admin-addresses
      parameters:
        - name: owner
          in: query
          required: true
          schema:
            type: string
            format: hash
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                owner:
                  description: "Owner ID, must match the one given in headers"
                  type: string
                line1:
                  description: "First line of address"
                  type: string
                line2:
                  description: "Second line of address"
                  type: string
                postalCode:
                  type: string
                city:
                  type: string
                countryCode:
                  type: string
      responses:
        204:
          description: "OK"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
  /addresses/create-wealthkernel:
    post:
      description: "Creates all eligible user addresses in Wealthkernel"
      tags:
        - admin-addresses
      responses:
        204:
          description: OK
        401:
          $ref: "#/components/responses/Unauthorized"
  /accounts/create-wealthkernel:
    post:
      description: "Creates all eligible user accounts in Wealthkernel"
      tags:
        - admin-accounts
      responses:
        204:
          description: OK
        401:
          $ref: "#/components/responses/Unauthorized"
  /accounts/sync-wealthkernel:
    post:
      description: "Syncs all user accounts in Wealthkernel"
      tags:
        - admin-accounts
      responses:
        204:
          description: OK
        401:
          $ref: "#/components/responses/Unauthorized"
  /bank-accounts/create-wealthkernel:
    post:
      description: "Creates all eligible user bank accounts in Wealthkernel"
      tags:
        - admin-bank-accounts
      responses:
        204:
          description: OK
        401:
          $ref: "#/components/responses/Unauthorized"
  /bank-accounts/sync-wealthkernel:
    post:
      description: "Syncs all user bank accounts in Wealthkernel"
      tags:
        - admin-bank-accounts
      responses:
        204:
          description: OK
        401:
          $ref: "#/components/responses/Unauthorized"
  /users:
    get:
      description: "Retrieves users. Pagination can be applied to retrieved data by using parameters pageSize, page"
      tags:
        - admin-users
      parameters:
        - in: query
          name: populatePortfolios
          schema:
            type: boolean
          required: false
          description: Determines if users' portfolios should be populated
        - in: query
          name: sort
          schema:
            type: string
          required: false
          description: projecting field to be used for sorting starting with '-' for desc/ '+' for asc ordering
          example: sort=-createdAt (desc order)
        - in: query
          name: role
          explode: true
          schema:
            type: array
            items:
              $ref: "#/components/schemas/UserTypeEnum"
          required: false
          description: roles of user
        - in: query
          name: email
          schema:
            type: string
          required: false
          description: User full email being used for filtering
        - in: query
          name: pageSize
          schema:
            type: number
            format: integer
          required: false
          description: determines pagination's page size
        - in: query
          name: page
          schema:
            type: number
            format: integer
          required: false
          description: determines which page to retrieve (starting from 1) based on page size
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  pagination:
                    $ref: "#/components/schemas/Pagination"
                  users:
                    type: array
                    items:
                      $ref: "#/components/schemas/User"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /users/{id}:
    get:
      description: Retrieves a user by id
      tags:
        - admin-users
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
          description: User's id
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/User"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
    post:
      description: "Updates user referenced by path param id"
      tags:
        - admin-users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                firstName:
                  type: string
                lastName:
                  type: string
                countryOfBirth:
                  type: string
                dateOfBirth:
                  type: string
                  format: date
                  description: date in ISO format 'YYYY-MM-DDTHH:MM:SSZ'
                nationality:
                  type: string
                taxResidencyCountryCode:
                  type: string
                taxResidencyProofType:
                  type: string
                taxResidencyProofValue:
                  type: string
                taxResidencyIsUK:
                  type: boolean
                viewedWelcomePage:
                  type: boolean
                uKResidentStatus:
                  type: boolean
                submittedRequiredInfoAt:
                  type: string
                  format: date
                  description: date in ISO format 'YYYY-MM-DDTHH:MM:SSZ'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: "#/components/schemas/User"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /users/auth0/{id}:
    post:
      description: "Creates or Updates user referenced by path param id, using auth0 info"
      tags:
        - admin-users
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                createdAt:
                  type: string
                  format: date-time
                email:
                  type: string
                emailDisposable:
                  type: boolean
                emailVerified:
                  type: boolean
                role:
                  type: array
                  items:
                    type: string
                auth0:
                  type: object
                  properties:
                    id:
                      type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: "#/components/schemas/User"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /users/create-wealthkernel-parties:
    post:
      description: Creates Wealthkernel parties for all eligible users that have submitted the required info but are missing a party entity. It can also run for single user if a user id is provided"
      tags:
        - admin-users
      responses:
        204:
          description: OK
        401:
          $ref: "#/components/responses/Unauthorized"
  /uses/referrals/count:
    get:
      tags:
        - admin-users
      description: Returns the count of valid referrals for user email provided
      parameters:
        - in: query
          name: userEmail
          schema:
            type: string
          required: true
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  referralsCount:
                    type: number
                    format: int
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"

  /rewards:
    get:
      description: "Retrieves rewards. Pagination can be applied to retrieved data by using parameters pageSize, page"
      tags:
        - admin-rewards
      parameters:
        - in: query
          name: targetUser
          schema:
            type: string
          required: false
          description: target user of rewards to be retrieved
        - in: query
          name: populatePortfolio
          schema:
            type: boolean
          required: false
          description: Determines if users' portfolios should be populated
        - in: query
          name: sort
          schema:
            type: string
          required: false
          description: projecting field to be used for sorting starting with '-' for desc/ '+' for asc ordering
          example: sort=-createdAt (desc order)
        - in: query
          name: pageSize
          schema:
            type: number
            format: integer
          required: false
          description: determines pagination's page size
        - in: query
          name: page
          schema:
            type: number
            format: integer
          required: false
          description: determines which page to retrieve (starting from 1) based on page size
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                anyOf:
                - type: object
                  properties:
                    pagination:
                      $ref: "#/components/schemas/Pagination"
                    rewards:
                      type: array
                      items:
                        $ref: "#/components/schemas/Reward"
                - type: object
                  properties:
                    data:
                      type: array
                      items:
                        $ref: "#/components/schemas/Reward"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /rewards/{id}:
    get:
      tags:
        - admin-rewards
      description: Retrieves a reward by id
      parameters:
      - in: path
        name: id
        schema:
          type: string
        description: Reward's id
        required: true
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Reward"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
    post:
      tags:
        - admin-rewards
      description: Updates a reward by id
      parameters:
      - in: path
        name: id
        schema:
          type: string
        description: Reward's id
        required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: "object"
              properties:
                depositId:
                  type: string
      responses:
        204:
          description: OK
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        403:
          $ref: "#/components/responses/Forbidden"
  /rewards/sync-deposits:
    post:
      description: "Syncs the pending deposits transactions that are made from the Wealthyhood wallet to the investors' e-wallet to have available cash that will later be used to buy the reward ET"
      tags:
        - admin-rewards
      responses:
        204:
          description: OK
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
  /rewards/create-orders:
    post:
      description: "Creates order requests to Wealthkernel for the reward ETFs, when the corresponding transaction deposit has been settled and enough cash is available to the user's e-wallet"
      tags:
        - admin-rewards
      responses:
        204:
          description: OK
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
  /rewards/sync-orders:
    post:
      description: "Syncs the pending ETF orders that when settled will add the reward to the users' portfolio"
      tags:
        - admin-rewards
      responses:
        204:
          description: OK
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"

components:
  responses:
    Portfolio:
      description: "Operation was successful"
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Portfolio"
    NotFound:
      description: The specified resource was not found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ApiErrorResponse"
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ApiErrorResponse"
    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ApiErrorResponse"
    BadRequest:
      description: "Bad Request"
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ApiErrorResponse"
  schemas:
    TransactionCategoryEnum:
      type: string
      enum:
        - DepositCashTransaction
        - WithdrawalCashTransaction
        - AssetTransaction
    CurrencyEnum:
      type: string
      enum:
        - EUR
        - GBP
        - USD
    PortfolioModeEnum:
      type: string
      enum:
        - REAL
        - VIRTUAL

    WrapperTypeEnum:
      type: string
      enum:
        - GIA
        - ISA

    CashType:
      type: object
      properties:
        available:
          type: number
          format: double
        reserved:
          type: number
          format: double

    PersonalisationType:
      type: object
      properties:
        assetClasses:
          type: array
          items:
            type: string
        geography:
          type: string
        risk:
          type: number
        sectors:
          type: array
          items:
            type: string

    InitialHoldingsAllocationType:
      type: object
      properties:
        asset:
          $ref: "#/components/schemas/InvestmentProduct"
          nullable: true
        assetCommonId:
          type: string
        percentage:
          type: number
          format: double

    ApiErrorResponse:
      type: object
      properties:
        status:
          type: number
          format: integer
        error:
          type: object
          properties:
            message:
              type: string
            description:
              type: string
              nullable: true
        responseId:
          type: string
          format: "uuid"

    HoldingsType:
      type: object
      properties:
        asset:
          $ref: "#/components/schemas/InvestmentProduct"
          nullable: true
        assetCommonId:
          type: string
          description: "A valid asset id from supported assets"
        quantity:
          type: number
          format: double
    Portfolio:
      type: object
      properties:
        cash:
          $ref: "#/components/schemas/CashType"
        createdAt:
          type: string
          format: date-time
        currency:
          $ref: "#/components/schemas/CurrencyEnum"
        currentTicker:
          $ref: "#/components/schemas/DailyPortfolioTicker"
        holdings:
          type: array
          items:
            $ref: "#/components/schemas/HoldingsType"
        initialHoldingsAllocation:
          $ref: "#/components/schemas/InitialHoldingsAllocationType"
        lastUpdated:
          type: string
          format: date-time
        mode:
          $ref: "#/components/schemas/PortfolioModeEnum"
        name:
          type: string
          nullable: true
        owner:
          type: object
        personalisationPreferences:
          $ref: "#/components/schemas/PersonalisationType"
        totalDepositedAmount:
          type: number
          format: double
        wealthkernel:
          type: object
          properties:
            portfolioId:
              type: string
        wrapperType:
          $ref: "#/components/schemas/WrapperTypeEnum"

    PortfolioWithReturns:
      allOf:
        - $ref: "#/components/schemas/Portfolio"
        - type: object
          required:
            - rootCause
          properties:
            returnsValue:
              type: number
              format: double
            upByValue:
              type: number
              format: double
            holdings:
              type: array
              items:
                allOf:
                  - type: object
                    properties:
                      asset:
                        $ref: "#/components/schemas/InvestmentProduct"
                        nullable: true
                      assetCommonId:
                        type: string
                        description: "A valid asset id from supported assets"
                      quantity:
                        type: number
                        format: double
                      sinceBuyReturns:
                        type: number
                        format: double

    InvestmentProduct:
      type: object
      properties:
        assetClass:
          type: string
          enum:
            - EQUITIES
            - BONDS
            - COMMODITTIES
            - REAL_ESTATE
        currentTicker:
          type: object
        description:
          type: string
          nullable: true
        commonId:
          type: string
          description: "A valid asset id from supported assets"
        isin:
          type: string
        listed:
          type: boolean
          nullable: true
        name:
          type: string
        officialTickerName:
          type: string
        officialExchange:
          type: string
        wealthkernelPortfolioId:
          type: string
          nullable: true
    DailyPortfolioTicker:
      properties:
        currency:
          $ref: "#/components/schemas/CurrencyEnum"
        date:
          type: string
          format: date-time
        price:
          type: number
          format: double
        openingPrice:
          type: number
          format: double
          nullable: true
        closingPrice:
          type: number
          format: double
          nullable: true
        returnPercentage:
          type: number
          format: double
        dateLabel:
          type: string
          format: date-time
        portfolio:
          type: string
          description: "Portfolio's id"
        wealthkernelValue:
          type: number
          format: double
    PortfolioData:
      type: object
      properties:
        cash:
          type: object
          properties:
            GBP:
              $ref: "#/components/schemas/CashType"
        holdings:
          type: array
          items:
            $ref: "#/components/schemas/HoldingsType"
        initialHoldingAllocation:
          $ref: "#/components/schemas/InitialHoldingsAllocationType"
        mode:
          $ref: "#/components/schemas/PortfolioModeEnum"
        owner:
          type: string
        wrapperType:
          $ref: "#/components/schemas/WrapperTypeEnum"
    TrasnactionStatusEnum:
      type: string
      enum:
        - Pending
        - Cancelled
        - Rejected
        - Settled
    Transaction:
      type: object
      properties:
        owner:
          anyOf:
            - type: string
            - type: object
        portfolio:
          anyOf:
            - type: string
            - $ref: "#/components/schemas/Portfolio"
        wealthkernelPortfolioId:
          type: string
        consideration:
          type: object
          properties:
            currency:
              $ref: "#/components/schemas/CurrencyEnum"
            amount:
              type: number
              format: double
        category:
          $ref: "#/components/schemas/TransactionCategoryEnum"
        createdAt:
          type: string
          format: date-time
        status:
          $ref: "#/components/schemas/TrasnactionStatusEnum"
    PortfolioTransactionCategoryEnum:
      type: string
      enum:
        - buy
        - sell
        - update
    AssetTransaction:
      allOf:
        - $ref: "#/components/schemas/Transaction"
        - type: object
          required:
            - rootCause
          properties:
            orders:
              anyOf:
                - type: array
                  items:
                    type: string
                - type: array
                  items:
                    type: object
                    description: "Order schema to be implemented"
            originalInvestmentAmount:
              type: number
              format: double
    PaymentStatusEnum:
      type: string
      enum:
        - new
        - authorised
        - cancelled
        - failed
        - rejected
        - submitted
        - executed
    DepositStatusEnum:
      type: string
      enum:
        - Created
        - Active
        - Settled
        - Cancelled
        - Rejected
    DepositCashTransaction:
      allOf:
        - $ref: "#/components/schemas/Transaction"
        - type: object
          required:
            - rootCause
          properties:
            wealthkernel:
              type: object
              properties:
                id:
                  type: string
                status:
                  $ref: "#/components/schemas/DepositStatusEnum"
    WithdrawalStatusEnum:
      type: string
      enum:
        - Pending
        - Active
        - Settled
        - Cancelling
        - Cancelled
        - Rejected
    WithdrawalCashTransaction:
      allOf:
        - $ref: "#/components/schemas/Transaction"
        - type: object
          required:
            - rootCause
          properties:
            bankReference:
              type: string
            truelayer:
              type: object
              properties:
                id:
                  type: string
                status:
                  $ref: "#/components/schemas/PaymentStatusEnum"
            wealthkernel:
              type: object
              properties:
                id:
                  type: string
                status:
                  $ref: "#/components/schemas/WithdrawalStatusEnum"
    Pagination:
      type: object
      properties:
        page:
          type: number
          format: integer
        pageSize:
          type: number
          format: integer
        pages:
          type: number
          format: integer
        count:
          type: number
          format: integer
    UserTypeEnum:
      type: string
      enum:
        - INVESTOR
        - ADMIN
        - TEST_ACCOUNT
    User:
      type: object
      properties:
        auth0:
          type: object
          properties:
            id:
              type: string
        bankAccounts:
          type: array
          items:
            anyOf:
              - type: string
              - $ref: "#/components/schemas/BankAccount"
        portfolios:
          type: array
          items:
            anyOf:
              - type: string
              - $ref: "#/components/schemas/Portfolio"
        addresses:
          type: array
          items:
            type: object
        createdAt:
          type: string
          format: date
        currency:
          type: string
        dateOfBirth:
          type: string
          format: date
        email:
          type: string
        emailDisposable:
          type: string
        emailVerified:
          type: string
        firstName:
          type: string
        isUKTaxResident:
          type: boolean
        img:
          type: string
        initialInvestment:
          type: number
          format: double
        kycPassed:
          type: boolean
        lastName:
          type: string
        lastLogin:
          type: string
          format: date
        nationalities:
          type: array
          items:
            type: string
        role:
          type: array
          items:
            $ref: "#/components/schemas/UserTypeEnum"
        passports:
          type: array
          items:
            type: string
        monthlyInvestment:
          type: number
          format: double
        portfolioConversionStatus:
          type: string
          enum:
            - notStarted
            - inProgress
            - completed
        taxResidency:
          type: object
          properties:
            countryCode:
              type: string
            proofType:
              type: string
            value:
              type: string
        UKResidentStatus:
          type: string
          enum:
            - uk
            - non-uk
            -
        viewedWelcomePage:
          type: boolean
        virtualPortfolio:
          anyOf:
            - type: string
            - $ref: "#/components/schemas/Portfolio"
        wealthkernel:
          type: object
          properties:
            partyId:
              type: string
    BankAccount:
      type: object
      properties:
        owner:
          type: string
        name:
          type: string
        number:
          type: string
        sortCode:
          type: string
        truelayerProviderId:
          type: string
        wealthkernel?:
          type: object
          properties:
            id:
              type: string
            status:
              type: string
              enum:
                - Active
                - Inactive
    Provider:
      type: object
      properties:
        id:
          type: string
        logo:
          type: string
        icon:
          type: string
        displayable_name:
          type: string
        main_bg_color:
          type: string
        supports_app_to_app:
          type: boolean
        country_code:
          type: string
        divisions:
          type: array
          items:
            type: string
    OrderStatusEnum:
      type: string
      enum:
        - Pending
        - Open
        - Matched
        - Rejected
        - Cancelling
        - Cancelled
    Reward:
      type: object
      properties:
        asset:
          type: string
        consideration:
          type: object
          properties:
            currency:
              $ref: "#/components/schemas/CurrencyEnum"
            amount:
              type: number
              format: double
        quantity:
          type: number
          format: double
        referrer:
          anyOf:
            - type: string
            - $ref: "#/components/schemas/User"
        referral:
          anyOf:
            - type: string
            - $ref: "#/components/schemas/User"
        targetUser:
          anyOf:
            - type: string
            - $ref: "#/components/schemas/User"
        updatedAt:
          type: string
          format: date
        deposit:
          type: object
          properties:
            wealthkernel:
              type: object
              properties:
                id:
                  type: string
                status:
                  $ref: "#/components/schemas/DepositStatusEnum"
        order:
          type: object
          properties:
            wealthkernel:
              type: object
              properties:
                id:
                  type: string
                status:
                  $ref: "#/components/schemas/OrderStatusEnum"

  securitySchemes:
    bearerAuth: # arbitrary name for the security scheme
      type: http
      scheme: bearer
      bearerFormat: JWT # optional, arbitrary value for documentation purposes
