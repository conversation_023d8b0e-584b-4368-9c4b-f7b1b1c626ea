FROM node:20.11.1-slim

RUN apt update && apt install -y libcurl4 python3 build-essential procps pkg-config libunwind-dev

ARG NPM_TOKEN
ENV NPM_TOKEN $NPM_TOKEN

# Create a directory where our app will be placed
RUN mkdir -p /workdir

# Change directory so that our commands run inside this new dir
WORKDIR /workdir

# Copy dependency definitions
COPY .npmrc .npmrc
COPY package.json package.json
COPY package-lock.json package-lock.json

# Install dependecies
RUN npm install

# Copy source files
COPY . .

# Build the TypeScript code
RUN npm run build

# Expose the port the app runs in
EXPOSE 2000 9229 9227

# Copy the mounted secret files to the workdir
RUN --mount=type=secret,id=stage_env,dst=/tmp/stage.env \
    if [ -f /tmp/stage.env ]; then cp /tmp/stage.env /workdir/stage.env; fi
RUN --mount=type=secret,id=prod_env,dst=/tmp/prod.env \
    if [ -f /tmp/prod.env ]; then cp /tmp/prod.env /workdir/prod.env; fi

# Serve the app
CMD ["npm", "start"]
