<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Wealthyhood</title>
    <link rel="icon" type="image/x-icon" href="https://wealthyhood.com/img/logo-icon.png" />

    <style>
      body {
        min-height: 100%;
        background: #101327;
      }

      .container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        min-height: 100vh;
      }

      .loader {
        border: 6px solid #f3f3f3;
        border-radius: 50%;
        border-top: 6px solid #546be5;
        width: 50px;
        height: 50px;
        -webkit-animation: spin 0.5s linear infinite; /* Safari */
        animation: spin 0.5s linear infinite;
      }

      .prompt {
        margin-top: 10vh;
        text-align: center;
        color: white;
        font-family: "Roboto", "-apple-system", Arial, sans-serif !important;
        font-size: 1.5rem;
      }

      /* Safari */
      @-webkit-keyframes spin {
        0% {
          -webkit-transform: rotate(0deg);
        }
        100% {
          -webkit-transform: rotate(360deg);
        }
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    </style>

    <script>
      function isAndroid() {
        if (navigator) {
          return /Android/.test(navigator.userAgent) && !window.MSStream;
        } else {
          return false;
        }
      }

      function isIOS() {
        if (navigator) {
          return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
        } else {
          return false;
        }
      }

      window.onload = () => {
        if (isAndroid()) {
          document.getElementById("android-prompt").style.display = "block";
          document.getElementById("android-prompt-link").href += window.location.search;

          window.location.href = document.getElementById("android-prompt-link").href;
        } else if (isIOS()) {
          document.getElementById("ios-prompt").style.display = "block";
          document.getElementById("ios-prompt-link").href += window.location.search;
        }

        // 1. parse code & user id
        const urlParams = new window.URLSearchParams(window.location.search);
        const code = urlParams.get("code");
        let stateParamsStr = urlParams.get("state");
        const stateParamsObj = Object.fromEntries(
          stateParamsStr.split("__").map((entry) => {
            if (entry.startsWith("selectedPrice")) {
              return ["selectedPrice", entry.split("_").slice(1).join("_")]; // Since plans have _ in them, we re-build the string after splitting
            } else return entry.split("_");
          })
        );

        const ENV_URL_MAPPING = {
          development: "https://localhost:3000",
          staging: "https://demo.wealthyhood.com",
          production: "https://app.wealthyhood.com"
        };

        // 2. make request to API to trigger account storage
        if (stateParamsObj["platform"] === "web") {
          fetch("/truelayer/link-account", {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              code,
              userId: stateParamsObj["userId"]
            })
          })
            .then((response) => response.json())
            .then((data) => {
              // 3. redirect to web link that will handle corresponding modal opening
              const ENDPOINT_MAPPING = {
                dashboard: "",
                "portfolio-lab": "portfolios/lab",
                autopilot: "investor/autopilot",
                "asset-discovery": "portfolios/asset-discovery",
                accounts: "investor/account",
                billing: "investor/billing",
                "change-plan": "investor/change-plan",
                "select-plan": "investor/select-plan",
                "select-plan-success": "investor/select-plan/success",
                "portfolio-creation-success": ""
              };

              const domain = ENV_URL_MAPPING[stateParamsObj["env"]];
              const endpoint = ENDPOINT_MAPPING[stateParamsObj["redirectPage"]] || "";

              if (data.account_number) {
                stateParamsStr += `__accountNumber_${data.account_number}`;
              }

              if (data.bankAccountId) {
                stateParamsStr += `__bankAccountId_${data.bankAccountId}`;
              }

              let optionalQueryParams = "";
              if (stateParamsObj["selectedPrice"]) {
                optionalQueryParams += `&select=${stateParamsObj["selectedPrice"]}`;
              }

              window.location.href = `${domain}/${endpoint}?state=${stateParamsStr}${optionalQueryParams}`;
            })
            .catch(() => {
              window.location.href = ENV_URL_MAPPING[stateParamsObj["env"]];
            });
        }
      };
    </script>
  </head>
  <body>
    <div class="container">
      <img
        style="width: 675px; height: 110px"
        src="https://wealthyhood.com/svg/logo-full-light.svg"
        alt="Wealthyhood Logo"
      />
      <div class="loader"></div>
      <h4 id="android-prompt" class="prompt" style="display: none">
        If you are not redirected to the app within a few seconds,
        <a id="android-prompt-link" href="intent://wealthyhood.bank.redirect" style="color: #6a82fe">tap here</a>.
      </h4>
      <h4 id="ios-prompt" class="prompt" style="display: none">
        If you are not redirected to the app within a few seconds,
        <a id="ios-prompt-link" href="com.wealthyhood://bank-redirect" style="color: #6a82fe">tap here</a>.
      </h4>
    </div>
  </body>
</html>
