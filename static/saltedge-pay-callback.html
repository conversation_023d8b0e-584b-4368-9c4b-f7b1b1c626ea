<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Wealthyhood</title>
    <link rel="icon" type="image/x-icon" href="https://wealthyhood.com/img/logo-icon.png" />

    <style>
      body {
        min-height: 100%;
        background: #101327;
      }

      .container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        min-height: 100vh;
      }

      .loader {
        border: 6px solid #f3f3f3;
        border-radius: 50%;
        border-top: 6px solid #546be5;
        width: 50px;
        height: 50px;
        -webkit-animation: spin 0.5s linear infinite; /* Safari */
        animation: spin 0.5s linear infinite;
      }

      .prompt {
        margin-top: 10vh;
        text-align: center;
        color: white;
        font-family: "Roboto", "-apple-system", Arial, sans-serif !important;
        font-size: 1.5rem;
      }

      /* Safari */
      @-webkit-keyframes spin {
        0% {
          -webkit-transform: rotate(0deg);
        }
        100% {
          -webkit-transform: rotate(360deg);
        }
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    </style>

    <script>
      function isAndroid() {
        if (navigator) {
          return /Android/.test(navigator.userAgent) && !window.MSStream;
        } else {
          return false;
        }
      }

      function isIOS() {
        if (navigator) {
          return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
        } else {
          return false;
        }
      }

      window.onload = () => {
        let queryParametersString = window.location.search;

        if (isAndroid()) {
          document.getElementById("android-prompt").style.display = "block";
          document.getElementById("android-prompt-link").href += queryParametersString;

          // If platform is Android, we immediately redirect to the intent link
          window.location.href = document.getElementById("android-prompt-link").href;
        } else if (isIOS()) {
          document.getElementById("ios-prompt").style.display = "block";
          document.getElementById("ios-prompt-link").href = "com.wealthyhood://saltedge-just-pay" + queryParametersString;
        }
      };
    </script>
  </head>
  <body>
    <div class="container">
      <img
        style="width: 675px; height: 110px"
        src="https://wealthyhood.com/svg/logo-full-light.svg"
        alt="Wealthyhood Logo"
      />
      <div class="loader"></div>
      <h4 id="android-prompt" class="prompt" style="display: none">
        If you are not redirected to the app within a few seconds,
        <a id="android-prompt-link" href="intent://wealthyhood.pay.redirect" style="color: #6a82fe">tap here</a>.
      </h4>
      <h4 id="ios-prompt" class="prompt" style="display: none">
        If you are not redirected to the app within a few seconds,
        <a id="ios-prompt-link" href="com.wealthyhood://saltedge-just-pay" style="color: #6a82fe">tap here</a>.
      </h4>
    </div>
  </body>
</html>
