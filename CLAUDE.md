# CLAUDE.md - Wealthyhood API Guidelines

## Build/Test Commands
- Build: `npm run build`
- Lint: `npm run lint-fix`
- Test: `npm test`
- Run specific test: `jest <test-file-path> -t "<test-name>"`
- Watch mode: `npm run watch`
- Debug test: `npm run debug-test`

## Code Style Guidelines
- TypeScript with strict types - avoid `any` when possible
- Use double quotes for strings
- Semi-colons required
- Max line length: 115 characters
- Imports should be organized: 3rd party first, then local
- Error handling: Throw typed errors from `ApiErrors.ts` (BadRequestError, NotFoundError, etc.)
- Naming: 
  - Controllers/Services: PascalCase classes with kebab-case filenames
  - Variables/parameters: camelCase
  - Enums: PascalCase with Enum suffix
- Logging: Use logger service with module/method context and appropriate level
- Use dependency injection pattern
- Parameter validation with ParamsValidationUtil
- Database: MongoDB with Mongoose
- Decimal.js for precise financial calculations