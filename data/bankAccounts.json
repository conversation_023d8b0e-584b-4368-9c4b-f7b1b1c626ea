[{"_id": "5fe30f4d3fffa300478946d2", "name": "<PERSON>", "number": "********", "sortCode": "01-21-31", "truelayerProviderId": "mock-payments-gb-redirect", "bankId": "mock-payments-gb-redirect", "owner": "5e29b9209e499e006e33f418", "providers": {"wealthkernel": {"id": "add-343ebvlof2423s", "status": "Active"}}}, {"_id": "628c85ff12f7afcabd87cb99", "name": "<PERSON><PERSON><PERSON>", "number": "********", "sortCode": "01-21-31", "truelayerProviderId": "mock-payments-gb-redirect", "bankId": "mock-payments-gb-redirect", "owner": "628c8a6110de9e8e4147863c", "createdAt": **********, "providers": {"wealthkernel": {"id": "bac-355rbdsr5242vc", "status": "Active"}}}, {"_id": "628c8a64f5f9e39562f9d76a", "name": "Paris Kolovos", "number": "********", "sortCode": "01-21-31", "truelayerProviderId": "mock-payments-gb-redirect", "bankId": "mock-payments-gb-redirect", "owner": "628c88bc3771cae4045a73b0", "createdAt": **********, "providers": {"wealthkernel": {"id": "bac-355rbdsu5242vc", "status": "Active"}}}, {"_id": "628c85ff12f7afcabd87cb09", "name": "<PERSON><PERSON>", "number": "********", "sortCode": "01-21-31", "truelayerProviderId": "mock-payments-gb-redirect", "bankId": "mock-payments-gb-redirect", "owner": "628c8a6110de9e8e4247863c", "createdAt": **********, "providers": {"wealthkernel": {"id": "bac-366onh4km242iq", "status": "Active"}}}, {"_id": "628c85ff12f7afcabd87cb10", "name": "<PERSON><PERSON><PERSON>", "number": "********", "sortCode": "04-00-04", "truelayerProviderId": "mock-payments-gb-redirect", "bankId": "mock-payments-gb-redirect", "owner": "628c8a6110de9e8e4247863d", "createdAt": **********, "providers": {"wealthkernel": {"id": "bac-36cx6iq5c242va", "status": "Active"}}}, {"_id": "628c85ff12f7afcabd87cb11", "name": "<PERSON><PERSON>", "number": "********", "sortCode": "04-00-04", "truelayerProviderId": "mock-payments-gb-redirect", "bankId": "mock-payments-gb-redirect", "owner": "628c8a6110de9e8e4247863e", "createdAt": **********, "providers": {"wealthkernel": {"id": "bac-36f4v2bku242ok", "status": "Active"}}}, {"_id": "628c85ff12f7afcabd87cb12", "name": "<PERSON>", "number": "********", "sortCode": "04-00-04", "truelayerProviderId": "mock-payments-gb-redirect", "bankId": "mock-payments-gb-redirect", "owner": "628c8a6110de9e8e424786cf", "createdAt": **********, "providers": {"wealthkernel": {"id": "bac-36f4v2bku242cf", "status": "Active"}}}]